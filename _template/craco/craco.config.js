const fs = require('fs');
const { resolve, join } = require('path');
const { getPlugin, removePlugins, getLoaders, whenProd, when, whenDev } = require('@craco/craco');
const CracoLessPlugin = require('craco-less');
const InterpolateHtml = require('craco-interpolate-html-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const ESLintWebpackPlugin = require('eslint-webpack-plugin');
const FileManagerPlugin = require('filemanager-webpack-plugin');
const { codeInspectorPlugin } = require('code-inspector-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const pxtorem = require('postcss-pxtorem');
const { CracoAliasPlugin } = require('react-app-alias-ex');
const InterpolateHtmlPlugin = require('react-dev-utils/InterpolateHtmlPlugin');
const ModuleNotFoundPlugin = require('react-dev-utils/ModuleNotFoundPlugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const { DefinePlugin, ids, ProvidePlugin } = require('webpack');
const WebpackBar = require('webpackbar');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const { InjectManifest } = require('workbox-webpack-plugin');
const CreateFilesWebpackPlugin = require('./plugins/create-files-webpack-plugin');
const HtmlEntrypointsPlugin = require('./plugins/html-entrypoints-plugin.js');

// 项目启动相关处理===================================================================================
const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
// 研发环境的配置文件类型
const DEVELOP_ENV = process.env.DEVELOP_ENV || '';
// 开发时读取的配置文件
const CONFIG_FILE_PATH = DEVELOP_ENV
  ? resolve('.config', 'develop', `config.${DEVELOP_ENV}.js`)
  : resolve('.config', 'config.js');
// 通用名称
const CONFIG_NAME = 'config.js';
const PACKAGE_JSON_NAME = 'package.json';
const TEMPLATE_NAME = 'template.html';
// 打包相关变量
const CURRENT_TIME = new Date().getTime();
const MAIN_NAME = 'main.js';
const ENTRYPOINTS_NAME = 'entrypoints.js';
const VERSION_NAME = 'version.json';
const MONACO_EDITOR_NAME = 'monaco-editor@0.34.0';
// html里面的src
const HTML_SRC_CONFIG = `${CONFIG_NAME}?t=${CURRENT_TIME}`;
const HTML_SRC_MAIN = `${MAIN_NAME}?t=${CURRENT_TIME}`;
// html的变量
const HTML_VAR_TITLE = '<%- title %>';
const HTML_VAR_DESCRIPTION = '<%- description %>';
const HTML_VAR_EXTRA = '<%- extra %>';

// 编译提示
console.log('===========Status:', IS_DEVELOPMENT, DEVELOP_ENV, CURRENT_TIME);
// 模板文件
const TEMPLATE_PATH = resolve(__dirname, '../resources', TEMPLATE_NAME);
const MONACO_EDITOR_DIR = resolve(__dirname, '../resources', MONACO_EDITOR_NAME);
const PUBLIC_DIR = resolve(__dirname, '../resources', 'public');
const DESIGNABLE_DIR = resolve(__dirname, '../resources', 'designable');
const ALIAS_PATH = resolve(__dirname, '../../tsconfig.alias.comm.json');
const CACHE_PATH = resolve(__dirname, '../../node_modules/.cache');
const PWA_PATH = resolve(__dirname, '../../shares/product-micro-modules/src/datlas/comm/service-worker.ts');
const FILE_OPTIONS = 'utf-8';

// 读取html模板内容==================================================================================
const HTML_TEMPLATE = fs.readFileSync(TEMPLATE_PATH, FILE_OPTIONS);
// 获取项目的相关信息
const getProductInfo = (options) => {
  const product = JSON.parse(fs.readFileSync(PACKAGE_JSON_NAME, FILE_OPTIONS));
  let templateContent = HTML_TEMPLATE.replace(HTML_VAR_TITLE, product.cnName);
  templateContent = templateContent.replace(HTML_VAR_DESCRIPTION, product.description);
  // 调整id
  templateContent = templateContent.replace('id="root"', `id="${options.rootId || 'root'}"`);
  return [product, templateContent];
};

const getAmisSdkVersion = (options) => {
  const amisDir = resolve(__dirname, '../../node_modules/amis');
  const version = require(`${amisDir}/package.json`).version;
  return [amisDir, version];
}

// 追加craco plugin=================================================================================
const addCracoPlugin = (options) => {
  return [
    // 启用less
    {
      plugin: CracoLessPlugin,
      options: {
        sourceMap: false,
        lessLoaderOptions: {
          lessOptions: {
            javascriptEnabled: true,
          },
        },
      },
    },
    // 设置别名
    { plugin: CracoAliasPlugin, options: { tsconfig: ALIAS_PATH } },
  ];
};

// 修改通用的配置=====================================================================================
const addWebpackPlugins = (config, options) => {
  config.plugins.push(
    // 进度提示
    new WebpackBar(),
    // 复制配置文件
    new CopyWebpackPlugin({
      patterns: [
        { from: CONFIG_FILE_PATH, to: CONFIG_NAME },
        { from: PUBLIC_DIR, to: join('static') },
      ],
    }),
    new ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process',
    }),
  );
  return config;
};
// 临时存储，方便其他地方修改
let DEFINE_PLUGIN_VALUE;
const modifyDefinePlugin = (config, options, defineValue) => {
  const result = getPlugin(config, (it) => it.constructor === DefinePlugin);
  if (result.isFound) {
    const plugin = result.match;
    const dfs = {
      __IS_DEVELOPMENT: IS_DEVELOPMENT,
      __BUILD_CURRENT_TIME: CURRENT_TIME,
      __DEVELOP_PROXY_API_URL: JSON.stringify(''),
      __DEVELOP_ENV_ORIGIN: JSON.stringify(''),

    };
    plugin.definitions = Object.assign(plugin.definitions, dfs);
    DEFINE_PLUGIN_VALUE = plugin.definitions;
  }
  return config;
};
const modifyWebpackResolve = (config, options) => {
  // 不引入node的pollify
  config.resolve.fallback = {
    path: false,
    process: require.resolve('process/browser'),
    buffer: require.resolve('buffer'),
  };
  return config;
};
const overrideCommConfigPlugin = (options) => ({
  plugin: {
    overrideWebpackConfig: ({ webpackConfig: config }) => {
      addWebpackPlugins(config, options);
      modifyDefinePlugin(config, options);
      modifyWebpackResolve(config, options);
      return config;
    },
  },
});

// 优化线上的配置=====================================================================================
const modifyProdWebpackOutput = (config, options) => {
  // 解决css的url资源引入问题
  config.output.publicPath = '../../';
  config.output.uniqueName = 'mdt-datlas';
  // 确保全局对象是self
  config.output.globalObject = 'self';
  return config;
};
const modifyProdWebpackSplitChunk = (config, options) => {
  config.plugins.push(new ids.DeterministicModuleIdsPlugin({ maxLength: 6 }));
  config.plugins.push(new ids.DeterministicChunkIdsPlugin({ maxLength: 6 }));
  config.optimization = {
    ...config.optimization,
    chunkIds: false,
    moduleIds: false,
    runtimeChunk: 'single',
    splitChunks: {
      maxSize: 600*1024*8,
      cacheGroups: {
        micro_vendorms: {
          test: /\/node_modules\/maptalks\//,
          name: 'micro_vendorms',
          chunks: 'async',
          priority: 80,
        },
        comm_vendorrr: {
          test: /\/node_modules\/refractor\//,
          name: 'comm_vendorrr',
          chunks: 'async',
          priority: 80,
        },
        comm_vendorme: {
          test: /\/node_modules\/monaco-editor\//,
          name: 'comm_vendorme',
          chunks: 'async',
          priority: 80,
        },
        comm_vendorelk: {
          test: /\/node_modules\/elkjs\/lib\//,
          name: 'comm_vendorelk',
          chunks: 'async',
          priority: 80,
        },
        micro_vendorlib_1: {
          test: /socket.io|lodash\/lodash.js/,
          name: 'micro_vendorlib',
          chunks: 'initial',
          priority: 85,
        },
        micro_vendorlib: {
          test: /\/node_modules\/(graphql|@apollo\/client|picomatch|buffer|axios|buffer|eventemitter2)\//,
          name: 'micro_vendorlib',
          chunks: 'initial',
          priority: 85,
        },
        prod_vendoriui: {
          test: /\/node_modules\/(@metro|@datlas|react-virtualized|dvt-aggregation|ali-react-table-fork|styled-components|react-virtuoso)\//,
          name: 'prod_vendoriui',
          chunks: 'initial',
          priority: 90,
        },
        micro_vendoris: {
          test: /@datlas\/design\/esm\/components\/icons\/.*\.js$/,
          name: 'micro_vendoris',
          chunks: 'all',
          priority: 95,
        },
        micro_vendorrt: {
          test: /\/node_modules\/(react|react-dom|react-router|history)\/cjs\//,
          name: 'micro_vendorrt',
          chunks: 'all',
          priority: 100,
        },
      },
    },
  };
  return config;
};
const removeProdWebpackPlugins = (config, options) => {
  // 删除ModuleNotFoundPlugin 和 ESLintWebpackPlugin，在研发阶段做保障即可
  removePlugins(config, (it) => {
    return it.constructor === ModuleNotFoundPlugin || it.constructor === ESLintWebpackPlugin;
  });
  return config;
};
const addMonacoLoaderConfig = (config, options) => {
  if (options.monaco) {
    const use = [resolve(__dirname, './loaders/add-monaco-loader-config.js')];
    if (options.designable) {
      use.unshift(resolve(__dirname, './loaders/add-designable-loader-config.js'));
    }
    config.module.rules.push({ enforce: 'pre', test: /src\/start\.tsx/, use });
  }
};
const modifyAmisMonacoToMonacaLoader = (config, options) => {
  if (options.amis) {
    const use2 = [resolve(__dirname, './loaders/modify-amis-monaco-loader-config.js')];
    config.module.rules.push(
      { enforce: 'pre', test: /(amis-ui\/.*\/Editor|amis\/.*\/Code).js$/, use: use2 }
    );
  }
  return config;
}
const modifyProdOthers = (config, options) => {
  const [product, templateContent] = getProductInfo(options);
  const productName = product.name;
  const cfs = `window.__DM_${product.name.split('-').join('_').toUpperCase()}_CFS`;
  const outputPath = config.output.path;
  // 模板内容
  // 在微应用下documentfragment无法代理拦截，故采用单个加入
  const entrypointsTemplateContent = `!(function(jsArr,cssArr){var cfs=${cfs};var pp=cfs.deployPublicPath;var cssList=Array.isArray(cssArr)?cssArr:[];var headEle = document.head;cssList.forEach(function(it){var link=document.createElement("link");link.href=pp+it;link.rel="stylesheet";headEle.appendChild(link)});var jsList=Array.isArray(jsArr)?jsArr:[];jsList.forEach(function(it){var script=document.createElement("script");script.src=pp+it;headEle.appendChild(script)});})({{jsArr}},{{cssArr}});`;
  const mainTemplateContent = `(function(){var cfs=${cfs};var dp=window.__MICRO_APP_ENVIRONMENT__?window.__MICRO_APP_PUBLIC_PATH__:cfs.deployPublicPath||'/';!/^((https?):)?\\/\\//gm.test(dp)&&(dp=String(new URL(dp,window.location.origin)));dp.slice(-1)!=='/'&&(dp+='/');cfs.deployPublicPath=dp;var entry=document.createElement('script');entry.type='text/javascript';entry.src=dp+'static/entrypoints.js?t=${CURRENT_TIME}';document.head.appendChild(entry)})();`;
  const versionTemplateContent = `{"name":"${productName}","version": "${product.version}","createTime": "${new Date().toLocaleString()}"}`;
  const debugContent = `<script>(function(){if(window.location.href.includes('mdtdebug=1') || window.__DEBUG_MDT_VCONSOLE){if(!window.__DEBUG_MDT_VCONSOLE_SRC__){window.__DEBUG_MDT_VCONSOLE_SRC__='${options.secdir}static/libs/eruda-3.2.1.js';}var ele=document.createElement('script');ele.src=window.__DEBUG_MDT_VCONSOLE_SRC__;ele.onload=function(){eruda.init();};document.head.appendChild(ele)}})();</script>`
  // 获取extra内容
  const getHtmlExtra = (path = '') => {
    return `<script src='${path}${HTML_SRC_CONFIG}'></script>${options.debug ? debugContent : ''}<script src='${path}${HTML_SRC_MAIN}'></script>`;
  };
  const htmlPluginResult = getPlugin(config, (it) => it.constructor === HtmlWebpackPlugin);
  const htmlOptions = htmlPluginResult.match.userOptions;
  // 拦截注入
  htmlOptions.inject = false;
  // 根目录
  htmlOptions.templateContent = templateContent.replace(HTML_VAR_EXTRA, getHtmlExtra('/'));
  // 不使用template;
  delete htmlOptions.template;
  // 将资源输入到entrypoint.js中
  config.plugins.push(
    new HtmlEntrypointsPlugin({
      templateContent: entrypointsTemplateContent,
      filePath: resolve(outputPath, ENTRYPOINTS_NAME),
      fileOpt: FILE_OPTIONS,
    }),
  );
  // 生成micro.html,datlas.html
  config.plugins.push(
    // 使用相对目录
    new HtmlWebpackPlugin({
      filename: 'micro.html',
      templateContent: templateContent.replace(HTML_VAR_EXTRA, getHtmlExtra('')),
      inject: false,
      minify: htmlOptions.minify,
    }),
    // 使用二级目录(部署的目录)
    new HtmlWebpackPlugin({
      filename: 'datlas.html',
      templateContent: templateContent.replace(HTML_VAR_EXTRA, getHtmlExtra(options.secdir)),
      inject: false,
      minify: htmlOptions.minify,
    }),
  );
  // 添加pwa
  if (options.pwa) {
    config.plugins.push(new InjectManifest({
      swSrc: PWA_PATH,
      // Service Worker注册时的作用范围（scope）是根据其所在的 JS 文件的位置决定的
      // https://pwa.alienzhou.com/9-sheng-chan-huan-jing-zhong-pwa-shi-jian-de-wen-ti-yu-jie-jue-fang-an
      // swDest: 'static/service-worker.js',
      exclude: [
        /\.map$/,
        /asset-manifest\.json$/,
        /LICENSE/,
        /\/media\//,
        /config\.js/,
        /main\.js/,
        /\.html/,
      ],
      maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
    }));
  }

  // 移动编译后的部分产物
  const copys = [
    { source: VERSION_NAME, destination: join('static', VERSION_NAME) },
  ];
  const moves = [
    { source: ENTRYPOINTS_NAME, destination: join('static', ENTRYPOINTS_NAME) },
  ]
  const creates = [
    { path: resolve(outputPath, VERSION_NAME), content: versionTemplateContent, options: FILE_OPTIONS },
    { path: resolve(outputPath, MAIN_NAME), content: mainTemplateContent, options: FILE_OPTIONS },
  ];
  if (options.monaco) {
    copys.push({ source: MONACO_EDITOR_DIR, destination: join('static', MONACO_EDITOR_NAME) });
  }
  if (options.designable) {
    copys.push({ source: DESIGNABLE_DIR, destination: join('static') });
  }
  if (options.amissdk) {
    const [amisDir, sdkVersion] = getAmisSdkVersion(options);
    const prefixLabel = 'mdtamissdk';
    const sdkDirName = `${prefixLabel}-${sdkVersion}`;
    const jsonFileName =  `${prefixLabel}.json`;
    copys.push({source: join(amisDir, 'sdk'), destination: join('static', sdkDirName)});
    moves.push({source: jsonFileName, destination: join('static', jsonFileName)});
    creates.push({
      path: resolve(outputPath, jsonFileName),
      content: JSON.stringify({sdkPath: sdkDirName}),
      options: FILE_OPTIONS
    })
  }
  config.plugins.push(
    new FileManagerPlugin({
      context: outputPath,
      events: { onEnd: { move: moves, copy: copys }},
    }),
  );

  // 追加输出文件
  config.plugins.push(new CreateFilesWebpackPlugin({
    patterns: creates,
  }));

  // 优化webpack的cache
  config.cache = {
    type: 'filesystem',
    name: productName,
    cacheDirectory: resolve(CACHE_PATH, 'webpack'),
    buildDependencies: { config: [__filename] },
  };

  // 优化babel-loader
  const babelResult = getLoaders(config, (it) => {
    return it.loader && it.loader.includes('babel-loader');
  });
  babelResult.matches.forEach(({ loader }) => {
    const options = loader.options;
    options.cacheDirectory = resolve(CACHE_PATH, productName, 'babel-loader');
    const bpls = options.plugins || [];
    bpls.push("@babel/plugin-transform-class-static-block");
    options.plugins = bpls;
    loader.use = [
      { loader: 'thread-loader', options: { workers: 3 } },
      { loader: loader.loader, options },
    ];
    delete loader.loader;
    delete loader.options;
  });

  // postcss追加pxtorem插件
  const postcssResult = getLoaders(config, (it) => {
    return it.loader && it.loader.includes('postcss-loader');
  });
  postcssResult.matches.forEach(({ loader }) => {
    loader.options.postcssOptions.plugins.push(pxtorem);
  });

  return config;
};
const overrideProdConfigPlugin = (options) => ({
  plugin: {
    overrideWebpackConfig: ({ webpackConfig: config }) => {
      modifyProdWebpackOutput(config, options);
      modifyProdWebpackSplitChunk(config, options);
      removeProdWebpackPlugins(config, options);
      addMonacoLoaderConfig(config, options);
      modifyProdOthers(config, options);
      modifyAmisMonacoToMonacaLoader(config, options);
      return config;
    },
  },
});

// 本地开发启用代理===================================================================================
const overrideDevConfigPlugin = (options) => ({
  plugin: {
    overrideWebpackConfig: ({ webpackConfig: config }) => {
      const [product, templateContent] = getProductInfo(options);
      // 获取extra内容
      const getHtmlExtra = (path) => {
        return `<script src='${path}${HTML_SRC_CONFIG}'></script>`;
      };
      const htmlPluginResult = getPlugin(config, (it) => it.constructor === HtmlWebpackPlugin);
      const htmlOptions = htmlPluginResult.match.userOptions;
      htmlOptions.templateContent = templateContent.replace(HTML_VAR_EXTRA, getHtmlExtra(config.output.publicPath));
      // 不使用template;
      delete htmlOptions.template;

      // 优化不压缩
      config.optimization.minimize = false;

      // 优化babel-loader
      const babelResult = getLoaders(config, (it) => {
        return it.loader && it.loader.includes('babel-loader');
      });
      babelResult.matches.forEach(({ loader }) => {
        const options = loader.options;
        const bpls = options.plugins || [];
        bpls.push("@babel/plugin-transform-class-static-block");
        options.plugins = bpls;
        loader.use = [{ loader: 'thread-loader' }, { loader: loader.loader, options }];
        delete loader.loader;
        delete loader.options;
      });
      // 修改amis的monaco加载
      config = modifyAmisMonacoToMonacaLoader(config, options);
      // 追加元素点击定位源代码位置插件
      config.plugins.push(
        codeInspectorPlugin({
          bundler: 'webpack',
        }),
      );
      return config;
    },
  },
});

// 本地开发启用代理===================================================================================
const defaultDevProxy = (env, definePluginValue) => {
  const envOriginMap = {
    dev: 'https://datlas.maicedata-dev.com',
    staging: 'https://datlas.maicedata-staging.com',
    debug: 'https://datlas.maicedata.com',
  };
  const origin = envOriginMap[env];
  if (definePluginValue) {
    definePluginValue.__DEVELOP_PROXY_API_URL = JSON.stringify('/proxyapi');
    definePluginValue.__DEVELOP_ENV_ORIGIN = JSON.stringify(origin);
  }
  return {
    '/proxyapi': {
      target: `${origin}/api`,
      secure: false,
      changeOrigin: true,
      pathRewrite: { '^/proxyapi': '' },
    },
  };
};
const overrideDevServerConfigPlugin = (options) => ({
  plugin: {
    overrideDevServerConfig: ({ devServerConfig: config }) => {
      config.proxy = (options.updateDevProxy || defaultDevProxy)(DEVELOP_ENV, DEFINE_PLUGIN_VALUE);
      config.headers = { 'Access-Control-Allow-Origin': '*' };
      return config;
    },
  },
});

// 追加性能优化分析的配置==============================================================================
const addPerformanceAnalyzePlugin = (options) => ({
  plugin: {
    overrideWebpackConfig: ({ webpackConfig: config }) => {
      // 目前SpeedMeasurePlugin 和 MiniCssExtractPlugin 不兼容，故先移出，再加入进来
      // const result = getPlugin(config, (it) => it.constructor === MiniCssExtractPlugin);
      // removePlugins(config, (it) => it.constructor === MiniCssExtractPlugin);

      // 打包耗时分析
      // const smp = new SpeedMeasurePlugin();
      // const wrapConfig = smp.wrap(config);
      // wrapConfig.plugins.push(result.match);
      // 打包体积分析
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          reportFilename: 'report.html',
        }),
      );
      return config;
    },
  },
});

const loadProductConfig = (options) => {
  options = options && typeof options === 'object' ? options : {};
  const withAnalyze = process.argv.includes('--analyze');

  return [
    overrideCommConfigPlugin(options),
    ...addCracoPlugin(options),
    ...whenProd(() => [overrideProdConfigPlugin(options)], []),
    ...whenDev(() => [overrideDevConfigPlugin(options), overrideDevServerConfigPlugin(options)], []),
    ...when(withAnalyze, () => [addPerformanceAnalyzePlugin(options)], []),
  ];
};

module.exports = { loadProductConfig };
