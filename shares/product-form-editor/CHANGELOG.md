# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.20.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.17...@mdt/product-form-editor@0.20.18) (2025-07-24)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.16...@mdt/product-form-editor@0.20.17) (2025-07-15)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.15...@mdt/product-form-editor@0.20.16) (2025-07-08)

### Features

- ✨ 增加密文展示形式, DataTable 增加密文的展示形式 ([7966081](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/796608167ef35ac8fd005a6ad778d3f8d0781d9b))

## [0.20.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.14...@mdt/product-form-editor@0.20.15) (2025-07-03)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.13...@mdt/product-form-editor@0.20.14) (2025-06-10)

### Bug Fixes

- 🐛 一表通描述超出展示优化 ([7b610a9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7b610a9348a6eddb05dfabc38634700468d0eb64))

## [0.20.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.12...@mdt/product-form-editor@0.20.13) (2025-05-29)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.11...@mdt/product-form-editor@0.20.12) (2025-05-19)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.10...@mdt/product-form-editor@0.20.11) (2025-05-14)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.9...@mdt/product-form-editor@0.20.10) (2025-04-27)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.8...@mdt/product-form-editor@0.20.9) (2025-04-21)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.7...@mdt/product-form-editor@0.20.8) (2025-04-02)

### Bug Fixes

- 🐛 修复一表通若干问题 ([acfad8b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/acfad8b6f9e6fabc9c633b6effd2fe79f37a4f14))

## [0.20.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.6...@mdt/product-form-editor@0.20.7) (2025-03-31)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.5...@mdt/product-form-editor@0.20.6) (2025-03-31)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.4...@mdt/product-form-editor@0.20.5) (2025-03-27)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.3...@mdt/product-form-editor@0.20.4) (2025-03-27)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.2...@mdt/product-form-editor@0.20.3) (2025-03-13)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.1...@mdt/product-form-editor@0.20.2) (2025-03-10)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.20.0...@mdt/product-form-editor@0.20.1) (2025-02-27)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.5...@mdt/product-form-editor@0.20.0) (2025-02-24)

### Bug Fixes

- 🐛 预览文字支持换行 ([14cded7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/14cded7221f42d4c01acd93fbc8c3d3793a562c8))

### Features

- ✨ 响应器规则添加 ([194c80e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/194c80ead2f752e278dd10b36feedc311268bbde))

## [0.19.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.4...@mdt/product-form-editor@0.19.5) (2025-02-17)

### Bug Fixes

- 修复些 bug ([7f9db2c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7f9db2cce4369662bc4a093485de19882e09926a))

### Features

- ✨ 树形结构增加设置默认展开与否属性 ([ed31e96](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed31e96168d297fb8e3423b054af53dddf24f5d2))

## [0.19.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.3...@mdt/product-form-editor@0.19.4) (2025-01-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.19.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.2...@mdt/product-form-editor@0.19.3) (2025-01-16)

### Bug Fixes

- 🐛 修改 displayType 到属性值 ,删除从 field 到 props 的逻辑处理 ([65926b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65926b0839bc2c3e35e6c71682289e99b5f59383))

### Features

- ✨ 支持不同的预览界面的展现形式 ([a25c50f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a25c50f7dbfb1a5f347028259629e1eff86e32d6))

## [0.19.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.1...@mdt/product-form-editor@0.19.2) (2025-01-09)

### Bug Fixes

- 🐛 暗色下预览 text 文字颜色修复 ([c0995b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c0995b2ad55fd99e58793a7386b505833e7941ac))

### Features

- ✨ 表单 url 预览可点击 ([ff262f4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ff262f4ac2db7aca7f52cd19e060aad4d96a987a))

## [0.19.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.19.0...@mdt/product-form-editor@0.19.1) (2025-01-06)

### Bug Fixes

- 🐛 fix issues ([55032b3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55032b30bada0e0608f6c24a4fb444a129834d71))
- 🐛 修复整数小数输入框一直红色的问题 i ([261869c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/261869ced3aaf515091a51dabb7ffbc040a4ebd6))
- 🐛 联想填空国际化,修改错误提示的显示 ([eb4762b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eb4762ba115d411563860c7ea46758d166854e71))
- 🐛 表单设计预览模式不允许设置表单 ([3ae500c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ae500cf0d27e7a15f0b72aeea7199f62ca9972d))

### Features

- ✨ input 增加数据源和数据源输入限制,增加错误提示 ([ef9ffbd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef9ffbd605a651dce53ea93b61a578b76c039eb8))
- ✨ 一表通文件上传校验数据 ([1696f17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1696f174f6b53727a2c58272f0ca8d3bd7fe608f))
- ✨ 提交按钮增加显示条件处理 ([a6ea52d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a6ea52d0f19b1a8fc67338331def30700d9a7f50))
- ✨ 联想填空增加数据限制和错误提示 ([126b5c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/126b5c87cb2feef16c4c548a4b8c62bebcbd13cd))

# [0.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.8...@mdt/product-form-editor@0.19.0) (2024-12-23)

### Features

- ✨ 数据源加载 loading 态 ([4cac800](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cac80086a06d6660f1ee27a050e62ef71cdba4b))

## [0.18.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.7...@mdt/product-form-editor@0.18.8) (2024-12-16)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.6...@mdt/product-form-editor@0.18.7) (2024-12-16)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.5...@mdt/product-form-editor@0.18.6) (2024-12-16)

### Bug Fixes

- 🐛 修改级联的错误名称 ([780c567](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/780c5679baf1b4426b02ee30a2d2e1f0dd551354))

### Features

- ✨ 显隐设置增加表达式设置,同时兼容之前 condition 模式 ([c726e8c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c726e8ccf2fb42d9b78af534158702ab9c16b542))

## [0.18.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.4...@mdt/product-form-editor@0.18.5) (2024-12-03)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.3...@mdt/product-form-editor@0.18.4) (2024-12-02)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.1...@mdt/product-form-editor@0.18.3) (2024-12-02)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.1...@mdt/product-form-editor@0.18.2) (2024-12-02)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.18.0...@mdt/product-form-editor@0.18.1) (2024-11-26)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.17.2...@mdt/product-form-editor@0.18.0) (2024-11-26)

### Features

- ✨ 数据包创建表单时间类型修正 ([d089c77](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d089c7774b1a3cd755b115ee1bcefabb7c3a7cb3))
- ✨ 时间类型存储切换 ok ([5a2e4b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a2e4b04b10212f948f488c61e8138e6cc8e71e0))
- ✨ 说明文字支持更多类型 ([86d5c94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86d5c94217edabe5444cccdaa83a4a88d62936b4))

## [0.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.17.1...@mdt/product-form-editor@0.17.2) (2024-11-14)

### Features

- ✨ 周期改版 ([901090d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/901090db719c8b5efe07849b13e8e46057ea029b))

## [0.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.17.0...@mdt/product-form-editor@0.17.1) (2024-11-07)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.17...@mdt/product-form-editor@0.17.0) (2024-11-05)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.16...@mdt/product-form-editor@0.16.17) (2024-11-04)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.15...@mdt/product-form-editor@0.16.16) (2024-10-31)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.14...@mdt/product-form-editor@0.16.15) (2024-10-29)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.13...@mdt/product-form-editor@0.16.14) (2024-10-28)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.12...@mdt/product-form-editor@0.16.13) (2024-10-27)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.11...@mdt/product-form-editor@0.16.12) (2024-10-25)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.10...@mdt/product-form-editor@0.16.11) (2024-10-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.9...@mdt/product-form-editor@0.16.10) (2024-10-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.8...@mdt/product-form-editor@0.16.9) (2024-10-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.7...@mdt/product-form-editor@0.16.8) (2024-10-22)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.6...@mdt/product-form-editor@0.16.7) (2024-10-15)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.5...@mdt/product-form-editor@0.16.6) (2024-09-12)

### Bug Fixes

- 表单预览优化 ([daaaf23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/daaaf23fc8d6d7c1bc398e606ebaa01f915e53be))

## [0.16.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.4...@mdt/product-form-editor@0.16.5) (2024-08-29)

### Bug Fixes

- 🐛 Switch formily 给定默认值为 false ([4fefef2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4fefef2c981e361f256c1168987f45bcde6a8052))

## [0.16.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.3...@mdt/product-form-editor@0.16.4) (2024-08-27)

### Bug Fixes

- 修复时间白屏 ([b82fc3c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b82fc3c6f809efb98d567b57b466dd0da811f160))
- 🐛 联想增加阅读态 ([a2d8e18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a2d8e18c2145750828500acdaa2220032aff3699))

### Features

- ✨ 增加联想填空题型 ([ee005ea](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee005eaf25c0af597dacc2df1e166ec4eb699d5f))
- 日期类型时间戳+默认值填写优化 ([4533be2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4533be20d00bdee4788a189649641a5d342cbb0c))

## [0.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.2...@mdt/product-form-editor@0.16.3) (2024-08-19)

### Features

- ✨ 选人 ([9d77e48](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d77e4889ae897aaa654d7a821aba4854f44719c))

## [0.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.1...@mdt/product-form-editor@0.16.2) (2024-08-13)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.16.0...@mdt/product-form-editor@0.16.1) (2024-08-12)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.15.2...@mdt/product-form-editor@0.16.0) (2024-08-09)

### Bug Fixes

- 🐛 style lint fix ([72a6ae1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72a6ae12a7cbe0d7caab832d558bc6d0ecf4b0ca))
- 🐛 题型设置其他选项 bug fix ([b7fe60a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b7fe60a911ca85edbc0ad92a55494484c5e04535))
- 默认值缺失 ([d734ec4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d734ec4e26b59079f6ff348d0b7ca38fe1f65320))

## [0.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.15.1...@mdt/product-form-editor@0.15.2) (2024-08-07)

### Features

- ✨ treeselect 题型属性配置 ([3ded1f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ded1f9d36c09478d8a447858df6ac2087925464))

## [0.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.15.0...@mdt/product-form-editor@0.15.1) (2024-08-05)

### Features

- ✨ 新增题目说明设置 ([0eb3436](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0eb34364448d842a6d5fc7141d2b23e4d29270e3))

# [0.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.14.4...@mdt/product-form-editor@0.15.0) (2024-08-02)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.14.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.14.3...@mdt/product-form-editor@0.14.4) (2024-08-02)

### Bug Fixes

- 🐛 修复 TreeSelect 搜索按照 value 的问题 ([389f48d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/389f48d1b1b3fe83b56c8d532fd58ba947e10ee0))

### Features

- ✨ usertask 指定组织负责人 ([72472d9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72472d95d9c871233868bef78f993d8bef58b21a))
- ✨ add new question ([6fb9a82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fb9a823593847e70adba43ddc80f83650f6f88c))

## [0.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.14.2...@mdt/product-form-editor@0.14.3) (2024-07-29)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.14.1...@mdt/product-form-editor@0.14.2) (2024-07-25)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.14.0...@mdt/product-form-editor@0.14.1) (2024-07-24)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.13.2...@mdt/product-form-editor@0.14.0) (2024-07-22)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.13.1...@mdt/product-form-editor@0.13.2) (2024-07-17)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.13.0...@mdt/product-form-editor@0.13.1) (2024-07-10)

### Bug Fixes

- 🐛 Cascader 处理 array 包裹 string 的情况 ([1676711](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16767113acf843eb0561b108272ff37821a8d110))
- 🐛 一表通反馈问题修复 ([6ba7036](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ba7036000cb142a646491d6ac1d3d3e072730a6))

# [0.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.12.1...@mdt/product-form-editor@0.13.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))

## [0.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.12.0...@mdt/product-form-editor@0.12.1) (2024-06-24)

### Features

- ✨ 依赖配置添加强制覆盖选项 ([1956403](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1956403b3b0e984f27e36454f8b3af0fd0aacc2a))

# [0.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.11.2...@mdt/product-form-editor@0.12.0) (2024-06-17)

### Bug Fixes

- import error ([9711b35](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9711b35133ca7d974c1d449b66275060991722f6))

### Features

- ✨ 增加组件国际化的全局配置 ([7b6cfbe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7b6cfbe1badc7261a5c455549683621c532480a9))
- ✨ 市民信息组件& 子流程数据查询调整 ([ac52dd2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac52dd28fcde51da30f8e3b2db36ddde1df8a5e6))

## [0.11.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.11.1...@mdt/product-form-editor@0.11.2) (2024-06-03)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.11.0...@mdt/product-form-editor@0.11.1) (2024-05-20)

### Features

- ✨ 优化及 bug fix ([988c2eb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/988c2eb38ce87beb8f325558415c0b0ee5da742f))
- ✨ 增加上传的图片压缩功能 ([7c5cd06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7c5cd066c01a32b83d35823b9b30497dfa6c850b))

# [0.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.13...@mdt/product-form-editor@0.11.0) (2024-05-13)

### Bug Fixes

- 🐛 数字输入框不能清空 ([dc52f94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc52f94f10ea4b675c4717ca087ccdf2ce246980))

### Features

- ✨ excel 模版 ([d57485d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d57485d2749ee5743d1942b68e7f0d5cd4aed178))
- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [0.10.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.7...@mdt/product-form-editor@0.10.13) (2024-04-29)

### Features

- add amis editor ([e11f1e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e11f1e9eac9d81f2763cab3e19c694cd11fac8dd))

## [0.10.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.6...@mdt/product-form-editor@0.10.7) (2024-04-24)

### Bug Fixes

- 🐛 metro select ([c41e5c1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c41e5c1da04c82bb3bdfa1242652f1eb213acbdc))

## [0.10.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.5...@mdt/product-form-editor@0.10.6) (2024-04-24)

### Bug Fixes

- 🐛 数值类型使用 numberpicker 组件 ([7e61551](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e615511d85c6e599dc3417816af4796e447ad29))

## [0.10.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.4...@mdt/product-form-editor@0.10.5) (2024-04-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.10.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.3...@mdt/product-form-editor@0.10.4) (2024-04-23)

### Features

- ✨ 数据源支持当前机构部门列表 & qlang 依赖项有默认值时没有执行 sql ([c608935](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c608935ba7667ba1ac9a9eed4cff661db314199e))

## [0.10.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.2...@mdt/product-form-editor@0.10.3) (2024-04-15)

### Features

- ✨ 水平模式、暗色系优化 ([0d7611b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0d7611b9b588feb57a5c8b73bb3feea3abbecc62))
- ✨ 选择题支持其它选项 ([768d239](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/768d2397cbdfc199bd6a86225ceccbea38638221))

## [0.10.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.1...@mdt/product-form-editor@0.10.2) (2024-04-08)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.10.0...@mdt/product-form-editor@0.10.1) (2024-04-01)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.9.0...@mdt/product-form-editor@0.10.0) (2024-03-25)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.8.1...@mdt/product-form-editor@0.9.0) (2024-03-11)

### Bug Fixes

- 🐛 修复九宫格页面的样式污染 ([d7ce829](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d7ce829bb1d23ded6648c98d9c76ba38713c7e59))

### Features

- ✨ 文件夹功能 ([9d6680e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d6680e70da17e00d743efcf602480e9e62f0f40))
- ✨ 智能搜索&数据源支持权限及用户列表 ([f717ead](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f717ead66bfb87cacc9be624dcb6ce4dc0e00f34))

## [0.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.8.0...@mdt/product-form-editor@0.8.1) (2024-03-07)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.6...@mdt/product-form-editor@0.8.0) (2024-03-01)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.7.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.5...@mdt/product-form-editor@0.7.6) (2024-02-28)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.7.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.4...@mdt/product-form-editor@0.7.5) (2024-02-23)

### Features

- ✨ bpmn 变量管理&审批调整 ([c7bf101](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c7bf101bf05471e43a1cd926094d2d80c0e35754))

## [0.7.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.3...@mdt/product-form-editor@0.7.4) (2024-01-29)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.7.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.2...@mdt/product-form-editor@0.7.3) (2024-01-15)

### Features

- ✨ bpmn 全局变量相关优化 ([e7b033c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7b033c3641e97300198aa6b84d87bf7bbf6c2f6))

## [0.7.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.1...@mdt/product-form-editor@0.7.2) (2024-01-08)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.7.0...@mdt/product-form-editor@0.7.1) (2024-01-02)

### Bug Fixes

- 🐛 change the write ([175163a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/175163a8527ade6f752eb11c7e7a11d5fa8ac73e))

# [0.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.6.2...@mdt/product-form-editor@0.7.0) (2024-01-02)

### Features

- ✨ 接入移动端级联&定制表单覆盖初始表单字段值&timer 定制表单 ([e94a877](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e94a8776bbe3e63f96515202eddddc3cb8b3d632))

## [0.6.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.6.1...@mdt/product-form-editor@0.6.2) (2023-12-25)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.6.0...@mdt/product-form-editor@0.6.1) (2023-12-25)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.5.2...@mdt/product-form-editor@0.6.0) (2023-12-25)

### Features

- ✨ sso 增加微信和企业微信授权分发流程, 增加分发路由中转页 ([6d341f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6d341f8b2136c94d40610edb5f7ce71a1017cc0f))
- ✨ 增加服务导航页 ([296c20a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/296c20a9eb42f02f8ddbc7c57882cb5970fa0670))
- ✨ 表单添加创建者参数& 上传数据包填报模版时支持重选数据包;文件组件添加最大最小数量限制 ([4ebb430](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ebb43037bf9d7753c44ad3f89d60b5809adb0e1))

## [0.5.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.5.1...@mdt/product-form-editor@0.5.2) (2023-12-18)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.5.0...@mdt/product-form-editor@0.5.1) (2023-12-11)

**Note:** Version bump only for package @mdt/product-form-editor

# [0.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.4.2...@mdt/product-form-editor@0.5.0) (2023-12-08)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.4.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.4.1...@mdt/product-form-editor@0.4.2) (2023-12-04)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.4.0...@mdt/product-form-editor@0.4.1) (2023-12-04)

### Features

- ✨ 级联选择&下载数据包时支持指定列 ([8a97c2e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a97c2ea17d665b6cf3105bd455f5073d1b56e68))

# [0.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.3.0...@mdt/product-form-editor@0.4.0) (2023-12-01)

### Features

- ✨ 机构偏好 ([92a66b9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/92a66b963cdd7c2907a76802152bfbb131c51a5d))
- ✨ 自增表格&资源分享支持数据源 ([27820ab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27820abbd075bea5471885660183f851e76241b5))

# [0.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.2.0...@mdt/product-form-editor@0.3.0) (2023-11-20)

### Features

- ✨ 自增卡片 ([7dc9f0b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7dc9f0bec8926c123d8583b578e0c0b2bfdd041b))

# [0.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.11...@mdt/product-form-editor@0.2.0) (2023-11-13)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.10...@mdt/product-form-editor@0.1.11) (2023-11-06)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.9...@mdt/product-form-editor@0.1.10) (2023-11-03)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.8...@mdt/product-form-editor@0.1.9) (2023-11-01)

### Bug Fixes

- 🐛 修复打包报错的问题 ([2785eb5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2785eb5d713bd478d056a12bdd4ebc19e911fd85))

## [0.1.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.7...@mdt/product-form-editor@0.1.8) (2023-10-31)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.6...@mdt/product-form-editor@0.1.7) (2023-10-31)

### Performance Improvements

- ⚡️ html load faster ([e7f2d23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7f2d233d799c6081d3cae94f1ca0663ebe20e28))

## [0.1.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.5...@mdt/product-form-editor@0.1.6) (2023-10-30)

### Features

- ✨ 增加全局默认值 ([385fae8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/385fae8753bf74ec8acfd07273a8d80ef87f1b04))

### Performance Improvements

- ⚡️ build faster ([1c14c93](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c14c9346709282613bed42addcdfcf7d359575f))

## [0.1.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.4...@mdt/product-form-editor@0.1.5) (2023-10-26)

### Bug Fixes

- 🐛 标题在移动端显示问题 ([39c6224](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39c62244f12bd43688273ff2e0f8e06cc3be14ce))

## [0.1.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.3...@mdt/product-form-editor@0.1.4) (2023-10-25)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.2...@mdt/product-form-editor@0.1.3) (2023-10-23)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.1...@mdt/product-form-editor@0.1.2) (2023-10-16)

**Note:** Version bump only for package @mdt/product-form-editor

## [0.1.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-form-editor@0.1.0...@mdt/product-form-editor@0.1.1) (2023-10-10)

### Bug Fixes

- 🐛 mobile style ([0de8cdb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0de8cdb271898b84d3577b4745832d2bc3c3ec19))

### Features

- add ai assiast ([18399e0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18399e0c30afbc12e9a1ce8647e212b614423341))

# 0.1.0 (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))
