import { MetroSettingProp } from '../shared';

export const AutoComplete = {
  'zh-CN': {
    title: '联想填空',
    settings: {
      'x-component-props': {
        title: '联想填空',
        filterOption: '搜索筛选',
        noValueContent: '空值内容',
        displayType: { title: '预览形式', dataSource: ['链接', '自动识别链接', 'Markdown', 'Html', '密文'] },
        secretVisible: { title: '密文默认可见' },
        secretRegex: {
          title: '密文规则',
          dataSource: ['手机号', '手机号(严格)', '邮箱', '身份证', '银行卡', '姓名', '地址', '信用卡'],
        },
      },
      [MetroSettingProp.inputType]: { title: '输入框类型', dataSource: ['单行', '多行'] },
      [MetroSettingProp.inputContentType]: {
        title: '内容限制',
        dataSource: ['不限制', '身份证号', '手机号', '邮箱', 'URL地址', '在数据源内', '不在数据源内'],
      },
    },
  },
  'en-US': {
    title: 'AutoComplete',
    settings: {
      'x-component-props': {
        title: 'Auto Complete',
        filterOption: 'Search Filter',
        noValueContent: 'No Value Content',
        displayType: { title: 'Preview Type', dataSource: ['Link', 'Auto Link', 'Markdown', 'Html', 'Secret'] },
        secretVisible: { title: 'Secret Default Visible' },
        secretRegex: {
          title: 'Secret Rule',
          dataSource: ['Phone', 'Phone Strict', 'Email', 'ID Card', 'Bank Card', 'Name', 'Address', 'Credit Card'],
        },
      },
      [MetroSettingProp.inputType]: { title: 'Input Type', dataSource: ['Default', 'Multiple Line'] },
      [MetroSettingProp.inputContentType]: {
        title: 'Content Type',
        dataSource: ['Default', 'ID Card', 'Mobile', 'Email', 'URL', 'In data source', 'Not in data source'],
      },
    },
  },
};
