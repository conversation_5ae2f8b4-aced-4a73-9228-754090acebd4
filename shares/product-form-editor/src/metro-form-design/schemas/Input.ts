import { ISchema } from '@formily/react';
import i18n from '../../languages';
import { MetroSettingProp } from '../../metro-form-design/shared';

const isText = '{{$deps[0] === "text"}}';
const isNotText = '{{$deps[0] !== "text"}}';
const isNumber = '{{$deps[0] === "integer" || $deps[0] === "decimal"}}';
const isDecimal = '{{$deps[0] === "decimal"}}';
const isDataSource = '{{["notInDataSource", "inDataSource"].includes($deps[0])}}';

export const Input: ISchema = {
  type: 'void',
  properties: {
    'x-component-props': {
      type: 'object',
      properties: {
        placeholder: {
          type: 'string',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
        },
        displayType: {
          type: 'string',
          enum: ['link', 'autoLink', 'markdown', 'html', 'secret'],
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            layout: 'horizontal',
            tooltip: i18n.chain.metroFormDesign.displayTypeTip,
          },
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
          },
        },
        secretVisible: {
          type: 'boolean',
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-decorator-props': {
            layout: 'horizontal',
          },
          'x-reactions': {
            dependencies: ['.displayType'],
            fulfill: {
              state: {
                visible: '{{$deps[0] === "secret"}}',
              },
            },
          },
        },
        secretRegex: {
          type: 'string',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-decorator-props': {
            layout: 'horizontal',
            tooltip: i18n.chain.metroFormDesign.secretRegexTip,
          },
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            allowClear: true,
          },
          enum: ['PHONE', 'PHONE_STRICT', 'EMAIL', 'ID_CARD', 'BANK_CARD', 'NAME_CN', 'ADDRESS', 'CREDIT_CARD'],
          'x-reactions': {
            dependencies: ['.displayType'],
            fulfill: {
              state: {
                visible: '{{$deps[0] === "secret"}}',
              },
            },
          },
        },
      },
    },
    [MetroSettingProp.inputContentType]: {
      type: 'string',
      enum: ['text', 'integer', 'decimal', 'idcard', 'phone', 'email', 'url', 'inDataSource', 'notInDataSource'],
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'Select',
    },
    [MetroSettingProp.inputType]: {
      title: i18n.chain.metroFormDesign.inputType,
      type: 'string',
      enum: ['single', 'multiple'],
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'Radio.Group',
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isText,
          },
        },
      },
    },
    [MetroSettingProp.errorTip]: {
      title: i18n.chain.metroFormDesign.errorTip,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-component': 'Input',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isNotText,
          },
        },
      },
    },
    [MetroSettingProp.minLen]: {
      title: i18n.chain.metroFormDesign.minLength,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'NumberPicker',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isText,
          },
        },
      },
    },
    [MetroSettingProp.maxLen]: {
      title: i18n.chain.metroFormDesign.maxLength,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'NumberPicker',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isText,
          },
        },
      },
    },
    [MetroSettingProp.decimalCount]: {
      title: i18n.chain.metroFormDesign.decimalCount,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'NumberPicker',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isDecimal,
          },
        },
      },
    },
    [MetroSettingProp.min]: {
      title: i18n.chain.metroFormDesign.min,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'NumberPicker',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isNumber,
          },
        },
      },
    },
    [MetroSettingProp.max]: {
      title: i18n.chain.metroFormDesign.max,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      'x-component': 'NumberPicker',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isNumber,
          },
        },
      },
    },
    [MetroSettingProp.inputDataSourceKey]: {
      title: i18n.chain.metroFormDesign.inputDataSourceKey,
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'horizontal',
      },
      enum: ['label', 'value'],
      'x-component': 'Radio.Group',
      'x-component-props': {
        defaultValue: 'label',
      },
      'x-reactions': {
        dependencies: [MetroSettingProp.inputContentType],
        fulfill: {
          state: {
            visible: isDataSource,
          },
        },
      },
    },
  },
};
