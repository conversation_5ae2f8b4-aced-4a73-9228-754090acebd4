import { en as proComEn } from '@mdtProComm/languages';
import { Locale } from './cn';

export const en: Locale = {
  ...proComEn,
  proFormEditor: {
    columnName: 'Column',
    form: 'Form',
  },
  metroFormDesign: {
    treeDefaultExpandAll: 'Default expand all nodes',
    nameEmptyError: 'Please set the name attribute of the question first',
    enableDragComp: 'Enable drag component',
    usual: 'Usual',
    inputTip:
      'The field name stored internally in the system is used for internal data storage and association references. This identifier must be unique within a process; when used for data packet submission processes, this "form field" needs to correspond to the actual data packet field name.',
    //
    reactionsTip:
      'Used to configure linkage relationships between form fields. Although reactors, display conditions, and dependency configurations can coexist without conflict, duplicate configuration is not recommended. For conditional display combinations, it is recommended to use display conditions; for proxy requests or qlang scenarios, it is recommended to use dependency configuration; for other scenarios, use reactor rules. If you need to use them in combination, reactor rules calculate slightly faster than display conditions and dependency configurations (for example: if you hide a component with dependency configuration through reactor rules, the dependency configuration will not be triggered because the component is hidden)',
    displayTypeTip:
      'After selection, you can specify to display content in different forms, such as links, Markdown, HTML, etc.',
    secretRegexTip: 'Select the regular expression to be used to match the secret.',
    autoCompleteFilterTip: 'After being enabled, search suggestions will be filtered based on the input value.',
    noValueContentTip: 'After being enabled, the list will not be displayed when there is no value.',
    datasource: 'Datasource',
    advance: 'Advanced',
    visibleOn: 'Visible Condition',
    depSetting: 'Dependency Configuration',
    noDep: 'No Dependencies',
    name: 'Name',
    idCard: 'ID Card',
    birthday: 'Birthday',
    mobile: 'Mobile',
    address: 'Address',
    imgField: 'Image Field',
    img: 'Image',
    attachmentField: 'Attachment Field',
    attachment: 'Attachment',
    eg: 'Eg：',
    customeFormatter: 'Custome formatter',
    datetimeValueStrTip: 'Note: Date-time functions cannot be used when the value is a string.',
    fieldTitle: 'Title',
    fieldDesc: 'Description',
    btnContent: 'Button Content',
    btnStyle: 'Button Style',
    btnVisible: 'Buttion Visible Setting',
    defaultValue: 'Default Value',
    inputType: 'Input Box Type',
    minLength: 'Minimum Length',
    maxLength: 'Maximum Length',
    decimalCount: 'Decimal Places',
    min: 'Minimum Value',
    max: 'Maximum Value',
    inputDataSourceKey: 'Data source key',
    errorTip: 'Error Message',
    filedType: 'Question Type',
    rateCount: 'Total Stars',
    addOtherOption: 'Add Other Option',
    addOtherContent: 'Add Other Content',
    radioGroup: 'Single Choice',
    checkboxGroup: 'Multiple Choice',
    datePicker: 'Date and Time',
    input: 'Q&A',
    geometryInput: 'Geography',
    upload: 'Attachment',
    switch: 'Switch',
    rate: 'Rating',
    arrayCards: 'Auto-increment Cards',
    arrayTable: 'Auto-increment Table',
    cascader: 'Cascading Selection',
    autoComplete: 'Auto Complete',
    changeFieldType: 'Switch Question Type',
  },
};
