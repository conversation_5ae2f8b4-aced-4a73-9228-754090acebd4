import { cn as proComCn } from '@mdtProComm/languages';

export const cn = {
  ...proComCn,
  proFormEditor: {
    columnName: '列名',
    form: '表单',
  },
  metroFormDesign: {
    treeDefaultExpandAll: '默认展开所有节点',
    nameEmptyError: '请先设置题目的name属性',
    enableDragComp: '可拖入组件',
    usual: '常规',
    inputTip:
      '系统内部存储的字段名称，用于进行内部数据存储和关联引用，在一个流程内该标识需要保证唯一；当用于数据包填报流程时，该”表单字段“需要对应真实的数据包字段名称。',
    reactionsTip:
      '用于配置表单字段间的联动关系。虽然响应器、显示条件和依赖配置可以共存且不冲突，但不建议重复配置。对于带有条件的显示组合方式推荐使用显示条件，对于代理请求或qlang等场景推荐使用依赖配置，其他场景则使用响应器规则。如果需要组合使用的话，响应器规则的计算速度略高于现实条件和依赖配置（举个例子：比如通过响应器规则隐藏带有依赖配置的组件，那么依赖配置则会因为组件隐藏而不触发）',
    displayTypeTip: '选择后可以指定将内容以不同形式展示，例如链接、Markdown、HTML等',
    secretRegexTip: '选择可使用的正则表达式，用于匹配密文',
    autoCompleteFilterTip: '开启后，搜索联想会根据输入值过滤',
    noValueContentTip: '输入后在没有值的状态下不会展示联想列表',
    datasource: '数据源',
    advance: '高级',
    visibleOn: '显示条件',
    depSetting: '依赖配置',
    noDep: '暂无依赖项',
    name: '姓名',
    idCard: '身份证',
    birthday: '出生日期',
    mobile: '联系方式',
    address: '居住地址',
    imgField: '图片题',
    img: '图片',
    attachmentField: '附件题',
    attachment: '附件',
    eg: '例如：',
    customeFormatter: '自定义格式',
    datetimeValueStrTip: '注意: 值为字符串后将无法使用日期时间相关的函数。请确认你后续无需使用日期时间相关功能',
    fieldTitle: '标题',
    fieldDesc: '描述',
    btnContent: '按钮内容',
    btnStyle: '按钮样式',
    btnVisible: '按钮显示条件',
    defaultValue: '默认值',
    inputType: '输入框类型',
    minLength: '最小长度',
    maxLength: '最大长度',
    decimalCount: '小数位数',
    min: '最小值',
    max: '最大值',
    inputDataSourceKey: '数据源取值字段',
    errorTip: '错误提示',
    filedType: '题目类型',
    rateCount: '星星总数',
    addOtherOption: '添加其他选项',
    addOtherContent: '添加其他内容',
    radioGroup: '单选',
    checkboxGroup: '多选',
    datePicker: '日期与时间',
    input: '问答',
    geometryInput: '地理',
    upload: '附件',
    switch: '开关',
    rate: '评分',
    arrayCards: '自增卡片',
    arrayTable: '自增表格',
    cascader: '级联选择',
    autoComplete: '联想填空',
    changeFieldType: '切换题目类型',
  },
};

export type Locale = typeof cn;
