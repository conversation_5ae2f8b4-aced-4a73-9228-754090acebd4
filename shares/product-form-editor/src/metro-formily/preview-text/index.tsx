import _ from 'lodash';
import React, { createContext, useContext } from 'react';
import { Field } from '@formily/core';
import { observer, ReactFC, useField } from '@formily/react';
import { isArr, isValid, toArr } from '@formily/shared';
import { Spin } from '@metro/components';
import type { CascaderDefaultOptionType, CascaderProps } from '@metroDesign/cascader';
import type { DatePickerProps, RangePickerProps as DateRangePickerProps } from '@metroDesign/date-picker';
import type { InputProps } from '@metroDesign/input';
import type { InputNumberProps } from '@metroDesign/input-number';
import type { SelectProps } from '@metroDesign/select';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import type { TimePickerProps, TimeRangePickerProps } from '@metroDesign/time-picker';
import { Tooltip } from '@metroDesign/tooltip';
import type { TreeSelectProps } from '@metroDesign/tree-select';
import cls from 'classnames';
import HtmlPreview from '@mdtProComm/components/html-preview';
import MarkdownPreview from '@mdtProComm/components/markdown-preview';
import SecretPreview from '@mdtProComm/components/secret-preview';
import i18n from '../../languages';
import { formatDayjsValue, usePrefixCls } from '../__builtins__';
import './style.less';

export const PlaceholderContext = createContext<React.ReactNode>(i18n.chain.comText.none2);

export const Placeholder = PlaceholderContext.Provider;

export const usePlaceholder = (value?: any) => {
  const placeholder = useContext(PlaceholderContext) || i18n.chain.comText.none2;
  return isValid(value) && value !== '' ? value : placeholder;
};

const LinkBlankWrapper = ({ href, children }: { href: string; children: React.ReactNode }) => (
  <a href={href} target="_blank" className="link-text" rel="noopener noreferrer">
    {children}
  </a>
);

const PRE_STYLE_TOP: React.CSSProperties = { whiteSpace: 'pre-wrap', wordBreak: 'break-word', marginBottom: 0 };

const PreWithEllipsis: React.FC<{ value: any; ellipsis: number }> = ({ value, ellipsis }) => {
  const preRef = React.useRef<HTMLPreElement>(null);
  const [isOverflow, setIsOverflow] = React.useState(false);

  React.useEffect(() => {
    const pre = preRef.current;
    if (pre && ellipsis > 0) {
      setIsOverflow(pre.scrollHeight > pre.offsetHeight + 1);
    }
  }, [value, ellipsis]);

  const preStyle: React.CSSProperties = {
    ...PRE_STYLE_TOP,
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    WebkitLineClamp: ellipsis,
    maxHeight: ellipsis ? `${ellipsis * 1.5}em` : undefined,
  };

  const fullPre = (
    <div style={{ maxHeight: 400, overflow: 'auto' }}>
      <pre style={PRE_STYLE_TOP}>{value}</pre>
    </div>
  );

  const ellipsisPre = (
    <pre ref={preRef} style={preStyle}>
      {value}
    </pre>
  );

  return isOverflow ? <Tooltip title={fullPre}>{ellipsisPre}</Tooltip> : ellipsisPre;
};

const getDisplayValueDom = (props: any) => {
  const { value, displayType, ellipsis, secretVisible, secretRegex } = props;
  if (!value) return value;
  switch (displayType) {
    case 'link':
      return <LinkBlankWrapper href={value}>{value}</LinkBlankWrapper>;

    case 'autoLink': {
      const urlPattern = /((?:https?:\/\/|http:\/\/|localhost:|(?:\d{1,3}\.){3}\d{1,3}(?::\d+)?)[^\s)}"']+)/g;
      const parts = String(value).split(urlPattern);
      return (
        <pre style={PRE_STYLE_TOP}>
          {parts.map((part, index) =>
            urlPattern.test(part) ? (
              <LinkBlankWrapper key={index} href={part}>
                {part}
              </LinkBlankWrapper>
            ) : (
              part
            ),
          )}
        </pre>
      );
    }

    case 'markdown':
      return <MarkdownPreview md={value} />;

    case 'html':
      return <HtmlPreview html={value} />;

    case 'secret':
      return <SecretPreview value={value} defaultVisible={secretVisible} maskPattern={secretRegex} />;

    default: {
      if (ellipsis && typeof ellipsis === 'number' && ellipsis > 0) {
        return <PreWithEllipsis value={value} ellipsis={ellipsis} />;
      }
      return <pre style={PRE_STYLE_TOP}>{value}</pre>;
    }
  }
};

const Input: React.FC<React.PropsWithChildren<InputProps & { displayType?: string; ellipsis?: number }>> = observer(
  (props) => {
    const { className, style, addonBefore, prefix, suffix, addonAfter, value } = props || {};
    const prefixCls = usePrefixCls('form-text', props);
    const placeholderValue = usePlaceholder(value);
    const displayValue = getDisplayValueDom(props);
    const mergedValue = displayValue || placeholderValue;

    return (
      <Space className={cls(prefixCls, className)} style={style}>
        {addonBefore}
        {prefix}
        {mergedValue}
        {suffix}
        {addonAfter}
      </Space>
    );
  },
);

const NumberPicker: React.FC<React.PropsWithChildren<InputNumberProps>> = observer((props) => {
  const prefixCls = usePrefixCls('form-text', props);
  return (
    <Space className={cls(prefixCls, props.className)} style={props.style}>
      {props.addonBefore}
      {props.prefix}
      {usePlaceholder(
        props.formatter
          ? props.formatter(String(props.value), {
              userTyping: false,
              input: '',
            })
          : props.value,
      )}
      {/* @ts-ignore */}
      {props['suffix']}
      {props.addonAfter}
    </Space>
  );
});

// eslint-disable-next-line sonarjs/cognitive-complexity
const Select: React.FC<React.PropsWithChildren<SelectProps<any>>> = observer((props) => {
  const field = useField<Field>();
  const prefixCls = usePrefixCls('form-text', props);

  const dataSource: any[] = field?.dataSource?.length ? field.dataSource : props?.options?.length ? props.options : [];
  const placeholder = usePlaceholder();
  const getSelected = () => {
    const value = props.value;
    if (props.mode === 'multiple' || props.mode === 'tags') {
      if (props.labelInValue) {
        return isArr(value) ? value : [];
      } else {
        return isArr(value) ? value.map((val) => ({ label: val, value: val })) : [];
      }
    } else {
      if (props.labelInValue) {
        return isValid(value) ? [value] : [];
      } else {
        return isValid(value) ? [{ label: value, value }] : [];
      }
    }
  };

  const getLabel = (target: any) => {
    const labelKey = props.fieldNames?.label || 'label';
    return (
      dataSource?.find((item) => {
        const valueKey = props.fieldNames?.value || 'value';
        return item[valueKey] === target?.value;
      })?.[labelKey] ||
      target.label ||
      placeholder
    );
  };

  const getLabels = () => {
    const selected = getSelected();
    if (!selected.length) return placeholder;
    if (selected.length === 1) return <Tag>{getLabel(selected[0])}</Tag>;
    return selected.map((item, key) => {
      return <Tag key={key}>{getLabel(item)}</Tag>;
    });
  };
  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
});

// 优化：将 getSelected 拆分为更小的辅助函数，降低复杂度
function getMultipleSelected(props: any) {
  const value = props.value;
  if (props.labelInValue) {
    return isArr(value) ? value : [];
  }
  return isArr(value) ? value.map((val: any) => ({ label: val, value: val })) : [];
}

function getSingleSelected(props: any) {
  const value = props.value;
  if (props.labelInValue) {
    return value ? [value] : [];
  }
  return value ? [{ label: value, value }] : [];
}

function getTreeSelectSelected(props: any) {
  return props.multiple ? getMultipleSelected(props) : getSingleSelected(props);
}

function getTreeSelectDataSource(field: any, props: any) {
  if (field?.dataSource?.length) return field.dataSource;
  if (props?.treeData?.length) return props.treeData;
  return [];
}

const TreeSelect: React.FC<React.PropsWithChildren<TreeSelectProps<any>>> = observer((props) => {
  const field = useField<Field>();
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const dataSource = getTreeSelectDataSource(field, props);
  const loading = field.loading;

  // 构建 value => label 的 Map
  const valueLabelMap = React.useMemo(() => {
    const map = new Map();
    const traverse = (nodes: any[]) => {
      _.forEach(nodes, (item) => {
        if (item) {
          map.set(
            _.toString(item.value),
            item.label ?? (props.treeNodeLabelProp ? item[props.treeNodeLabelProp] : undefined),
          );
          if (_.isArray(item.children) && item.children.length) {
            traverse(item.children);
          }
        }
      });
    };
    traverse(dataSource);
    return map;
  }, [dataSource, props.treeNodeLabelProp]);

  const getLabels = () => {
    const selected = getTreeSelectSelected(props);
    if (!selected?.length) return <Tag>{placeholder}</Tag>;
    return selected.map(({ value, label }: any, key: number) => {
      const mapLabel = valueLabelMap.get(_.toString(value));
      return <Tag key={key}>{loading ? <Spin size="small" /> : mapLabel ?? label ?? placeholder}</Tag>;
    });
  };

  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
});

const Cascader: React.FC<React.PropsWithChildren<CascaderProps<any>>> = observer((props) => {
  const field = useField<Field>();
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const dataSource: any[] = field?.dataSource?.length ? field.dataSource : props?.options?.length ? props.options : [];
  const findSelectedItem = (items: CascaderDefaultOptionType[], val: string | number) => {
    return items.find((item) => item.value === val);
  };
  const findSelectedItems = (
    sources: CascaderDefaultOptionType[],
    selectedValues: Array<string[] | number[]>,
  ): Array<any[]> => {
    return selectedValues.map((value) => {
      const result: Array<CascaderDefaultOptionType> = [];
      let items = sources;
      value.forEach((val) => {
        const selectedItem = findSelectedItem(items, val);
        result.push({
          label: selectedItem?.label ?? '',
          value: selectedItem?.value,
        });
        items = selectedItem?.children ?? [];
      });
      return result;
    });
  };
  const getSelected = () => {
    const val = toArr(props.value);
    // unified conversion to multi selection mode
    return props.multiple ? val : [val];
  };
  const getLabels = () => {
    const selected = getSelected();
    const values = findSelectedItems(dataSource, selected);
    const labels = values
      .map((val: Array<CascaderDefaultOptionType>) => {
        return val.map((item) => item.label).join('/');
      })
      .join(' ');
    return labels || placeholder;
  };
  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
});

const DatePicker: React.FC<React.PropsWithChildren<DatePickerProps> & { valueType?: string }> = (props) => {
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const getLabels = () => {
    const { valueType, value } = props;
    let format = props.format;
    let val: any = value;
    if (valueType === 'timestamp' && value && typeof value === 'number') {
      val = value * 1000;
      format = format || 'YYYY-MM-DD';
    }
    const labels = formatDayjsValue(val, format, placeholder);
    return isArr(labels) ? labels.join('~') : labels;
  };
  return <div className={cls(prefixCls, props.className)}>{getLabels()}</div>;
};

const DateRangePicker: React.FC<React.PropsWithChildren<DateRangePickerProps>> = (props) => {
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const getLabels = () => {
    const labels = formatDayjsValue(props.value, props.format, placeholder);
    return isArr(labels) ? labels.join('~') : labels;
  };
  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
};

// eslint-disable-next-line sonarjs/no-identical-functions
const TimePicker: ReactFC<TimePickerProps> = (props) => {
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const getLabels = () => {
    const labels = formatDayjsValue(props.value, props.format, placeholder);
    return isArr(labels) ? labels.join('~') : labels;
  };
  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
};

// eslint-disable-next-line sonarjs/no-identical-functions
const TimeRangePicker: React.FC<React.PropsWithChildren<TimeRangePickerProps>> = (props) => {
  const placeholder = usePlaceholder();
  const prefixCls = usePrefixCls('form-text', props);
  const getLabels = () => {
    const labels = formatDayjsValue(props.value, props.format, placeholder);
    return isArr(labels) ? labels.join('~') : labels;
  };
  return (
    <div className={cls(prefixCls, props.className)} style={props.style}>
      {getLabels()}
    </div>
  );
};

export interface IPreviewTextProps extends React.HTMLAttributes<HTMLDivElement> {
  prefixCls?: string;
  value?: any;
  displayType?: string;
  ellipsis?: number;
}

const InternalPreviewText: ReactFC<IPreviewTextProps> = observer(
  (props: React.PropsWithChildren<IPreviewTextProps>) => {
    const prefixCls = usePrefixCls('form-text', props);
    const placeholderValue = usePlaceholder(props.value);
    const displayValue = getDisplayValueDom(props);
    const mergedValue = displayValue || placeholderValue;

    return (
      <div className={cls(prefixCls, props.className)} style={props.style}>
        {mergedValue}
      </div>
    );
  },
);

export const PreviewText = Object.assign(InternalPreviewText as any, {
  Input,
  Select,
  TreeSelect,
  Cascader,
  DatePicker,
  DateRangePicker,
  TimePicker,
  TimeRangePicker,
  Placeholder,
  usePlaceholder,
  NumberPicker,
});

export default PreviewText;
