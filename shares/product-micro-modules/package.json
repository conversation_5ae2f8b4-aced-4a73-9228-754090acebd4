{"name": "@mdt/product-micro-modules", "version": "1.48.33", "private": false, "description": "脉策产品级别通用", "keywords": ["mdt", "product", "comm", "modules"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@antv/xflow": "1.0.49", "@datasert/cronjs-matcher": "^1.4.0", "@i18n-chain/react": "2.0.1", "@mdt/business-bff-services": "^1.21.13", "@mdt/business-services": "^1.35.14", "@mdt/product-comm": "^1.28.18", "@mdt/product-form-editor": "^0.20.18", "@mdt/product-tasks": "^1.25.18", "@sql-generator/sql-builder-mdt": "^5.6.1", "@sql-generator/sql-editor-mdt": "^5.6.1", "browser-image-compression": "^2.0.2", "gcoord": "^1.0.6", "html2canvas": "^1.4.1", "jschardet": "^3.0.0", "json-logic-js": "^2.0.2", "jspdf": "^2.5.2", "modern-normalize": "^1.1.0", "qrcode.react": "3.1.0", "tinyduration": "3.3.0", "web-vitals": "2.1.4"}, "devDependencies": {"@types/json-logic-js": "^2.0.2", "@types/react-syntax-highlighter": "^13.5.2"}}