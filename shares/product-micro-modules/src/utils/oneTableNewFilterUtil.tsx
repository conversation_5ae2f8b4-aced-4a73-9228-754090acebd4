import _ from 'lodash';
import type { ICondition, IDatapkgRowsQueryOrderBy, IOperatorFilter, IRequestCancelToken } from '@mdtApis/interfaces';
import { queryDatapkgRowsPaginationTotalAsync } from '@mdtBsServices/datapkgs';
import { queryOnetableDownstreamUsersAsync } from '@mdtBsServices/flowork';
import { ColumnOperatorFilterEnum } from '@mdtProComm/constants';
import {
  BooleanEnum,
  COLUMN_ACTION,
  COLUMN_APPROVE_STATUS,
  COLUMN_ASSIGN_USER,
  COLUMN_CREATE_TIME,
  COLUMN_FLAG,
  COLUMN_STATUS,
  COLUMN_UPDATE_TIME,
  DataStatusEnum,
  FastViewTypeEnum,
} from './oneTableNewUtil';

export const getDownstreamUsers = (data: { users: number[]; reinforces?: number[] }) => {
  return _.uniq(_.concat([], data.users, data.reinforces || []));
};

export interface IQueryDownstreamUsersOptions {
  querySubData?: boolean;
  returnEmpty?: boolean;
}
// 查询下级已提交的用户
export const getDownstreamSubmitedUsers = async (
  rootWfId: string,
  assignWfId?: string,
  cancelToken?: IRequestCancelToken,
  queryOptions?: IQueryDownstreamUsersOptions,
) => {
  const { querySubData, returnEmpty } = queryOptions || {};
  // 总览界面无需查询用户
  if (returnEmpty) return Promise.resolve([]);
  const data = querySubData
    ? { root_dispatch_workflow_id: rootWfId, assign_workflow_id: assignWfId }
    : { root_dispatch_workflow_id: rootWfId, for_superior: true };
  const resp = await queryOnetableDownstreamUsersAsync(data, { cancelToken });
  return resp.success ? getDownstreamUsers(resp.data!) : [];
};

export const getDefaultSortBy = (): IDatapkgRowsQueryOrderBy[] => {
  return [
    { field: COLUMN_UPDATE_TIME, asc: false },
    { field: COLUMN_CREATE_TIME, asc: false },
    { field: 'id', asc: true },
  ];
};

// 获取用户范围
const getOperatorFilterUsers = (users: number[]): ICondition | null => {
  return _.isEmpty(users) ? null : { column: COLUMN_ASSIGN_USER, operator: ColumnOperatorFilterEnum.IN, param: users };
};

// 未删除
export const getOperatorFilterNotDelete = (): IOperatorFilter => {
  return {
    $or: [
      { column: COLUMN_FLAG, operator: ColumnOperatorFilterEnum.IS, param: null },
      { column: COLUMN_FLAG, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.False },
    ],
  };
};
// 未提交
const getOperatorFilterNotSubmit = (): IOperatorFilter => {
  return {
    $or: [
      { column: COLUMN_STATUS, operator: ColumnOperatorFilterEnum.IS, param: null },
      { column: COLUMN_STATUS, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.False },
    ],
  };
};
// 获取提交后新增的
const getSubmitedAddOperatorFilter = (): IOperatorFilter => {
  return {
    $and: [
      { column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.EQ, param: DataStatusEnum.Insert },
      getOperatorFilterNotDelete(),
    ],
  };
};
// 获取提交后更新的
const getSubmitedUpdateOperatorFilter = (): IOperatorFilter => {
  return {
    $and: [
      { column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.EQ, param: DataStatusEnum.Update },
      getOperatorFilterNotDelete(),
    ],
  };
};
// 获取提交后未操作的
const getSubmitedUntouchOperatorFilter = (): IOperatorFilter => {
  return {
    $and: [{ column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.IS, param: null }, getOperatorFilterNotDelete()],
  };
};
// 获取提交后删除的
const getSubmitedDeleteOperatorFilter = (): IOperatorFilter => {
  // 排除新增后又软删除的数据
  return {
    $and: [
      {
        $or: [
          { column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.IS, param: null },
          { column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.EQ, param: DataStatusEnum.Update },
        ],
      },
      { column: COLUMN_FLAG, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.True },
    ],
  };
};

// 获取填报新增数据
export const getFillDataAddOperatorFilter = getSubmitedAddOperatorFilter;
// 获取填报更新数据
export const getFillDataUpdateOperatorFilter = getSubmitedUpdateOperatorFilter;
// 获取填报未操作数据
export const getFillDataUntouchOperatorFilter = getSubmitedUntouchOperatorFilter;
// 获取填报删除数据
export const getFillDataDeleteOperatorFilter = (): ICondition => {
  return { column: COLUMN_FLAG, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.True };
};
// 获取填报审核通过的数据
export const getFillDataApprovedOperatorFilter = (): ICondition => {
  return { column: COLUMN_APPROVE_STATUS, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.True };
};
// 获取填报审核驳回的数据
export const getFillDataRejectedDataOperatorFilter = (): ICondition => {
  return { column: COLUMN_APPROVE_STATUS, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.False };
};
// 获取填报驳回的数据
export const getFillDataSyncDataOperatorFilter = (): ICondition => {
  return { column: COLUMN_STATUS, operator: ColumnOperatorFilterEnum.EQ, param: BooleanEnum.True };
};
// 获取未同步数据
export const getFillDataUnsyncDataOperatorFilter = getOperatorFilterNotSubmit;
// 获取未分配数据
export const getFillDataUnassignDataOperatorFilter = (primaryAssignee: number) => {
  return {
    $or: [
      { column: COLUMN_ASSIGN_USER, operator: ColumnOperatorFilterEnum.EQ, param: primaryAssignee },
      { column: COLUMN_ASSIGN_USER, operator: ColumnOperatorFilterEnum.IS, param: null },
    ],
  };
};

// 流程转移，取消, 用户数据需要同步转移
export const getTransformDatapkgAssignUserFilter = getOperatorFilterUsers;

export const getFillDataOperatorFilter = (users: number[]) => {
  return getOperatorFilterUsers(users);
};

// 详情页使用
export const getDetailDataOperatorFilter = (users: number[]): IOperatorFilter => {
  return {
    $and: [
      {
        $or: [
          getSubmitedAddOperatorFilter(),
          getSubmitedUpdateOperatorFilter(),
          getSubmitedDeleteOperatorFilter(),
          getSubmitedUntouchOperatorFilter(),
        ],
      },
      getOperatorFilterUsers(users),
    ].filter(Boolean) as IOperatorFilter[],
  };
};

export const getUnsyncDataOperatorFilter = getOperatorFilterNotSubmit;

export const getUnsyncDataAndOnlyAddOperatorFilter = (): IOperatorFilter => {
  return { $and: [getOperatorFilterNotSubmit(), getSubmitedAddOperatorFilter()] };
};

export const getUnsyncDataAndOnlyUpdateOperatorFilter = (): IOperatorFilter => {
  return { $and: [getOperatorFilterNotSubmit(), getSubmitedUpdateOperatorFilter()] };
};

export const getUnsyncDataAndOnlyDeleteOperatorFilter = (): IOperatorFilter => {
  return { $and: [getOperatorFilterNotSubmit(), getSubmitedDeleteOperatorFilter()] };
};

export const getUnsyncDataAndOnlyUntouchOperatorFilter = (): IOperatorFilter => {
  return { $and: [getOperatorFilterNotSubmit(), getSubmitedUntouchOperatorFilter()] };
};

export const getNeedApproveSubmitDataOperatorFilter = (users: number[]): IOperatorFilter => {
  return {
    $and: [getOperatorFilterUsers(users), getOperatorFilterNotSubmit()].filter(Boolean) as IOperatorFilter[],
  };
};

// 详情界面统计获取
interface IStaticsOptions {
  pkgId: string;
  users: number[];
  extraFilter?: IOperatorFilter | ICondition;
  extraBodyParams?: Record<string, any>;
}

const fastViewFuncMap: Record<string, Function> = {
  [FastViewTypeEnum.Add]: getSubmitedAddOperatorFilter,
  [FastViewTypeEnum.Update]: getSubmitedUpdateOperatorFilter,
  [FastViewTypeEnum.Delete]: getSubmitedDeleteOperatorFilter,
  [FastViewTypeEnum.Untouch]: getSubmitedUntouchOperatorFilter,
};
export const getFastViewOperatorFilter = (key: string) => {
  return fastViewFuncMap[key];
};

export const getDetailDataStaticsOperatorFilter = async ({
  users,
  pkgId,
  extraFilter,
  extraBodyParams,
}: IStaticsOptions) => {
  const comm = [getOperatorFilterUsers(users), extraFilter].filter(Boolean);
  const pss = [
    { $and: [...comm, getSubmitedAddOperatorFilter()] },
    { $and: [...comm, getSubmitedUpdateOperatorFilter()] },
    { $and: [...comm, getSubmitedUntouchOperatorFilter()] },
    { $and: [...comm, getSubmitedDeleteOperatorFilter()] },
  ].map(async (operatorFilter: Record<string, any>) => {
    return queryDatapkgRowsPaginationTotalAsync(
      pkgId,
      { operator_filter: operatorFilter, ...extraBodyParams },
      { params: { page_size: 0 } },
    ).then((resp) => resp.data?.total_count || 0);
  });
  const resps = await Promise.allSettled(pss);
  const [add, update, untouch, del] = _.map(resps, (it) => (it.status === 'fulfilled' ? it.value : 0));
  return { add, update, untouch, del, total: add + update + untouch + del };
};

// excel上传更新action
export const getExcelUpdateColumnActionOperatorFilter = (updateIds: number[]): IOperatorFilter => {
  return {
    $and: [
      { column: COLUMN_ACTION, operator: ColumnOperatorFilterEnum.IS, param: null },
      { column: 'id', operator: ColumnOperatorFilterEnum.IN, param: updateIds },
    ],
  };
};
