import _ from 'lodash';
import { toastApi } from '@metroDesign/toast';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { BPMN_ROOT_NODE_KEY } from '@mdtProComm/constants';
import { transformFrontend2Backend } from '@mdtProComm/utils/wfTmplUtil';
import i18n from '../../languages';
import { getXmlDocument } from '../xmlUtil';
import { getFieldOptionsByFormSpec, IFormFieldOption } from './common';
import { IFormSpec, parseFormSpecFromXml } from './parseFormSpecFromXml';

export type IUserTaskNodeOption = ILabelValue & {
  formSpec?: IFormSpec;
  fieldOptions: IFormFieldOption[];
};

// 获取用户任务节点及发起节点选项列表
export const getUserTaskNodeOptions = (
  bpmnXml: string,
  includeRoot = true,
  includeHiddenKey = false,
  transformToBackendType = false,
  ignoreFrontendKey = false,
  // eslint-disable-next-line max-params
): IUserTaskNodeOption[] => {
  // const xml = transformFrontend2Backend(_.replace(_.replace(bpmnXml, /\n$/, ''), /\&amp;/g, '&'))!;
  const xml = transformFrontend2Backend(_.replace(bpmnXml, /\n$/, ''))!;
  const doc = getXmlDocument(xml);
  if (!doc) {
    return [];
  }

  try {
    const userTaskEles = doc.querySelectorAll('userTask')!;
    const nodeIds: string[] = [];
    const list: IUserTaskNodeOption[] = _.map(userTaskEles, (ele) => {
      nodeIds.push(ele.id);
      return {
        label: ele.getAttribute('name') || ele.id,
        value: ele.id,
      } as any;
    });

    const map = parseFormSpecFromXml(bpmnXml, nodeIds);
    _.forEach(list, (it) => {
      it.formSpec = map[it.value];
      // 因为后端直接使用keys spce获取变量来校验, 为了兼容，不能直接使用true, 需要按需传入ignoreFrontendKey
      it.fieldOptions = getFieldOptionsByFormSpec(
        it.formSpec,
        includeHiddenKey,
        transformToBackendType,
        ignoreFrontendKey,
      );
    });

    includeRoot &&
      list.unshift({
        label: i18n.chain.proMicroModules.bpmn.startNode,
        value: BPMN_ROOT_NODE_KEY,
        formSpec: map.rootFormSpec,
        // 因为后端直接使用keys spce获取变量来校验, 为了兼容，不能直接使用true, 需要按需传入ignoreFrontendKey
        fieldOptions: getFieldOptionsByFormSpec(
          map.rootFormSpec,
          includeHiddenKey,
          transformToBackendType,
          ignoreFrontendKey,
        ),
      });
    return list;
  } catch (e) {
    console.error('xml transform error:', e);
    return [];
  }
};

// 获取可执行节点选项列表（动态设置执行人）
export const getExecutableNodeOptions = (bpmnXml: string): ILabelValue[] => {
  const executableNodeNames = ['userTask', 'scriptTask'];
  // const xml = transformFrontend2Backend(_.replace(_.replace(bpmnXml, /\n$/, ''), /\&amp;/g, '&'))!;
  const xml = transformFrontend2Backend(_.replace(bpmnXml, /\n$/, ''))!;
  const doc = getXmlDocument(xml);
  if (!doc) {
    toastApi.error(i18n.chain.comText.xmlParseError);
    return [];
  }

  try {
    const options: ILabelValue[] = [];
    _.forEach(executableNodeNames, (nodeName) => {
      const nodes = doc.querySelectorAll(nodeName)!;
      _.forEach(nodes, (ele) => {
        options.push({
          label: ele.getAttribute('name') || ele.id,
          value: ele.id,
        });
      });
    });
    return options;
  } catch (e) {
    console.error('xml transform error:', e);
    return [];
  }
};
