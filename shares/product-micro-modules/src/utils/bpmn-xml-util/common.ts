import _ from 'lodash';
import { QUESTION_COMP_KEY, SUBMITTER_KEY } from '@mdtProComm/constants';
import { getDbColumnTypeByQuestion, isFormHiddenKey, isFrontendFormItem } from '@mdtProComm/utils/questionUtil';
import type { IFormEditorSchema } from '../../pages/form-editor';
import { NODE_PROPS_ID } from '../../pages/form-editor/util';
import { IFormSpec } from './parseFormSpecFromXml';

export const getQuestionSchemaKey = (it: any) => it.name || it[NODE_PROPS_ID];
export interface IFormFieldOption {
  label: string;
  value: string;
  type: string;
  required?: boolean;
  component?: string;
}

export const getFormFieldOptions = (
  schema: Record<string, any>,
  includeHiddenKey = false,
  transformToBackendType = false,
  ignoreFrontendKey = false,
): IFormFieldOption[] => {
  const sortedFields = getFilteredAndSortedFormFields(schema, includeHidden<PERSON>ey, ignoreFrontendKey);
  return _.map(sortedFields, (it) => {
    return {
      label: it.title,
      value: getQuestionSchemaKey(it),
      type: transformToBackendType ? getDbColumnTypeByQuestion(it) : it.type,
      required: it.required,
      component: it[QUESTION_COMP_KEY],
    };
  });
};

export const getFilteredAndSortedFormFields = (
  schema: Record<string, any>,
  includeHiddenKey = false,
  ignoreFrontendKey = false,
) => {
  const fields = _.values(_.get(schema, 'schema.properties', {}));
  let filteredFields = includeHiddenKey ? fields : _.filter(fields, (it) => !isFormHiddenKey(getQuestionSchemaKey(it)));
  filteredFields = ignoreFrontendKey ? _.reject(filteredFields, isFrontendFormItem) : filteredFields;
  return _.sortBy(filteredFields, 'x-index');
};

export const isWfSpecPkg = (xml: string) => {
  return _.includes(xml, 'source="pkg"');
};

export const replacePkgId = (xml: string, pkgId: string) => {
  return xml.replace(/<mdt:pkgId id="pkgId">.*<\/mdt:pkgId>/gm, `<mdt:pkgId id="pkgId">${pkgId}</mdt:pkgId>`);
};

export const getPkgId = (xml: string) => {
  const reg = /<mdt:pkgId id="pkgId">(.*)<\/mdt:pkgId>/gm;
  return reg.exec(xml)?.[1];
};

export const getFieldOptionsByFormSpec = (
  formSpec?: IFormSpec,
  includeHiddenKey = false,
  transformToBackendType = false,
  ignoreFrontendKey = false,
) => {
  if (!formSpec) return [];
  return getFormFieldOptions(formSpec.spec, includeHiddenKey, transformToBackendType, ignoreFrontendKey);
};

// 获取类似字符串中的变量列表 "aaaa='sdfsdf';bbb=234;"
export const getVariableList = (varStr: string) => {
  // 使用正则表达式匹配中文字符、字母、数字以及下划线，\u4e00-\u9fa5范围是基本的中文字符
  // \w 匹配字母、数字、下划线，等同于[A-Za-z0-9_]
  const regex = /([\u4e00-\u9fa5\w]+)\s*=/g;
  let match;
  let keys = [];

  while ((match = regex.exec(varStr)) !== null) {
    keys.push(match[1]);
  }
  return keys;
};

// key: 对于bpmn流程因为所有form表单的key不能相同，加个nodeId以区分，数据包填报暂时不用考虑这种情况。
export const mergeWithSubmitterSchema = (editorSchema: IFormEditorSchema, key = ''): IFormEditorSchema => {
  const properties = editorSchema.formilySchema.schema?.properties || ({} as any);
  const keys = _.keys(properties);
  const invalidSubmitterKeys = _.filter(keys, (k) => _.startsWith(k, SUBMITTER_KEY));
  _.forEach(invalidSubmitterKeys, (ik) => delete properties[ik]);
  const submitterKey = `${SUBMITTER_KEY}${key}`;
  properties[submitterKey] = {
    type: 'string',
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-designable-id': submitterKey,
    'x-decorator-props': { style: { display: 'none' } },
    'x-index': 200,
  };
  return {
    ...editorSchema,
    formilySchema: {
      ...editorSchema.formilySchema,
      schema: {
        ...editorSchema.formilySchema.schema,
        properties,
      },
    },
  };
};
