import { ResidentInfo } from '@mdtProFormEditor/metro-form-design/components';
import { HIDDEN_FORM_VIEW_RESIDENT_INFO } from '../datlas/datlasConfig';

/**
 * 组件中间件 - 用于管理表单组件的动态注入
 */
class FormViewComponentMiddleware {
  private featureFlags: Record<string, boolean>;
  private componentRegistry: Record<string, any>;

  public constructor() {
    // 初始化特性标志
    this.featureFlags = {
      residentInfo: !HIDDEN_FORM_VIEW_RESIDENT_INFO,
    };

    // 初始化组件注册表
    this.componentRegistry = {
      ResidentInfo: ResidentInfo,
    };
  }

  /**
   * 获取组件集合
   * @param baseComponents 基础组件集合
   * @returns 处理后的组件集合
   */
  public processComponents(baseComponents: Record<string, any>): Record<string, any> {
    // 复制基础组件
    const processedComponents = { ...baseComponents };

    // 根据特性标志添加或移除组件
    if (this.featureFlags.residentInfo) {
      processedComponents.ResidentInfo = this.componentRegistry.ResidentInfo;
    } else if (processedComponents.ResidentInfo) {
      // 如果特性被禁用且组件存在，则删除它
      delete processedComponents.ResidentInfo;
    }

    return processedComponents;
  }

  /**
   * 获取设计器组件配置
   * @returns 设计器组件配置或undefined
   */
  public getDesignerComponents(): { components: Record<string, any> } | undefined {
    if (this.featureFlags.residentInfo) {
      return {
        components: {
          ResidentInfo: this.componentRegistry.ResidentInfo,
        },
      };
    }

    return undefined;
  }

  /**
   * 重新加载配置
   */
  public reloadConfiguration(): void {
    this.featureFlags.residentInfo = !HIDDEN_FORM_VIEW_RESIDENT_INFO;
  }

  /**
   * 手动设置特性开关
   * @param feature 特性名称
   * @param enabled 是否启用
   */
  public setFeature(feature: string, enabled: boolean): void {
    if (feature in this.featureFlags) {
      this.featureFlags[feature] = enabled;
    }
  }

  /**
   * 注册自定义组件
   * @param name 组件名称
   * @param component 组件实现
   */
  public registerComponent(name: string, component: any): void {
    this.componentRegistry[name] = component;
  }

  /**
   * 根据配置过滤组件
   * @param baseComponents 基础组件集合
   * @returns 过滤后的组件集合
   */
  public filterComponents(baseComponents: Record<string, any>): Record<string, any> {
    // 复制基础组件
    const filteredComponents = { ...baseComponents };

    // 根据特性标志移除组件
    if (HIDDEN_FORM_VIEW_RESIDENT_INFO && filteredComponents.ResidentInfo) {
      delete filteredComponents.ResidentInfo;
    }

    // 可添加其他需要过滤的组件

    return filteredComponents;
  }

  /**
   * 完整处理组件（过滤后再添加）
   * @param baseComponents 基础组件集合
   * @returns 完全处理后的组件集合
   */
  public processFullComponents(baseComponents: Record<string, any>): Record<string, any> {
    // 先过滤，再处理
    const filtered = this.filterComponents(baseComponents);
    return this.processComponents(filtered);
  }
}

// 导出单例实例
const formViewComponentMiddleware = new FormViewComponentMiddleware();
export default formViewComponentMiddleware;
