import _ from 'lodash';
import type { ITreeNode } from '@designable/core';
import { notification } from '@metroDesign/notification';
import { toastApi } from '@metroDesign/toast';
import { queryOnetableDownstreamUsersAsync } from '@mdtBsServices/flowork';
import { DbColumnTypeEnum, QUESTION_COMP_PROPS_KEY, ReportInfoAttrEnum } from '@mdtProComm/constants';
import { NameCacheEnum } from '@mdtProComm/controllers/NameCacheController';
import { getDatetimeDefaultFormat } from '@mdtProComm/utils/formilyUtil';
import { isMetroOptionHiddenKey, isPkgOrSubmitterKey } from '@mdtProComm/utils/questionUtil';
import {
  fieldNameToConfigMap,
  isResidentChildComp,
  isResidentIdCard,
  isResidentInfoComp,
  residentAddrName,
  residentBirthdayName,
  ResidentFieldNameEnum,
  residentIdCardName,
  residentMobileName,
  residentNameName,
} from '@mdtProFormEditor/metro-form-design';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { improveChildrenSchema, ITransformFieldSchemaOptions } from '../components/form-view/util';
import type { IColumnItem } from '../containers/table-pkg-column';
import { DatlasAppController } from '../datlas/app/DatlasAppController';
import { ONE_TABLE_INFO } from '../datlas/datlasConfig';
import i18n from '../languages';
import { BFF_REQUEST_FLAG } from '../pages/form-editor/util';
import { getFormFieldOptions } from './bpmn-xml-util';
import { reportInfoDefaultValueMap } from './oneTableReportInfoUtil';

export * from './oneTableReportInfoUtil';

export const ONE_TABLE_CITIZEN_REQUEST_NAME = 'one_table_citizen';

const residentChildNameToCitizenFieldMap = {
  [residentNameName]: 'name',
  [residentAddrName]: 'roomAddress[0].address',
  [residentMobileName]: 'mobile',
  [residentBirthdayName]: 'birthdayShort',
};
export const getCitizenField = (name: string) => {
  return residentChildNameToCitizenFieldMap[name] || name;
};

export enum FlowTypeEnum {
  REPORT_MANAGEMENT = 'report_management',
  ACCEPT_ORDER_TASK = 'accept_order_task',
  FILL_TASK = 'fill_task',
  APPROVAL_TASK = 'approval_task',
  CHILD_WORKFLOW = 'child_workflow',
}

export enum ReportConfigAttrEnum {
  START_DATE = 'startDate',
  END_DATE = 'endDate',
  DISPATCH_ORGS = 'dispatchOrgs',
  USER_ORGS = 'userOrgs',
}

export type IFieldMapKey = ReportConfigAttrEnum | ReportInfoAttrEnum | 'reportSpecId' | 'pkgId';

export const ONE_TABLE_COLUMN_PREFIX = 'one_table_data_';
export const ONE_TABLE_COLUMN_CREATE_USER_ID = `${ONE_TABLE_COLUMN_PREFIX}create_user_id`;
// 创建者部门id
export const ONE_TABLE_COLUMN_CREATE_ORG_ID = `${ONE_TABLE_COLUMN_PREFIX}create_org_id`;
export const ONE_TABLE_COLUMN_UPDATE_ORG_ID = `${ONE_TABLE_COLUMN_PREFIX}update_org_id`;
const ONE_TABLE_COLUMN_UPDATE_TIME = `${ONE_TABLE_COLUMN_PREFIX}update_time`;
const ONE_TABLE_COLUMN_CREATE_TIME = `${ONE_TABLE_COLUMN_PREFIX}create_time`;
const ONE_TABLE_COLUMN_UPDATE_USER_ID = `${ONE_TABLE_COLUMN_PREFIX}update_user_id`;
const ONE_TABLE_COLUMN_UPDATE_USER_NAME = `${ONE_TABLE_COLUMN_PREFIX}update_user_name`;
const ONE_TABLE_COLUMN_CREATE_USER_NAME = `${ONE_TABLE_COLUMN_PREFIX}create_user_name`;
export const ONE_TABLE_COLUMN_EXPAND = {
  [ONE_TABLE_COLUMN_UPDATE_TIME]: DbColumnTypeEnum.DATETIME,
  [ONE_TABLE_COLUMN_CREATE_TIME]: DbColumnTypeEnum.DATETIME,
  [ONE_TABLE_COLUMN_UPDATE_USER_ID]: DbColumnTypeEnum.USER_ID,
  [ONE_TABLE_COLUMN_CREATE_USER_ID]: DbColumnTypeEnum.USER_ID,
  [ONE_TABLE_COLUMN_UPDATE_USER_NAME]: DbColumnTypeEnum.STR,
  [ONE_TABLE_COLUMN_CREATE_USER_NAME]: DbColumnTypeEnum.STR,
};
export const ONE_TABLE_KEY_COLUMN_MAP = _.reduce(
  ONE_TABLE_COLUMN_EXPAND,
  (acc: Record<string, string>, name, key) => {
    acc[key.replace(ONE_TABLE_COLUMN_PREFIX, '')] = key;
    return acc;
  },
  {},
);
export const ONE_TABLE_COLUMN_LIST = ONE_TABLE_INFO.ignoreDownloadColumns || [
  ONE_TABLE_COLUMN_CREATE_USER_ID,
  ONE_TABLE_COLUMN_CREATE_ORG_ID,
  ONE_TABLE_COLUMN_UPDATE_ORG_ID,
  ONE_TABLE_COLUMN_UPDATE_TIME,
  ONE_TABLE_COLUMN_CREATE_TIME,
  ONE_TABLE_COLUMN_UPDATE_USER_ID,
  ONE_TABLE_COLUMN_UPDATE_USER_NAME,
  ONE_TABLE_COLUMN_CREATE_USER_NAME,
];

const ignoreOneTabelColumns = <T extends Record<string, any>>(columns: T[], key = 'name') => {
  return _.reject(columns, (c) => _.startsWith(c[key], ONE_TABLE_COLUMN_PREFIX));
};

export const ignoreMetroOptionColumns = <T extends Record<string, any>>(columns: T[], key = 'name') => {
  return _.reject(columns, (c) => isMetroOptionHiddenKey(c[key]));
};

export const ignoreFrontendColumns = <T extends Record<string, any>>(columns: T[], key = 'name') => {
  return _.flow(ignoreOneTabelColumns, ignoreMetroOptionColumns)(columns, key);
};

// 流程3的字段到流程1的字段映射
const REPORT_SPEC_MAP_DEFAULT_VALUE: Record<IFieldMapKey, string> = {
  dispatchOrgs: 'subdeliver_depart',
  reportSpecId: 'related_workflow_spec_id',
  pkgId: 'related_data_id',
  startDate: 'start_time',
  endDate: 'end_time',
  userOrgs: 'subdeliver_object',
  ...reportInfoDefaultValueMap,
};

export const getStartSpecId = (): string => {
  if (!ONE_TABLE_INFO.startSpecId) {
    toastApi.error(i18n.chain.proMicroModules.oneTable.startSpecError);
    return '';
  }
  return ONE_TABLE_INFO.startSpecId;
};

// 流程2的ID
export const getFlowSpecId = (): string => {
  if (!ONE_TABLE_INFO.flowSpecId) {
    toastApi.error(i18n.chain.proMicroModules.oneTable.flowSpecError);
    return '';
  }
  return ONE_TABLE_INFO.flowSpecId;
};

export const getReportSpecToStartSpecFieldMap = (): typeof REPORT_SPEC_MAP_DEFAULT_VALUE => {
  return ONE_TABLE_INFO.reportSpecToStartSpecFieldMap || REPORT_SPEC_MAP_DEFAULT_VALUE;
};

export const getAcceptTaskName = (): string => {
  return ONE_TABLE_INFO.acceptTaskName || '填报接单任务';
};

export const getFillingTaskName = (): string => {
  return ONE_TABLE_INFO.fillingTaskName || '报表填写任务';
};

export const getSubmitTaskName = (): string => {
  return ONE_TABLE_INFO.submitTaskName || '审核提交任务';
};

export const getFirstHandlingKey = () => {
  return ONE_TABLE_INFO.firstHandlingVar || 'first_handling';
};

export const getAssigneeKey = () => {
  return ONE_TABLE_INFO.assigneeVar || 'assignee';
};

// 接单处理方式
export const getSettleWayKey = () => {
  return ONE_TABLE_INFO.settleWayKey || 'settle_way';
};

export const getEnbaleCompleteWorkflowKey = () => {
  return ONE_TABLE_INFO.enableCompleteWorkflowKey || 'enable_complete_workflow';
};

// 接单任务处理方式
export enum AcceptTaskMethodEnum {
  ACCEPT = 'accept',
  REASSIGN = 'reassign',
  DISTRIBUTE = 'distribute',
  UPDATE = 'update',
  RE_APPROVAL = 'reApproval',
}
export const getAcceptTaskMethodValue = (type: AcceptTaskMethodEnum) => {
  const typeToValueMap = ONE_TABLE_INFO.acceptTaskMethodMap || {
    [AcceptTaskMethodEnum.ACCEPT]: 'accept',
    [AcceptTaskMethodEnum.REASSIGN]: 'reassign',
    [AcceptTaskMethodEnum.DISTRIBUTE]: 'distribute',
    [AcceptTaskMethodEnum.UPDATE]: 'update',
    [AcceptTaskMethodEnum.RE_APPROVAL]: 'reApproval',
  };
  const val = typeToValueMap[type];
  if (!val) {
    toastApi.error(`${i18n.chain.proMicroModules.oneTable.menu.settingError}：${JSON.stringify(typeToValueMap)}`);
  }
  return val;
};
export const getAcceptTaskMethodLabel = (type: AcceptTaskMethodEnum) => {
  const map = {
    [AcceptTaskMethodEnum.ACCEPT]: i18n.chain.proMicroModules.oneTable.btnGet,
    [AcceptTaskMethodEnum.REASSIGN]: i18n.chain.proMicroModules.oneTable.btnForword,
    [AcceptTaskMethodEnum.DISTRIBUTE]: i18n.chain.proMicroModules.oneTable.btnIssued,
    [AcceptTaskMethodEnum.UPDATE]: i18n.chain.proMicroModules.oneTable.btnGet,
    [AcceptTaskMethodEnum.RE_APPROVAL]: i18n.chain.proMicroModules.oneTable.btnReApproval,
  };
  return map[type];
};

// 报表审核或审核任务处理方式
export enum ApprovalMethodEnum {
  SUBMIT = 'submit',
  RETURN = 'return',
  ADD_OTHER = 'add_other',
  PASS = 'pass',
  COMPLETE = 'complete',
  SUBMIT_THEN_COMPLETE = 'submit_then_complete',
}
const approvalTaskSubmitKey = ONE_TABLE_INFO.approvalTaskSubmitKey || 'is_passed';
const approvalTaskDepartKey = ONE_TABLE_INFO.approvalTaskSubmitKey || 'redeliver_depart';

export const getApprovalTaskSubmitData = () => {
  return { [approvalTaskSubmitKey]: ApprovalMethodEnum.SUBMIT };
};

export const getApprovalTaskCompleteData = () => {
  return { [approvalTaskSubmitKey]: ApprovalMethodEnum.COMPLETE };
};

export const getApprovalTaskSubmitThenCompleteData = () => {
  return { [approvalTaskSubmitKey]: ApprovalMethodEnum.SUBMIT_THEN_COMPLETE };
};

export const getApprovalTaskReturnData = (departId: any) => {
  return { [approvalTaskSubmitKey]: ApprovalMethodEnum.RETURN, [approvalTaskDepartKey]: [departId] };
};

export const getApprovalTaskOtherData = (departIds: any) => {
  return { [approvalTaskSubmitKey]: ApprovalMethodEnum.ADD_OTHER, [approvalTaskDepartKey]: departIds };
};

export const getApprovalTaskPassData = (departId: any, childPassInfo: string) => {
  return {
    [approvalTaskSubmitKey]: ApprovalMethodEnum.PASS,
    [approvalTaskDepartKey]: [departId],
    child_pass_info: childPassInfo,
  };
};

export const isChildFlowFinished = (status: string) => {
  // TODO: 状态通过中文判断？
  return status === '已完成';
};

const stageIdToNameMap: Record<string, string> = {
  first_assign: i18n.chain.proMicroModules.oneTable.firstDispatch,
  assign_due_to_rejected: i18n.chain.proMicroModules.oneTable.rejectDispatch,
  subordinate: i18n.chain.proMicroModules.oneTable.btnForword2,
  transfer: i18n.chain.proMicroModules.oneTable.btnForword,
  accept: i18n.chain.proMicroModules.oneTable.btnGet,
  self_update: i18n.chain.proMicroModules.oneTable.fillForm,
  submit: i18n.chain.comButton.submit,
  reject: i18n.chain.proMicroModules.oneTable.btnRepeatFill,
};
export const getTaskStageName = (stage: string) => {
  return stageIdToNameMap[stage] || stage;
};

const taskNameToFlowTypeMap: Record<string, FlowTypeEnum> = {
  [getAcceptTaskName()]: FlowTypeEnum.ACCEPT_ORDER_TASK,
  [getFillingTaskName()]: FlowTypeEnum.FILL_TASK,
  [getSubmitTaskName()]: FlowTypeEnum.APPROVAL_TASK,
};
export const getFlowTypeByTaskName = (tName: string) => {
  return taskNameToFlowTypeMap[tName];
};

export const processRows = (
  source: any[],
  target: any[],
  userOrgId: number,
): {
  addRows: any[];
  deleteRows: any[];
  updateRows: any[];
} => {
  // 判断新增的数据, 没有id为空, 即代表新增, 排除空对象
  const addColumns = _.filter(target, (it) => !it.id && !_.isEmpty(it));
  const addRows = _.map(addColumns, (it) => {
    it[ONE_TABLE_COLUMN_CREATE_ORG_ID] = userOrgId;
    const keysToDelete = Object.keys(it).filter((key) => isPkgOrSubmitterKey(key));
    keysToDelete.forEach((key) => {
      delete it[key];
    });
    return it;
  });

  // 判断删除的数据
  const deleteRows = _.differenceBy(source, target, 'id');

  // 找出更新对象
  const updateRows: any[] = [];
  _.forEach(source, (originRow) => {
    if (!originRow) {
      return;
    }
    const matchedItem = target.find((editorRow: any) => editorRow.id === originRow.id);
    // 不相同，找出不相同的列即改动的值
    if (matchedItem && !_.isEqual(originRow, matchedItem)) {
      const columns = _.uniq([..._.keys(originRow), ..._.keys(matchedItem)]);
      const updateColumns = _.filter(columns, (column) => {
        return !_.isEqual(originRow[column], matchedItem[column]) && !isPkgOrSubmitterKey(column);
      });

      if (updateColumns.length !== 0) {
        const values = _.map(updateColumns, (column) =>
          matchedItem[column] !== undefined ? matchedItem[column] : null,
        );
        // 更新数据时，添加更新部门id
        updateRows.push({
          columns: ['id', ...updateColumns, ONE_TABLE_COLUMN_UPDATE_ORG_ID],
          values: [[matchedItem.id, ...values, userOrgId]],
        });
      }
    }
  });

  // 返回新增、删除和更新的行数据
  return {
    addRows,
    deleteRows,
    updateRows,
  };
};

export const getOnetableRootDownstreamUsers = async (rootWfId?: string) => {
  // 已经下发的用户
  const existUserMap: Record<string, boolean> = {};
  if (rootWfId) {
    const resp = await queryOnetableDownstreamUsersAsync({
      root_dispatch_workflow_id: rootWfId,
      check_submitted: false,
    });
    _.forEach(resp.data?.users, (it) => {
      existUserMap[`${it}`] = true;
    });
  }
  return existUserMap;
};

export const checkUserExistsInMap = (
  data: { userId: number; orgId: number }[],
  existUserMap: Record<string, boolean>,
) => {
  // 检查是否有人重复
  const repeatNames: string[] = [];
  const nameCache = DatlasAppController.getInstance().getNameCacheController();
  _.forEach(data, ({ userId }) => {
    if (userId && existUserMap[`${userId}`]) {
      const name = nameCache.getNameValue(userId, NameCacheEnum.USER);
      repeatNames.push(name || userId);
    }
  });
  if (repeatNames.length > 0) {
    toastApi.error(i18n.chain.proMicroModules.oneTable.tip.dispatchRepeat(_.join(repeatNames, ', ')));
    return true;
  }
  return false;
};

export const formValuesCommValidate = (values: Record<string, any>) => {
  const { formilySchema } = values;
  const properties = formilySchema.schema?.properties;
  const list = _.values(properties);
  const residentInfoComps = _.filter(list, isResidentInfoComp);
  if (residentInfoComps.length > 1) {
    return i18n.chain.proMicroModules.oneTable.tip.residentInfoCount;
  }
  const residentInfo: any = residentInfoComps[0];
  if (!residentInfo) return;
  const fieldList = residentInfo[MetroSettingProp.residentFieldList];
  const idCard = _.find(fieldList, (it) => it.fieldName === ResidentFieldNameEnum.ID_CARD && !_.isEmpty(it.isSelect));
  if (!idCard) {
    return i18n.chain.proMicroModules.oneTable.tip.residentIdCard;
  }
};

export const modifyResidentInfoDefaultSetting = (node: ITreeNode) => {
  if (isResidentInfoComp(node.props)) {
    const residentFieldListDefaultVal = _.map(fieldNameToConfigMap, (config, key) => {
      return { fieldName: key, fieldLabel: config.title, isSelect: config.isSelect ? ['Y'] : undefined };
    });
    return {
      name: i18n.chain.proMicroModules.oneTable.residentInfo,
      [MetroSettingProp.residentFieldList]: residentFieldListDefaultVal,
    };
  }
  if (isResidentChildComp(node.props)) {
    const residentSetting: Record<string, any> = { title: node.props?.title };
    if (isResidentIdCard(node)) {
      residentSetting[MetroSettingProp.inputContentType] = 'idcard';
    } else {
      const idCardName = residentIdCardName;
      const dynamicConfig = {
        overrideCurrent: false,
        variables: [{ field: idCardName, id: idCardName, type: 'text' }],
        config: {
          api: {
            method: 'post',
            url: `${BFF_REQUEST_FLAG}${ONE_TABLE_CITIZEN_REQUEST_NAME}`,
            body: { value: JSON.stringify({ idNo: `:${idCardName}` }) },
          },
        },
        filters: [
          { id: `dy_${node.id}`, name: 'Create', code: `return data.${getCitizenField(node.id!)}`, checked: true },
        ],
        dynamicType: 'api',
        useFilter: true,
      };
      residentSetting[MetroSettingProp.dynamic] = dynamicConfig;
    }

    return residentSetting;
  }
};

export const filterFormEmptyValues = (obj?: Record<string, any>): Record<string, any> => {
  return _.omitBy(
    obj,
    (value) =>
      _.isNil(value) ||
      (_.isString(value) && value.trim() === '') ||
      (_.isArray(value) && _.isEmpty(value)) ||
      (_.isObject(value) && _.isEmpty(value)),
  );
};

export const getColumnsFromFormSchame = (options: ITransformFieldSchemaOptions) => {
  const schema = improveChildrenSchema(options).schema;
  return _.reduce(
    _.reject(getFormFieldOptions({ schema }, true, true, true), isPkgOrSubmitterKey),
    (prev: Record<string, string>, it: any) => {
      prev[it.value] = it.type;
      return prev;
    },
    {},
  );
};

export const megerFormSpecProperAndSetting = (formSpec: any) => {
  const {
    formilySchema: { schema: fsm },
    allSettingValues,
  } = formSpec;

  const newProperties: Record<string, any> = {};
  const newSettingValues: Record<string, any> = {};

  loopProperties(newProperties, fsm.properties, allSettingValues, newSettingValues);

  return {
    formilySchema: {
      ...formSpec.formilySchema,
      schema: { ...fsm, properties: newProperties },
    },
    allSettingValues: newSettingValues,
  };
};

const loopProperties = (
  newProperties: Record<string, any>,
  properties: Record<string, any>,
  settingValues: Record<string, any>,
  newSettingValues: Record<string, any>,
) => {
  _.forEach(properties, (val: Record<string, any>, key) => {
    if (val.type === 'void' && val.properties) {
      loopProperties(newProperties, val.properties, settingValues, newSettingValues);
    } else {
      newProperties[key] = val;
      newSettingValues[key] = { ...val, ...settingValues[key] };
    }
  });
};

export const getColumnsMetaFromFormSetting = (formSpec: any) => {
  const { allSettingValues } = megerFormSpecProperAndSetting(formSpec);
  const rslt: Record<string, any> = {};
  _.forEach(allSettingValues, (value, key) => {
    const ps = value[QUESTION_COMP_PROPS_KEY] || {};
    if (ps.valueType === DbColumnTypeEnum.DATETIME || ps.valueType === DbColumnTypeEnum.TIMESTAMP) {
      rslt[key] = { view_format: { format: ps.format || getDatetimeDefaultFormat(ps) } };
    }
    if (ps.displayType === 'secret') {
      rslt[key] = { view_format: { format: 'secret' } };
    }
  });
  return _.isEmpty(rslt) ? undefined : rslt;
};

export const checkUploadExcelColumns = (excelColumns: string[], tableColumns: IColumnItem[], rows: any[]) => {
  const emptyColumns: number[] = [];
  const moreColumns: string[] = [];
  const errorColumns: number[] = [];
  const transformColumns: IColumnItem[] = [];
  _.forEach(excelColumns, (it, index) => {
    if (_.isEmpty(it)) {
      emptyColumns.push(index + 1);
      return;
    }
    const dc = tableColumns[index];
    if (!dc) {
      moreColumns.push(`${it}(${index + 1}${i18n.chain.proMicroModules.uploadExcelCheck.column})`);
      return;
    }
    if (it === 'id' || dc.title === it) {
      transformColumns.push(dc);
    } else {
      errorColumns.push(index + 1);
    }
  });
  const errors: string[] = [];
  emptyColumns.length && errors.push(i18n.chain.proMicroModules.uploadExcelCheck.columnEmpty(emptyColumns.join(',')));
  moreColumns.length && errors.push(i18n.chain.proMicroModules.uploadExcelCheck.columnNotExist(moreColumns.join(',')));
  errorColumns.length &&
    errors.push(i18n.chain.proMicroModules.uploadExcelCheck.columnNotMatch(errorColumns.join(',')));
  if (errors.length || !rows.length) {
    notification.open({
      message: i18n.chain.proMicroModules.oneTable.uploadExcelError,
      description: errors.map((it, index) => <p key={index}>{it}</p>),
      duration: null,
    });
    return;
  }
  return transformColumns;
};
