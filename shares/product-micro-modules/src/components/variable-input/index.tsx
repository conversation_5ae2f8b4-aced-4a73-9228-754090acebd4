import { get, has, omit } from 'lodash';
import React, { FC, KeyboardEvent, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { type ButtonProps, Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import i18n from '../../languages';
import './index.less';

export interface IVariableOption {
  label: string;
  value: string;
  prefix?: string;
  suffix?: string;
  children?: IVariableOption[];
}

export interface IVariableInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  options?: IVariableOption[];
  disabled?: boolean;
  delimiters?: [string, string];
  btnProps?: ButtonProps;
  btnText?: string;
}

interface ITagRange {
  start: number;
  end: number;
}

const periodVariableOptions: IVariableOption[] = [
  {
    label: '周期报表序号',
    value: 'period_sequence_number',
  },
  {
    label: '报表发起时间',
    value: 'now_group',
    children: [
      { label: '年', value: 'now.year' },
      { label: '月', value: 'now.month' },
      { label: '日', value: 'now.day' },
      { label: '时', value: 'now.hour' },
      { label: '分', value: 'now.minute' },
      { label: '秒', value: 'now.second' },
      { label: '季度', value: 'now.quarter' },
      { label: '当月第几周', value: 'now.week_of_month' },
      { label: '当年第几周', value: 'now.week_of_year' },
      { label: '星期几', value: 'now.isoweekday()' },
      { label: '星期几(中文)', value: 'now.chinese_week_day' },
      { label: '上中下旬', value: 'now.chinese_ten_day' },
      { label: '上下半年', value: 'now.chinese_half_year' },
      { label: '上下午', value: 'now.chinese_am_pm' },
    ],
  },
  {
    label: '下个周期发起时间',
    value: 'next_period_group',
    children: [
      { label: '年', value: 'next_period_start_time.year' },
      { label: '月', value: 'next_period_start_time.month' },
      { label: '日', value: 'next_period_start_time.day' },
      { label: '时', value: 'next_period_start_time.hour' },
      { label: '分', value: 'next_period_start_time.minute' },
      { label: '秒', value: 'next_period_start_time.second' },
      { label: '季度', value: 'next_period_start_time.quarter' },
      { label: '当月第几周', value: 'next_period_start_time.week_of_month' },
      { label: '当年第几周', value: 'next_period_start_time.week_of_year' },
      { label: '星期几', value: 'next_period_start_time.isoweekday()' },
      { label: '星期几(中文)', value: 'next_period_start_time.chinese_week_day' },
      { label: '上中下旬', value: 'next_period_start_time.chinese_ten_day' },
      { label: '上下半年', value: 'next_period_start_time.chinese_half_year' },
      { label: '上下午', value: 'next_period_start_time.chinese_am_pm' },
    ],
  },
];

const flattenOptions = (options: IVariableOption[]): IVariableOption[] => {
  const result: IVariableOption[] = [];
  const traverse = (currentOptions: IVariableOption[]) => {
    for (const option of currentOptions) {
      result.push(omit(option, 'children') as IVariableOption);
      if (option.children && option.children.length > 0) {
        traverse(option.children);
      }
    }
  };
  if (options) {
    traverse(options);
  }
  return result;
};

const parseTemplateString = (
  value: string,
  options: IVariableOption[],
): { plainText: string; tagRanges: ITagRange[] } => {
  const tagRanges: ITagRange[] = [];
  let plainText = '';
  let lastIndex = 0;
  const tagRegex = /\{\{([^{}]+)\}\}/g;
  let match;
  const flatOptions = flattenOptions(options);

  while ((match = tagRegex.exec(value || ''))) {
    plainText += value.substring(lastIndex, match.index);
    const tagValue = match[1];

    const option = flatOptions.find((opt) => opt.value === tagValue);
    const tagLabel = option ? option.label : tagValue;

    const start = plainText.length;
    plainText += tagLabel;
    tagRanges.push({ start, end: plainText.length });
    lastIndex = match.index + match[0].length;
  }
  plainText += (value || '').substring(lastIndex);
  return { plainText, tagRanges };
};

const generateTemplateString = (
  plainText: string,
  tagRanges: ITagRange[],
  options: IVariableOption[],
  delimiters: [string, string],
): string => {
  let result = plainText;
  const reversedTagRanges = [...tagRanges].sort((a, b) => b.start - a.start);
  const [startDelimiter, endDelimiter] = delimiters;
  const flatOptions = flattenOptions(options);

  for (const { start, end } of reversedTagRanges) {
    const tagLabel = result.substring(start, end);
    const option = flatOptions.find((opt) => opt.label === tagLabel);
    if (option) {
      const prefix = get(option, 'prefix', '');
      const suffix = get(option, 'suffix', '');
      const tagContent = `${prefix}${startDelimiter}${option.value}${endDelimiter}${suffix}`;
      result = `${result.substring(0, start)}${tagContent}${result.substring(end)}`;
    } else {
      const tagContent = `${startDelimiter}${tagLabel}${endDelimiter}`;
      result = `${result.substring(0, start)}${tagContent}${result.substring(end)}`;
    }
  }
  return result;
};

const VariableInput: FC<IVariableInputProps> = ({
  value = '',
  onChange,
  placeholder = i18n.chain.comPlaceholder.input,
  options = periodVariableOptions,
  disabled = false,
  delimiters = ['{{', '}}'],
  btnProps = {},
  btnText = i18n.chain.proMicroModules.tagInput.insert,
}) => {
  const [state, setState] = useState(() => parseTemplateString(value, options));
  const [isFocused, setIsFocused] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [cursorPos, setCursorPos] = useState(0);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const renderedContentRef = useRef<HTMLDivElement>(null);
  const virtualCursorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentStateValue = generateTemplateString(state.plainText, state.tagRanges, options, delimiters);
    if (value !== currentStateValue) {
      setState(parseTemplateString(value, options));
    }
  }, [value, state.plainText, state.tagRanges, options, delimiters]);

  useEffect(() => {
    if (!isFocused || !textareaRef.current || !renderedContentRef.current || !virtualCursorRef.current) {
      if (virtualCursorRef.current) virtualCursorRef.current.style.display = 'none';
      return;
    }

    const virtualCursor = virtualCursorRef.current;
    virtualCursor.style.display = 'none';

    const renderedContent = renderedContentRef.current;

    const sizer = document.createElement('div');
    sizer.className = 'module_variable-input-sizer';
    renderedContent.appendChild(sizer);

    const contentBeforeCursor: React.ReactNode[] = [];
    let lastIndex = 0;

    state.tagRanges.forEach(({ start, end }) => {
      if (start >= cursorPos) return;
      const clampedEnd = Math.min(end, cursorPos);

      if (start > lastIndex) {
        contentBeforeCursor.push(<span key={`text-${lastIndex}`}>{state.plainText.substring(lastIndex, start)}</span>);
      }
      contentBeforeCursor.push(
        <span key={`tag-${start}`} className="module_variable-input-rendered-tag">
          {state.plainText.substring(start, clampedEnd)}
        </span>,
      );
      lastIndex = clampedEnd;
    });

    if (cursorPos > lastIndex) {
      contentBeforeCursor.push(
        <span key={`text-${lastIndex}`}>{state.plainText.substring(lastIndex, cursorPos)}</span>,
      );
    }

    const marker = <span id="cursor-marker">&#8203;</span>;

    ReactDOM.render(
      <>
        {contentBeforeCursor}
        {marker}
      </>,
      sizer,
      () => {
        const markerEl = sizer.querySelector('#cursor-marker') as HTMLSpanElement;
        if (markerEl) {
          virtualCursor.style.top = `${markerEl.offsetTop}px`;
          virtualCursor.style.left = `${markerEl.offsetLeft}px`;
          virtualCursor.style.display = 'block';
        } else {
          virtualCursor.style.display = 'none';
        }

        ReactDOM.unmountComponentAtNode(sizer);
        if (sizer.parentNode) {
          sizer.parentNode.removeChild(sizer);
        }
      },
    );
  }, [cursorPos, state.plainText, state.tagRanges, isFocused]);

  const updateState = (newText: string, newRanges: ITagRange[], newCursor: number, delimiters: [string, string]) => {
    const sortedRanges = newRanges.sort((a, b) => a.start - b.start);
    const newState = { plainText: newText, tagRanges: sortedRanges };
    setState(newState);
    setCursorPos(newCursor);
    onChange?.(generateTemplateString(newText, sortedRanges, options, delimiters));

    setTimeout(() => {
      textareaRef.current?.setSelectionRange(newCursor, newCursor);
    }, 0);
  };

  // eslint-disable-next-line sonarjs/cognitive-complexity
  const handleSelect = () => {
    if (!textareaRef.current) return;

    const { selectionStart, selectionEnd } = textareaRef.current;

    if (selectionStart === selectionEnd) {
      const cursorPosition = selectionStart;
      const tagAtCursor = state.tagRanges.find((r) => cursorPosition > r.start && cursorPosition < r.end);

      if (tagAtCursor) {
        const distanceToStart = cursorPosition - tagAtCursor.start;
        const distanceToEnd = tagAtCursor.end - cursorPosition;
        const newCursorPosition = distanceToStart < distanceToEnd ? tagAtCursor.start : tagAtCursor.end;

        textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    } else {
      let finalStart = selectionStart;
      let finalEnd = selectionEnd;

      state.tagRanges.forEach((range) => {
        const overlaps = range.start < selectionEnd && range.end > selectionStart;

        if (overlaps) {
          finalStart = Math.min(finalStart, range.start);
          finalEnd = Math.max(finalEnd, range.end);
        }
      });

      if (finalStart !== selectionStart || finalEnd !== selectionEnd) {
        textareaRef.current.setSelectionRange(finalStart, finalEnd);
      }
    }

    setCursorPos(textareaRef.current.selectionStart || 0);
  };

  // eslint-disable-next-line sonarjs/cognitive-complexity
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    const { key } = e;
    const textarea = e.currentTarget;
    const { selectionStart, selectionEnd } = textarea;

    if (selectionStart !== selectionEnd && (key === 'Backspace' || key === 'Delete')) {
      e.preventDefault();
      const newText = state.plainText.substring(0, selectionStart) + state.plainText.substring(selectionEnd);
      const lengthDiff = selectionEnd - selectionStart;
      const newRanges = state.tagRanges
        .filter((r) => r.end <= selectionStart || r.start >= selectionEnd)
        .map((r) => {
          if (r.start >= selectionEnd) {
            return { start: r.start - lengthDiff, end: r.end - lengthDiff };
          }
          return r;
        });
      updateState(newText, newRanges, selectionStart, delimiters);
      return;
    }

    if (selectionStart !== selectionEnd) return;

    const tagAtCursor = state.tagRanges.find((r) => selectionStart > r.start && selectionStart < r.end);
    if (tagAtCursor) {
      e.preventDefault();
      const newPos = key === 'ArrowLeft' || key === 'Backspace' ? tagAtCursor.start : tagAtCursor.end;
      textarea.setSelectionRange(newPos, newPos);
      setCursorPos(newPos);
      return;
    }

    const tagBefore = state.tagRanges.find((r) => r.end === selectionStart);
    if (tagBefore && (key === 'Backspace' || key === 'ArrowLeft')) {
      e.preventDefault();
      if (key === 'Backspace') {
        deleteTag(tagBefore);
      } else {
        const newPos = tagBefore.start;
        textarea.setSelectionRange(newPos, newPos);
        setCursorPos(newPos);
      }
      return;
    }

    const tagAfter = state.tagRanges.find((r) => r.start === selectionStart);
    if (tagAfter && (key === 'Delete' || key === 'ArrowRight')) {
      e.preventDefault();
      if (key === 'Delete') {
        deleteTag(tagAfter);
      } else {
        const newPos = tagAfter.end;
        textarea.setSelectionRange(newPos, newPos);
        setCursorPos(newPos);
      }
    }
  };

  const deleteTag = (tagToDelete: ITagRange) => {
    const { plainText, tagRanges } = state;
    const newText = plainText.substring(0, tagToDelete.start) + plainText.substring(tagToDelete.end);
    const lengthDiff = tagToDelete.end - tagToDelete.start;
    const newRanges = tagRanges
      .filter((r) => r !== tagToDelete)
      .map((r) => {
        if (r.start > tagToDelete.start) {
          return { start: r.start - lengthDiff, end: r.end - lengthDiff };
        }
        return r;
      });
    updateState(newText, newRanges, tagToDelete.start, delimiters);
  };

  const handleTagSelect = (option: IVariableOption) => {
    const { label: tagLabel } = option;
    const { plainText, tagRanges } = state;
    const textarea = textareaRef.current;
    if (!textarea) return;
    const { selectionStart: start, selectionEnd: end } = textarea;

    const newText = plainText.substring(0, start) + tagLabel + plainText.substring(end);
    const tagLength = tagLabel.length;
    const diff = tagLength - (end - start);

    const newRanges = tagRanges
      .filter((r) => r.start >= end || r.end <= start)
      .map((r) => {
        if (r.start >= end) {
          return { start: r.start + diff, end: r.end + diff };
        }
        return r;
      });
    newRanges.push({ start, end: start + tagLength });

    updateState(newText, newRanges, start + tagLength, delimiters);
    setDropdownVisible(false);
  };

  const buildMenuItems = (optionsToBuild: IVariableOption[]) => {
    return optionsToBuild.map((option) => {
      // 只保留 Dropdown 需要的属性，排除 prefix 和 suffix
      const item: any = {
        key: option.value,
        label: option.label,
      };
      if (option.children && option.children.length > 0) {
        item.children = buildMenuItems(option.children);
      } else {
        item.onClick = () => handleTagSelect(option);
      }
      return item;
    });
  };

  const menuItems = buildMenuItems(options);

  const renderStyledContent = () => {
    const { plainText, tagRanges } = state;
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;
    tagRanges.forEach(({ start, end }, i) => {
      if (start > lastIndex) {
        elements.push(<span key={`text-${lastIndex}`}>{plainText.substring(lastIndex, start)}</span>);
      }
      elements.push(
        <span key={`tag-${i}`} className="module_variable-input-rendered-tag">
          {plainText.substring(start, end)}
        </span>,
      );
      lastIndex = end;
    });
    if (lastIndex < plainText.length) {
      elements.push(<span key={`text-${lastIndex}`}>{plainText.substring(lastIndex)}</span>);
    }
    elements.push(<span key="zws">&#8203;</span>);
    return elements;
  };

  const handleFocus = () => {
    setIsFocused(true);
    setTimeout(() => {
      if (textareaRef.current) {
        setCursorPos(textareaRef.current.selectionStart || 0);
      }
    }, 0);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    const oldText = state.plainText;
    const newCursor = e.target.selectionStart || 0;

    let start = 0;
    while (start < oldText.length && start < newText.length && oldText[start] === newText[start]) {
      start++;
    }

    let oldEnd = oldText.length;
    let newEnd = newText.length;
    while (oldEnd > start && newEnd > start && oldText[oldEnd - 1] === newText[newEnd - 1]) {
      oldEnd--;
      newEnd--;
    }

    const removedLength = oldEnd - start;
    const addedLength = newEnd - start;
    const lengthChange = addedLength - removedLength;

    const newRanges: ITagRange[] = [];
    for (const range of state.tagRanges) {
      const isOverlapping = Math.max(range.start, start) < Math.min(range.end, oldEnd);

      if (isOverlapping) {
        continue;
      }

      if (range.start >= oldEnd) {
        newRanges.push({ start: range.start + lengthChange, end: range.end + lengthChange });
      } else {
        newRanges.push(range);
      }
    }
    updateState(newText, newRanges, newCursor, delimiters);
  };

  return (
    <div
      className={`module_variable-input-container ${isFocused ? 'module_variable-input-focused' : ''}`}
      onClick={() => textareaRef.current?.focus()}
    >
      <div className="module_variable-input-editor">
        <div
          ref={renderedContentRef}
          className="module_variable-input-rendered module_variable-input-shared-text-styles"
          style={{ boxSizing: 'border-box' }}
        >
          {renderStyledContent()}
          {!state.plainText && <div className="placeholder">{placeholder}</div>}
          {isFocused && <div ref={virtualCursorRef} className="module_variable-input-virtual-cursor" />}
        </div>
        <textarea
          ref={textareaRef}
          value={state.plainText}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={() => setIsFocused(false)}
          onSelect={handleSelect}
          disabled={disabled}
          className="module_variable-input-textarea module_variable-input-shared-text-styles"
          spellCheck="false"
          style={{ boxSizing: 'border-box' }}
        />
      </div>
      <div className="module_variable-input-toolbar">
        <Dropdown
          menu={{ items: menuItems }}
          trigger={['click']}
          open={dropdownVisible}
          onOpenChange={setDropdownVisible}
        >
          <Button {...btnProps} disabled={disabled}>
            {btnText}
          </Button>
        </Dropdown>
      </div>
    </div>
  );
};

export default VariableInput;
