.module_variable-input-container {
  position: relative;
  height: 100px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--metro-border-1);
  background-color: var(--metro-secondary-default);
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
}

.module_variable-input-container.module_variable-input-focused {
  border-color: var(--metro-primary-default);
  border-right-width: 1px;
}

.module_variable-input-editor {
  position: relative;
  flex: 1;
  width: 100%;
  min-height: 60px;
  max-height: 60px;
  cursor: text;
}

.module_variable-input-shared-text-styles {
  width: 100%;
  height: 100%;
  padding: 8px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.module_variable-input-rendered {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .placeholder {
    color: var(--metro-text-2);
    position: absolute;
    top: 8px;
    left: 8px;
  }
}

.module_variable-input-rendered-tag {
  display: inline-flex;
  align-items: center;
  color: var(--metro-primary-default);
  background-color: var(--metro-primary-tp-0);
  border: 1px solid var(--metro-primary-border-1);
  border-radius: 6px;
  padding: 0 4px;
  margin: 0 2px;
  user-select: none;
  white-space: nowrap;
}

.module_variable-input-textarea {
  position: absolute;
  top: 0;
  left: 0;
  color: transparent;
  caret-color: transparent;
  z-index: 2;
}

.module_variable-input-sizer {
  position: absolute;
  visibility: hidden;
  top: 0;
  left: 0;
  height: auto;
  width: 100%;
  padding: 10px 8px;
  box-sizing: border-box;
  pointer-events: none;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.module_variable-input-virtual-cursor {
  position: absolute;
  width: 2px;
  height: 14px;
  background-color: var(--metro-text-1);
  animation: blink 1s steps(1) infinite;
  z-index: 2;
  pointer-events: none;
  display: none;
  opacity: 0.75;
}

@keyframes blink {
  50% {
    background-color: transparent;
  }
}

.module_variable-input-toolbar {
  position: absolute;
  right: 5px;
  bottom: 5px;
  z-index: 3;
}
