import React, { useState } from 'react';
import VariableInput, { IVariableOption } from './index';

// 示例选项，包含 prefix 和 suffix
const exampleOptions: IVariableOption[] = [
  {
    label: '用户名',
    value: 'username',
    prefix: 'user_',
    suffix: '_info',
  },
  {
    label: '时间相关',
    value: 'time_group',
    children: [
      {
        label: '当前年份',
        value: 'current_year',
        prefix: 'year_',
        suffix: '_end',
      },
      {
        label: '当前月份',
        value: 'current_month',
        prefix: 'month_',
        suffix: '_data',
      },
    ],
  },
  {
    label: '普通变量',
    value: 'normal_var',
    // 没有 prefix 和 suffix
  },
];

const VariableInputExample: React.FC = () => {
  const [value, setValue] = useState('');

  const handleChange = (newValue: string) => {
    setValue(newValue);
    console.log('变更后的值:', newValue);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>VariableInput 示例</h3>
      <p>当前值: {value}</p>
      
      <VariableInput
        value={value}
        onChange={handleChange}
        options={exampleOptions}
        placeholder="请输入内容或选择变量"
      />
      
      <div style={{ marginTop: '20px' }}>
        <h4>使用说明:</h4>
        <ul>
          <li>选择"用户名"变量时，会生成: user_{'{{username}}}_info</li>
          <li>选择"当前年份"变量时，会生成: year_{'{{current_year}}}_end</li>
          <li>选择"普通变量"时，会生成: {'{{normal_var}}'}</li>
        </ul>
      </div>
    </div>
  );
};

export default VariableInputExample;
