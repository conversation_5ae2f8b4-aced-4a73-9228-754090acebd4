import _ from 'lodash';
import { createRef, RefObject } from 'react';
import type { RuleCondition, RuleField, RuleFormInstance, RuleValue } from '@metro/rule-form';
import { BehaviorSubject } from 'rxjs';
import { formatToBeijingISO } from '@mdtBsComm/utils/dayUtil';
import { deepMerge } from '@mdtBsComm/utils/deepMergeUtil';
import { ColumnOperatorFilterEnum, FieldTypeEnum } from '@mdtProComm/constants';
import { getColumnOperatorFilterLabel } from '@mdtProComm/utils/columnUtil';
import { IRuleFormProps } from '../../components/rule-form';
import type {
  IColumn,
  IControllerOptions,
  ITransformColumnToFields,
  ITransformCondition,
  ITransformSubmitValue,
  ITransformToValue,
  ITransformToValueCondition,
} from './interface';

export const DEFAULT_VALUES = { relation: 'and', conditions: [] };
export const DEFAULT_FILED_NAMES_MAP: Record<string, string> = {
  and: '$and',
  or: '$or',
  not: '$not',
};

class FilterRuleFormController<T> {
  private values$ = new BehaviorSubject<RuleValue>(DEFAULT_VALUES);
  private form: RefObject<RuleFormInstance>;
  private ruleFormProps: IRuleFormProps = {};
  private onSubmitCallback: IControllerOptions<T>['onSubmitCallback'];
  private transformSubmitValue: ITransformSubmitValue<T>;
  private transformToValue: ITransformToValue<T>;
  private transformCondition: ITransformCondition;
  private transformToValueCondition: ITransformToValueCondition;
  private transformColumnToFields: ITransformColumnToFields;
  private fieldNames: Record<string, string>;
  private reverseFieldNames: Record<string, string>;
  private maxHeight: IControllerOptions<T>['maxHeight'];
  private TitleView?: IControllerOptions<T>['TitleView'];
  private FooterView?: IControllerOptions<T>['FooterView'];
  private titleText?: IControllerOptions<T>['titleText'];
  private okText?: IControllerOptions<T>['okText'];
  private cancelText?: IControllerOptions<T>['cancelText'];
  private okButtonProps?: IControllerOptions<T>['okButtonProps'];
  private cancelButtonProps?: IControllerOptions<T>['cancelButtonProps'];
  private customHandleCustomCondition?: (node: RuleCondition) => RuleCondition;

  public constructor(options: IControllerOptions<T>) {
    const { columns, ruleFormProps = {}, overrideRuleFormProps = true, onSubmitCallback } = options;
    this.onSubmitCallback = onSubmitCallback;
    this.transformSubmitValue = options.transformSubmitValue || this.defaultTransformSubmitValue;
    this.transformCondition = options.transformCondition || this.defaultTransformCondition;
    this.transformToValue = options.transformToValue || this.defaultTransformToValue;
    this.transformToValueCondition = options.transformToValueCondition || this.defaultTransformToValueCondition;
    this.transformColumnToFields = options.transformColumnToFields || this.defaultTransformColumnToFields;
    this.fieldNames = options.fieldNames || DEFAULT_FILED_NAMES_MAP;
    this.reverseFieldNames = _.invert(this.fieldNames);
    this.maxHeight = options.maxHeight || '100%';
    this.form = createRef<RuleFormInstance>();
    this.customHandleCustomCondition = options.handleCustomCondition;

    const mergedDefaultValue =
      options.defaultValue ??
      (options.defaultTransformValue ? this.transformToValue(options.defaultTransformValue) : DEFAULT_VALUES);
    this.values$.next(mergedDefaultValue);

    const defaultProps = this.generateDefaultRuleFormParams(columns);
    const mergedRuleFormProps = overrideRuleFormProps
      ? deepMerge(defaultProps, ruleFormProps)
      : ruleFormProps ?? defaultProps;
    this.ruleFormProps = !_.isEmpty(options.fieldTypeExclude)
      ? {
          ...mergedRuleFormProps,
          fields: _.filter(mergedRuleFormProps.fields, (field) => !options.fieldTypeExclude?.includes(field.type)),
        }
      : mergedRuleFormProps;
    this.TitleView = options.TitleView;
    this.FooterView = options.FooterView;
    this.titleText = options.titleText;
    this.okText = options.okText;
    this.cancelText = options.cancelText;
    this.okButtonProps = options.okButtonProps;
    this.cancelButtonProps = options.cancelButtonProps;
  }

  public destroy() {
    this.values$.complete();
    this.ruleFormProps = undefined!;
    this.onSubmitCallback = undefined!;
    this.transformSubmitValue = undefined!;
    this.transformCondition = undefined!;
    this.transformToValue = undefined!;
    this.fieldNames = undefined!;
    this.reverseFieldNames = undefined!;
    this.form = undefined!;
    this.TitleView = undefined;
    this.FooterView = undefined;
    this.titleText = undefined;
    this.okText = undefined;
    this.cancelText = undefined;
    this.okButtonProps = undefined;
    this.cancelButtonProps = undefined;
    this.maxHeight = undefined!;
  }

  public getMaxHeight() {
    return this.maxHeight;
  }

  public getTiltleView() {
    return this.TitleView;
  }

  public getFooterView() {
    return this.FooterView;
  }

  public getTitleText() {
    return this.titleText;
  }

  public getOkText() {
    return this.okText;
  }

  public getCancelText() {
    return this.cancelText;
  }

  public getOkButtonProps() {
    return this.okButtonProps || {};
  }

  public getCancelButtonProps() {
    return this.cancelButtonProps || {};
  }

  public getValues$() {
    return this.values$;
  }

  public getForm() {
    return this.form;
  }

  public getRuleFormProps() {
    return this.ruleFormProps;
  }

  public onTransformChange(values: T) {
    this.onChange(this.transformToValue(values));
  }

  public onChange(values: RuleValue) {
    this.values$.next(values);
  }

  public onSubmit = async () => {
    let filter = await this.form.current?.submit();
    // 有可能是rule-form没有渲染， 要看一下控制器变量是否被外部赋值
    if (!filter && !_.eq(this.values$.getValue(), DEFAULT_VALUES)) {
      filter = this.values$.getValue();
    }
    filter && this.onChange(filter);
    this.onSubmitCallback?.(this.transformSubmitValue(filter));
  };

  public onReset = async () => {
    const defaultValues = _.cloneDeep(DEFAULT_VALUES);
    await this.form.current?.reset();
    this.onChange(defaultValues);
    // 清空即提交
    this.onSubmitCallback?.(this.transformSubmitValue(defaultValues));
  };

  // 转换T结构到组件值
  public defaultTransformToValue(value: T): RuleValue {
    if (_.isEmpty(value)) {
      return DEFAULT_VALUES;
    }

    if (!_.isObject(value)) {
      console.error('value must be an object');
      return DEFAULT_VALUES;
    }

    const transformNode = (node: any): any => {
      const originalKey = Object.keys(node)[0];
      const key = this.reverseChangeFiledName(originalKey);
      const isNotKey = key === 'not';
      let newNode = node;
      const curNode = node[originalKey];
      const isAndOrOr = key && _.isArray(curNode);

      if (isAndOrOr) {
        return {
          relation: key,
          conditions: curNode.map(transformNode),
        };
      }
      if (isNotKey) {
        newNode = { ...curNode, not: true };
      }

      return this.transformToValueCondition(newNode);
    };

    return transformNode(value) as RuleValue;
  }

  // 自定义提交的数据结构
  // eslint-disable-next-line sonarjs/cognitive-complexity
  public defaultTransformSubmitValue(filter?: RuleValue): T {
    if (_.isEmpty(filter) || _.isEmpty(filter?.conditions)) {
      return {} as unknown as T;
    }

    const maps: any = {
      and: this.fieldNames['and'] || '$and',
      or: this.fieldNames['or'] || '$or',
      not: this.fieldNames['not'] || '$not',
    };

    const transform = (node: RuleValue | RuleCondition): any => {
      if ('relation' in node) {
        const newOperator = maps[node.relation];
        if (newOperator && _.isArray(node.conditions)) {
          return {
            [newOperator]: _.map(node.conditions, transform),
          };
        }
      }

      const condition = this.transformCondition(node as RuleCondition);
      const { not: conditionNot, ...rest } = condition;

      if (condition.operator === ColumnOperatorFilterEnum.NE) {
        return {
          [maps['or']]: [rest, { param: null, operator: ColumnOperatorFilterEnum.IS, column: condition.column }],
        };
      }
      if (conditionNot ?? node.not) {
        return { [maps['not']]: rest };
      }
      return rest;
    };

    return transform(filter!) as T;
  }

  public defaultTransformToValueCondition(node: T): RuleCondition {
    // 粗糙转换，需要根据业务场景进行定制
    const condition: RuleCondition = {
      value: (node as any)?.param,
      operator: (node as any)?.operator,
      name: (node as any)?.column,
      not: (node as any)?.not,
      type: (node as any)?.type || '',
    };
    return condition;
  }

  public defaultTransformCondition(
    node: RuleCondition,
    transformActionCondition?: (node: RuleCondition) => RuleCondition,
  ) {
    const overrideActionFunc = transformActionCondition ?? this.handleColumnActionCondition.bind(this);
    const condition = _.flow(
      this.handleUserIdCondition.bind(this),
      overrideActionFunc,
      this.handleDateCondition.bind(this),
      this.handleCustomCondition.bind(this),
    )(_.cloneDeep(node)) as RuleCondition;
    let mergedParam = condition.value;
    if (condition.operator === ColumnOperatorFilterEnum.IS && _.isEmpty(condition.value)) {
      mergedParam = null;
    }
    if (condition.operator === ColumnOperatorFilterEnum.ILIKE) {
      mergedParam = `%${mergedParam}%`;
    }
    return { param: mergedParam, operator: condition.operator, column: condition.name, not: condition.not };
  }

  public defaultTransformColumnToFields(columns: IColumn[]): RuleField[] {
    return _.map(columns, ({ type, name, title, dataSource }) => {
      return {
        type,
        name,
        title,
        dataSource,
      };
    });
  }

  protected handleUserIdCondition(node: RuleCondition): RuleCondition {
    // 默认实现：user_id 或 user_select_input 且 CONTAIN 只取第一个
    const isSingleUser = node.type === 'user_id';
    const isSelectUser = typeof node.type === 'string' && node.type.startsWith('UserSelectInput');
    const isSelectUserContain = isSelectUser && node.operator === ColumnOperatorFilterEnum.CONTAIN;
    if (isSingleUser || isSelectUserContain) {
      let value = node.value;
      value = node.value?.[0];
      return { ...node, value };
    }
    return node;
  }

  protected handleColumnActionCondition(node: RuleCondition): RuleCondition {
    return node;
  }

  protected handleDateCondition(node: RuleCondition): RuleCondition {
    // date 类型使用现有逻辑转 ISO 字符串
    if (node.type === FieldTypeEnum.DATE) {
      return { ...node, value: this.processDateValue(node.value) };
    }

    // datetime 类型转成+8带时区的value
    if (node.type === FieldTypeEnum.DATETIME) {
      return { ...node, value: this.processDateTimeValue(node.value) };
    }

    return node;
  }

  protected handleCustomCondition(node: RuleCondition): RuleCondition {
    if (this.customHandleCustomCondition) {
      return this.customHandleCustomCondition(node);
    }
    return node;
  }

  /**
   * 处理日期类型值，转为ISO字符串
   */
  private processDateValue(value: any): any {
    if (Array.isArray(value)) {
      return value.map((v) => (typeof v === 'string' ? v : v?.toJSON?.() ?? v));
    }
    return typeof value === 'string' ? value : value?.toJSON?.() ?? value;
  }

  /**
   * 处理日期时间类型值，转为带+8时区的ISO字符串
   */
  private processDateTimeValue(value: any): any {
    if (Array.isArray(value)) {
      return value.map((v) => formatToBeijingISO(v));
    }
    return formatToBeijingISO(value);
  }

  private generateDefaultRuleFormParams(columns: IColumn[]): IRuleFormProps {
    return {
      fields: this.transformColumnToFields(columns),
      types: _.map(columns, ({ type }) => ({ value: type, label: type })),
      operators: _.map(ColumnOperatorFilterEnum, (key) => ({ label: getColumnOperatorFilterLabel(key), value: key })),
      reactive: false,
      mapper: {
        [FieldTypeEnum.STR]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.ILIKE,
          ColumnOperatorFilterEnum.START_WITH,
        ],
        [FieldTypeEnum.INT]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.GT,
          ColumnOperatorFilterEnum.GE,
          ColumnOperatorFilterEnum.LT,
          ColumnOperatorFilterEnum.LE,
          ColumnOperatorFilterEnum.BETWEEN,
        ],
        [FieldTypeEnum.NUMBER]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.GT,
          ColumnOperatorFilterEnum.GE,
          ColumnOperatorFilterEnum.LT,
          ColumnOperatorFilterEnum.LE,
          ColumnOperatorFilterEnum.BETWEEN,
        ],
        [FieldTypeEnum.FLOAT]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.GT,
          ColumnOperatorFilterEnum.GE,
          ColumnOperatorFilterEnum.LT,
          ColumnOperatorFilterEnum.LE,
          ColumnOperatorFilterEnum.BETWEEN,
        ],
        [FieldTypeEnum.DATE]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.GT,
          ColumnOperatorFilterEnum.GE,
          ColumnOperatorFilterEnum.LT,
          ColumnOperatorFilterEnum.LE,
          ColumnOperatorFilterEnum.BETWEEN,
        ],
        [FieldTypeEnum.DATETIME]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.IS,
          ColumnOperatorFilterEnum.GT,
          ColumnOperatorFilterEnum.GE,
          ColumnOperatorFilterEnum.LT,
          ColumnOperatorFilterEnum.LE,
          ColumnOperatorFilterEnum.BETWEEN,
        ],
        [FieldTypeEnum.ARRAY_STR]: [
          ColumnOperatorFilterEnum.EQ,
          ColumnOperatorFilterEnum.NE,
          ColumnOperatorFilterEnum.INTERSECT,
          ColumnOperatorFilterEnum.SUPERSET,
          ColumnOperatorFilterEnum.SUBSET,
        ],
        [FieldTypeEnum.USER_ID]: [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE],
      },
      widgets: {
        [FieldTypeEnum.STR]: {
          default: 'Input',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.USER_ID]: {
          default: 'UserSelectId',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.ARRAY_STR]: 'Multiple',
        [FieldTypeEnum.NUMBER]: {
          default: 'InputNumber',
          between: 'InputNumberBetween',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.FLOAT]: {
          default: 'InputNumber',
          between: 'InputNumberBetween',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.INT]: {
          default: 'InputNumber',
          between: 'InputNumberBetween',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.DATE]: {
          default: 'DatePicker',
          between: 'RangePicker',
          [ColumnOperatorFilterEnum.IS]: null,
        },
        [FieldTypeEnum.DATETIME]: {
          default: 'TimePicker',
          between: 'TimeRangePicker',
          [ColumnOperatorFilterEnum.IS]: null,
        },
      },
    };
  }

  private reverseChangeFiledName = (name: string) => {
    return this.reverseFieldNames[name];
  };
}

export { FilterRuleFormController };
