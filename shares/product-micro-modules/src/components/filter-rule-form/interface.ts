import { CSSProperties, FC, ReactNode } from 'react';
import type { RuleCondition, RuleField, RuleFormProps, RuleValue } from '@metro/rule-form';
import { type ButtonProps } from '@metroDesign/button';
import type { IRuleFormProps } from '../../components/rule-form';
import { FilterRuleFormController } from './FilterRuleFormController';

export type ITransformSubmitValue<T> = (filter?: RuleValue) => T;
export type ITransformCondition = (value: RuleCondition) => any;
export type ITransformToValue<T> = (filter: T) => RuleValue;
export type ITransformToValueCondition = (value: any) => RuleCondition;
export type ITransformColumnToFields = (columns: IColumn[]) => RuleField[];

export interface IColumn {
  name: string;
  type: string;
  view_format?: Record<string, string>;
  title?: string;
  dataSource?: any[];
  component?: string;
  componentProps?: Record<string, any>;
}
export interface IControllerOptions<T> {
  defaultValue?: RuleValue; // 初始化组件默认值
  defaultTransformValue?: T; // 初始化转义默认值，通过transformToValue变为defaultValue
  columns: IColumn[]; // 构造规则的列信息
  ruleFormProps?: IRuleFormProps; // 规则表单的属性控制
  overrideRuleFormProps?: boolean; // 覆盖合并，默认true, 关闭后强制替换
  fieldNames?: Record<string, string>; // 字段名映射 eg: {and: '$and'}
  onSubmitCallback?: (filter?: T) => void; // 提交回调
  transformSubmitValue?: ITransformSubmitValue<T>; // 提交结果转换
  transformCondition?: ITransformCondition; // 提交的条件转换（包含在transformSubmitValue内处理）
  transformToValue?: ITransformToValue<T>; // T结构转换成value，默认方法只能保证容错率
  transformToValueCondition?: ITransformToValueCondition; // T结构的条件转换（包含在transformToValue内处理）
  transformColumnToFields?: (columns: IColumn[]) => RuleField[]; // 外部可能拿不到columns, 提供口子转换fields
  fieldTypeExclude?: string[]; // fields排除的类型
  maxHeight?: CSSProperties['maxHeight'];
  TitleView?: FC<ICtrl<T>>;
  FooterView?: FC<ICtrl<T>>;
  titleText?: ReactNode;
  okText?: ReactNode;
  cancelText?: ReactNode;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  handleCustomCondition?: (node: RuleCondition) => RuleCondition;
}

export interface IProps<T = any> {
  controller: FilterRuleFormController<T>;
  components?: RuleFormProps['components'];
  Footer?: FC<ICtrl<T>>;
  Title?: FC<ICtrl<T>>;
}

export interface ICtrl<T = any> {
  controller: FilterRuleFormController<T>;
}
