import _ from 'lodash';
import { FC } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { DatePicker } from '@metroDesign/date-picker';
import { Empty } from '@metroDesign/empty';
import { Flex } from '@metroDesign/flex';
import { Input } from '@metroDesign/input';
import { InputNumber } from '@metroDesign/input-number';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Select } from '@metroDesign/select';
import { Space } from '@metroDesign/space';
import { Typography } from '@metroDesign/typography';
import classnames from 'classnames';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { createDate, DATE_FORMATTER_2 } from '@mdtBsComm/utils/dayUtil';
import { MetroRuleForm } from '../../components/rule-form';
import i18n from '../../languages';
import { UserSelectInput } from '../user-select-input/UserSelectInput';
import type { ICtrl, IProps } from './interface';
import './index.less';

export const FilterRuleForm: FC<IProps> = ({ controller, components, Footer, Title }) => {
  const ruleFormProps = controller.getRuleFormProps();
  const form = controller.getForm();
  const values = useObservableState(controller.getValues$());
  const TitleCtrlView = controller.getTiltleView();
  const FooterCtrlView = controller.getFooterView();
  const TitleView = Title ?? TitleCtrlView ?? DefaultTitle;
  const FooterView = Footer ?? FooterCtrlView ?? DefaultFooter;
  const maxHeight = controller.getMaxHeight();
  const defaultComponents = {
    Input,
    InputNumber,
    Select: SelectView,
    DatePicker,
    TimePicker: DateTimePicker,
    TimeRangePicker: DateTimeBetweenPicker,
    RangePicker: DatePicker.RangePicker,
    InputNumberBetween,
    Multiple,
    UserSelect: UserSelectView,
    UserSelectId: UserSelectUserIdView,
  };
  const mergedComponents = { ...defaultComponents, ...components, ...ruleFormProps.components };
  return (
    <div className="filter-rule-form-container">
      <TitleView controller={controller} />
      <Scrollbar style={{ maxHeight }} forceVisible>
        <MetroRuleForm {...ruleFormProps} value={values} ref={form} components={mergedComponents} />
      </Scrollbar>
      <FooterView controller={controller} />
    </div>
  );
};

const UserSelect: FC<{ value: number[]; onChange: (value: number[]) => void }> = ({ value, onChange, ...rest }) => {
  return (
    <div
      className={classnames('filter-rule-form-user-select', {
        'filter-rule-form-user-select-has-value': value?.length,
      })}
    >
      <UserSelectInput
        value={value}
        onChange={onChange}
        btnLabel={`${i18n.chain.comText.add}${i18n.chain.proMicroModules.oneTable.tableColumns.filler}`}
        {...rest}
      />
    </div>
  );
};
const UserSelectView: FC = (props: any) => <UserSelect {...props} />;
const UserSelectUserIdView: FC = (props: any) => <UserSelect {...props} isMuti={false} />;

const SelectView: FC = (props: any) => <Select {...props} dropdownMatchSelectWidth={isPc()} width={120} showSearch />;
const DefaultFooter: FC<ICtrl> = ({ controller }) => {
  const okText = controller.getOkText();
  const cancelText = controller.getCancelText();
  const okButtonProps = controller.getOkButtonProps();
  const cancelButtonProps = controller.getCancelButtonProps();
  const onSubmit = () => controller.onSubmit();
  const onReset = () => controller.onReset();
  return (
    <Flex justify="flex-end" gap="small">
      <Button {...cancelButtonProps} onClick={onReset}>
        {cancelText || i18n.chain.proMicroModules.filter.clear}
      </Button>
      <Button type="primary" {...okButtonProps} onClick={onSubmit}>
        {okText || i18n.chain.proMicroModules.filter.startSearch}
      </Button>
    </Flex>
  );
};

const DefaultTitle: FC<ICtrl> = ({ controller }) => {
  const titleText = controller.getTitleText();
  return <Typography.Title level={4}>{titleText || i18n.chain.proMicroModules.filter.advancedFilter}</Typography.Title>;
};

const Multiple: FC<{ value: string[]; onChange: (value: string[]) => void }> = (props) => (
  <Select
    mode="tags"
    width={140}
    maxTagCount="responsive"
    showSearch
    notFoundContent={
      <Empty description={i18n.chain.proMicroModules.ruleForm.multipleEmpty} image={Empty.PRESENTED_IMAGE_SIMPLE} />
    }
    {...props}
  />
);

const InputNumberBetween: FC<{
  value: number[];
  onChange: (value: number[]) => void;
}> = (props) => {
  const { value, onChange, ...rest } = props;
  const [min, max] = Array.isArray(value) ? value : [];
  return (
    <Space>
      <InputNumber {...rest} value={min} max={max} onChange={(changedValue) => onChange?.([changedValue!, max])} />
      <InputNumber {...rest} value={max} min={min} onChange={(changedValue) => onChange?.([min, changedValue!])} />
    </Space>
  );
};

const DateTimePicker: FC = (props: any) => (
  <DatePicker format={DATE_FORMATTER_2} {...props} showTime value={createDate(props.value)} />
);
const DateTimeBetweenPicker: FC = (props: any) => (
  <DatePicker.RangePicker format={DATE_FORMATTER_2} {...props} value={_.map(props.value, createDate)} showTime />
);
