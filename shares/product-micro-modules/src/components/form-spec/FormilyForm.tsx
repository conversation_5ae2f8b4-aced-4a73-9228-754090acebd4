import React, { forwardRef, ForwardRefRenderFunction, useImperativeHandle, useMemo } from 'react';
import { createForm } from '@formily/core';
import { createSchemaField } from '@formily/react';
import {
  ArrayItems,
  ArrayTable,
  Button,
  Checkbox,
  DatePicker,
  Form,
  FormCollapse,
  FormGrid,
  FormItem,
  Input,
  NumberPicker,
  Password,
  PreviewText,
  Radio,
  Select,
  Space,
  Switch,
  TimePicker,
  Upload,
} from '@mdtFormily/index';
import { getFormValuesFromUrl } from '@mdtProComm/utils/urlUtil';
import { FormilyUserSelector } from '../../containers/formily-user-selector/FormilyUserSelector';
import i18n from '../../languages';
import { TableWithCurdFormily } from '../table-with-curd-formily';
import TagInputFormily from '../tag-input-formily';
import VariableInputFormily from '../variable-input-formily';
import { IFormSpecProps, IFormSpecRefHandle } from './index';
import './index.less';

const Text: React.FC<{
  value?: string;
  content?: string;
  mode?: 'normal' | 'h1' | 'h2' | 'h3' | 'p';
}> = ({ value, mode, content, ...props }) => {
  const tagName = mode === 'normal' || !mode ? 'div' : mode;
  return React.createElement(tagName, props, value || content);
};

const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormCollapse,
    Input,
    Password,
    Radio,
    Select,
    Switch,
    NumberPicker,
    FormGrid,
    Checkbox,
    DatePicker,
    TimePicker,
    ArrayItems,
    Space,
    PreviewText,
    Upload,
    Text,
    ArrayTable,
    UserSelector: FormilyUserSelector,
    TagInputFormily,
    VariableInputFormily,
    TableWithCurdFormily,
  },
});

const FormilyFormInner: ForwardRefRenderFunction<IFormSpecRefHandle, IFormSpecProps> = (
  { formSpec, onSubmit, confirmText = i18n.chain.comButton.submit, readonly, formData, scope, effects },
  ref,
) => {
  const defaultValueFromUrl = getFormValuesFromUrl();

  const form = useMemo(
    () =>
      createForm({
        initialValues: formData ?? defaultValueFromUrl,
        validateFirst: readonly ? false : true,
        readPretty: readonly ? true : false,
        effects,
      }),
    [readonly, formData, effects],
  );
  const { form: formProps, schema } = formSpec!.spec || {};

  useImperativeHandle(ref, () => ({
    getValues: async () => {
      try {
        return await form.submit();
      } catch {
        return;
      }
    },
  }));

  const handleSubmit = (values: any) => {
    if (readonly) return;
    onSubmit?.(values);
  };

  const submitBtn =
    !readonly && onSubmit ? (
      <div className="tool-wrap">
        <Button type="primary" htmlType="submit">
          {confirmText}
        </Button>
      </div>
    ) : null;

  const cls = `mdt-formily-form${readonly ? ' formily-form-readonly' : ''}`;
  return (
    <Form className={cls} {...formProps} form={form} onAutoSubmit={handleSubmit}>
      <SchemaField schema={schema} scope={scope} />
      {submitBtn}
    </Form>
  );
};

export const FormilyForm = forwardRef(FormilyFormInner);
