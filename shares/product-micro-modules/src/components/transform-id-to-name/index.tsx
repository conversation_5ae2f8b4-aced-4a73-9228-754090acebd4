import { FC, memo, useEffect, useState } from 'react';
import { Spin } from '@metroDesign/spin';
import { Tooltip } from '@metroDesign/tooltip';
import { TooltipPlacement } from '@metroDesign/tooltip/Tooltip';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { NameCacheEnum } from '@mdtProComm/controllers/NameCacheController';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';

interface IProps {
  id: number | string;
  type?: string;
  placement?: TooltipPlacement;
  showFullName?: boolean;
}

export const TransformIdToName: FC<Omit<IProps, 'showFullName'>> = memo(({ id, type }) => {
  let nameType = NameCacheEnum.ROLE;
  if (type === DbColumnTypeEnum.USER_ID || NameCacheEnum.USER) {
    nameType = NameCacheEnum.USER;
  }
  if (type === 'user_uuid') {
    nameType = NameCacheEnum.USER_UUID;
  }
  if (type === NameCacheEnum.GROUP) {
    nameType = NameCacheEnum.GROUP;
  }
  if (type === NameCacheEnum.USERORG) {
    nameType = NameCacheEnum.USERORG;
  }
  const name$ = DatlasAppController.getInstance().getNameCacheController().getName$(id, nameType);
  return useObservableState(name$);
});

export const TransformUserIdToOrgName: FC<Omit<IProps, 'type'>> = memo(({ id, placement, showFullName }) => {
  const [roleId, setRoleId] = useState<number | string>('');

  useEffect(() => {
    const name$ = DatlasAppController.getInstance().getNameCacheController().getName$(id, NameCacheEnum.USERORG);
    const sub = name$.subscribe((nr: number | string) => setRoleId(nr));
    return () => {
      sub.unsubscribe();
    };
  }, [id]);

  if (`${roleId}` === `${id}`) {
    return <Spin size="small" brand />;
  }

  if (!roleId) {
    return <span>{id}</span>;
  }

  return <ShowFullOrgIdToName id={roleId as number} placement={placement} showFullName={showFullName} />;
});

export const ShowRoleIdToName: FC<Omit<IProps, 'type'>> = memo(({ id, placement, showFullName }) => {
  const name$ = DatlasAppController.getInstance().getNameCacheController().getName$(id, NameCacheEnum.ROLE);
  const orgName = useObservableState(name$) as Record<string, string>;

  if (showFullName) {
    return <span>{orgName.fullName ?? <Spin size="small" brand />}</span>;
  }

  return (
    <Tooltip placement={placement} title={orgName.fullName || ''}>
      <span>{orgName.name ?? <Spin size="small" brand />}</span>
    </Tooltip>
  );
});

export const ShowFullOrgIdToName: FC<Omit<IProps, 'type'>> = memo(({ id, placement, showFullName }) => {
  const name$ = DatlasAppController.getInstance().getNameCacheController().getName$(id, NameCacheEnum.ORGLIST);
  const orgName = useObservableState(name$) as Record<string, string>;

  if (showFullName) {
    return <span>{orgName.fullName ?? <Spin size="small" brand />}</span>;
  }

  return (
    <Tooltip placement={placement} title={orgName.fullName || ''}>
      <span>{orgName.name ?? <Spin size="small" brand />}</span>
    </Tooltip>
  );
});

export const ShowFullGroupIdToName: FC<Omit<IProps, 'type'>> = memo(({ id, placement, showFullName }) => {
  const name$ = DatlasAppController.getInstance().getNameCacheController().getName$(id, NameCacheEnum.GROUP);
  const orgName = useObservableState(name$) as Record<string, string>;

  if (showFullName) {
    return <span>{orgName.fullName ?? <Spin size="small" brand />}</span>;
  }

  return (
    <Tooltip placement={placement} title={orgName.fullName || ''}>
      <span>{orgName.name ?? <Spin size="small" brand />}</span>
    </Tooltip>
  );
});
