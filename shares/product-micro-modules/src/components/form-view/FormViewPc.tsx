import { forwardRef, ForwardRefRenderFunction } from 'react';
import {
  ArrayCards,
  ArrayCollapse,
  ArrayItems,
  ArrayTable,
  AutoComplete,
  Cascader,
  Checkbox,
  ColorPicker,
  createMetroSchemaField,
  DatePicker,
  DescriptionText,
  FormCollapse,
  FormGrid,
  FormItem,
  FormTab,
  Input,
  NumberPicker,
  Password,
  PreviewText,
  Radio,
  Rate,
  ResidentInfo,
  Select,
  Space,
  Switch,
  TimePicker,
  TreeSelect,
} from '@mdtProFormEditor/metro-formily/index';
import { DatapkgSelectorComp as PkgSelect } from '../../containers/datapkg-selector';
import { FormilyUserSelector } from '../../containers/formily-user-selector';
import { FilterPanel } from '../filter-panel-formily';
import { GeometryInput } from '../geometry-input';
import { TableWithCurdFormily } from '../table-with-curd-formily';
import TagInputFormily from '../tag-input-formily';
import { Upload } from '../upload';
import { UploadOnly } from '../upload-only';
import { UserSelectInput } from '../user-select-input';
import VariableInputFormily from '../variable-input-formily';
import { FormViewComm, IFormSpecProps, IFormSpecRefHandle, Text } from './FormViewComm';

const SchemaFieldPc = createMetroSchemaField({
  components: {
    FormItem,
    FormCollapse,
    Input,
    Password,
    Radio,
    Rate,
    Select,
    Switch,
    NumberPicker,
    FormGrid,
    Checkbox,
    ColorPicker,
    DatePicker,
    TimePicker,
    ArrayItems,
    ArrayCards,
    ArrayCollapse,
    Space,
    PreviewText,
    Upload,
    UploadOnly,
    Text,
    ArrayTable,
    GeometryInput,
    UserSelectInput,
    FormTab,
    PkgSelect,
    TagInputFormily,
    VariableInputFormily,
    TableWithCurdFormily,
    UserSelector: FormilyUserSelector,
    Cascader,
    FilterPanel,
    TreeSelect,
    ResidentInfo,
    DescriptionText,
    AutoComplete,
  },
});

const FormViewInner: ForwardRefRenderFunction<IFormSpecRefHandle, IFormSpecProps> = (props, ref) => {
  return <FormViewComm {...props} SchemaField={SchemaFieldPc} ref={ref} />;
};

const FormViewPc = forwardRef(FormViewInner);

export default FormViewPc;
