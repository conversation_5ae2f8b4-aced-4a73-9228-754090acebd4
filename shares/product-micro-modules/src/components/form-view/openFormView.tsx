import React, { createRef, ReactNode } from 'react';
import { drawerApi } from '@metroDesign/drawer';
import { Modal } from '@metroDesign/modal';
import { Spin } from '@metroDesign/spin';
import i18n from '../../languages';
import { IFormSpecProps, IFormSpecRefHandle } from './FormViewComm';
import { FormView } from '.';
import './index.less';

type FetchFormData = Promise<any> | (() => Promise<any>);

const getBaseConfig = (compProps: any, isModal: boolean) => {
  if (isModal) {
    return {
      width: '50%',
      closable: false,
      maskClosable: false,
      keyboard: false,
      className: 'modal-form-view',
      ...compProps,
    };
  } else {
    return {
      title: i18n.chain.proMicroModules.formView.fillForm,
      width: '45%',
      className: 'drawer-form-view',
      okButtonProps: { style: { display: 'none' } },
      cancelButtonProps: { style: { display: 'none' } },
      closable: true,
      ...compProps,
    };
  }
};

const createOnOkHandler = (
  compProps: any,
  formViewProps: IFormSpecProps,
  formRef: React.RefObject<IFormSpecRefHandle>,
) => {
  return async (close: Function) => {
    const values = await formRef.current?.getValues();
    if (!values) return;

    if (compProps.onOk) {
      return compProps.onOk(close, values);
    }

    if (!formViewProps.onSubmit) {
      close();
      return;
    }
    const result = await formViewProps.onSubmit(values);
    if (result.success) {
      close();
    }
  };
};

const handleAsyncFormView = (
  compProps: any,
  formViewProps: IFormSpecProps & { fetchFormData?: FetchFormData },
  isModal: boolean,
) => {
  const fetchFormData = formViewProps.fetchFormData!;
  const formRef = createRef<IFormSpecRefHandle>();
  const openApi = isModal ? Modal : drawerApi;
  const { headerRender, extraRender, ...rest } = compProps || {};
  const config = getBaseConfig(rest, isModal);

  const instance = openApi.open({
    ...config,
    content: <Spin style={{ width: '100%' }} />,
  });

  Promise.resolve(typeof fetchFormData === 'function' ? fetchFormData() : fetchFormData).then((data) => {
    instance.update({
      content: (
        <>
          {headerRender}
          <FormView {...formViewProps} formData={data} ref={formRef} />
          {extraRender}
        </>
      ),
      onOk: createOnOkHandler(compProps, formViewProps, formRef),
      okButtonProps: { style: {} },
      cancelButtonProps: { style: {} },
    });
  });
};

const handleSyncFormView = (
  compProps: any & { headerRender?: ReactNode; extraRender?: ReactNode },
  formViewProps: IFormSpecProps,
  isModal: boolean,
) => {
  const { headerRender, extraRender, ...rest } = compProps || {};
  if (isModal) {
    const formRef = createRef<IFormSpecRefHandle>();
    Modal.open({
      ...getBaseConfig(rest, true),
      onOk: createOnOkHandler(compProps, formViewProps, formRef),
      content: (
        <>
          {headerRender}
          <FormView ref={formRef} {...formViewProps} />
          {extraRender}
        </>
      ),
    });
  } else {
    drawerApi.open({
      ...getBaseConfig(rest, false),
      content: (
        <>
          {headerRender}
          <FormView {...formViewProps} />
          {extraRender}
        </>
      ),
    });
  }
};

export const openFormView = (
  compProps: any & { headerRender?: ReactNode; extraRender?: ReactNode },
  formViewProps: IFormSpecProps & { fetchFormData?: FetchFormData },
  isModal = false,
) => {
  formViewProps.fetchFormData
    ? handleAsyncFormView(compProps, formViewProps, isModal)
    : handleSyncFormView(compProps, formViewProps, isModal);
};
