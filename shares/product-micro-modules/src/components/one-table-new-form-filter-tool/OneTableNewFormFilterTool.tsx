import { ChangeEvent, FC, memo, useCallback, useEffect, useRef, useState } from 'react';
import { Close, Delete1, FilterOutlined } from '@metro/icons';
import { Badge } from '@metroDesign/badge';
import { Button } from '@metroDesign/button';
import { DatePicker } from '@metroDesign/date-picker';
import { Flex } from '@metroDesign/flex';
import { Input } from '@metroDesign/input';
import { Popover } from '@metroDesign/popover';
import { Space } from '@metroDesign/space';
import { Typography } from '@metroDesign/typography';
import { useDebounce } from 'ahooks';
import { Dayjs } from 'dayjs';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DebounceTimeEnum } from '@mdtProComm/constants';
import i18n from '../../languages';
import { FormView } from '../form-view';
import { type IFilterValue, OneTableNewFormFilterToolController } from './OneTableNewFormFilterToolController';
import { useControlledPopover } from './useControlledPopover';
import './index.less';

interface IProps {
  controller: OneTableNewFormFilterToolController;
}

const Content: FC<IProps> = memo(({ controller }) => {
  const spec = controller.getFilterSpec();
  const open = useObservableState(controller.getOpenAdvancedFilter$());
  const { metaQuery } = useObservableState(controller.getFilterValue$()) || {};
  const popoverRef = useRef<HTMLDivElement>(null);
  useControlledPopover(popoverRef, open, controller.setOpenAdvancedFilter.bind(controller), [
    '.metro-select-dropdown',
    '.metro-select-arrow',
  ]);

  const handleChange = (value: IFilterValue['metaQuery']) => {
    controller.setMetaQuery(value);
  };

  const clickClose = () => {
    controller.setOpenAdvancedFilter(false);
  };

  const reset = () => {
    controller.resetMetaQuery();
  };

  return (
    <div ref={popoverRef}>
      <Space direction="vertical" block>
        <Flex justify="space-between" align="center">
          <Typography.Title level={4} style={{ marginBottom: 0 }}>
            {i18n.chain.proMicroModules.oneTable.advanceFilter.title}
          </Typography.Title>
          <Button onlyIcon ghost icon={<Close />} onClick={clickClose} />
        </Flex>
        <FormView {...spec} formData={metaQuery} onChange={handleChange} />
        <Button.Link icon={<Delete1 />} primary onClick={reset}>
          {i18n.chain.proMicroModules.oneTable.advanceFilter.reset}
        </Button.Link>
      </Space>
    </div>
  );
});

const SearchInput = memo(
  ({ value, onChange, placeholder }: { value?: string; onChange: (value: string) => void; placeholder: string }) => {
    const [inputValue, setInputValue] = useState(value || '');

    useEffect(() => {
      if (value !== undefined) {
        setInputValue(value);
      }
    }, [value]);

    const debouncedValue = useDebounce(inputValue, { wait: DebounceTimeEnum.MAX });

    useEffect(() => {
      if (debouncedValue !== value) {
        onChange(debouncedValue);
      }
    }, [debouncedValue, onChange, value]);

    const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
    }, []);

    return <Input placeholder={placeholder} style={{ width: 256 }} value={inputValue} onChange={handleChange} />;
  },
);

export const OneTableNewFormFilterTool: FC<IProps> = memo(({ controller, ...rest }) => {
  const { name, createTimeMax, createTimeMin } = useObservableState(controller.getFilterValue$()) || {};
  const open = useObservableState(controller.getOpenAdvancedFilter$());
  const dot = useObservableState(controller.getFilterDotTip$());
  const enableAdvancedSearch = controller.getEnableAdvancedFilter();
  const enableDateSearch = controller.getEnableDateSearch();
  const enableNameSearch = controller.getEnableNameSearch();

  const rangeValue = createTimeMin && createTimeMax ? [createTimeMin, createTimeMax] : undefined;

  const handleDateChange = useCallback(
    (values: Dayjs[]) => {
      const [min, max] = values ?? [];
      controller.setCreateTimeRange(min, max);
    },
    [controller],
  );

  const togglePopover = useCallback(
    (e: any) => {
      e.stopPropagation();
      controller.setOpenAdvancedFilter(!open);
    },
    [controller, open],
  );

  const handleNameChange = useCallback(
    (value: string) => {
      controller.setName(value);
    },
    [controller],
  );

  return (
    <Flex className="one-table-new-form-filter-tool" gap="small">
      {enableAdvancedSearch ? (
        <Badge {...rest} dot={dot}>
          <Popover
            open={open}
            overlayInnerStyle={{ width: 700 }}
            content={<Content controller={controller} />}
            overlayClassName="one-table-new-form-filter-tool-content"
            placement="bottom"
            destroyTooltipOnHide
          >
            <Button icon={<FilterOutlined />} onClick={togglePopover} style={{ height: '34px' }}>
              {i18n.chain.proMicroModules.oneTable.advanceFilter.title}
            </Button>
          </Popover>
        </Badge>
      ) : null}
      {enableDateSearch ? (
        // @ts-ignore
        <DatePicker.RangePicker values={rangeValue} onChange={handleDateChange} />
      ) : null}
      {enableNameSearch ? (
        <SearchInput
          placeholder={i18n.chain.proMicroModules.oneTable.formPlaceholder}
          value={name}
          onChange={handleNameChange}
        />
      ) : null}
    </Flex>
  );
});
