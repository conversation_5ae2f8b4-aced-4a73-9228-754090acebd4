import _ from 'lodash';
import type {
  ICfs,
  ICfsUserExpireTip,
  IDevelopValue,
  IRequestEncodeApis,
  IRequestEncodeApisOptions,
  IRequestTransformApiMethod,
} from '@mdtProComm/interfaces';
import type { IOneTableInfo, IWorkflowInfo } from '../interfaces';

export let PRODUCT_NAME = '';
export let SITE_ORIGIN = '';
export let DEPLOY_ORIGIN = '';
export let SUB_MICRO_ORIGIN = '';
export let IS_MICRO_ENV = false;
export let MICRO_APP_NAME = '';
export let BASE_NAME = '';
export let WINDOW_TITLE = '';
export let WINDOW_DESC = '';
export let ENABLE_HASH_ROUTER = false;
export let FORMILY_CORE_PATH = '';
export let ENABLE_WEB_VITALS = false;
export let ENABLE_PWA = false;
export let HEADER_LOGO_URL = '';
export let HEADER_HIDDEN_APP = false;
export let ENV = '';
export let IS_ENV_DEV = false;
export let IS_ENV_STAGING = false;
export let IS_ENV_PROD = false;
export let IS_DEVELOP = false;
export let PROXY_URL = '';
export let API_URL = '';
export let MAP_API_URL = '';
export let ENCODE_APIS: IRequestEncodeApis | undefined;
export let ENCODE_APIS_OPTIONS: IRequestEncodeApisOptions | undefined;
export let TRANSFORM_API_METHOD: IRequestTransformApiMethod | undefined;
export let ENABLE_BFF = false;
export let BFF_URL = '';
export let BFF_PUBLIC_FLOWORK_URL = '';
export let BFF_INNER_GRAPHQL_URL = '';
export let SOCKET_URL = '';
export let SOCKET_OPTIONS: any;
export let SSO_LOGOUT_URL = '';
export let SSO_HASH: boolean;
export let VERIFY_URL = '';
export let HELP_CENTER_URL = '';
export let ALLOW_JUMP_PRODUCTS = false;
export let JUMP_ACTION_TYPE = '';
export let JUMP_DATA_FACTORY_URL = '';
export let JUMP_DATA_MARKET_URL = '';
export let JUMP_WORKFLOW_URL = '';
export let JUMP_ONE_TABLE_URL = '';
export let JUMP_MY_DATA_URL = '';
export let JUMP_ORGANIZATION_MANAGEMENT_URL = '';
export let JUMP_RESOURCE_SHARE_URL = '';
export let JUMP_FORM_DESIGN_URL = '';
export let JUMP_DATA_MAP_URL = '';
export let JUMP_MAP_EDITOR_URL = '';
export let JUMP_DATLAS_ADMIN_URL = '';
export let JUMP_COLLECTOR_URL = '';
export let USER_EXPIRE_TIP: ICfsUserExpireTip = false;
export let LANGUAGE = '';
export let KEY_ROUTE_MAP: Record<string, string> | undefined;
// 数据市场数据管理员 角色id
export let DM_DATA_MANAGER_ROLE_ID: number;
export let FILE_PREVIEW_URL = '';
export let AMIS_JSON_EDITOR_URL = '';
// 微信
export let ENABLE_WX_SDK = false;
export let WX_SDK_URL = '';
export let WX_SIGNATURE_KEY = '';
export let WX_APP_ID = '';
// 流程引擎的图片限制大小
export let WF_LIMIT_SIZE: number;
export let COMPRESSION_IMAGE_PATH = '';
// 一表通配置信息（流程1、2的id及报表流程到流程1的字段映射关系）
export let ONE_TABLE_INFO: IOneTableInfo;
export let WORKFLOW_INFO: IWorkflowInfo;
// onetable电子表格每次编辑数据请求大小
export let ONE_TABLE_DATAPKG_ROWS_SIZE = 1000;
// onetable 通知催办额外的标题
export let ONE_TABLE_EXPEDITE_TITLE_EXTRA = '';
// croniter是后端使用的python库, 不支持?及年设置,秒在最后
export let TIMER_CRON_MODE = '';
// 是否启用预加载
export let ENABLE_PRELOAD_LARGER_DATA = false;
// 隐藏左上角菜单按钮
export let HIDDEN_HEADER_LEFT_MENU = false;
// 没有当前产品权限时是否跳转有权限的产品
export let REDIRECT_IF_NO_PRODUCT_PERMISSION = true;
// 隐藏表单编辑器中的居民信息
export let HIDDEN_FORM_VIEW_RESIDENT_INFO = true;
// 开启表格可见列设置
export let ENABLE_TABLE_COLUMN_SETTING = false;
// 开启数据表格可见列设置
export let ENABLE_DATA_TABLE_COLUMN_SETTING = true;

const PRODUCT_PATH_DATAMARKET = 'datamarket';
const PRODUCT_PATH_MAP: Record<string, string> = {
  'data-market': PRODUCT_PATH_DATAMARKET,
  'data-factory': 'datafactory',
  'one-table': 'onetable',
  workflow: 'workflow',
};
let oneTableAppConfig: Record<string, Record<string, any>>;
let workflowAppConfig: Record<string, Record<string, any>>;
// eslint-disable-next-line complexity,sonarjs/cognitive-complexity
export const initCommConfig = (productName: string, develop: IDevelopValue, config?: ICfs, overrideConfig?: ICfs) => {
  const isMicro = window.__MICRO_APP_ENVIRONMENT__;
  const originalCfs = config || window[`__DM_${productName.replace(/-/g, '_').toUpperCase()}_CFS` as keyof Window];
  const cfs = _.merge(originalCfs, overrideConfig);
  const dp = cfs.deployPublicPath || (isMicro ? window.__MICRO_APP_PUBLIC_PATH__ : '/');
  cfs.deployPublicPath = dp;
  // 运行环境
  const env = cfs.deployEnv;
  // 部署域名
  const isDevelop = develop.isDevelop || false;
  let origin = isDevelop ? develop.developEnvOrigin! : window.location.origin;
  // 微应用环境下，获取子应用的origin
  isMicro && (origin = new URL(getAbsoluteUrl(dp)).origin);
  const productPath = PRODUCT_PATH_MAP[productName] || PRODUCT_PATH_DATAMARKET;

  // 通用
  PRODUCT_NAME = `mdt-${productName}`;
  IS_MICRO_ENV = isMicro ?? false;
  MICRO_APP_NAME = window.__MICRO_APP_NAME__ || '';
  // 部署相关设置
  DEPLOY_ORIGIN = getAbsoluteUrl(dp);
  SITE_ORIGIN = getAbsoluteUrl(cfs.deploySitePath || dp);
  SUB_MICRO_ORIGIN = getAbsoluteUrl(origin);
  BASE_NAME = window.__MICRO_APP_BASE_ROUTE__ || cfs.deployRouterPath;
  WINDOW_TITLE = cfs.deployWindowTitle || document.title;
  WINDOW_DESC = cfs.deployWindowDesc || WINDOW_TITLE;
  ENABLE_HASH_ROUTER = cfs.deployEnableHashRouter || false;
  FORMILY_CORE_PATH = getAbsoluteUrl('static/@formily/core/dist/formily.core.all.d.ts', DEPLOY_ORIGIN);
  // 微信相关
  ENABLE_WX_SDK = cfs.enableWxSdk ?? false;
  WX_SDK_URL = getAbsoluteUrl(cfs.wxSdkUrl || '/static/libs/jweixin-1.6.0.js', DEPLOY_ORIGIN);
  COMPRESSION_IMAGE_PATH = getAbsoluteUrl(
    cfs.compressionImagePath || '/static/libs/browser-image-compression.js',
    DEPLOY_ORIGIN,
  );
  WX_SIGNATURE_KEY = cfs.wxSignatureKey || 'eb83b880-7fe0-4aff-841c-cd3a95612394';
  WX_APP_ID = cfs.wxAppId || 'wx9db14b6130787f9c'; // 公众号唯一标识
  // 处理headerLogo
  HEADER_LOGO_URL = getAbsoluteUrl(cfs.deployHeaderLogoUrl || '', dp);
  HEADER_HIDDEN_APP = cfs.deployHiddenApp || false;
  // 运行环境设置
  ENV = env || '';
  IS_ENV_DEV = env === 'dev';
  IS_ENV_STAGING = env === 'staging';
  IS_ENV_PROD = env === 'prod';
  IS_DEVELOP = isDevelop;
  PROXY_URL = develop.developProxyApiUrl || '';
  // PROXY_URL = '';
  // 性能分析
  ENABLE_WEB_VITALS = !!cfs.deployEnableWebVitals;
  ENABLE_PWA = !!cfs.deployEnablePwa;
  // api请求相关配置
  API_URL = cfs.backendApiUrl ?? `${origin}/api`;
  ENCODE_APIS = cfs.backendEncodeApis;
  ENCODE_APIS_OPTIONS = cfs.backendEncodeApisOptions;
  TRANSFORM_API_METHOD = cfs.backendTransformApiMethod;
  // 地图api
  MAP_API_URL = cfs.backendMapApiUrl ?? API_URL;
  // bff相关配置
  BFF_URL = cfs.bffUrl ?? `${API_URL}/datlas_bff/graphql`;

  ENABLE_BFF = !!BFF_URL;
  const bffPublicFloworkUrl = '/public/datlas_bff/flowork';
  BFF_PUBLIC_FLOWORK_URL = isDevelop ? bffPublicFloworkUrl : `${API_URL}${bffPublicFloworkUrl}`;
  const bffInnerGraphqlUrl = '/datlas_bff/dispatcher/inner_graphql';
  BFF_INNER_GRAPHQL_URL = `${API_URL}${bffInnerGraphqlUrl}`;

  // socket相关配置
  SOCKET_URL = cfs.backendSocketUrl || origin;
  SOCKET_OPTIONS = cfs.backendSocketOptions;
  // SSO相关配置
  SSO_LOGOUT_URL = cfs.ssoLogoutUrl ?? `${origin}/sso/`;
  SSO_HASH = cfs.ssoEnableHashRouter ?? true;
  VERIFY_URL = cfs.ssoVerifyUrl || new URL(`${SSO_HASH ? '#/' : ''}frontend`, SSO_LOGOUT_URL).toString();
  // 帮助中心相关配置
  HELP_CENTER_URL = cfs.helpCenterUrl ?? `${origin}/statics/help/datlas/#/`;
  // 通用功能配置
  USER_EXPIRE_TIP = cfs.userExpireTip || false;
  // 产品跳转
  ALLOW_JUMP_PRODUCTS = cfs.allowJumpProducts ?? true;
  JUMP_ACTION_TYPE = cfs.jumpActionType || 'replace';
  JUMP_DATA_FACTORY_URL = cfs.jumpDataFactoryUrl ?? `${origin}/datafactory/`;
  JUMP_DATA_MARKET_URL = cfs.jumpDataMarketUrl ?? `${origin}/datamarket/`;
  JUMP_WORKFLOW_URL = cfs.jumpWorkflowUrl ?? `${origin}/workflow/`;
  JUMP_ONE_TABLE_URL = cfs.jumpOneTableUrl ?? `${origin}/onetable/`;
  JUMP_MY_DATA_URL = cfs.jumpMyDataUrl ?? `${origin}/${productPath}/mydata/`;
  JUMP_ORGANIZATION_MANAGEMENT_URL = cfs.jumpOrganizationManagementUrl ?? `${origin}/${productPath}/orgadmin/`;
  JUMP_RESOURCE_SHARE_URL = cfs.jumpResourceShareUrl ?? `${origin}/${productPath}/share/`;
  JUMP_FORM_DESIGN_URL = cfs.jumpFormDesignUrl ?? `${origin}/${productPath}/formdesign/`;
  JUMP_DATA_MAP_URL = cfs.jumpDataMapUrl ?? `${origin}/dataapp/`;
  JUMP_MAP_EDITOR_URL = cfs.jumpMapEditorUrl ?? `${origin}/dataeditor/`;
  JUMP_DATLAS_ADMIN_URL = cfs.jumpDatlasAdminUrl || '';
  JUMP_COLLECTOR_URL = cfs.jumpCollectorUrl || '';
  LANGUAGE = cfs.productLanguage || '';
  KEY_ROUTE_MAP = cfs.productKeyRouteMap || {};
  DM_DATA_MANAGER_ROLE_ID = cfs.dmDataManagerRoleId || 581855;
  FILE_PREVIEW_URL = cfs.filePreviewUrl ?? `${origin}/fileview/onlinePreview`;
  AMIS_JSON_EDITOR_URL = cfs.amisJsonEditor ?? `${origin}/mdtamiseditor/`;
  WF_LIMIT_SIZE = cfs.wfFileLimitSize || 500;
  ONE_TABLE_DATAPKG_ROWS_SIZE = cfs.oneTableDatapkgRowsSize || 1000;
  ONE_TABLE_EXPEDITE_TITLE_EXTRA = cfs.oneTableExpediteTitleExtra || '';
  ONE_TABLE_INFO = _.merge(
    {
      periodicMinInterval: 30,
      formEnableNameSearch: true,
      formEnableAdvancedFilter: true,
      formEnableDateSearch: true,
      includeOverrideAdvancedFilterKey: true,
      reportConfig: {
        formSpec: {},
        selectedReinforceUsersSpec: {},
        selectedTransferUserSpec: {},
        exportToMyDataSpec: {},
        flowSpec: {},
        flowEndlessSpec: {},
        flowPeriodicSpec: {},
        advancedFilterSpec: {},
      },
    },
    cfs.oneTableInfo,
  );
  oneTableAppConfig = cfs.oneTableAppConfig || {};
  WORKFLOW_INFO = _.merge({}, cfs.workflowInfo);
  workflowAppConfig = cfs.workflowAppConfig || {};
  TIMER_CRON_MODE = cfs.timerCronMode ?? 'croniter';
  ENABLE_PRELOAD_LARGER_DATA = cfs.enablePreloadLargerData ?? false;
  HIDDEN_HEADER_LEFT_MENU = cfs.hiddenHeaderLeftMenu ?? false;
  REDIRECT_IF_NO_PRODUCT_PERMISSION = cfs.redirectIfNoProductPermission ?? true;
  HIDDEN_FORM_VIEW_RESIDENT_INFO = cfs.hiddenFormViewResidentInfo ?? true;
  ENABLE_TABLE_COLUMN_SETTING = cfs.enableTableColumnSetting ?? false;
  ENABLE_DATA_TABLE_COLUMN_SETTING = cfs.enableDataTableColumnSetting ?? true;
  // 修改一些html通用配置
  initHtmlElement(cfs);
  return cfs;
};

export const updateWorkflowAppConfig = (appName: string) => {
  const info = workflowAppConfig[appName];
  WORKFLOW_INFO = _.merge({}, WORKFLOW_INFO, info);
};

export const updateOneTableInfo = (appName: string) => {
  const info = oneTableAppConfig[appName];
  ONE_TABLE_INFO = _.merge({}, ONE_TABLE_INFO, info);
};

export const updateOneTableAppConfig = (
  appName: string,
  config: any,
  options?: {
    updateOneTableInfoRedirect?: boolean;
    override?: boolean;
  },
) => {
  const { override = true, updateOneTableInfoRedirect = true } = options || {};
  const appConfig = { [appName]: config };
  oneTableAppConfig = override ? _.merge(oneTableAppConfig, appConfig) : appConfig;
  updateOneTableInfoRedirect && updateOneTableInfo(appName);
};

const initHtmlElement = (cfs: ICfs) => {
  // 提示
  let tipEle = document.querySelector('.dm-window_title_tip');
  tipEle && (tipEle.innerHTML = IS_MICRO_ENV ? '资源加载中...' : '欢迎使用' + WINDOW_TITLE);
  tipEle = null;
  if (IS_MICRO_ENV) return;
  // 标题
  document.title = WINDOW_TITLE;
  // 描述
  let descEle = document.querySelector('head meta[name="description"]');
  descEle?.setAttribute('content', WINDOW_DESC);
  descEle = null;
  // favicon, 防止刷新时脉策logo及标题
  // let icoEle: HTMLLinkElement | null = document.createElement('link');
  // icoEle.rel = 'icon';
  // icoEle.href = getAbsoluteUrl(cfs.deployFavIcon || cfs.deployFavicon || `${DEPLOY_ORIGIN}static/favicon.ico`);
  // document.head.appendChild(icoEle);
  // icoEle = null;
  // manifest
  if (ENABLE_PWA) {
    let mfEle: HTMLLinkElement | null = document.createElement('link');
    mfEle.rel = 'manifest';
    mfEle.href = getAbsoluteUrl(cfs.deployManifest || `${DEPLOY_ORIGIN}static/manifest.json`);
    document.head.appendChild(mfEle);
    mfEle = null;
  }
};

export const getAbsoluteUrl = (url: string, target?: string) => {
  if (!url || url.startsWith('data:') || /^((https?):)?\/\//gm.test(url)) return url;
  return String(new URL(url, target || window.location.origin));
};

export const getDefaultMicroConfig = (subName: string) => {
  return { entry: new URL(`${subName}/micro.html`, SUB_MICRO_ORIGIN).toString() };
};
