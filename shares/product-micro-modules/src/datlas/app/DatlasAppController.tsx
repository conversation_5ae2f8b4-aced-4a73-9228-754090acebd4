import _ from 'lodash';
import { FC } from 'react';
import { I18n } from '@i18n-chain/core';
import dayjs from 'dayjs';
import { History } from 'history';
import { compressToEncodedURIComponent } from 'lz-string';
import { ApolloService, IBffRequestConfig } from '@mdtBsBffServices/ApolloService';
import { getFromStorage, removeFromStorage, saveToStorage } from '@mdtBsComm/utils/storageUtil';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { LanguageEnum } from '@mdtProComm/constants';
import { BaseUserPermissionController } from '@mdtProComm/controllers/BaseUserPermissionController';
import { IJumpToOtherProductOptions, IRequestRequestConfig, IStoreToken } from '@mdtProComm/interfaces';
import { isInsideIframe } from '@mdtProComm/utils/commonUtil';
import { getLanguageFromUrl, getQFromUrl, getTokenFromUrl, modifyParamsOfUrl } from '@mdtProComm/utils/urlUtil';
import 'dayjs/locale/zh-cn'; // 加载国际化资源
import { AbstractAppController } from '../../controllers/AbstractAppController';
import { DatlasAppSideMenuController } from '../../datlas/app-side-menu';
import { Locale } from '../../languages';
import { AppHeaderController, AppHeaderModel, ChangeTypeEnum } from '../../pages/app-header';
import { bffProxyConfigData } from '../../shared/enums';
import { DatlasRouterController } from '../comm/DatlasRouterController';
import {
  microAutoAuthByToken,
  microGetRequirementData,
  microJumpToDataFactory,
  microJumpToOrganizationManagement,
} from '../comm/microUtil';
import {
  ALLOW_JUMP_PRODUCTS,
  API_URL,
  BFF_INNER_GRAPHQL_URL,
  BFF_PUBLIC_FLOWORK_URL,
  BFF_URL,
  ENCODE_APIS,
  ENCODE_APIS_OPTIONS,
  HEADER_LOGO_URL,
  HELP_CENTER_URL,
  IS_DEVELOP,
  IS_MICRO_ENV,
  JUMP_ACTION_TYPE,
  JUMP_COLLECTOR_URL,
  JUMP_DATA_FACTORY_URL,
  JUMP_DATA_MAP_URL,
  JUMP_DATA_MARKET_URL,
  JUMP_DATLAS_ADMIN_URL,
  JUMP_FORM_DESIGN_URL,
  JUMP_MAP_EDITOR_URL,
  JUMP_MY_DATA_URL,
  JUMP_ORGANIZATION_MANAGEMENT_URL,
  JUMP_RESOURCE_SHARE_URL,
  JUMP_WORKFLOW_URL,
  LANGUAGE,
  PRODUCT_NAME,
  PROXY_URL,
  SOCKET_OPTIONS,
  SOCKET_URL,
  SSO_LOGOUT_URL,
  TRANSFORM_API_METHOD,
  updateOneTableInfo,
  USER_EXPIRE_TIP,
  VERIFY_URL,
} from '../datlasConfig';

export type IDatlasAppController = DatlasAppController<any, any, any>;
interface IDatlasAppControllerOptions {
  i18n?: I18n<Locale, Locale>;
  ignoreGlobalData?: boolean;
}
interface IHeaderOptions {
  defaultProduct: string;
  defaultModule?: string;
  dmCloudFlag?: boolean;
  dynamicOpt?: Record<string, boolean>;
  applyDatapkgUrl?: string;
}
const WINDOW_REMOVE_TOKENS = [
  '__core-js_shared__',
  '__DM_DATA_FACTORY_CFS',
  '__DM_DATA_MARKET_CFS',
  '__DM_DESIGNABLE_CFS',
  '__DM_MY_DATA_CFS',
  '__DM_ORGANIZATION_MANAGEMENT_CFS',
  '__DM_RESOURCE_SHARE_CFS',
  '__DM_WORKFLOW_CFS',
  '__dm_memory_leak_list',
  '_',
  '$i',
  'saveAs',
  'webpackChunkmdt_datlas',
  'webpackJsonp',
  'regeneratorRuntime',
  'handler',
  'onmessage',
  'Mousetrap',
  'dispatch',
  'Designable',
  'Zousan',
  'Hammer',
];
const REFLECT_REMOVE_TOKENS = [
  'decorate',
  'defineMetadata',
  'deleteMetadata',
  'getMetadata',
  'getMetadataKeys',
  'getOwnMetadata',
  'getOwnMetadataKeys',
  'hasMetadata',
  'hasOwnMetadata',
  'metadata',
];

abstract class DatlasAppController<
  R extends DatlasRouterController,
  U extends BaseUserPermissionController,
  M extends DatlasAppSideMenuController,
> extends AbstractAppController {
  // 全局私有变量
  private static instance?: any;
  // 获取实例
  public static getInstance(history?: History) {
    if (!DatlasAppController.instance) {
      // @ts-ignore
      DatlasAppController.instance = new this(history!);
    }
    return DatlasAppController.instance;
  }

  // 销毁单例
  public static destroy() {
    DatlasAppController.instance?.destroy();
    DatlasAppController.instance = undefined;
    this.fixMemoryLeak();
  }

  // 修复内存泄漏
  private static fixMemoryLeak() {
    const docEle = document as any;
    const reactListens = docEle['__react_document_listens'] || [];
    _.forEach(reactListens, (it) => {
      const [name, func] = it || [];
      docEle.removeEventListener(name, func);
      docEle.removeEventListener(name, func, true);
    });
    reactListens.length = 0;
    // 释放window事件
    (window.__dm_memory_leak_list || []).forEach((it: Function) => it.call(it));
    const keys = _.keys(docEle).filter((key) => key.includes('__react'));
    keys.forEach((key) => {
      if (docEle[key] instanceof Set) {
        docEle[key].clear();
      } else if (Array.isArray(docEle[key])) {
        docEle[key].length = 0;
      }
    });
    // 释放全局内存
    WINDOW_REMOVE_TOKENS.forEach((key: any) => {
      Array.isArray(window[key]) && ((window[key] as unknown as any[]).length = 0);
      delete window[key];
    });
    REFLECT_REMOVE_TOKENS.forEach((key: string) => {
      // @ts-ignore
      Reflect[key] = null;
    });
  }

  protected removeBffProxy?: () => void;
  protected removePublicTransform?: () => void;
  protected routerController?: R;
  protected userPermissionController?: U;
  protected appHeaderController?: AppHeaderController;
  protected appSideMenuController?: M;
  private i18n?: I18n<Locale, Locale>;
  private ignoreGlobalData?: boolean;

  public constructor(options: IDatlasAppControllerOptions = {}) {
    super(BFF_URL);
    const { i18n, ignoreGlobalData } = options;
    this.ignoreGlobalData = ignoreGlobalData;
    this.i18n = i18n;
    this.initBffProxy();
    this.initPublicTransform();
  }

  public destroy() {
    super.destroy();
    this.routerController?.destroy();
    this.routerController = undefined;
    this.userPermissionController?.destroy();
    this.userPermissionController = undefined;
    this.appHeaderController?.destroy();
    this.appHeaderController = undefined;
    this.appSideMenuController?.destroy();
    this.appSideMenuController = undefined;
    this.i18n = undefined;
    this.removeBffProxy?.();
    this.removeBffProxy = undefined;
    this.removePublicTransform?.();
    this.removePublicTransform = undefined;
  }

  // 未登录适配
  public adaptaUserWithUnlogin() {}

  public getRouterController() {
    return this.routerController!;
  }

  public getUserPermissionController() {
    return this.userPermissionController!;
  }

  public getAppHeaderController() {
    return this.appHeaderController!;
  }

  public getAppSideMenuController() {
    return this.appSideMenuController!;
  }

  // 复写autoAuthByToken
  public autoAuthByToken() {
    // 微应用走这种模式
    if (IS_MICRO_ENV) {
      microAutoAuthByToken(this.login.bind(this));
    } else {
      super.autoAuthByToken();
    }
  }

  /** 产品跳转(脚本注释) */

  public jumpToProductDataFactory(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    IS_MICRO_ENV ? microJumpToDataFactory(param) : this.jumpToOtherProduct(JUMP_DATA_FACTORY_URL, param, options);
  }

  public jumpToProductDataMarket(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_DATA_MARKET_URL, param, options);
  }

  public jumpToProductWorkflow(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_WORKFLOW_URL, param, options);
  }

  public jumpToProductMyData(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_MY_DATA_URL, param, options);
  }

  public jumpToOrganizationManagement(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    IS_MICRO_ENV
      ? microJumpToOrganizationManagement(param)
      : this.jumpToOtherProduct(JUMP_ORGANIZATION_MANAGEMENT_URL, param, options);
  }

  public jumpToProductResourceShare(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_RESOURCE_SHARE_URL, param, options);
  }

  public jumpToProductFormDesign(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_FORM_DESIGN_URL, param, options);
  }

  public jumpToProductDataMap(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_DATA_MAP_URL, param, options);
  }

  public jumpToDatlasAdmin(param?: Record<string, any>) {
    this.jumpToDatlasAdminFunc(JUMP_DATLAS_ADMIN_URL, param);
  }

  public jumpToProductCollector(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_COLLECTOR_URL, param, options);
  }

  public jumpToProductMapEditor(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.jumpToOtherProduct(JUMP_MAP_EDITOR_URL, param, { ...options, windowName: '_blank' });
  }

  public clickHeaderLogo() {
    this.getRouterController()!.gotoHome();
  }

  public saveTokenToStorage(value: object) {
    saveToStorage(`${PRODUCT_NAME}_token`, value);
  }

  public getTokenFromStorage(): IStoreToken {
    const val = getFromStorage(`${PRODUCT_NAME}_token`);
    return parseStrToObj<IStoreToken>(val);
  }

  public removeTokenFromStorage() {
    removeFromStorage(`${PRODUCT_NAME}_token`);
  }

  // 获取自定义布局
  public getCustomAppLayout(): FC<any> | undefined {
    return undefined;
  }

  public async handlePreferences() {
    // 注册水印
    this.registerWatermark();
    // 注册密码修改提醒
    this.datlasRegisterPsdChangeTip();
    // 注册用户过期提醒
    this.datlasRegisterUserExpireTip();
    // 修改网页信息
    this.updateHtmlInfo();
    // 追加用户操作监听
    this.listenUserInactivity();
    // 处理serviceWorker
    this.handleServiceWorker();
  }

  protected initAppHeader(options: IHeaderOptions) {
    const upc = this.getUserPermissionController();
    const { enableAdmin, ...restPermission } = upc.getHeaderMenusPermission();
    const { dynamicOpt = {}, ...resetOpt } = options;
    return new AppHeaderController(
      {
        logoUrl: HEADER_LOGO_URL,
        ssoUrl: SSO_LOGOUT_URL,
        verifyUrl: VERIFY_URL,
        helpUrl: HELP_CENTER_URL,
        userName: this.getUserName(),
        dynamicOpt: {
          ...upc.getUserModulePermission(ALLOW_JUMP_PRODUCTS),
          enableMenuHome: true,
          ...(dynamicOpt || {}),
        },
        productOpt: upc.getUserProductPermission(ALLOW_JUMP_PRODUCTS),
        menusOpt: { ...restPermission, enableAdmin: enableAdmin && !!JUMP_DATLAS_ADMIN_URL },
        theme: this.getTheme(),
        language: this.getLanguage(),
        disabledLanguage: !this.i18n,
        jumpActionType: JUMP_ACTION_TYPE,
        applyDatapkgUrl: this.routerController!.getApplyDatapkgUrl(),
        settingCallbackFunc: (obj) => {
          const lang = obj[ChangeTypeEnum.LANGUAGE];
          if (lang && this.i18n) {
            this.saveLanguageToStorage(lang);
            const encodedQ = compressToEncodedURIComponent(
              JSON.stringify({
                ...getQFromUrl(),
                language: lang,
              }),
            );
            window.location.href = modifyParamsOfUrl(window.location.href, 'q', encodedQ);
          }
        },
        ...resetOpt,
      },
      this,
      AppHeaderModel,
    );
  }

  protected afterAuthFaith(logout?: boolean) {
    // 删除token
    !IS_DEVELOP && this.removeTokenFromStorage();
    SSO_LOGOUT_URL ? this.redirectToSso(SSO_LOGOUT_URL, logout) : this.showLoginPage();
  }

  protected async afterAuthSuccess(disableToken?: boolean): Promise<any> {
    updateOneTableInfo(this.getAppName());
    // 存储token到store
    !disableToken && this.saveTokenToStorage(this.getStoreToken());
    const requirementData = IS_MICRO_ENV ? microGetRequirementData() : [];
    if (this.ignoreGlobalData) {
      return [];
    }
    // 获取登录用户的必要信息,比如权限，dataset等
    return await super.initRequirementData(requirementData);
  }

  // 初始化token
  protected initLatestToken() {
    let st = getTokenFromUrl();
    // 如果url不存在token, 则尝试从store中获取
    !st.tk && (st = this.getTokenFromStorage());
    return st || {};
  }

  // 获取request配置
  protected getRequestOptions() {
    return {
      baseUrl: API_URL,
      proxyUrl: PROXY_URL,
      encodeApis: ENCODE_APIS,
      encodeApisOptions: ENCODE_APIS_OPTIONS,
      transformApiMethod: TRANSFORM_API_METHOD,
    };
  }

  // 获取socket的配置
  protected getSocketOptions(auth: string) {
    return {
      socketUrl: SOCKET_URL,
      socketOptions: SOCKET_OPTIONS,
      authorization: auth,
    };
  }

  protected initLanguageConfig(prefLanguage: LanguageEnum) {
    // url接收的参数
    const urlLanguage = getLanguageFromUrl();
    // store的存储的参数
    const storeLanguage = this.getLanguageFromStorage();
    // 默认值
    const defaultLanguages = [LanguageEnum.CN, LanguageEnum.EN]; // 支持的语言
    let language = '';
    // url > 偏好 > 缓存 > 产品配置 > 默认
    if (_.includes(defaultLanguages, urlLanguage)) {
      language = urlLanguage!;
    } else if (_.includes(defaultLanguages, prefLanguage)) {
      language = prefLanguage;
    } else if (_.includes(defaultLanguages, storeLanguage)) {
      language = storeLanguage;
    } else if (_.includes(defaultLanguages, LANGUAGE)) {
      language = LANGUAGE;
    } else {
      language = LanguageEnum.CN;
    }
    if (this.i18n?.getLocaleName() === language) {
      // 手动重置i18n的状态，因为如果language没有变locale方法实际没有作用。
      // @ts-ignore
      this.i18n?.publish('', {});
    }
    // 加载语言资源
    this.i18n?.locale(language);
    language === LanguageEnum.CN && dayjs.locale('zh-cn');
    // 存储语言
    this.saveLanguageToStorage(language);

    return language;
  }

  // defaultOwner 用于整个页面需要bff代理设置一个默认代理人（eg: 流程填报页面）
  protected redirectToBffPublicFlowork(config: IRequestRequestConfig) {
    const url = config.url;
    !_.startsWith(url, 'http') && (config.url = `${API_URL}${url}`);
    return {
      ...config,
      url: BFF_PUBLIC_FLOWORK_URL,
      method: 'post',
      quiet: true,
      data: _.pick(config, bffProxyConfigData),
    };
  }

  protected redirectToBffInnerGraphql(config: IRequestRequestConfig) {
    return {
      ...config,
      url: BFF_INNER_GRAPHQL_URL,
      method: 'post',
      quiet: true,
      data: _.pick(config, bffProxyConfigData),
    };
  }

  private saveLanguageToStorage(value: string) {
    saveToStorage(`${PRODUCT_NAME}_language`, value);
  }

  private getLanguageFromStorage(): string {
    return getFromStorage(`${PRODUCT_NAME}_language`);
  }

  private showLoginPage() {
    this.changeVisible(true);
    // TODO 如果不走sso, 需要本项目有登录界面, 可后期追加
  }

  private transformRequestConfig = (config: IRequestRequestConfig) => {
    if (config.public) {
      config.baseURL = `${config.baseURL || ''}/public`;
    }
    return config;
  };

  private isMicroOrInsideIframe = () => {
    return !!IS_MICRO_ENV || isInsideIframe();
  };

  private registerWatermark() {
    this.initWatermark();
    this.isMicroOrInsideIframe() && this.getWatermarkWrapController()!.hide();
  }

  private datlasRegisterPsdChangeTip() {
    !this.isMicroOrInsideIframe() && this.registerPasswordChangeTip();
  }

  private datlasRegisterUserExpireTip() {
    !this.isMicroOrInsideIframe() && this.registerUserExipreTip(USER_EXPIRE_TIP);
  }

  private initPublicTransform() {
    const { request } = this.getAxiosInstance().interceptors;
    const req1 = request.use(this.transformRequestConfig);
    this.removePublicTransform = () => {
      request.eject(req1);
    };
  }

  private initBffProxy() {
    const requestCls = this.getRequest()!;
    const ins = requestCls.getIns().interceptors;
    const reqIns = ins.request.use((config: IRequestRequestConfig) => {
      config.enableBffProxy = config.enableBffProxy ?? false;
      return config.enableBffProxy ? this.redirectToBffPublicFlowork(config) : config;
    });
    const respIns = ins.response.use((resp) => {
      const cf = (resp.config || {}) as IRequestRequestConfig;
      if (cf.enableBffProxy) return { ...resp, data: resp.data?.page_data };
      return resp;
    });

    const bffIns = ApolloService.getAxiosIns().interceptors;
    // 添加加密支持
    const bffReqEncode = bffIns.request.use(requestCls.encodeGraphqlRequest);
    const bffReqMethod = bffIns.request.use(requestCls.transformRequestMethod);
    const bffReqIns = bffIns.request.use((config: IBffRequestConfig) => {
      config.enableBffProxy = config.enableBffProxy ?? false;
      return config.enableBffProxy ? this.redirectToBffInnerGraphql(config) : config;
    });
    const bffRespIns = bffIns.response.use((resp) => {
      const cf = (resp.config || {}) as IBffRequestConfig;
      if (cf.enableBffProxy) return { ...resp, data: { data: _.get(resp, 'data.page_data.data') } };
      return resp;
    });

    this.removeBffProxy = () => {
      ins.request.eject(reqIns);
      ins.response.eject(respIns);
      bffIns.request.eject(bffReqEncode);
      bffIns.request.eject(bffReqMethod);
      bffIns.request.eject(bffReqIns);
      bffIns.response.eject(bffRespIns);
    };
  }
}

export { DatlasAppController };
