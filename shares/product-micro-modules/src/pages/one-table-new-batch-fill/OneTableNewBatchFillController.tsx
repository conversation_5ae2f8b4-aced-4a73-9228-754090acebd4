import _ from 'lodash';
import React from 'react';
import { AlertOutlined, DoneCheckFilled, HelpOutlined } from '@metro/icons';
import { Flex } from '@metroDesign/flex';
import { Modal } from '@metroDesign/modal';
import { Table } from '@metroDesign/table';
import { toastApi } from '@metroDesign/toast';
import { ToastContainer } from '@metroDesign/toast';
import { Typography } from '@metroDesign/typography';
import { BehaviorSubject, of } from 'rxjs';
import type { IDatapkgRows } from '@mdtApis/interfaces';
import { getCurrentDateStr, normalizeDate } from '@mdtBsComm/utils/dayUtil';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import {
  DataListCompTableCurdController,
  DataListCompTableWithCurd,
  IVirtualizedTableProps,
} from '@mdtBsComponents/data-list-comp-table-curd';
import { ExcelResolver } from '@mdtProComm/components/excel-template';
import { DbColumnTypeEnum, QUESTION_COMP_KEY, QUESTION_COMP_PROPS_KEY, QuestionCompEnum } from '@mdtProComm/constants';
import { fixStringFormatValue, getDatetimeDefaultFormat } from '@mdtProComm/utils/formilyUtil';
import { isArrayStringComps } from '@mdtProComm/utils/questionUtil';
import { downloadExcelTemplateAsync, getDefaultTemplateDescription } from '../../components/form-view/util';
import type { IColumnItem } from '../../containers/table-pkg-column';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import type { IOneTableNewOperatorDataComm } from '../../interfaces';
import i18n from '../../languages';
import {
  checkUploadExcelColumns,
  COLUMN_ACTION,
  COLUMN_ASSIGN_USER,
  COLUMN_CREATE_TIME,
  COLUMN_CREATE_USER_ID,
  COLUMN_STATUS,
  COLUMN_UPDATE_TIME,
  COLUMN_UPDATE_USER_ID,
  DataStatusEnum,
  getProcessRowsOptions,
  megerFormSpecProperAndSetting,
} from '../../utils/oneTableNewUtil';
import { DataSource } from '../form-editor/service';
import { ModifyDataSourceSetting } from './ModifyDataSourceSetting';
import type { IOneTableNewBatchFillModel, IRowCheckError } from './OneTableNewBatchFillModel';

export interface IControllerOptions {
  Model: IOneTableNewBatchFillModel;
  itemData: IOneTableNewOperatorDataComm;
  tableColumns: IColumnItem[];
  onBatchSuccess?: () => void;
  onBatchFailed?: () => void;
}

export class OneTableNewBatchFillController {
  private uping$ = new BehaviorSubject<boolean>(false);
  private Model: IOneTableNewBatchFillModel;
  private itemData: IOneTableNewOperatorDataComm;
  private tableColumns?: IColumnItem[];
  private onBatchSuccess?: () => void;
  private onBatchFailed?: () => void;
  private dataSource: DataSource;

  public constructor({ Model, onBatchSuccess, onBatchFailed, ...resetOpt }: IControllerOptions) {
    this.Model = Model;
    this.itemData = resetOpt.itemData;
    this.onBatchSuccess = onBatchSuccess;
    this.onBatchFailed = onBatchFailed;
    this.dataSource = new DataSource(undefined, this.itemData.formOwner);
    this.init(resetOpt);
  }

  public destroy() {
    this.uping$.complete();
    this.itemData = null!;
    this.tableColumns = undefined;
    this.onBatchSuccess = undefined;
    this.onBatchFailed = undefined;
    this.dataSource?.destroy();
    this.dataSource = null!;
  }

  public getUping$() {
    return this.uping$;
  }

  public downloadTemp = async () => {
    const { formSpec, formName } = this.itemData;
    const description = getDefaultTemplateDescription(i18n.chain.proMicroModules.oneTable.excelDescIndex('ID'));
    await downloadExcelTemplateAsync(formSpec.formilySchema, formName, { description });
  };

  public uploadExcel = (files: File[]) => {
    const resolver = new ExcelResolver({
      parseMode: 'array',
      plainText: true,
      descriptionKey: i18n.chain.upload.excelDescTitle,
    });
    this.uping$.next(true);
    resolver.handleExcel(files).subscribe((data) => {
      const [excelColumns, ...resetValues] = data as [string[], any[]];
      const transformColumns = this.checkExcelColumns(excelColumns, resetValues);
      this.dealExcelData(resetValues, transformColumns, resolver.getStartRow()).then(() => {
        this.uping$.next(false);
      });
    });
  };

  // 获取信息
  private async init(options: Omit<IControllerOptions, 'Model'>) {
    this.tableColumns = _.reject(options.tableColumns, (it) => it.name === COLUMN_ACTION);
  }

  // 尝试修正用户上传数据与数据库类型匹配
  // eslint-disable-next-line sonarjs/cognitive-complexity,complexity
  private tryToFixExcelRowBySetting(rowData: any, columnType: string, columnSetting?: Record<string, any>) {
    if (_.isNil(rowData) || rowData === '') return rowData;
    const isStr = _.isString(rowData);
    const { [QUESTION_COMP_KEY]: compName, [QUESTION_COMP_PROPS_KEY]: compProps = {} } = columnSetting || {};
    // ========数组情况需要修正
    // 选人整数数组
    if (
      ((compName === QuestionCompEnum.USER_SELECT_INPUT && !_.includes(rowData, 'userId')) ||
        columnType === DbColumnTypeEnum.ARRAY_INT ||
        columnType === DbColumnTypeEnum.ARRAY_FLOAT) &&
      isStr
    ) {
      const fval = _.replace(rowData, /[\[\'\"\]]/gi, '').replace(/null/gi, '');
      return _.split(fval, /\s*[,，]\s*/)
        .map((it) => {
          const v = _.toNumber(it);
          return _.isEqual(`${v}`, it) ? v : it;
        })
        .filter(Boolean);
    }
    // 级联字符串数组
    if (compName === QuestionCompEnum.CASCADER && isStr) {
      const fval = _.replace(rowData, /[\[\'\"\]]/gi, '').replace(/null/gi, '');
      return _.split(fval, /\s*[,，/]\s*/).filter(Boolean); // 排除空字符串
    }
    // 其他多选数组
    if ((columnType === DbColumnTypeEnum.ARRAY_STR || isArrayStringComps(compName, compProps)) && isStr) {
      const fval = _.replace(rowData, /[\[\'\"\]]/gi, '').replace(/null/gi, '');
      return _.split(fval, /\s*[,，]\s*/).filter(Boolean); // 排除空字符串
    }

    // 存储是对象的形式，需要进行修正
    if (
      // 选择人输出带orgId
      (compName === QuestionCompEnum.USER_SELECT_INPUT && _.includes(rowData, 'userId')) ||
      // 附件
      (compName === QuestionCompEnum.UPLOAD && _.includes(rowData, 'value')) ||
      // 自增卡片
      compName === QuestionCompEnum.ARRAY_CARDS ||
      // 自增表格
      compName === QuestionCompEnum.ARRAY_TABLE ||
      columnType === DbColumnTypeEnum.JSON ||
      columnType === DbColumnTypeEnum.MEDIA_JSON
    ) {
      const fval = _.replace(rowData, /\'/gi, '"');
      const val = parseStrToObj(fval, null);
      return val || rowData;
    }

    // ========日期情况, 按需修正
    if (
      compName === QuestionCompEnum.DATEPICKER ||
      compName === QuestionCompEnum.TIMEPICKER ||
      columnType === DbColumnTypeEnum.DATETIME ||
      columnType === DbColumnTypeEnum.TIMESTAMP ||
      columnType === DbColumnTypeEnum.DATE
    ) {
      const format = compProps.format || getDatetimeDefaultFormat(compProps);
      let date = normalizeDate(rowData, format);
      if (!date) {
        const [fixVal, fixFormat] = fixStringFormatValue(rowData, format);
        date = normalizeDate(fixVal, fixFormat);
        if (!date) return rowData;
      }
      // 尝试将非规范日期数据转为datetime数据, 便于校验及存储
      if (compProps.valueType === DbColumnTypeEnum.DATETIME || columnType === DbColumnTypeEnum.DATETIME) {
        return date.format();
      }
      // 存储值是时间戳
      if (compProps.valueType === DbColumnTypeEnum.TIMESTAMP || columnType === DbColumnTypeEnum.TIMESTAMP) {
        return date.unix();
      }
      // 兼容
      if (columnType === DbColumnTypeEnum.DATE) {
        return date.format('YYYY-MM-DD');
      }
      // 存储值是string或者列类型是string
      if (compProps.valueType === 'string' || columnType === DbColumnTypeEnum.STR) {
        return date.format(format); // 尝试转换为标准格式
      }
    }

    // ========布尔情况，按需修正
    if (columnType === DbColumnTypeEnum.BOOL) {
      const fval = _.toLower(rowData);
      if (rowData === 0 || rowData === false || fval === '0' || fval === 'false' || fval === '否' || fval === 'no') {
        return false;
      }

      if (rowData === 1 || rowData === true || fval === '1' || fval === 'true' || fval === '是' || fval === 'yes') {
        return true;
      }
    }

    // ========数字情况, 按需修正
    if (
      columnType === DbColumnTypeEnum.INT ||
      columnType === DbColumnTypeEnum.FLOAT ||
      columnType === DbColumnTypeEnum.BIGINT
    ) {
      // 尝试将string数据转换为number
      const fval = _.toNumber(rowData);
      return _.isEqual(`${fval}`, rowData) ? fval : rowData;
    }

    // 如果要求是字符串，则自动转换下
    if (columnType === DbColumnTypeEnum.STR && !isStr) {
      return _.toString(rowData);
    }

    // 其他情况不处理
    return rowData;
  }

  private renderHowModify = (value: any, record: IRowCheckError) => {
    const { dataSourceSetting } = record;
    if (dataSourceSetting) {
      const nodeDataSource = this.dataSource.addNodeDataSource(dataSourceSetting.name, dataSourceSetting);
      return (
        <ModifyDataSourceSetting
          datasource={nodeDataSource!}
          setting={dataSourceSetting}
          notAllowed={record.notAllowed}
        />
      );
    }
    return value;
  };

  private openErrorWin(errors: IRowCheckError[]) {
    const pageSize = 10;
    let controller = new DataListCompTableCurdController({
      dataListControllerOptions: {
        loadDataListFunc: () => {
          return of([errors.length, _.slice(errors, 0, pageSize)] as [number, IRowCheckError[]]);
        },
        loadNextPageDataListFunc: (params) => {
          const start = params.page_num * pageSize;
          return of(_.slice(errors, start, start + pageSize));
        },
      },
      paginationProps: { defaultPageSize: pageSize },
      pagination: true,
      paginationAtBefore: false,
      tableOptions: (): IVirtualizedTableProps => {
        return {
          columns: [
            { code: 'row', width: 120, name: i18n.chain.proMicroModules.oneTable.tableColumns.rowIndex },
            { code: 'question', width: 200, name: i18n.chain.proMicroModules.oneTable.tableColumns.question },
            { code: 'errorTip', width: 300, name: i18n.chain.proMicroModules.oneTable.tableColumns.errorTip },
            {
              code: 'howModify',
              width: 300,
              name: i18n.chain.proMicroModules.oneTable.tableColumns.howModify,
              render: this.renderHowModify,
            },
          ],
          type: 'page-bg',
          primaryKey: 'id',
          withVerticalBorder: false,
        };
      },
    });
    controller.loadDataList();
    Modal.open({
      wrapClassName: 'one-table-new-check-error-modal',
      title: i18n.chain.proMicroModules.oneTable.uploadExcelValid.errorWinTitle,
      width: '90vw',
      closable: true,
      footer: null,
      centered: true,
      maskClosable: false,
      destroyOnClose: true,
      onCancel: () => {
        errors.length = 0;
        controller.destroy();
        controller = null!;
      },
      children: () => {
        return (
          <>
            <ToastContainer
              type="error"
              message={i18n.chain.proMicroModules.oneTable.uploadExcelValid.errorWinBodyTip}
            />
            <DataListCompTableWithCurd controller={controller} />
          </>
        );
      },
    });
  }

  private showErrorLimitModal(limitErrorRows: any[], columns?: IColumnItem[], startRow?: number): Promise<boolean> {
    const tableColumns = _.map(columns, (it) => ({
      title: (
        <Flex align="center" gap={4}>
          <DoneCheckFilled style={{ color: 'var(--metro-success-default)' }} />
          {it.title}
        </Flex>
      ),
      dataIndex: it.name,
      key: it.name,
    }));

    let maxErrorCols = 0;
    const errorDatasource = _.map(limitErrorRows, (row, rowIndex) => {
      const result: any = {};
      if (_.isArray(row)) {
        maxErrorCols = Math.max(maxErrorCols, row.length);

        _.forEach(row, (cellValue, cellIndex) => {
          if (columns && cellIndex < columns.length) {
            const columnName = columns[cellIndex].name;
            result[columnName] = cellValue;
          } else {
            const unknownIndex = cellIndex - (columns?.length || 0) + 1;
            result[`unknown_${unknownIndex}`] = cellValue;
          }
        });
      }

      result.rowIndex = (startRow || 0) + rowIndex;
      return result;
    });

    if (columns && maxErrorCols > columns.length) {
      const unknownColsCount = maxErrorCols - columns.length;

      _.times(unknownColsCount, (i) => {
        const unknownIndex = i + 1;
        tableColumns.push({
          title: (
            <Flex align="center" gap={4}>
              <AlertOutlined style={{ color: 'var(--metro-warning-default)' }} />
              {i18n.chain.proMicroModules.oneTable.uploadExcelValid.unkonwnCol}
            </Flex>
          ),
          dataIndex: `unknown_${unknownIndex}`,
          key: `unknown_${unknownIndex}`,
        });
      });
    }

    return new Promise((resolve) => {
      Modal.confirm({
        icon: <HelpOutlined />,
        width: '50vw',
        title: i18n.chain.proMicroModules.oneTable.uploadExcelValid.errorLimitTip,
        content: (
          <Flex vertical gap="small">
            <Typography.Text type="secondary">
              {i18n.chain.proMicroModules.oneTable.uploadExcelValid.errorLimitDescription}
            </Typography.Text>
            <Table
              columns={tableColumns}
              dataSource={errorDatasource}
              scroll={{ x: 'max-content' }}
              pagination={{ pageSize: 10 }}
            />
          </Flex>
        ),
        onOk: () => {
          resolve(true);
        },
        onCancel: () => {
          resolve(false);
        },
        okText: i18n.chain.comButton.continue,
      });
    });
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  private async dealExcelData(rows: any[], columns?: IColumnItem[], startRow?: number) {
    const cLen = _.size(columns);
    if (!cLen) return;
    if (rows.length > 1000) {
      toastApi.error(i18n.chain.proMicroModules.oneTable.uploadExcelMaxRowError);
      return;
    }

    const limitErrorRows = _.filter(rows, (row) => row.length > cLen);

    if (limitErrorRows.length > 0) {
      const shouldContinue = await this.showErrorLimitModal(limitErrorRows, columns, startRow);
      if (!shouldContinue) return;
    }

    const { formSpec, pkgId, isFormManageLevelUser } = this.itemData;
    const { allSettingValues } = megerFormSpecProperAndSetting(formSpec);
    const fcs = _.map(columns, (it) => it.name);
    const addRowValues: any[] = [];
    const updateRowValues: any[] = [];
    const updateIds: number[] = [];
    const toCheckRows: Record<string, any>[] = [];

    _.forEach(rows, (row: any[]) => {
      const processedRow = row.length > cLen ? row.slice(0, cLen) : row;
      const id: number | null = processedRow[0];
      const checkRow: Record<string, any> = {};

      for (let i = 1; i < cLen; i++) {
        const col = columns![i];
        const rval = this.tryToFixExcelRowBySetting(processedRow[i], col.type, allSettingValues[col.name]);
        processedRow[i] = rval;
        checkRow[col.name] = rval;
      }

      if (id) {
        // 因为新增和编辑校验会有些差异,所以如果是更新校验，需要有个内部字段标识下
        checkRow.__mdt_row_check_type = 'update';
        updateIds.push(id);
        updateRowValues.push(processedRow);
      } else {
        addRowValues.push(processedRow.slice(1));
      }
      // 追加到待检查队列
      toCheckRows.push(checkRow);
    });

    if (toCheckRows.length > 0) {
      const resp = await this.Model.checkUploadRows(
        { rows: toCheckRows, schema: formSpec, pkgId, schemaOwner: this.itemData.formOwner },
        allSettingValues,
        startRow,
      );
      if (resp.hasError) {
        toastApi.error(i18n.chain.proMicroModules.oneTable.uploadExcelDataError);
        resp.errors && this.openErrorWin(resp.errors);
        return;
      }
    }

    const currentUser = DatlasAppController.getInstance().getUserId();
    const currentTime = getCurrentDateStr();
    const processOptions = getProcessRowsOptions(isFormManageLevelUser);
    const addRows: IDatapkgRows[] = [];
    // 新增数据需要填充status, 更新则不需要
    if (addRowValues.length) {
      const addColumns = [
        ...fcs.slice(1),
        COLUMN_ACTION,
        COLUMN_ASSIGN_USER,
        COLUMN_CREATE_USER_ID,
        COLUMN_UPDATE_USER_ID,
        COLUMN_CREATE_TIME,
        COLUMN_UPDATE_TIME,
        COLUMN_STATUS,
      ];
      const addValues = _.map(addRowValues, (row) => {
        row.push(
          DataStatusEnum.Insert,
          currentUser,
          currentUser,
          currentUser,
          currentTime,
          currentTime,
          processOptions.addRowStatusVal,
        );
        return row;
      });
      addRows.push({ columns: addColumns, values: addValues });
    }
    const updateRows: IDatapkgRows[] = [];
    if (updateRowValues.length > 0) {
      const updateColumns = [...fcs, COLUMN_UPDATE_USER_ID, COLUMN_UPDATE_TIME];
      const updateValues = _.map(updateRowValues, (row) => {
        row.push(currentUser, currentTime);
        return row;
      });
      updateRows.push({ columns: updateColumns, values: updateValues });
    }
    const resp = await this.Model.addOrUpdateDatapkgRow(pkgId, addRows, updateRows, _.uniq(updateIds));
    if (resp.addSuccess || resp.updateSuccess) {
      toastApi.success(i18n.chain.comTip.optSuccess);
      this.onBatchSuccess?.();
    } else {
      this.onBatchFailed?.();
    }
  }

  private checkExcelColumns(excelColumns: string[], rows: any[]) {
    const columns = this.tableColumns || [];
    return checkUploadExcelColumns(excelColumns, columns, rows);
  }
}
