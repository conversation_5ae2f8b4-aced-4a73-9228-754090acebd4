import { from } from 'rxjs';
import { map, switchMap, takeWhile } from 'rxjs/operators';
import type {
  IDatapkgColumnBatchPatch,
  IDatapkgColumnPost,
  IDatasetDatapkgFromEmptyPost,
  IFloworkForm,
  IFloworkFormPatch,
  IFloworkFormPost,
  IRequestRequestConfig,
} from '@mdtApis/interfaces';
import {
  batchPostDatapkgColumnsAsync,
  deleteDatapkgAsync,
  patchDatapkgAsync,
  postDatapkgCollaborateAsync,
} from '@mdtBsServices/datapkgs';
import { postDatapkgToDatasetFromEmptyAsync } from '@mdtBsServices/datasets';
import {
  deleteWorkflowAsync,
  getFloworkFormAsync,
  patchFloworkFormAsync,
  postFloworkFormAsync,
  postWorkflowSpecFlowAsync,
} from '@mdtBsServices/flowork';
import { DatapkgModel } from '@mdtProComm/models/DatapkgModel';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import {
  COLUMN_ASSIGN_USER,
  getColumnsFromFormSchame,
  getColumnsMetaFromFormSetting,
  getNewRootWfspecId,
  ONE_TABLE_NEW_COLUMN_EXPAND,
} from '../../utils/oneTableNewUtil';
import { IManagementData, OneTableNewReportDetailModel } from '../one-table-new-report-detail';

interface IStartFlowOption {
  is_instant_start?: boolean;
  current_start_time?: number;
  cycle_timer?: string;
}

export class OneTableNewReportCreateModel {
  public static async queryDatapkgDetail(pkgId: string) {
    const resp = await DatapkgModel.queryDatapkgDetail(pkgId).toPromise();
    return resp.data;
  }

  public static async queryDatapkgColumns(pkgId: string) {
    const resp = await DatapkgModel.queryDatapkgColumns(pkgId).toPromise();
    return resp.data || [];
  }

  public static async updateDatapkgColumns(pkgId: string, columns: IDatapkgColumnBatchPatch[]) {
    return DatapkgModel.updateDatapkgColumns({ pkgId, updateColumns: columns });
  }

  public static getForm(formId: string, config?: IRequestRequestConfig) {
    return from(getFloworkFormAsync(formId, config)).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => this.transformToFormConfig(resp.data)),
    );
  }

  public static createNewForm(data: IFloworkFormPost, config?: IRequestRequestConfig) {
    return from(postFloworkFormAsync(data, config)).pipe(takeWhile((v) => !v.canceled));
  }

  public static updateForm(formId: string, data: IFloworkFormPatch, config?: IRequestRequestConfig) {
    return from(patchFloworkFormAsync(formId, data, config)).pipe(takeWhile((v) => !v.canceled));
  }

  public static addFields(
    pkgId: string,
    data: { columns: IDatapkgColumnPost[]; availableColumns: string[] },
    config?: IRequestRequestConfig,
  ) {
    // 使用批量接口一次性处理所有columns
    return from(batchPostDatapkgColumnsAsync(pkgId, data.columns, config)).pipe(
      switchMap((response) => {
        if (!response.success) {
          return from(Promise.resolve({ success: false, data: null }));
        }

        if (data.availableColumns && data.availableColumns.length > 0) {
          return from(patchDatapkgAsync(pkgId, { available_columns: data.availableColumns }, config));
        }
        return from(Promise.resolve({ success: true, data: null }));
      }),
    );
  }

  public static deleteFields(pkgId: string, data: { availableColumns: string[] }, config?: IRequestRequestConfig) {
    return from(patchDatapkgAsync(pkgId, { available_columns: data.availableColumns }, config)).pipe(
      takeWhile((v) => !v.canceled),
    );
  }

  public static async startWorkflow(
    formId: string,
    formData?: IFloworkForm,
    options?: IStartFlowOption,
    needCurrentFormData?: boolean,
  ) {
    const specId = getNewRootWfspecId();
    let data = formData || (await getFloworkFormAsync(formId)).data;
    if (!data) return { success: false };
    // 创建数据包
    const pkgId = await this.createDatepkg(data.name, data.form_spec);
    if (!pkgId) return { success: false };
    // 更新form
    const formResp = await patchFloworkFormAsync(data.id, {
      extra_meta: { ...data.extra_meta, bind_datapkg_id: pkgId },
    });
    if (!formResp.success) {
      await deleteDatapkgAsync(pkgId);
      return { success: false };
    }
    // 发起流程
    const flowResp = await postWorkflowSpecFlowAsync(specId, {
      form_data: {
        ...options,
        form_name: data.name,
        bind_form_id: data.id,
      },
    });
    if (!flowResp.success) {
      await Promise.allSettled([
        deleteDatapkgAsync(pkgId),
        // 设为null值则为删除
        patchFloworkFormAsync(data.id, { extra_meta: { ...data.extra_meta, bind_datapkg_id: null } }),
      ]);
      return { success: false };
    }
    const collaborateResp = await postDatapkgCollaborateAsync(pkgId, {
      row_permission_strategy: {
        strategy: 'onetable',
        config: {
          root_dispatch_workflow_id: flowResp.data!.workflow_id,
          user_column: [COLUMN_ASSIGN_USER],
        },
      },
    });
    const wfId = flowResp.data!.workflow_id;
    if (!collaborateResp.success) {
      await Promise.allSettled([
        deleteWorkflowAsync(wfId, { params: { delete_linked_workflows: 'descendants' } }),
        deleteDatapkgAsync(pkgId),
        patchFloworkFormAsync(data.id, { extra_meta: { ...data.extra_meta, bind_datapkg_id: null } }),
      ]);
      return { success: false };
    }

    let managementData: IManagementData | undefined;
    if (needCurrentFormData) {
      // 获取源数据，给到填报和下发
      managementData = await OneTableNewReportDetailModel.getManagementForm(wfId);
    }
    return { success: true, data: managementData };
  }

  private static async createDatepkg(pkgName: string, formSpec: Record<string, any>) {
    const app = DatlasAppController.getInstance();
    let columns = getColumnsFromFormSchame({ schema: formSpec.formilySchema.schema!, allSettingValues: {} });
    // 默认新增填写表单的用户列
    columns = { ...columns, ...ONE_TABLE_NEW_COLUMN_EXPAND };
    const createData: IDatasetDatapkgFromEmptyPost = {
      name: `${pkgName}_${new Date().getTime()}`,
      geometry_type: 'plain',
      ownership: 'app',
      product_type: 'onetable',
      columns,
    };
    createData.columns_meta = getColumnsMetaFromFormSetting(formSpec);
    const datasetId = app.getDatasetsController().getAppDatasetId();
    const resp = await postDatapkgToDatasetFromEmptyAsync(datasetId, createData);
    return resp.data?.datapkg_id;
  }

  private static transformToFormConfig(data?: IFloworkForm) {
    return data && { name: data.name, formSpec: data.form_spec, extraMeta: data.extra_meta || {} };
  }
}

export type IOneTableNewReportCreateModel = typeof OneTableNewReportCreateModel;
