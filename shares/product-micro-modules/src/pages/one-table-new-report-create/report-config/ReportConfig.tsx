import { FC } from 'react';
import { FormView } from '../../../components/form-view';
import i18n from '../../../languages';
import { ReportConfigController } from './ReportConfigController';
import './index.less';

export interface IProps {
  controller: ReportConfigController;
  className?: string;
}

export const ReportConfig: FC<IProps> = ({ controller, className }) => {
  const spec = controller.getConfigSettingSpec();

  return (
    <div className={`new-report-config ${className || ''}`}>
      <div className="config-inner one-table-report-layout">
        <div className="one-table-form-view-title">{i18n.chain.proMicroModules.oneTable.publishSetting}</div>
        <FormView
          className="one-table-form-view"
          ref={controller.getFormRef() as any}
          formilySchema={spec.formilySchema}
          allSettingValues={spec.allSettingValues}
          formData={controller.getConfigData()}
          onChange={(value) => controller.onFormChangeFunc(value)}
        />
      </div>
    </div>
  );
};
