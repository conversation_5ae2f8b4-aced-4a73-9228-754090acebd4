import { createRef } from 'react';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFormSpecRefHandle } from '../../../components/form-spec';
import { FormTypeEnum, getFormTypeSpec, getSourcePkgId } from '../../../utils/oneTableNewUtil';
import type { IFormEditorSchema } from '../../form-editor';

export type IReportConfigData = Record<string, any>;

export interface IReportConfigControllerOptions {
  formType: FormTypeEnum;
  configData?: IReportConfigData;
  sourcePkgId?: string;
  onFormChange?: (values: any, formRef: IFormSpecRefHandle) => void;
}

export class ReportConfigController extends RequestController {
  private onFormChange?: (values: any, formRef: IFormSpecRefHandle) => void;
  private formRef = createRef<IFormSpecRefHandle>();
  private configSettingSpec: IFormEditorSchema;
  private configData?: IReportConfigData;

  public constructor({ configData, formType, onFormChange, sourcePkgId }: IReportConfigControllerOptions) {
    super();
    this.configData = configData;
    const spec = getFormTypeSpec(formType, getSourcePkgId(configData) || sourcePkgId) as IFormEditorSchema;
    this.configSettingSpec = spec;
    this.onFormChange = onFormChange;
  }

  public destroy() {
    super.destroy();
    this.formRef = null!;
    this.configData = undefined;
  }

  public onFormChangeFunc(value: any) {
    this.onFormChange?.(value, this.formRef.current!);
  }

  public getConfigSettingSpec() {
    return this.configSettingSpec;
  }

  public async getValue(): Promise<undefined | IReportConfigData> {
    return this.formRef.current?.getValues();
  }

  public getFormRef() {
    return this.formRef;
  }

  public getConfigData() {
    return this.configData;
  }

  public getOnFormChange() {
    return this.onFormChange;
  }
}
