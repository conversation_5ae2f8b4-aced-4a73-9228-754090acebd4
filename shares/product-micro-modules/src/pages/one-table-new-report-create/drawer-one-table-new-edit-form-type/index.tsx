import _ from 'lodash';
import { FC, useCallback, useState } from 'react';
import { Button } from '@metroDesign/button';
import { drawerApi, DrawerProps } from '@metroDesign/drawer';
import { Flex } from '@metroDesign/flex';
import { Menu } from '@metroDesign/menu';
import { Steps } from '@metroDesign/steps';
import { toastApi } from '@metroDesign/toast';
import { isPc } from '@mdtBsComm/utils';
import i18n from '../../../languages';
import { FORM_TYPE_ITEMS } from '../../../shared/onetablenew';
import { FormTypeEnum, transformFeatureFlagsToBackend } from '../../../utils/oneTableNewUtil';
import { OneTableNewReportCreateModel } from '../OneTableNewReportCreateModel';
import { ReportConfig, ReportConfigController } from '../report-config';

interface IFooterProps {
  current: number;
  loading?: boolean;
  onPrev?: () => void;
  onSave?: () => void;
}

const OneTableEditFormFooter: FC<IFooterProps> = ({ current, loading, onPrev, onSave }) => (
  <Flex justify="flex-end" gap={8}>
    {current === 1 && <Button onClick={onPrev}>{i18n.chain.comButton.previous}</Button>}
    <Button onClick={onSave} disabled={current === 0} loading={loading} type="primary">
      {i18n.chain.comButton.finish}
    </Button>
  </Flex>
);

export interface IOpenOneTableNewEditFormTypeOptions {
  itemData: any;
  successCallback?: () => void;
  cancelCallback?: () => void;
}

const StepMenu: FC<{
  formType: FormTypeEnum;
  onSelect: (type: FormTypeEnum) => void;
  items: typeof FORM_TYPE_ITEMS;
}> = ({ formType, onSelect, items }) => (
  <div className="form-header-tool-modal">
    <Menu onClick={({ key }) => onSelect(key as FormTypeEnum)} items={items} selectedKeys={[formType]} />
  </div>
);

export function openOneTableNewEditFormTypeDrawer(
  options: IOpenOneTableNewEditFormTypeOptions,
  drawerProps: Omit<DrawerProps, 'children'> = {},
) {
  const DrawerContent: FC<{ onClose: () => void }> = ({ onClose }) => {
    const [current, setCurrent] = useState(0);
    const [selectedType, setSelectedType] = useState<FormTypeEnum>(options.itemData.formType);
    const [controller, setController] = useState<ReportConfigController | null>(null);
    const [loading, setLoading] = useState(false);

    // 切换类型时，重建 controller
    const handleTypeSelect = useCallback((type: FormTypeEnum) => {
      setSelectedType(type);
      setCurrent(1);
      controller?.destroy();
      setController(new ReportConfigController({ formType: type }));
    }, []);

    const onPrev = useCallback(() => setCurrent(0), []);
    const onSave = useCallback(async () => {
      let configData = await controller?.getFormRef()?.current?.getValues?.();
      if (_.isUndefined(configData)) {
        return;
      }
      setLoading(true);
      configData = { ...configData, onetable_form_type: selectedType };
      transformFeatureFlagsToBackend(configData, true);
      const data = { extra_meta: configData };
      const { success } = await OneTableNewReportCreateModel.updateForm(options.itemData.formId, data).toPromise();
      setLoading(false);
      if (!success) return;
      options.successCallback?.();
      toastApi.success(i18n.chain.proMicroModules.oneTable.editConfigFormSucces);
      controller?.destroy();
      onClose();
    }, [controller, selectedType, onClose]);

    // 过滤类型
    const { isPeriodic, isEndless } = options.itemData;
    const items = FORM_TYPE_ITEMS.filter((item) => {
      if (isPeriodic) return item.key !== FormTypeEnum.PERIODIC;
      if (isEndless) return item.key !== FormTypeEnum.ENDLESS;
      return item.key !== FormTypeEnum.NORMAL;
    });

    return (
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div style={{ flex: 1 }}>
          <Steps
            size="small"
            current={current}
            items={[
              { title: i18n.chain.proMicroModules.oneTable.chooseFormType },
              { title: i18n.chain.proMicroModules.oneTable.editFormConfig },
            ]}
            style={{ marginBottom: 24 }}
          />
          {current === 0 && <StepMenu formType={selectedType} onSelect={handleTypeSelect} items={items} />}
          {current === 1 && controller && <ReportConfig controller={controller} />}
        </div>
        <OneTableEditFormFooter current={current} loading={loading} onPrev={onPrev} onSave={onSave} />
      </div>
    );
  };

  drawerApi.open({
    width: isPc() ? '70vw' : '100vw',
    title: i18n.chain.proMicroModules.oneTable.editFormType,
    destroyOnClose: true,
    ...drawerProps,
    children: (onClose: any) => <DrawerContent onClose={onClose} />,
    footer: null,
    onClose: () => {
      options.cancelCallback?.();
    },
  });
}
