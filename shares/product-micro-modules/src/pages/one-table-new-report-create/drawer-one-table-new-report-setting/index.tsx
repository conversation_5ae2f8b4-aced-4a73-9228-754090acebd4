import { type DrawerProps, drawerApi } from '@metroDesign/drawer';
import { toastApi } from '@metroDesign/toast';
import { isPc } from '@mdtBsComm/utils';
import i18n from '../../../languages';
import { FormTypeEnum, transformFeatureFlagsToBackend } from '../../../utils/oneTableNewUtil';
import { OneTableNewReportCreateModel } from '../OneTableNewReportCreateModel';
import type { IReportConfigData } from '../report-config';
import { ReportSetting, ReportSettingController } from '../report-setting';

// 打开创建界面
export interface IOpenOneTableNewReportSettingOptions {
  formId: string;
  formType: FormTypeEnum;
  sourcePkgId?: string;
  reportInfo?: Record<string, any>;
  configData?: IReportConfigData;
  successCallback?: () => void;
  cancelCallback?: () => void;
}

export const openOneTableNewReportSettingDrawer = async (
  controllerOptions: IOpenOneTableNewReportSettingOptions,
  drawerProps: Omit<DrawerProps, 'children'> = {},
) => {
  const ctrl = new ReportSettingController();
  await ctrl.initData(
    controllerOptions.formType,
    controllerOptions.reportInfo,
    controllerOptions.configData,
    controllerOptions.sourcePkgId,
  );
  drawerApi.open({
    width: isPc() ? '100vw' : '75vw',
    title: i18n.chain.proMicroModules.oneTable.reportConfig,
    okText: i18n.chain.comButton.finish,
    destroyOnClose: true,
    ...drawerProps,
    onConfirm: async (onClose) => {
      const { name, description, ...extraRest } = (await ctrl.getValue()) || {};
      const flowData = (await ctrl.getConfigController()?.getValue()) || {};
      const extraMeta = { ...extraRest, ...flowData };
      transformFeatureFlagsToBackend(extraMeta, true);
      const data = { name, description, extra_meta: extraMeta };
      const { success } = await OneTableNewReportCreateModel.updateForm(controllerOptions.formId, data).toPromise();
      if (!success) return;
      controllerOptions.successCallback?.();
      toastApi.success(i18n.chain.proMicroModules.oneTable.editFormSuccess);
      ctrl.destroy();
      onClose?.();
    },
    onClose: () => {
      controllerOptions.cancelCallback?.();
      ctrl.destroy();
    },
    children: () => <ReportSetting controller={ctrl} />,
  });
};
