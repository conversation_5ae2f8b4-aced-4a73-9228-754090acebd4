import { FC } from 'react';
import { Menu } from '@metroDesign/menu';
import { Scrollbar } from '@metroDesign/scrollbar';
import { useCreation } from 'ahooks';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Spin from '@mdtDesign/spin';
import { FormView } from '../../../components/form-view';
import i18n from '../../../languages';
import { ReportConfig } from '../report-config';
import { MenuEnum, ReportSettingController } from './ReportSettingController';
import './index.less';

interface IProps {
  controller: ReportSettingController;
}

const ReportSetting: FC<IProps> = ({ controller }) => {
  const { formilySchema, allSettingValues } = controller.getFormSpec();
  const activeMenu = useObservableState(controller.getActiveMenu$());
  const loading = useObservableState(controller.getLoading$());
  const show = useObservableState(controller.getShowView$());

  const configShowCls = show && activeMenu === MenuEnum.CONFIG ? 'config-show' : '';
  const configView = useCreation(() => {
    return loading ? null : (
      <ReportConfig className="create-report_config" controller={controller.getConfigController()} />
    );
  }, [loading]);

  const infoShowCls = show && activeMenu === MenuEnum.INFO ? 'info-show' : '';
  const infoView = useCreation(() => {
    return loading ? null : (
      <div className="create-report_info one-table-report-layout">
        <div className="one-table-form-view-title">{i18n.chain.proMicroModules.oneTable.reportInfo}</div>
        <FormView
          className="one-table-form-view"
          ref={controller.getInfoFormRef()}
          formilySchema={formilySchema}
          allSettingValues={allSettingValues}
          formData={controller.getReportInfo()}
          onChange={(val) => {
            controller.changeFormName(val.name);
          }}
        />
      </div>
    );
  }, [loading]);

  const reportInfo = loading ? <Spin /> : <div className={infoShowCls}>{infoView}</div>;
  const reportConfig = loading ? <Spin /> : <div className={configShowCls}>{configView}</div>;

  const reportSettingShowCls =
    show && (activeMenu === MenuEnum.INFO || activeMenu === MenuEnum.CONFIG) ? 'report-setting-show' : '';
  return (
    <Scrollbar style={{ height: 'calc(100% - 60px)', width: '100%', display: reportSettingShowCls ? 'block' : 'none' }}>
      <div className={`report-config-page ${reportSettingShowCls}`}>
        <div className="page-wrapper">
          <Menu
            onClick={(e) => controller.handleMenuChange(e.key)}
            selectedKeys={[activeMenu]}
            className="report-config-page-menu one-table-report-layout"
            items={[
              { label: i18n.chain.proMicroModules.oneTable.reportInfo, key: MenuEnum.INFO },
              { label: i18n.chain.proMicroModules.oneTable.publishSetting, key: MenuEnum.CONFIG },
            ]}
          />
          {reportInfo}
          {reportConfig}
        </div>
      </div>
    </Scrollbar>
  );
};

export { ReportSetting };
