import { createRef } from 'react';
import { BehaviorSubject } from 'rxjs';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFormSpecRefHandle } from '../../../components/form-view';
import { FormTypeEnum, getFormSpec } from '../../../utils/oneTableNewUtil';
import { IFormEditorSchema } from '../../form-editor';
import { IReportConfigData, ReportConfigController } from '../report-config';

export enum MenuEnum {
  INFO = 'report-info',
  CONFIG = 'report-config',
}

export interface IControllerOptions {
  formType: FormTypeEnum;
}

class ReportSettingController extends RequestController {
  private infoFormRef = createRef<IFormSpecRefHandle>();
  private reportInfo?: Record<string, any>;
  private activeMenu$ = new BehaviorSubject(MenuEnum.INFO);
  private loading$ = new BehaviorSubject(true);
  private formName$ = new BehaviorSubject('');
  // 这里是为了解决在tab下整体的view隐藏的问题
  private showView$ = new BehaviorSubject(true);
  private configController?: ReportConfigController;
  private formType?: FormTypeEnum;
  private formSpec: IFormEditorSchema;

  public constructor(options?: IControllerOptions) {
    super();
    this.formType = options?.formType;
    this.formSpec = getFormSpec(this.formType) as IFormEditorSchema;
  }

  public getFormSpec() {
    return this.formSpec;
  }

  public getInfoFormRef() {
    return this.infoFormRef;
  }

  public async getValue(): Promise<any> {
    // 在详情页编辑报表直接获取getValues 拿不到？
    return this.infoFormRef.current?.getInstance().values;
  }

  public destroy() {
    super.destroy();
    this.infoFormRef = null!;
    this.activeMenu$.complete();
    this.reportInfo = undefined;
    this.formName$.complete();
    this.loading$.complete();
    this.showView$.complete();
    this.configController?.destroy();
  }

  public getShowView$() {
    return this.showView$;
  }

  public getLoading$() {
    return this.loading$;
  }

  public getFormName$() {
    return this.formName$;
  }

  public getConfigController() {
    return this.configController!;
  }

  public changeFormName(name: string) {
    this.formName$.next(name);
  }

  public getActiveMenu$() {
    return this.activeMenu$;
  }

  public getReportInfo() {
    return this.reportInfo;
  }

  public handleMenuChange = (val: string) => {
    this.activeMenu$.next(val as MenuEnum);
  };

  public async initData(
    formType: FormTypeEnum,
    reportInfo?: Record<string, any>,
    configData?: IReportConfigData,
    sourcePkgId?: string,
  ) {
    this.reportInfo = reportInfo;
    this.changeFormName(this.reportInfo?.name || '');
    this.configController = new ReportConfigController({ formType, configData, sourcePkgId });
    this.loading$.next(false);
  }
}

export { ReportSettingController };
