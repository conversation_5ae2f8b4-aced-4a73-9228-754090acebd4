import { FC } from 'react';
import { ChevronDown, ChevronUp } from '@metro/icons';
import { ActionSheet } from '@metro/mobile-components/dist/esm/action-sheet';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Flex } from '@metroDesign/flex';
import { Col, Row } from '@metroDesign/grid';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Spin } from '@metroDesign/spin';
import { Typography } from '@metroDesign/typography';
import { LoadingWrapper } from '@mdtBsComm/components/loading-wrapper';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { FormView } from '../../components/form-view';
import i18n from '../../languages';
import { OneTableNewPreviewFormController } from './OneTableNewPreviewFormController';
import './index.less';

interface IProps {
  controller: OneTableNewPreviewFormController;
}

const FormBlockForm: FC<IProps> = ({ controller }) => {
  const formProps = useObservableState(controller.getPreviewFormProps$());
  const formData = useObservableState(controller.getCurrentEditData$());
  const hasPre = useObservableState(controller.getHasPre$());
  const hasNext = useObservableState(controller.getHasNext$());
  const currentFormLoading = useObservableState(controller.getCurrentFormLoading$());
  const { id: currentId } = useObservableState(controller.getCurrentEditData$());

  return (
    <div className="form">
      <Flex justify="space-between" align="center">
        <div>
          <Typography.Text type="secondary">{i18n.chain.proMicroModules.previewCurrentDataId}</Typography.Text>
          <span>{currentId}</span>
        </div>
        <Flex>
          <Button ghost icon={<ChevronUp />} disabled={!hasPre} onClick={async () => controller.saveEditPreData()}>
            {i18n.chain.proMicroModules.previousRow}
          </Button>
          <Button ghost icon={<ChevronDown />} disabled={!hasNext} onClick={async () => controller.saveEditNextData()}>
            {i18n.chain.proMicroModules.nextRow}
          </Button>
        </Flex>
      </Flex>

      <Spin spinning={currentFormLoading} fillParent>
        <Scrollbar style={{ height: 'calc(100% - 32px)' }}>
          <div id={`form-${currentId}`}>
            <FormView {...formProps} formData={formData} readonly />
          </div>
        </Scrollbar>
      </Spin>
    </div>
  );
};

const MoreOperation: FC<IProps> = ({ controller }) => {
  const enableCopy = useObservableState(controller.getEnableCopy$());
  const enableDelete = useObservableState(controller.getEnableDelete$());
  const isDeleted = useObservableState(controller.getDeleteStatus$());
  const actionVisible = useObservableState(controller.getActionVisible$());
  const moreItems = controller.getMoreItems(enableCopy, enableDelete, isDeleted);
  const actions = controller.itemsToActions(moreItems);

  return moreItems.length ? (
    isPc() ? (
      <Dropdown menu={{ items: moreItems }}>
        <Button ghost>{i18n.chain.proMicroModules.moreOperation}</Button>
      </Dropdown>
    ) : (
      <>
        <Button ghost onClick={() => controller.setActionVisible(true)}>
          {i18n.chain.proMicroModules.moreOperation}
        </Button>
        <ActionSheet visible={actionVisible} actions={actions} onClose={() => controller.setActionVisible(false)} />
      </>
    )
  ) : (
    <div />
  );
};

const FormFooter: FC<IProps> = ({ controller }) => {
  const enableClose = controller.getEnableClose();
  const enableEdit = controller.getEnableEdit();

  const onPreview = () => {
    controller.previewCallbackFunc();
  };

  const onClose = () => {
    controller.onCloseFunc();
  };

  return (
    <Flex justify="space-between" className="footer">
      <Flex gap="small">
        {enableClose ? <Button onClick={onClose}>{i18n.chain.comButton.close}</Button> : <div />}
        <MoreOperation controller={controller} />
      </Flex>

      <Flex gap="small">
        <LoadingWrapper>
          <Button onClick={controller.exportToPDF}>{i18n.chain.proMicroModules.oneTable.exportToPdf}</Button>
          {enableEdit ? (
            <Button type="primary" onClick={onPreview}>
              {i18n.chain.proMicroModules.oneTable.editData}
            </Button>
          ) : null}
        </LoadingWrapper>
      </Flex>
    </Flex>
  );
};

const FormBlock: FC<IProps> = ({ controller }) => {
  return (
    <Col className="form-block">
      <FormBlockForm controller={controller} />
      <FormFooter controller={controller} />
    </Col>
  );
};

export const OneTableNewPreviewForm: FC<IProps> = ({ controller }) => {
  return (
    <Row className="one-table-new-preview-form">
      <FormBlock controller={controller} />
    </Row>
  );
};
