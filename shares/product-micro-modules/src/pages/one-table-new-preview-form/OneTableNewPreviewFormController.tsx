import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { exportPDF } from '../../utils/export-pdf';
import { DataTableActionEnum } from '../one-table-new-data-table';
import {
  IControllerOptions as IProps,
  OneTableNewFillFormController,
} from '../one-table-new-fill-form/OneTableNewFillFormController';

export interface IControllerOptions extends Omit<IProps, 'submitConfirmOptionsFunc' | 'defaultValue'> {
  previewCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  copyCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  onDeleteClose?: (onClose?: () => void) => void;
  onClose?: () => void;
  showEdit?: boolean;
  defaultValue: Record<string, any>;
}

export class OneTableNewPreviewFormController extends OneTableNewFillFormController {
  private previewFormProps$ = new BehaviorSubject<any>({});
  private showEdit?: boolean;
  private previewCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  private onDeleteClose?: (onClose?: () => void) => void;
  private copyCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  private onClose?: () => void;
  private currentFormLoading$ = new BehaviorSubject<boolean>(true);

  public constructor(options: IControllerOptions) {
    const { previewCallback, onClose, onDeleteClose, copyCallback, showEdit, defaultValue, itemData, ...restOptions } =
      options;
    super({
      ...restOptions,
      defaultValue,
      itemData,
      onlyForm: true,
      submitConfirmOptionsFunc: () => ({
        onlySaveCallBack: () => {},
        submitSuccessCallback: () => {},
      }),
    });
    this.showEdit = showEdit;
    this.previewCallback = previewCallback;
    this.onClose = onClose;
    this.onDeleteClose = onDeleteClose;
    this.copyCallback = copyCallback;
    this.getFormProps$().subscribe((v) => {
      this.previewFormProps$.next(this.getNewFormPropsWithSecretVisible(v));
    });
    this.getDataTableController()
      .getDataListLoading$()
      .pipe(skip(1)) // 跳过初始的false值
      .subscribe((isLoading) => {
        console.log('isLoading', isLoading);
        this.currentFormLoading$.next(isLoading);
      });
  }

  public destroy() {
    super.destroy();
    this.currentFormLoading$.complete();
    this.previewFormProps$.complete();
  }

  public getCurrentFormLoading$() {
    return this.currentFormLoading$;
  }

  public getPreviewFormProps$() {
    return this.previewFormProps$;
  }

  public getEnableClose() {
    return !!this.onClose;
  }

  public getEnableEdit() {
    return !!this.showEdit;
  }

  public previewCallbackFunc() {
    const value = this.getCurrentEditData$().getValue();
    this.previewCallback?.(value);
  }

  public onDeleteCloseFunc() {
    this.onDeleteClose?.();
  }

  public copyCallbackFunc() {
    const value = this.getCurrentEditData$().getValue();
    this.copyCallback?.(value);
  }

  public onCloseFunc() {
    this.onClose?.();
  }

  public exportToPDF = async () => {
    const { id: elementId } = this.getCurrentEditData$().getValue();
    if (!elementId) {
      return;
    }
    return await exportPDF(`form-${elementId}`, this.getItemData().formName, { scale: 0.68 });
  };

  public getMoreItems(enableCopy: boolean, enableDelete: boolean, isDeleted: boolean) {
    const items = super.getMoreItems(enableCopy, enableDelete, isDeleted);
    return _.map(items, (item) => {
      if (item.key === DataTableActionEnum.COPY) {
        return {
          ...item,
          onClick: () => this.copyCallbackFunc(),
        };
      }
      if (item.key === DataTableActionEnum.Delete) {
        return {
          ...item,
          onClick: async () => {
            await this.deleteData();
            this.onDeleteCloseFunc();
          },
        };
      }
      if (item.key === DataTableActionEnum.Redel) {
        return {
          ...item,
          onClick: async () => {
            await this.deleteData(true);
            this.onDeleteCloseFunc();
          },
        };
      }
      return item;
    });
  }

  public getNewFormPropsWithSecretVisible(formProps: any) {
    const secretVisible = !!this.getDataTableController().getShowAllSecret$().getValue();
    const { formilySchema, ...rest } = formProps;
    return {
      ...rest,
      formilySchema: {
        ...formilySchema,
        schema: this.processSchemaForSecretFields(formilySchema.schema, secretVisible),
      },
    };
  }

  private processSchemaForSecretFields(schema: any, secretVisible: boolean): any {
    if (!schema) return schema;

    const processedSchema = _.cloneDeep(schema);

    const SECRET_DISPLAY_TYPE = 'secret';
    const STRING_TYPE = 'string';
    const X_COMPONENT_PROPS = 'x-component-props';
    const PROPERTIES = 'properties';

    if (!_.has(processedSchema, PROPERTIES)) {
      return processedSchema;
    }

    _.forEach(processedSchema.properties, (property) => {
      if (
        _.get(property, 'type') === STRING_TYPE &&
        _.get(property, `${X_COMPONENT_PROPS}.displayType`) === SECRET_DISPLAY_TYPE
      ) {
        _.set(property, X_COMPONENT_PROPS, _.merge({}, _.get(property, X_COMPONENT_PROPS, {}), { secretVisible }));
      }

      if (_.has(property, PROPERTIES)) {
        _.set(property, PROPERTIES, this.processSchemaForSecretFields(property.properties, secretVisible));
      }

      if (_.has(property, `items.${PROPERTIES}`)) {
        _.set(
          property,
          `items.${PROPERTIES}`,
          this.processSchemaForSecretFields(_.get(property, `items.${PROPERTIES}`), secretVisible),
        );
      }
    });

    return processedSchema;
  }
}
