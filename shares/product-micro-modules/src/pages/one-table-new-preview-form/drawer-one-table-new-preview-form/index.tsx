import { FC } from 'react';
import { type DrawerProps, drawerApi } from '@metroDesign/drawer';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { isPc } from '@mdtBsComm/utils';
import i18n from '../../../languages';
import { OneTableNewFillFormModel } from '../../one-table-new-fill-form/OneTableNewFillFormModel';
import { type IControllerOptions, OneTableNewPreviewForm, OneTableNewPreviewFormController } from '../index';

export type IOpenOneTableNewPreviewFormOptions = Omit<IControllerOptions, 'Model'>;

const ChildInner: FC<any> = ({ options, onClose }) => {
  const [controller] = useController(() => {
    const { previewCallback, onDeleteClose, copyCallback, ...restOptions } = options;
    const ctrl = new OneTableNewPreviewFormController({
      Model: OneTableNewFillFormModel,
      ...restOptions,
      previewCallback: (value) => {
        previewCallback?.(value, onClose);
      },
      copyCallback: (value) => {
        copyCallback?.(value, onClose);
      },
      onDeleteClose: () => {
        onDeleteClose?.(onClose);
      },
    });
    return [ctrl, null];
  }, []);
  return <OneTableNewPreviewForm controller={controller} />;
};

// 预览单行数据界面
export const openOneTableNewPreviewFormDrawer = (
  options: IOpenOneTableNewPreviewFormOptions,
  drawerOptions: Omit<DrawerProps, 'children'> = {},
) => {
  drawerApi.open({
    width: isPc() ? '800px' : '100%',
    className: 'one-table-new-preview-form-drawer',
    closable: true,
    destroyOnClose: true,
    footer: null,
    title: i18n.chain.proMicroModules.oneTable.detail.tabDataLabel,
    ...drawerOptions,
    children: (onClose) => {
      return <ChildInner options={options} onClose={onClose} />;
    },
  });
};
