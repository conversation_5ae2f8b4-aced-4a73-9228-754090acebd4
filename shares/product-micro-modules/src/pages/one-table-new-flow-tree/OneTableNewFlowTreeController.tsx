import { BehaviorSubject } from 'rxjs';
import { FlowConfigController } from '../../containers/xflow-canvas';
import { ONE_TABLE_INFO } from '../../datlas/datlasConfig';
import type { IOneTableNewOperatorDataComm } from '../../interfaces';
import { IFlowData, IOneTableNewFlowTreeModel } from './OneTableNewFlowTreeModel';

export interface IControllerOptions {
  Model: IOneTableNewFlowTreeModel;
  itemData: IOneTableNewOperatorDataComm;
  showDeliveryCancelOrRejectList?: boolean;
  onlyOne?: 'table' | 'tree';
}

export class OneTableNewFlowTreeController {
  private Model: IOneTableNewFlowTreeModel;
  private loading$ = new BehaviorSubject<boolean>(true);
  private showTable$: BehaviorSubject<boolean>;
  private flowConfigController?: FlowConfigController;
  private itemData: IOneTableNewOperatorDataComm;
  private datasource?: IFlowData;
  private showDeliveryCancelOrRejectList?: boolean;
  private onlyOne?: 'table' | 'tree';

  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.itemData = options.itemData;
    this.onlyOne = options.onlyOne;
    this.showTable$ = new BehaviorSubject<boolean>(
      options.onlyOne === 'table' ? true : !ONE_TABLE_INFO.distributeGraphModeTree,
    );
    this.showDeliveryCancelOrRejectList = options.showDeliveryCancelOrRejectList;
    this.loadingData();
  }

  public destroy() {
    this.loading$.complete();
    this.showTable$.complete();
    this.flowConfigController?.destroy();
    this.flowConfigController = null!;
    this.itemData = null!;
    this.onlyOne = null!;
    this.datasource = null!;
    this.showDeliveryCancelOrRejectList = null!;
  }

  public getLoading$() {
    return this.loading$;
  }

  public getShowTable$() {
    return this.showTable$;
  }

  public getOnlyOne() {
    return this.onlyOne;
  }

  public changeShowTable(show: boolean) {
    this.showTable$.next(show);
  }

  public getDatasource() {
    return this.datasource!;
  }

  public getShowDeliveryCancelOrRejectList() {
    return this.showDeliveryCancelOrRejectList;
  }

  public async loadingData() {
    this.loading$.next(true);
    this.Model.getFlowTreeData(this.itemData.assignWfId, this.itemData.isCheckGranted).then((data) => {
      this.datasource = data;
      this.loading$.next(false);
    });
  }
}
