import _ from 'lodash';
import { IOnetableDownstreamStat, IWorkflow, IWorkflowGenealogy } from '@mdtApis/interfaces';
import { formateDateByUnix } from '@mdtBsComm/utils/dayUtil';
import {
  batchQueryWorkflowGenealogyAsync,
  queryOnetableDownstreamStatAsync,
  queryWorkflowsByPostAsync,
} from '@mdtBsServices/flowork';
import { OneTableNewDataStateEnum } from '@mdtProComm/constants';
import i18n from '../../languages';
import {
  getAssignees,
  getFlowStatusLable,
  getPrimaryAssignee,
  getReasonWithAssignWfData,
  getWorkflowIsRuning,
} from '../../utils/oneTableNewUtil';

export interface IFlowData {
  root: string;
  flowDataMap: Record<string, IWorkflow>;
  flowGenealogyMap: Record<string, IWorkflowGenealogy>;
  flowStatsMap?: Record<string, IOnetableDownstreamStat & { root: IOnetableDownstreamStat; percent?: string }>;
}

export interface INodeData {
  wfId: string;
  userId: number;
  userIds: number[];
  dataStatus: string;
  tagColor: string;
  tagLabel: string;
  time: string;
  showBtn: boolean;
  isRoot?: boolean;
  reason?: string;
  isCancelledOrRefused?: boolean;
  dataDeliveryInfo?: IOnetableDownstreamStat & { root: IOnetableDownstreamStat; percent?: string };
}

export class OneTableNewFlowTreeModel {
  public static async getFlowTreeData(wfId: string, isCheckGranted?: boolean) {
    return batchQueryWorkflowGenealogyAsync({ ids: [wfId], ignore_canceled: false, down: true, max_level: 10 }).then(
      async (resp) => {
        const genealogy = _.get(resp, 'data[0].genealogy', []) as IWorkflowGenealogy[];
        const ids = _.uniq(_.flattenDeep(_.map(genealogy, (it) => [it.source, it.targets])));
        if (_.isEmpty(ids)) return { root: wfId, flowDataMap: {}, flowGenealogyMap: {} };

        const workflowsPromise = queryWorkflowsByPostAsync(
          { ids, with_data: true, initiator_is_me: false, orderby: '-create_time' },
          { params: { page_size: ids.length } },
        );

        const statsPromise = queryOnetableDownstreamStatAsync({
          assign_workflow_id: wfId,
          max_depth: 20,
          row_permission_params: {
            check_data_submitted: false,
            permission_scope: 'all',
            check_granted: isCheckGranted,
          },
        });

        const [workflowsResp, statsResp] = await Promise.all([workflowsPromise, statsPromise]);

        const flowDataMap = _.keyBy(workflowsResp.data, 'workflow_id');
        const flowGenealogyMap = _.keyBy(genealogy, 'source');

        const rootStat = _.find(
          statsResp?.data || [],
          (it) => it.assign_workflow_id === wfId,
        ) as IOnetableDownstreamStat;

        const flowStatsMap = _.reduce(
          statsResp?.data || [],
          (result: IFlowData['flowStatsMap'], stat) => {
            result![stat.assign_workflow_id] = {
              ...stat,
              percent:
                rootStat?.stat_include_downstream?.total_assign_rows > 0
                  ? (
                      ((stat.stat_include_downstream?.total_assign_rows || 0) /
                        rootStat?.stat_include_downstream?.total_assign_rows) *
                      100
                    ).toFixed(1)
                  : '0.0',
              root: rootStat,
            };
            return result;
          },
          {},
        );

        return { root: wfId, flowDataMap, flowGenealogyMap, flowStatsMap };
      },
    );
  }

  public static transformFlowData(
    id: string,
    dataMap: Record<string, IWorkflow>,
    genealogyMap: Record<string, IWorkflowGenealogy>,
    statsMap?: IFlowData['flowStatsMap'],
  ): INodeData {
    const item = dataMap[id];
    const data = item.data!;
    const { level, targets } = genealogyMap[id] || {};

    const [cc, ...dsp] = getFlowStatusLable(item);
    const runing = getWorkflowIsRuning(item.status);
    const { data_state: ds } = data;
    const reason = getReasonWithAssignWfData(data, cc);

    let color = 'warning';
    let tag = i18n.chain.dataStateLabel.unsubmitted;
    if (ds === OneTableNewDataStateEnum.REFUSED) {
      color = 'error';
      tag = i18n.chain.dataStateLabel.refused;
    } else if (runing && (ds === OneTableNewDataStateEnum.SUBMITTED || ds === OneTableNewDataStateEnum.APPROVED)) {
      color = 'success';
      tag = i18n.chain.dataStateLabel.submitted;
    } else if (!runing) {
      tag = dsp[0];
      color = dsp[1];
    }

    const userId = getPrimaryAssignee(data);

    return {
      wfId: item.workflow_id,
      userId: userId,
      userIds: _.uniq([userId, ...getAssignees(data)]),
      dataStatus: ds,
      tagColor: color,
      tagLabel: tag,
      isRoot: level === 1,
      showBtn: level > 1 && _.size(targets) > 0,
      time: formateDateByUnix(item.create_time, 2),
      reason,
      dataDeliveryInfo: statsMap?.[id],
      isCancelledOrRefused: item.status === 'cancelled' || ds === OneTableNewDataStateEnum.REFUSED,
    };
  }
}

export type IOneTableNewFlowTreeModel = typeof OneTableNewFlowTreeModel;
