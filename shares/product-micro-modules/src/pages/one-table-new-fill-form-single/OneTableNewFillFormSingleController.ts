import _ from 'lodash';
import { createRef } from 'react';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject } from 'rxjs';
import { IFormSpecRefHandle } from '../../components/form-view';
import i18n from '../../languages';
import { getProcessRowsOptions, processRows } from '../../utils/oneTableNewUtil';
import { IFormData } from '../one-table-new-report-detail/OneTableNewReportDetailModel';
import { IOneTableNewFillFormSingleModel } from './OneTableNewFillFormSingleModel';

export interface IControllerOptions {
  Model: IOneTableNewFillFormSingleModel;
  rootWfId?: string;
  submitSingle?: boolean;
}

class OneTableNewFillFormSingleController {
  private Model: IOneTableNewFillFormSingleModel;
  private itemData?: IFormData;
  // 表单索引
  private formRef = createRef<IFormSpecRefHandle>();
  private loading$ = new BehaviorSubject<boolean>(true);
  private isSaving$ = new BehaviorSubject<boolean>(false);
  private isFinished$ = new BehaviorSubject<boolean>(false);
  private submitSingle?: boolean;
  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.submitSingle = options.submitSingle;
    this.init(options.rootWfId);
  }

  public destroy() {
    this.loading$.complete();
    this.formRef = null!;
    this.itemData = undefined;
    this.isSaving$.complete();
  }

  public getIsSaving$() {
    return this.isSaving$;
  }

  public getIsFinished$() {
    return this.isFinished$;
  }

  public getLoading$() {
    return this.loading$;
  }

  public getItemData() {
    return this.itemData;
  }

  public getFormRef() {
    return this.formRef;
  }

  public async init(rootWfId?: string) {
    if (!rootWfId) {
      return this.loading$.next(false);
    }
    const managementData = await this.Model.getManagementForm(rootWfId);
    if (managementData?.formData) {
      this.itemData = managementData.formData;
    }
    this.loading$.next(false);
  }

  public async addNewRow() {
    this.isSaving$.next(true);
    const values = (await this.formRef.current?.getValues()) as any;
    const data = this.itemData!;
    const { addRows } = processRows([], _.compact([values]), getProcessRowsOptions(data.isFormManageLevelUser));
    if (!_.isEmpty(addRows.columns)) {
      const newRow = await this.Model.addRow(data.pkgId, addRows);
      if (newRow) {
        toastApi.success(i18n.chain.comTip.addSuccess);
        this.formRef.current?.getInstance().reset();
        if (this.submitSingle) {
          this.isFinished$.next(true);
        }
      }
    }
    this.isSaving$.next(false);
  }
}

export { OneTableNewFillFormSingleController };
