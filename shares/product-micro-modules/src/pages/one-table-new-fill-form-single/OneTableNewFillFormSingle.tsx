import { FC } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { Empty } from '@metroDesign/empty';
import { Flex } from '@metroDesign/flex';
import { Result } from '@metroDesign/result';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Spin } from '@metroDesign/spin';
import { Typography } from '@metroDesign/typography';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { FormView } from '../../components/form-view';
import i18n from '../../languages';
import { OneTableNewFillFormSingleController } from './OneTableNewFillFormSingleController';
import './index.less';

interface IProps {
  controller: OneTableNewFillFormSingleController;
}

const OneTableNewFillFormSingle: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getLoading$());
  const isSaving = useObservableState(controller.getIsSaving$());
  const isFinished = useObservableState(controller.getIsFinished$());
  if (loading) return <Spin fillParent tip={i18n.chain.comDataLoading} />;
  if (isFinished)
    return (
      <Flex block justify="center" align="center">
        <Result
          status="success"
          title={i18n.chain.proMicroModules.wfStartWorkflow.submitSuccess}
          subTitle={i18n.chain.proMicroModules.wfStartWorkflow.submitThanks}
        />
      </Flex>
    );
  const itemData = controller.getItemData();
  const isNotRunning = !itemData || !itemData.isRunning;

  return (
    <Scrollbar style={{ height: '100vh', width: '100%' }}>
      <Flex className="module_pages_one-table-new-fill-form-single_container" vertical justify="center" align="center">
        {isNotRunning ? (
          <Empty description={i18n.chain.proMicroModules.oneTable.noRunningErrorTip} />
        ) : (
          <>
            <Typography.Title level={4}>
              {itemData?.isPeriodic ? itemData.formName + itemData.versionName : itemData.formName}
            </Typography.Title>
            <FormView {...itemData.formSpec} ref={controller.getFormRef()} />
            <Button
              type="primary"
              style={{ width: 120 }}
              loading={isSaving}
              onClick={async () => await controller.addNewRow()}
            >
              {i18n.chain.comButton.submit}
            </Button>
          </>
        )}
      </Flex>
    </Scrollbar>
  );
};

export { OneTableNewFillFormSingle };
