import _ from 'lodash';
import { IDatapkgRows } from '@mdtApis/interfaces';
import { postDatapkgRowsAsync } from '@mdtBsServices/datapkgs';
import { queryOnetableManagedFormsAsync } from '@mdtBsServices/flowork';
import { OneTableNewReportDetailModel } from '../one-table-new-report-detail/OneTableNewReportDetailModel';

class OneTableNewFillFormSingleModel {
  public static async getManagementForm(rootWfId: string) {
    const resp = await queryOnetableManagedFormsAsync({
      root_workflow_id: rootWfId,
      with_form_info: true,
      with_assign_workflow_info: true,
    });
    const data = resp.data?.[0];
    return data ? OneTableNewReportDetailModel.transformToManagementData(data) : undefined;
  }

  public static async addRow(datapkgId: string, data: IDatapkgRows) {
    const resp = await postDatapkgRowsAsync(datapkgId, data);
    if (!resp.success) return;
    return this.transformDataToTable(resp.data!);
  }

  private static transformDataToTable(data: IDatapkgRows) {
    const rowValues = data.values[0];
    const row: Record<string, any> = {};
    _.forEach(data.columns, (it, index) => {
      row[it] = rowValues[index];
    });
    return row;
  }
}

export type IOneTableNewFillFormSingleModel = typeof OneTableNewFillFormSingleModel;
export { OneTableNewFillFormSingleModel };
