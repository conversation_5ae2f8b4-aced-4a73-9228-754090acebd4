import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import {
  IOnetableGrantedForm,
  IOnetableGrantedFormQueryPost,
  IPaginationQuery,
  IRequestCancelToken,
  IRequestRequestConfig,
} from '@mdtApis/interfaces';
import { formateDateByUnix } from '@mdtBsComm/utils/dayUtil';
import { queryResourceAsync } from '@mdtBsServices/auth';
import { queryOnetableGrantedFormsAsync, queryOnetableGrantedFormsPaginationAsync } from '@mdtBsServices/flowork';
import { IOneTableNewCardComm, IOneTableNewOperatorDataComm } from '../../interfaces';
import i18n from '../../languages';
import {
  getAffiliatedOrgs,
  getBindPkgId,
  getFormOwner,
  getFrontendPersonalStatus,
  getPeriodicVersionName,
  getPrimaryAssignee,
  getVersionGroup,
  isEndlessForm,
  isNeedApproval,
  isPeriodicForm,
  TaskPersonalStatusEnum,
  transformFeatureFlagsToFrontEnd,
} from '../../utils/oneTableNewUtil';

export interface ICardItemData extends IOneTableNewCardComm, IOneTableNewOperatorDataComm {
  status: TaskPersonalStatusEnum;
  orgList?: number[];
  orgUserId: number; // 如果orgList为空, 则使用创建者的作为部门
}

export interface IScopeItem {
  label: any;
  value: number;
  scope: string;
}

export class OneTableNewGrantedFormModel {
  public static transformToCardData(item: IOnetableGrantedForm, dataStatus: TaskPersonalStatusEnum): ICardItemData {
    const { bind_form: bf = {} } = item;
    const { id, name, extra_meta: extraMeta = {}, form_spec: formSpec } = bf;
    const assignWf = item.assign_workflow!;
    const data = assignWf.data || {};
    const rootWfId = item.root_workflow_id;

    return {
      id: id,
      name: name,
      createTime: formateDateByUnix(assignWf.create_time, 0),
      isPeriodic: isPeriodicForm(extraMeta),
      isEndless: isEndlessForm(extraMeta),
      isCollaborate: false,
      primaryAssignee: getPrimaryAssignee(assignWf.data),
      // isFormManageLevelUser: isFormManageLevelUser(rootWfId, assignWf.parent_workflow_id),
      isFormManageLevelUser: false,
      isNeedApproval: isNeedApproval(data),
      status: dataStatus,
      formId: id,
      formName: name,
      formSpec: formSpec,
      formOwner: getFormOwner(bf),
      rootWfId: rootWfId,
      assignWfId: item.assign_workflow_id!,
      pkgId: getBindPkgId(extraMeta),
      featureFlags: transformFeatureFlagsToFrontEnd(extraMeta),
      versionName: getPeriodicVersionName(item),
      versionGroup: getVersionGroup(item),
      orgList: getAffiliatedOrgs(extraMeta),
      orgUserId: bf.user_id,
    };
  }

  public static queryScopeData(cancelToken?: IRequestCancelToken) {
    return from(
      queryResourceAsync({ params: { resource_types: 'all_app_form,all_org_form', action: 'manage' }, cancelToken }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        return _.map(resp.data, (item): IScopeItem => {
          const val = item.resource_id as unknown as number;
          if (item.resource_type === 'all_app_form') {
            return { label: i18n.chain.proMicroModules.grantedOtForm.scopeAppName, value: val, scope: 'app' };
          }
          return { label: '', value: val, scope: 'org' };
        });
      }),
    );
  }

  public static queryItemById(wfId: string, options: IOnetableGrantedFormQueryPost = {}) {
    return from(
      queryOnetableGrantedFormsAsync(
        { root_workflow_ids: [wfId], with_assign_workflow_info: true, unfold_version_history: true, ...options },
        { quiet: true },
      ),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const it = (resp.data || [])[0];
        if (!it) return;
        const dataStatus = getFrontendPersonalStatus('', it.assign_workflow?.status);
        return it ? this.transformToCardData(it, dataStatus) : undefined;
      }),
    );
  }

  // 获取首页数据
  public static queryFirstPage(
    dataStatus: TaskPersonalStatusEnum,
    data: IOnetableGrantedFormQueryPost,
    config?: IRequestRequestConfig<IPaginationQuery>,
  ) {
    return from(queryOnetableGrantedFormsPaginationAsync(data, config)).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const data = resp.data || { total_count: 0, dataResult: [] };
        return [data.total_count, _.map(data.dataResult, (it) => this.transformToCardData(it, dataStatus))] as [
          number,
          ICardItemData[],
        ];
      }),
    );
  }

  // 获取下一页flow列表
  public static queryNextPage(
    dataStatus: TaskPersonalStatusEnum,
    data: IOnetableGrantedFormQueryPost,
    config?: IRequestRequestConfig<IPaginationQuery>,
  ) {
    return from(queryOnetableGrantedFormsAsync(data, config)).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        return _.map(resp.data || [], (it) => this.transformToCardData(it, dataStatus));
      }),
    );
  }
}

export type IOneTableNewGrantedFormModel = typeof OneTableNewGrantedFormModel;
