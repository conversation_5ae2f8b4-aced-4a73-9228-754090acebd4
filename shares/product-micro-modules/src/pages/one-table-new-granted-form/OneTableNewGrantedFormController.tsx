import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { IOnetableGrantedFormQueryPost } from '@mdtApis/interfaces';
import { assignIfNotEmpty } from '@mdtBsComm/utils/assignUtil';
import { RequestController } from '@mdtBsControllers/request-controller';
import { OneTableNewFormFilterToolController } from '../../components/one-table-new-form-filter-tool';
import { ShowFullOrgIdToName } from '../../components/transform-id-to-name';
import { CardCurdWithSimpleSearchController, IPaginationParams } from '../../containers/card-curd-with-simple-search';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { doOneTableNewOperatorViewDetail } from '../../shared/onetablenew';
import { DetailFromPageEnum, getQueryWorkflowStatus, TaskPersonalStatusEnum } from '../../utils/oneTableNewUtil';
import { CardItem } from './_util/CardItem';
import { HeaderTitle, HeaderTool } from './_util/HeaderTool';
import type { ICardItemData, IOneTableNewGrantedFormModel, IScopeItem } from './OneTableNewGrantedFormModel';

interface IControllerOptions {
  Model: IOneTableNewGrantedFormModel;
}

export class OneTableNewGrantedFormController extends RequestController {
  private Model: IOneTableNewGrantedFormModel;
  private cardController: CardCurdWithSimpleSearchController;
  private cardDataStatus$ = new BehaviorSubject<TaskPersonalStatusEnum>(TaskPersonalStatusEnum.RUNNING);
  private filterToolController: OneTableNewFormFilterToolController;
  private selectScopeItem$ = new BehaviorSubject<IScopeItem | null>(null);
  private scopeList: IScopeItem[] = [];

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    this.cardController = new CardCurdWithSimpleSearchController<any>({
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: () => this.queryFirstPageData(),
            loadNextPageDataListFunc: (params: IPaginationParams) => this.queryNextPageData(params),
            getBackendFilterParams: () => this.getQueryParams(),
          },
          compOptions: () => this.initListOptions(),
        },
        cardItemViewController: () => this,
      },
      headerOptions: {
        title: <HeaderTitle controller={this} />,
        hideInput: true,
        renderExtendOthers: () => {
          return <HeaderTool controller={this} />;
        },
      },
    });

    this.filterToolController = new OneTableNewFormFilterToolController({
      onChange: this.onFilterValueChange,
    });

    this.cardDataStatus$.subscribe(() => {
      this.filterToolController.resetAll();
    });

    this.initScope();
  }

  public destroy() {
    super.destroy();
    this.cardDataStatus$.complete();
    this.cardController?.destroy();
    this.cardController = null!;
    this.filterToolController?.destroy();
    this.filterToolController = null!;
    this.scopeList.length = 0;
    this.selectScopeItem$.complete();
    this.selectScopeItem$.next(null);
  }

  public getFilterToolController() {
    return this.filterToolController;
  }

  public openWorkItemIdentifier = async (identifier: Record<string, any>) => {
    const { fromPage, rootWfId } = identifier;
    if (!rootWfId || fromPage !== DetailFromPageEnum.GRANTED) return;
    let item = _.find(this.cardController.getDataListValue(), { rootWfId });
    const selectScopeItem = this.selectScopeItem$.getValue();
    const scope = (selectScopeItem?.scope || 'org') as 'org';
    const orgId = scope === 'org' ? selectScopeItem?.value : undefined;
    if (!item) {
      item = await this.Model.queryItemById(rootWfId, { org_id: orgId, scope }).toPromise();
    }
    item && this.viewDetail(item);
  };

  public getSelectScopeItem$() {
    return this.selectScopeItem$;
  }

  public getScopeList() {
    return this.scopeList;
  }

  public changeSelectScopeItem(scopeItem: IScopeItem) {
    this.selectScopeItem$.next(scopeItem);
  }

  public getCardDataStatus$() {
    return this.cardDataStatus$;
  }

  public changeCardDataStatus(status: TaskPersonalStatusEnum) {
    this.cardDataStatus$.next(status);
  }

  public getCardController() {
    return this.cardController;
  }

  public viewDetail(item: ICardItemData) {
    const selectScopeItem = this.selectScopeItem$.getValue();
    const scope = (selectScopeItem?.scope || 'org') as 'org';
    const orgId = scope === 'org' ? selectScopeItem?.value : undefined;
    doOneTableNewOperatorViewDetail({
      fromPage: DetailFromPageEnum.GRANTED,
      rootWfId: item.rootWfId,
      grantedOptions: { org_id: orgId, scope },
    });
  }

  private queryFirstPageData() {
    const query = this.getQueryParams();
    return this.Model.queryFirstPage(query.dataStatus, query.params, {
      cancelToken: query.cancelToken,
      params: { fetch_total_count: true },
    });
  }

  private queryNextPageData(params: IPaginationParams) {
    const query = this.getQueryParams();
    return this.Model.queryNextPage(query.dataStatus, query.params, { params, cancelToken: query.cancelToken });
  }

  private getQueryParams() {
    const dataStatus = this.cardDataStatus$.getValue();
    const cancelToken = this.getCancelToken();
    const selectScopeItem = this.selectScopeItem$.getValue();
    const scope = (selectScopeItem?.scope || 'org') as 'org';
    const orgId = scope === 'org' ? selectScopeItem?.value : undefined;

    const params: IOnetableGrantedFormQueryPost = {
      scope: scope,
      level: 'read',
      org_id: orgId,
      orderby: '-create_time',
      with_assign_workflow_info: true,
      with_version_history: true,
      workflow_status: getQueryWorkflowStatus(dataStatus === TaskPersonalStatusEnum.COMPLETE),
    };
    assignIfNotEmpty(params, 'bind_form_query', this.filterToolController.getTransformedFilterValue());

    return { params, cancelToken, dataStatus };
  }

  private onFilterValueChange = () => {
    if (!this.selectScopeItem$.getValue()) return;
    this.cardController.loadDataList();
  };

  private initListOptions() {
    return {
      itemGap: 16,
      itemHeight: 180,
      itemWidth: 270,
      useVirtual: true,
      CardItemView: CardItem,
    };
  }

  private initScope() {
    const appIns = DatlasAppController.getInstance();
    const cancelToken = this.getCancelToken();
    this.Model.queryScopeData(cancelToken).subscribe((data) => {
      const scopeList = _.map(data, (it) => {
        !it.label && (it.label = <ShowFullOrgIdToName id={it.value} />);
        return it;
      });
      // 本机构排最前面
      this.scopeList = _.sortBy(scopeList, 'scope');
      let selectScopeItem = _.find(data, { scope: 'app' });
      // 如果不存在，则查找用户所属的部门
      if (!selectScopeItem) {
        const orgId = appIns.getUserOrgId();
        selectScopeItem = _.find(data, { scope: 'orgId', value: orgId }) || data[0];
      }
      this.selectScopeItem$.next(selectScopeItem);
    });
    this.selectScopeItem$.pipe(skip(1)).subscribe((scope) => {
      scope ? this.cardController.loadDataList() : this.cardController.initDataDirectly([0, []]);
    });
  }
}
