import _ from 'lodash';
import { BehaviorSubject, of } from 'rxjs';
import { DataListCompCardController } from '@mdtBsComponents/data-list-comp-card';
import { ModalToggleFullScreenController } from '@mdtBsComponents/modal-toggle-fullscreen';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { dialogApi } from '@mdtDesign/dialog';
import { MenuInfo } from '@mdtDesign/menu';
import { ColorType } from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import { DATAPKG_DELETE_KEY, DATAPKG_UPDATE_KEY, EmitterActionEnum, PkgOperationType } from '@mdtProComm/constants';
import { IDatapkgColumn, IDatapkgPatch, ILabelValue } from '@mdtProComm/interfaces';
import { isInnerDatapkg } from '@mdtProComm/utils/datapkgUtil';
import { getDetailModuleFromUrl } from '@mdtProComm/utils/urlUtil';
import { addCreateDownloadDatapkgTask } from '@mdtProTasks/util';
import { defaultUploadFunc } from '../../components/markdown-editor';
import { defaultLoadFunc } from '../../components/markdown-preview';
import { CreateDatapkgFromSqlController } from '../../containers/create-datapkg-from-sql';
import { DataListTaggroupsController } from '../../containers/data-list-taggroups';
import { MarkdownDatapkgDescriptionController } from '../../containers/markdown-datapkg-description';
import { IDownloadPkgOptions, PopoverPkgDownloadController } from '../../containers/popover-pkg-download';
import { TableDatapkgDesensitizeController } from '../../containers/table-datapkg-desensitize';
import { ILatestMonitor, TableDatapkgMonitorController } from '../../containers/table-datapkg-monitor';
import { XflowDatapkgGenealogyController } from '../../containers/xflow-datapkg-genealogy';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { CreateDatapkgFromSqlModel } from '../../models/CreateDatapkgFromSqlModel';
import { CollaborateListController } from '../collaborate-list';
import { DatapkgDataPreviewController } from '../datapkg-data-preview';
import { DrawerWithinPageController } from '../drawer-within-page';
import { ModifyDatapkgColumnController } from '../modify-datapkg-column';
import { IPkgBoundWfspecControllerOptions, PkgBoundWfspec, PkgBoundWfspecController } from '../pkg-bound-wfspec';
import { UpdateDatapkgController } from '../update-datapkg';
import { UpdateDatapkgModel } from '../update-datapkg/UpdateDatapkgModel';
import { IDatapkgDetail, IDatapkgDetailFactoryModel, IOverviewDataItem } from './DatapkgDetailFactoryModel';
import { ContentMenuKeys, OverviewCardItem } from './DetailContentTabs';

export type IUpdatingDatapkg = Partial<IDatapkgDetail>;

export interface IControllerOptions {
  pkgId: string;
  pkgName?: string;
  uploadMaxMb?: number;
  Model: IDatapkgDetailFactoryModel;
  backFunc?: () => void;
  openEdit?: boolean;
  dataAppUrl?: string;
}
export type IChecklog = [string, ColorType];
export class DatapkgDetailFactoryController {
  private menus: ILabelValue[] = [];
  // 数据概览
  private overviewController: DataListCompCardController<IOverviewDataItem, this>;
  // 数据概览中的标签组
  private tagGroupsController?: DataListTaggroupsController;
  // 数据概览中的主题库
  private themesController?: DataListTaggroupsController;
  // 数据包说明
  private pkgDescriptionController?: MarkdownDatapkgDescriptionController;
  // 质量监控
  private monitorController?: TableDatapkgMonitorController;
  // 表格预览
  private dataPreviewController?: DatapkgDataPreviewController;
  // 字段预览
  private fieldsController?: ModifyDatapkgColumnController;
  // 协作编辑
  private collaborateEditController?: CollaborateListController;
  // 字段脱敏
  private desensitizeController?: TableDatapkgDesensitizeController;
  // 血缘关系
  private genealogyController?: XflowDatapkgGenealogyController;
  // 删除数据包
  private deleteController: ModalWithBtnsCompEmotionController<IDatapkgDetail>;
  // 外链数据包SQL编辑弹窗
  private sqlEditController?: CreateDatapkgFromSqlController;
  private dataEditController?: UpdateDatapkgController;
  private downloadController: PopoverPkgDownloadController;
  private fullScreenController: ModalToggleFullScreenController;
  // 填报
  private fillDesignController?: DrawerWithinPageController<IPkgBoundWfspecControllerOptions>;

  private Model: IDatapkgDetailFactoryModel;
  private app: DatlasAppController<any, any, any>;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  private pkg$ = new BehaviorSubject<IDatapkgDetail>({} as IDatapkgDetail);
  // 菜单列表中选中的菜单key
  private activeMenuKey$ = new BehaviorSubject(ContentMenuKeys.DATA_PREVIEW);
  private isLoading$ = new BehaviorSubject(true);
  private isEmpty$ = new BehaviorSubject(false);
  private userName$?: BehaviorSubject<string>;
  private readonly pkgId: string;
  private uploadMaxMb: number;
  private checklog$ = new BehaviorSubject<ILatestMonitor>(['', 'success', '']);
  private backFunc?: () => void;
  private showOverview$ = new BehaviorSubject(true);
  private enablePublish = false;
  private isApplyingPublish$ = new BehaviorSubject(false);
  private dataAppUrl?: string;
  private operationMap: Record<string, any> = {};
  private columns: IDatapkgColumn[] = [];

  public constructor(app: DatlasAppController<any, any, any>, options: IControllerOptions) {
    this.Model = options.Model;
    this.app = app;
    this.pkgId = options.pkgId;
    this.uploadMaxMb = options.uploadMaxMb || 200;
    this.backFunc = options.backFunc;
    this.dataAppUrl = options.dataAppUrl;

    this.operationMap = {
      [PkgOperationType.DELETE]: this.handleDelete,
    };

    this.overviewController = new DataListCompCardController<IOverviewDataItem, this>({
      dataListCompControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.loadPkgOverviewData,
        },
        compOptions: {
          itemWidth: '100%',
          itemGap: 0,
          itemKey: 'key',
          useVirtual: false,
          CardItemView: OverviewCardItem,
        },
      },
    });
    this.deleteController = new ModalWithBtnsCompEmotionController<IDatapkgDetail>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.downloadController = new PopoverPkgDownloadController({
      downloadPkgFunc: this.downloadDataPkg,
      showExportFilterResult: true,
      defaultExportFilterResult: true,
      loadColumnOptionsFunc: async () => this.columns,
    });
    this.fullScreenController = new ModalToggleFullScreenController({
      uiOptions: { headerTitle: '' },
      modalWrapClassName: 'datapkg-full-screen-modal',
    });

    this.init().then(() => {
      options.openEdit && this.handleEditData();
    });
    this.Model.queryDatapkgApplyStatus().subscribe((applying) => applying && this.isApplyingPublish$.next(true));
  }

  public destroy() {
    this.menus = [];
    this.overviewController.destroy();
    this.tagGroupsController?.destroy();
    this.tagGroupsController = undefined;
    this.themesController?.destroy();
    this.themesController = undefined;
    this.pkgDescriptionController?.destroy();
    this.pkgDescriptionController = undefined;
    this.monitorController?.destroy();
    this.monitorController = undefined;
    this.dataPreviewController?.destroy();
    this.dataPreviewController = undefined;
    this.fieldsController?.destroy();
    this.fieldsController = undefined;
    this.collaborateEditController?.destroy();
    this.collaborateEditController = undefined;
    this.desensitizeController?.destroy();
    this.desensitizeController = undefined;
    this.genealogyController?.destroy();
    this.genealogyController = undefined;
    this.deleteController.destroy();
    this.sqlEditController?.destroy();
    this.sqlEditController = undefined;
    this.dataEditController?.destroy();
    this.dataEditController = undefined;
    this.downloadController.destroy();
    this.fullScreenController.destroy();
    this.fillDesignController?.destroy();
    this.fillDesignController = undefined;
    this.Model.destroy();
    this.Model = null!;
    this.app = null!;
    this.pkg$.complete();
    this.activeMenuKey$.complete();
    this.isLoading$.complete();
    this.isEmpty$.complete();
    this.userName$?.complete();
    this.userName$ = undefined;
    this.checklog$.complete();
    this.backFunc = undefined;
    this.showOverview$.complete();
    this.isApplyingPublish$.complete();
    this.operationMap = {};
    this.columns = [];
  }

  public getIsApplyingPublish$() {
    return this.isApplyingPublish$;
  }

  public getEnablePublish() {
    return this.enablePublish;
  }

  public getFullScreenController() {
    return this.fullScreenController;
  }

  public getDownloadController() {
    return this.downloadController;
  }

  public getShowOverview$() {
    return this.showOverview$;
  }

  public changeShowOverview(val: boolean) {
    return this.showOverview$.next(val);
  }

  public getBackFunc() {
    return this.backFunc;
  }

  public getMenus() {
    return this.menus;
  }

  public getChecklog$() {
    return this.checklog$;
  }

  public getUserName$() {
    return this.userName$!;
  }

  public getIsLoading$() {
    return this.isLoading$;
  }

  public getIsEmpty$() {
    return this.isEmpty$;
  }

  public getPkg$() {
    return this.pkg$;
  }

  public getOverviewController() {
    return this.overviewController;
  }

  public getTagGroupsController() {
    return this.tagGroupsController!;
  }

  public getThemesController() {
    return this.themesController!;
  }

  public getPkgDescriptionController() {
    return this.pkgDescriptionController!;
  }

  public getDescController() {
    return this.pkgDescriptionController!;
  }

  public getMonitorController() {
    return this.monitorController!;
  }

  public getTableController() {
    return this.dataPreviewController!;
  }

  public getColumnController() {
    return this.fieldsController!;
  }

  public getCollaborateEditController() {
    return this.collaborateEditController!;
  }

  public getDesensitizeController() {
    return this.desensitizeController!;
  }

  public getGenealogyController() {
    return this.genealogyController!;
  }

  public getDeleteController() {
    return this.deleteController;
  }

  public getFillDesignController() {
    return this.fillDesignController;
  }

  public getDataEditController() {
    return this.dataEditController!;
  }

  // 外链数据包编辑SQL controller
  public getSqlEditController() {
    return this.sqlEditController!;
  }

  public getActiveMenuKey$() {
    return this.activeMenuKey$;
  }

  public changeActiveMenuKey$(key: ContentMenuKeys) {
    this.activeMenuKey$.next(key);
  }

  // 修改定时刷新
  public handleSaveRefreshtimer = async (timer: string) => {
    return this.updateDatapkgMeta({ refresh_timer: timer || null });
  };

  // 修改下次刷新
  public handleSaveNextRefreshTime = async (next?: number | null) => {
    if (next === this.pkg$.getValue().nextRefreshTime) {
      return true;
    }
    return this.updateDatapkgMeta({ next_refresh_time: next || null });
  };

  // 修改数据包名称
  public handleSaveName = async (name: string) => {
    if (!name || name === this.pkg$.getValue().name) {
      return true;
    }
    return this.updateDatapkgMeta({ name });
  };

  // 编辑数据
  public handleEditSql = () => {
    const pkg = this.pkg$.getValue();
    this.sqlEditController!.openModal({
      pkgId: pkg.id,
      name: pkg.name,
      sql: pkg.sql || '',
      datasource: pkg.datasetId,
      geometryType: pkg.geometryType,
      keyColumns: pkg.keyColumns as Record<string, string>,
      ownership: pkg.ownership,
    }).subscribe((result) => {
      result.success && this.init();
    });
  };

  public handlePublish = () => {
    dialogApi.warning({
      title: i18n.chain.proMicroModules.datapkgDetail.personalDataToOrg,
      description: i18n.chain.proMicroModules.datapkgDetail.personalDataToOrgDesc,
      okText: i18n.chain.comButton.confirm,
      cancelText: i18n.chain.comButton.cancel,
      onOk: (_e: any, onClose?: () => void) => {
        this.Model.applyPublishToApp().subscribe((success) => {
          if (success) {
            toastApi.success(i18n.chain.proMicroModules.datapkgDetail.submitApplay);
            this.isApplyingPublish$.next(true);
          }
        });
        onClose?.();
      },
    });
  };

  // 编辑数据
  public handleEditData = () => {
    const pkg = this.pkg$.getValue();
    if (pkg.enableSqlEdit) {
      this.handleEditSql();
      return;
    }
    this.dataEditController?.openModal({
      pkgId: this.pkgId,
      pkgName: pkg.name,
      datasetId: this.app!.getDatasetsController()!.getAppDatasetId(),
      ownership: pkg.ownership,
      geometryType: pkg.geometryType,
    });
  };

  // 立即刷新
  public handleRefresh = async () => {
    if (this.pkg$.getValue().isOutSql) {
      const result = await this.Model.refreshDatapkg().toPromise();
      if (!result) {
        toastApi.error(i18n.chain.proMicroModules.datapkgDetail.dataUpdateFailed);
        return;
      }
    }
    this.fieldsController?.destroy();
    this.tagGroupsController?.destroy();
    this.themesController?.destroy();
    this.monitorController?.destroy();
    this.collaborateEditController?.destroy();
    this.desensitizeController?.destroy();
    this.genealogyController?.destroy();
    await this.init(true);
    toastApi.success(i18n.chain.proMicroModules.datapkgDetail.dataUpdateSuccess);
  };

  public handleMoreOperation = (info: MenuInfo) => {
    this.operationMap[info.key]?.();
  };

  // 数据填报
  public handleFill = () => {
    return this.fillDesignController?.openModal({
      app: this.app,
      pkgId: this.pkgId,
      pkgName: this.pkg$.getValue().name,
    });
  };

  private async updateDatapkgMeta(meta: IDatapkgPatch) {
    const pkgId = this.pkg$.getValue().id;
    const isSuccess = await this.Model.updateDatapkg(meta).toPromise();
    if (!isSuccess) {
      toastApi.error(i18n.chain.proMicroModules.datapkgDetail.updateFailed);
      return false;
    }
    toastApi.success(i18n.chain.proMicroModules.datapkgDetail.updateSuccess);

    const newPkg = { ...this.pkg$.getValue(), ...meta };
    this.pkg$.next(newPkg);
    const emitter = this.app.getEmitterController()?.getEmitterIns();
    emitter?.emit(DATAPKG_UPDATE_KEY, {
      action: EmitterActionEnum.UPDATE_PKG,
      value: { pkgId, updatingObj: meta },
    });
    return true;
  }

  // 打开删除弹窗
  private handleDelete = () => {
    this.deleteController.openModal(this.pkg$.getValue()).subscribe((resp) => {
      if (!resp.success) return;
      const emitter = this.app.getEmitterController()?.getEmitterIns();
      emitter?.emit(DATAPKG_DELETE_KEY, {
        action: EmitterActionEnum.DEL_PKG,
        value: { pkgId: resp.result!.id },
      });
      this.backFunc?.();
    });
  };

  private loadColumns = (columns: IDatapkgColumn[]) => {
    return of(columns);
  };

  // 确定删除
  private deleteDataToService = async (data?: IDatapkgDetail) => {
    const { name } = data!;
    const resp = await this.Model.deleteDatapkg().toPromise();
    resp && toastApi.success(i18n.chain.proMicroModules.datapkgDetail.delPkg(name));
    return { success: !!resp, result: data };
  };

  private initDeleteModalOptions = (): IEmotionProps => {
    return {
      emotion: 'alert',
      title: i18n.chain.proMicroModules.datapkgDetail.delPkgConfirm(this.pkg$.getValue().name),
      description: i18n.chain.proMicroModules.datapkgDetail.delPkgConfirmDesc,
    };
  };

  private loadPkgOverviewData = () => {
    return this.Model.transformPkgToOverviewData(this.pkg$.getValue());
  };

  private initMenus() {
    const pkg = this.pkg$.getValue();
    const menus = [];
    pkg.enableDataPreviewMenu &&
      menus.push({
        value: ContentMenuKeys.TABLE_PREVIEW,
        label: i18n.chain.proMicroModules.datapkgDetail.tablePreview,
      });
    pkg.enableFieldsMenu &&
      menus.push({
        value: ContentMenuKeys.FIELD_PREVIEW,
        label: i18n.chain.proMicroModules.datapkgDetail.filedPreview,
      });
    pkg.enableDescriptionMenu &&
      menus.push({
        value: ContentMenuKeys.PKG_DESCRIPTION,
        label: i18n.chain.proMicroModules.datapkgDetail.pkgDesc,
      });
    pkg.enableMonitorMenu &&
      menus.push({
        value: ContentMenuKeys.PKG_MONITOR,
        label: i18n.chain.proMicroModules.datapkgDetail.pkgMonitor,
      });
    pkg.enableCollaborateMenu &&
      menus.push({
        value: ContentMenuKeys.COLLABORATE_EDIT,
        label: i18n.chain.proMicroModules.datapkgDetail.collaborateEdit,
      });
    pkg.enableDesensitizeMenu &&
      menus.push({
        value: ContentMenuKeys.FIELD_DESENSITIZE,
        label: i18n.chain.proMicroModules.datapkgDetail.fieldDesensitize,
      });
    pkg.enableGenealogyMenu &&
      menus.push({
        value: ContentMenuKeys.GENEALOGY,
        label: i18n.chain.proMicroModules.datapkgDetail.genealogy,
      });

    if (_.size(menus)) {
      this.menus = menus;
      this.activeMenuKey$.next(menus[0].value);
    }
  }

  private downloadDataPkg = async ({
    downloadType,
    exportGeoField,
    geoFormat,
    exportFilterResult,
    columns,
    column_mapping,
  }: IDownloadPkgOptions) => {
    const pkgName = this.pkg$.getValue().name;
    addCreateDownloadDatapkgTask(this.pkgId, pkgName, {
      file_type: downloadType,
      geo: exportGeoField,
      geometry_format: geoFormat,
      condition: exportFilterResult ? this.dataPreviewController!.getFilerController().getFilterList$().getValue() : [],
      only: columns,
      column_mapping,
    });
    return true;
  };

  // eslint-disable-next-line sonarjs/cognitive-complexity
  private init = async (isRefresh?: boolean) => {
    this.isLoading$.next(true);
    const { datapkgDetail, columns, themes } = await this.Model.queryDatapkgData().toPromise();
    if (!datapkgDetail.id) {
      this.isEmpty$.next(true);
      this.isLoading$.next(false);
      return;
    }
    this.columns = columns;
    this.enablePublish =
      datapkgDetail.isPersonal &&
      !datapkgDetail.isAppExternal &&
      this.app.getUserPermissionController().getDatapkgPermission().enablePersonalPublish;
    const isAppSelf = !datapkgDetail.isAppExternal && !datapkgDetail.isPersonal;
    this.userName$ = datapkgDetail.userName
      ? new BehaviorSubject(datapkgDetail.userName)
      : this.app.getNameCacheController()!.getUserName$(datapkgDetail.userId);
    this.pkg$.next(datapkgDetail);

    const subModels = this.Model.initSubModels(datapkgDetail, columns, themes);

    // 数据概览
    this.overviewController.loadDataList();

    // 表格预览
    const { id, name, geometryType } = datapkgDetail;
    datapkgDetail.enableDataPreviewMenu &&
      (this.dataPreviewController = new DatapkgDataPreviewController(subModels.datapkgDataPreviewModel, {
        pkgId: datapkgDetail.id,
        hasSearch: datapkgDetail.enableDataSearch,
        enableQueryName: datapkgDetail.enableQueryName,
        columns,
        useRuleForm: true,
        hasPreviewGeometryData: datapkgDetail.enablePreviewGeometryData,
        previewGeometryUrl: this.app.getPreviewGeoDataHref(
          {
            packageUuid: id,
            geometryType,
            objectType: name,
          },
          this.dataAppUrl,
        ),
      }));

    // 字段预览
    const isInnerPkg = isInnerDatapkg(datapkgDetail.connRole);
    datapkgDetail.enableFieldsMenu &&
      (this.fieldsController = new ModifyDatapkgColumnController(subModels.fieldsModel, {
        pkgId: this.pkgId,
        keyColumns: datapkgDetail.keyColumns,
        columns,
        enableEditColumnType: !datapkgDetail.isOutSql,
        enableEditColumnName: isInnerPkg,
        enableDeleteColumn: isInnerPkg,
        enableModifyDDL: isInnerPkg,
        enableEdit: datapkgDetail.enableFieldEdit,
        onModifiedFunc: () => {
          this.handleRefresh();
        },
      }));

    // 标签、主题库
    if (isAppSelf) {
      this.tagGroupsController = new DataListTaggroupsController({
        Model: subModels.taggroupsSelectModel,
        enableEdit: datapkgDetail.enableThemeTagEdit,
      });

      this.themesController = new DataListTaggroupsController({
        Model: subModels.themesSelectModel,
        enableEdit: datapkgDetail.enableThemeTagEdit,
        type: i18n.chain.proMicroModules.datapkgDetail.theme,
      });
    }

    // 文档说明
    datapkgDetail.enableDescriptionMenu &&
      (this.pkgDescriptionController = new MarkdownDatapkgDescriptionController({
        pkgId: datapkgDetail.id,
        content: datapkgDetail.descContent,
        templateId: datapkgDetail.descTemplateId,
        enableEdit: datapkgDetail.enableDescriptionEdit,
        Model: subModels.datapkgDescModel,
        uploadFileFunc: defaultUploadFunc,
        loadFileFunc: defaultLoadFunc,
      }));

    // 质量监控
    if (datapkgDetail.enableMonitorMenu) {
      this.monitorController = new TableDatapkgMonitorController(
        datapkgDetail.enableMonitorEdit,
        subModels.datapkgMonitorModel!,
      );
      this.monitorController.getChecklog$().subscribe((val) => {
        this.checklog$.next(val);
      });
    }

    // 协作编辑
    datapkgDetail.enableCollaborateMenu &&
      (this.collaborateEditController = new CollaborateListController({
        app: this.app,
        pkgId: datapkgDetail.id,
        Model: subModels.datapkgCollaborateModel!,
        isCollaborateDatapkg: datapkgDetail.isCollaborateDatapkg,
        enableEdit: datapkgDetail.enableCollaborateEdit,
      }));

    // 字段脱敏
    datapkgDetail.enableDesensitizeMenu &&
      (this.desensitizeController = new TableDatapkgDesensitizeController(subModels.datapkgDesensitizeModel!));

    // sql编辑
    datapkgDetail.enableSqlEdit &&
      (this.sqlEditController = new CreateDatapkgFromSqlController({
        Model: new CreateDatapkgFromSqlModel(),
      }));

    // 数据编辑
    datapkgDetail.enableDataEdit &&
      (this.dataEditController = new UpdateDatapkgController({
        Model: UpdateDatapkgModel,
        appId: this.app.getAppId(),
        maxUploadMb: this.uploadMaxMb,
        columns,
        keyColumns: datapkgDetail.keyColumns,
        onFileUploadSuccessFunc: () => {
          this.handleRefresh();
        },
        goMapEditorFunc: (pkgId, mode) => {
          this.app!.jumpToProductMapEditor({ pkgId: this.pkgId, mode });
        },
      }));

    // 血缘图
    datapkgDetail.enableGenealogyMenu &&
      (this.genealogyController = new XflowDatapkgGenealogyController({
        pkgId: datapkgDetail.id,
        pkgName: datapkgDetail.name,
        enableEdit: datapkgDetail.enableGenealogyEdit,
        Model: subModels.datapkgGenealogyModel!,
      }));

    datapkgDetail.enableDataEdit &&
      (this.fillDesignController = new DrawerWithinPageController({
        InnerView: PkgBoundWfspec,
        InnerViewController: PkgBoundWfspecController,
        Model: subModels.pkgBoundWfspecModel,
        width: '49vw',
        title: i18n.chain.proMicroModules.datapkgDetail.fillData,
      }));

    const detailModule = getDetailModuleFromUrl();
    detailModule &&
      _.includes(_.values(ContentMenuKeys), detailModule) &&
      this.activeMenuKey$.next(detailModule as ContentMenuKeys);

    !isRefresh && this.initMenus();
    this.isLoading$.next(false);
  };
}
