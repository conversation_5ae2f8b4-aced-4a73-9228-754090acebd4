import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { IOrgs } from '@mdtProComm/interfaces';
import { getCurrentOrgs } from '@mdtProComm/utils/orgUtil';
import { GrantedOtFormController } from '../granted-ot-form/GrantedOtFormController';
import { type IOneTableNewGrantedFormModel } from './OneTableNewGrantedFormModel';

interface IStatus {
  scopeApp?: boolean;
  checked?: boolean;
  loading?: boolean;
}

export class OneTableNewGrantedOrgFormController extends GrantedOtFormController {
  private hasPermission$ = new BehaviorSubject<IStatus>({ loading: true });

  public destroy() {
    super.destroy();
    this.hasPermission$.complete();
  }

  public getHasPermission$() {
    return this.hasPermission$;
  }

  protected async init() {
    const model = this.Model as unknown as IOneTableNewGrantedFormModel;
    model.queryScopeData(this.getCancelToken()).subscribe((resp) => {
      this.hasPermission$.next({ checked: resp.hasPermission, scopeApp: resp.scopeApp });
      // 没有权限，不展示
      if (!resp.hasPermission) return;
      if (resp.scopeApp) {
        super.init();
        return;
      }
      // 处理组织
      this.initOrgTree(resp.orgList);
    });
  }

  private getAllSubOrg(cacheMap: Record<string, string>, allData: IOrgs[], rootId: string) {
    const sub = getCurrentOrgs(allData, rootId);
    _.forEach(sub, ({ key }: IOrgs) => {
      cacheMap[key] = key;
      this.getAllSubOrg(cacheMap, allData, key);
    });
  }

  // 初始化组织和角色列表
  private initSubOrgAndRoleList = (subOrgIds: string[]) => {
    this.isOrgLoading$.next(true);
    return this.Model.queryRolesAndOrgs(this.appId, this.rootKey)
      .pipe(
        map(({ orgList }) => {
          if (!this.orgController) return;
          const rootMap: Record<string, string> = {};
          const cacheMap: Record<string, string> = {};
          _.forEach(subOrgIds, (it) => {
            cacheMap[it] = it;
            rootMap[it] = it;
            this.getAllSubOrg(cacheMap, orgList, it);
          });
          const subOrgTree = _.filter(orgList, ({ key }: IOrgs) => cacheMap[key]) as IOrgs[];
          const orgIdNameMap: Record<string, string> = {};
          _.forEach(subOrgTree, (it) => {
            orgIdNameMap[it.key] = it.title;
            if (!rootMap[it.key]) return;
            it.pid = `app_${this.appId}`;
          });
          this.orgIdNameMap = orgIdNameMap;
          this.orgController.init(subOrgTree, subOrgIds[0]);
          this.selectedOrg$.next({ isRoot: false, value: subOrgIds[0] });
          this.isOrgLoading$.next(false);
          return true;
        }),
      )
      .subscribe();
  };

  private initOrgTree(subOrgIds: string[]) {
    // 默认选择第一个
    this.selectedOrg$.next({ isRoot: false, value: subOrgIds[0] });
    this.initSubOrgAndRoleList(subOrgIds);
    this.getSelectedOrgReleationData();
    this.initModify();
  }
}
