import _ from 'lodash';
import { from, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { IPaginationQuery, IUserQuery } from '@mdtApis/interfaces';
import { ROLE_DATA } from '@mdtBsBffServices/role';
import { USER_DATA } from '@mdtBsBffServices/user';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { ResourceShareBffService } from '@mdtProComm/bff-services';
import { IFormData } from '../../containers/drawer-modify-form-role-group';
import { UserLazySelectorModel } from '../../containers/user-lazy-selector';
import { IGroup, IResourceShareMenuModel } from '../resource-share-menu';

export class MenuModelBff implements IResourceShareMenuModel {
  private appId: number;
  private userId: number;
  private userUuid: string;

  public constructor(appId: number, userId: number, userUuid: string) {
    this.appId = appId;
    this.userId = userId;
    this.userUuid = userUuid;
  }

  public getUserSelectorModel() {
    return new UserLazySelectorModel({ appId: this.appId, userKey: 'uuid' });
  }

  public queryUsers(params: IUserQuery): Observable<[number, TreeLabelValueItemInterface[]]> {
    return from(
      ResourceShareBffService.queryUsersWithTotalCount({
        appId: this.appId,
        query: { page_size: 100, page_num: 0, ...params },
        respData: USER_DATA,
      }),
    ).pipe(
      map((resp) => {
        if (!resp.success) return [0, []];
        const data = _.map(resp.page_data, (user) => ({
          title: user.name,
          key: `${user.uuid}`,
        }));
        return [resp.total_count || 0, _.reject(data, ['key', `${this.userUuid}`])];
      }),
    );
  }

  public queryNextUsers(params: IPaginationQuery): Observable<TreeLabelValueItemInterface[]> {
    return from(
      ResourceShareBffService.queryUsers({
        appId: this.appId,
        query: params,
        respData: USER_DATA,
      }),
    ).pipe(
      map((resp) => {
        if (!resp.success) return [];
        const data = _.map(resp.page_data, (user) => ({
          title: user.name,
          key: `${user.uuid}`,
        }));
        return _.reject(data, ['key', `${this.userUuid}`]);
      }),
    );
  }

  public queryGroups(): Observable<IGroup[]> {
    const params = { user_must_in: true, app_id: this.appId };
    return from(ResourceShareBffService.queryGroups({ params, respData: ROLE_DATA })).pipe(
      map((resp) => {
        if (!resp.success) return [];
        return _.map(resp.page_data, (group) => ({
          name: group.name,
          id: group.id,
          description: group.description || '',
        }));
      }),
    );
  }

  public queryGroupUsers(groupId: number): Observable<TreeLabelValueItemInterface[]> {
    return from(ResourceShareBffService.queryUsersOfRoles({ params: { role_ids: `${groupId}` } })).pipe(
      map((resp) => {
        if (!resp.success) return [];
        const data = _.map(resp.page_data, (user) => ({
          title: user.name,
          key: `${user.uuid}`,
        }));
        return _.reject(data, ['key', `${this.userUuid}`]);
      }),
    );
  }

  public modifyGroup(formData: IFormData, oData?: IGroup): Observable<IBusinessResult<IGroup>> {
    const querys = { name: formData.name, description: formData.description, app_id: this.appId, role_type: 'group' };
    const oIds = _.map(oData?.users, 'key') as string[];
    const nIds = _.map(formData.users, 'id') as string[];

    if (!oData) {
      return from(
        ResourceShareBffService.createRole({ data: { ...querys, users: _.uniq([...nIds, `${this.userUuid}`]) } }),
      ).pipe(
        map((resp) => {
          return { success: resp.success };
        }),
      );
    } else {
      return from(ResourceShareBffService.updateRole({ data: { ...querys, id: oData.id } })).pipe(
        switchMap((resp) => {
          if (!resp.success) return of({ success: false });
          const nUsers = _.difference(nIds, oIds);
          const dUsers = _.difference(oIds, nIds);
          if (_.isEmpty(nUsers) && _.isEmpty(dUsers)) return of({ success: true });
          const data = {
            new_users: _.isEmpty(nUsers) ? undefined : nUsers,
            del_users: _.isEmpty(dUsers) ? undefined : dUsers,
          };
          return from(
            ResourceShareBffService.updateUsersOfRole({
              data: {
                role_id: oData.id,
                app_id: this.appId,
                ...data,
              },
            }),
          ).pipe(
            map((uResp) => {
              return { success: uResp.success };
            }),
          );
        }),
      );
    }
  }

  public deleteGroup(item: IGroup): Observable<IBusinessResult<IGroup>> {
    return from(ResourceShareBffService.deleteRoles({ roleIds: `${item.id}` })).pipe(
      map((resp) => ({ success: resp.success })),
    );
  }
}
