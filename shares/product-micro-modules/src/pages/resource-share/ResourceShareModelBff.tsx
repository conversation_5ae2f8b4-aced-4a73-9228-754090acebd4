import { AbstractAppController } from '../../controllers/AbstractAppController';
import { ResourceShareListModelBff } from '../resource-share-list/ResourceShareListModelBff';
import { MenuModelBff } from './MenuModelBff';

export class ResourceShareModelBff {
  public static getSubModels(app: AbstractAppController) {
    return {
      menuModel: new MenuModelBff(app.getAppId(), app.getUserId(), app.getUserUuid()),
      listModel: new ResourceShareListModelBff({
        app: app,
      }),
    };
  }
}

export type IResourceShareModel = typeof ResourceShareModelBff;
