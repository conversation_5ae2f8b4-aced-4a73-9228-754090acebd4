import { AbstractAppController } from '../../controllers/AbstractAppController';
import { ResourceShareListModel } from '../resource-share-list';
import { MenuModel } from './MenuModel';

export class ResourceShareModel {
  public static getSubModels(app: AbstractAppController) {
    return {
      menuModel: new MenuModel(app.getAppId(), app.getUserId(), app.getUserUuid()),
      listModel: new ResourceShareListModel({
        app: app,
      }),
    };
  }
}

export type IResourceShareModel = typeof ResourceShareModel;
