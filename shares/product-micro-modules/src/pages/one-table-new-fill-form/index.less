.one-table-new-fill-form {
  width: 100%;
  height: 100%;

  .footer {
    .footer-more-btn {
      position: absolute;
      top: 0;
      right: -10px;
    }
  }

  .block-ui {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
    width: 100vw;
    height: 100vw;

    .metro-spin-loading {
      display: none;
    }
  }

  .table-block {
    position: relative;
    display: flex;
    flex-direction: column;
    height: calc(100% - 3px); // 外层有1px的padding
    padding-right: 40px;

    .data-edit-tip {
      height: 32px;
      font-weight: 500;
      line-height: 28px;

      .tip {
        margin-left: 6px;
        color: var(--metro-text-2);
        font-weight: 400;
        font-size: 13px;
      }
    }

    .btns {
      position: absolute;
      top: 0;
      right: 200px;
    }

    .page_datapkg-data-preview_tool-bar {
      position: absolute;
      top: -34px;
      right: 12px;
      z-index: 2;
    }
  }

  .form-block {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 0 20px 0 40px;

    .header {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      margin-bottom: 16px;

      .title {
        font-weight: 500;
        font-size: 18px;
      }

      .tip {
        flex: 1;
        height: 27px;
        padding-left: 8px;
      }

      .tip-only-form {
        position: absolute;
        top: 22px;
        right: 0;
      }

      .data-tip {
        position: absolute;
        bottom: -14px;
        color: var(--metro-text-2);
        font-size: 12px;

        > span {
          color: var(--metro-text-1);
          font-weight: 500;
        }
      }
    }

    .form {
      flex: 1;
      flex-grow: 1;
      min-height: 0;

      .metro-form-view {
        padding-right: 20px;
      }
    }

    .only-form {
      .metro-form-view {
        padding-right: unset;
      }
    }
  }
}
