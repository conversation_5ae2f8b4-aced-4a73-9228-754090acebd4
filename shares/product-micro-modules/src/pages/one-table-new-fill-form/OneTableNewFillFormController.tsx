import _ from 'lodash';
import { createRef } from 'react';
import { toJS } from '@formily/reactive';
import { Download } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Divider } from '@metroDesign/divider';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import { IPaginationParams } from '@mdtBsControllers/data-list-controller';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFormSpecRefHandle } from '../../components/form-view';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import type { IOneTableNewOperatorDataComm } from '../../interfaces';
import i18n from '../../languages';
import { DataTableActionEnum } from '../../pages/one-table-new-data-table';
import { OneTableNewReportDetailModel } from '../../pages/one-table-new-report-detail/OneTableNewReportDetailModel';
import {
  BooleanEnum,
  COLUMN_FLAG,
  DataTableTypeEnum,
  getProcessRowsOptions,
  processRows,
} from '../../utils/oneTableNewUtil';
import { ignoreFrontendColumns } from '../../utils/oneTableNewUtil';
import { OneTableNewFillFormDataTableController } from './OneTableNewFillFormDataTableController';
import { IOneTableNewFillFormModel } from './OneTableNewFillFormModel';

export interface ISubmitConfirmOptions {
  onlySaveCallBack: () => void;
  submitSuccessCallback: () => void;
  deleteDataCallback?: () => void;
}

export interface IControllerOptions {
  Model: IOneTableNewFillFormModel;
  itemData: IOneTableNewOperatorDataComm;
  rootWfId?: string;
  submitConfirmOptionsFunc: (...args: any[]) => ISubmitConfirmOptions;
  onlyForm?: boolean; // 只展示填报表单
  defaultValue?: Record<string, any>; // 展示当前填报的数据
  /** 根据defaultValue直接复制一条新数据进入编辑状态 */
  directCopy?: boolean;
  paginationParams?: IPaginationParams;
  requestParams?: Record<string, any>;
  firstRequestParams?: Record<string, any>;
  secretVisible?: boolean;
}

interface ISaveResult {
  noChanged?: boolean;
  row?: Record<string, any>;
}

export interface IBlockUi {
  loading: boolean;
  callback?: () => void;
}

export class OneTableNewFillFormController extends RequestController {
  public static async create(
    options: Omit<IControllerOptions, 'itemData'>,
  ): Promise<OneTableNewFillFormController | null> {
    let itemData: IOneTableNewOperatorDataComm | undefined;
    if (!itemData && options.rootWfId) {
      const managementData = await OneTableNewReportDetailModel.getManagementForm(options.rootWfId);
      itemData = (managementData?.formData ?? undefined) as IOneTableNewOperatorDataComm | undefined;
    }
    if (!itemData) return null;
    return new OneTableNewFillFormController({ ...options, itemData });
  }

  private Model: IOneTableNewFillFormModel;
  private itemData: IOneTableNewOperatorDataComm;
  private dataStatus$ = new BehaviorSubject<string[]>([]);
  // 当前编辑第几行
  private currendIndex$ = new BehaviorSubject(-1);
  private blockUi$ = new BehaviorSubject<IBlockUi>({ loading: false });
  // 是否有下一条
  private hasNext$ = new BehaviorSubject(false);
  // 是否有上一条
  private hasPre$ = new BehaviorSubject(false);
  // 数据源数据(目前先一次性加载1w条数据)
  private dataTableController: OneTableNewFillFormDataTableController;
  // 表单索引
  private formRef = createRef<IFormSpecRefHandle>();
  // 表单Id
  private formProps$: BehaviorSubject<Record<string, any>>;
  // 当前表单值
  private currentFormValues$ = new BehaviorSubject<Record<string, any>>({});
  private orginFormValues$ = new BehaviorSubject<Record<string, any>>({});
  private submitConfirmOptionsFunc?: () => ISubmitConfirmOptions;
  private showCompleteBtn?: boolean;
  private isFormManageLevelUser: boolean;
  private onlyForm?: boolean;
  private isSaving$ = new BehaviorSubject<boolean>(false);
  private enableDelete$ = new BehaviorSubject<boolean>(false);
  private enableCopy$ = new BehaviorSubject<boolean>(false);
  private deleteStatus$ = new BehaviorSubject<boolean>(false);
  private actionVisible$ = new BehaviorSubject<boolean>(false);

  public constructor(options: IControllerOptions) {
    super();
    const { itemData } = options;
    if (!itemData) throw new Error('itemData is required for OneTableNewFillFormController');
    this.Model = options.Model;
    this.itemData = itemData;
    this.onlyForm = options.onlyForm;
    this.submitConfirmOptionsFunc = options.submitConfirmOptionsFunc;
    const { isFormManageLevelUser, formOwner } = itemData;
    this.formProps$ = new BehaviorSubject<Record<string, any>>({
      ...itemData.formSpec,
      // 创建者不需要代理
      owner: DatlasAppController.getInstance().getUserId() !== formOwner.userId ? formOwner : undefined,
    });
    this.showCompleteBtn = isFormManageLevelUser ? false : true;
    this.isFormManageLevelUser = isFormManageLevelUser;
    const controller = new OneTableNewFillFormDataTableController({
      dataTableType: DataTableTypeEnum.FILL,
      itemData: itemData,
      modifyItemData: this.innerModifyItemData,
      firstRequestParams: options.firstRequestParams,
      requestParams: options.requestParams,
      dataPreviewOptions: {
        initialPageNum: options.paginationParams?.page_num,
        pkgId: itemData.pkgId,
        hasBlurSearch: true,
        secretVisible: options.secretVisible,
        renderOtherHeaderBtns: () => {
          return (
            <>
              <Divider type="vertical" style={{ marginLeft: 16 }} />
              <Button size="small" icon={<Download />} onClick={this.openBatchDialog}>
                {i18n.chain.proMicroModules.oneTable.btnBatchFill}
              </Button>
            </>
          );
        },
      },
    });
    this.dataTableController = controller;
    this.initReletion(options.defaultValue, options.directCopy);
    this.cacluateStatus();
  }

  public destroy() {
    super.destroy();
    this.submitConfirmOptionsFunc = undefined;
    this.formRef = null!;
    this.itemData = null!;
    this.blockUi$.complete();
    this.blockUi$.next({ loading: false });
    this.dataStatus$.complete();
    this.currendIndex$.complete();
    this.hasNext$.complete();
    this.hasPre$.complete();
    this.dataTableController.destroy();
    this.dataStatus$.complete();
    this.formProps$.complete();
    this.formProps$.next({});
    this.currentFormValues$.complete();
    this.currentFormValues$.next({});
    this.orginFormValues$.complete();
    this.orginFormValues$.next({});
    this.isSaving$.complete();
    this.enableDelete$.complete();
    this.enableCopy$.complete();
    this.deleteStatus$.complete();
    this.actionVisible$.complete();
  }

  public getItemData() {
    return this.itemData;
  }

  public getActionVisible$() {
    return this.actionVisible$;
  }

  public setActionVisible(visible: boolean) {
    this.actionVisible$.next(visible);
  }

  public getEnableDelete$() {
    return this.enableDelete$;
  }

  public getEnableCopy$() {
    return this.enableCopy$;
  }

  public getDeleteStatus$() {
    return this.deleteStatus$;
  }

  public getOnlyForm() {
    return this.onlyForm;
  }

  public getShowCompleteBtn() {
    return this.showCompleteBtn;
  }

  public getIsSaving$() {
    return this.isSaving$;
  }

  public changeFormValues = (values: Record<string, any>) => {
    this.currentFormValues$.next(toJS(values));
  };

  public getFormRef() {
    return this.formRef;
  }

  public getBlockUi$() {
    return this.blockUi$;
  }

  public getCurrentEditData$() {
    return this.orginFormValues$;
  }

  public getCurrentIndex$() {
    return this.currendIndex$;
  }

  public getFormProps$() {
    return this.formProps$;
  }

  public getDataStatus$() {
    return this.dataStatus$;
  }

  public getHasNext$() {
    return this.hasNext$;
  }

  public getHasPre$() {
    return this.hasPre$;
  }

  public getDataTableController() {
    return this.dataTableController;
  }

  public editPreData() {
    const index = this.currendIndex$.getValue() - 1;
    const tc = this.dataTableController;
    const nowPage = tc.getCurrentPageValue();
    const pageSize = tc.getPageSize();
    // 符合切换到上一页
    if (index === -1 && nowPage > 0) {
      this.blockUi$.next({
        loading: true,
        callback: () => {
          this.changeFormValueToNextIndex(pageSize - 1);
        },
      });
      tc.clickPagination(nowPage - 1);
    } else {
      this.changeFormValueToNextIndex(index);
    }
  }

  public async saveEditPreData() {
    await this.saveFormData();
    this.editPreData();
  }

  public editNextData() {
    const index = this.currendIndex$.getValue() + 1;
    const tc = this.dataTableController;
    const nowTotal = tc.getDataListValue().length;
    const hasNextPageValue = tc.getHasNextPageValue();
    // 符合切换到下一页
    if (index >= nowTotal && hasNextPageValue) {
      this.blockUi$.next({
        loading: true,
        callback: () => {
          this.changeFormValueToNextIndex(0);
        },
      });
      tc.clickPagination(tc.getCurrentPageValue() + 1);
    } else {
      this.changeFormValueToNextIndex(index);
    }
  }

  public async saveEditNextData() {
    await this.saveFormData();
    this.editNextData();
  }

  public openBatchDialog = async () => {
    // 如果不是动态引用，单独使用模块会有引用依赖报错（fill-form-controller -> onetablenew -> preview-form-controller -> fill-form-controller）
    const { doOneTableNewOperatorBatchFillForm } = await import('../../shared/onetablenew');
    doOneTableNewOperatorBatchFillForm({
      itemData: this.itemData,
      tableColumns: this.dataTableController.getOriginTableColumns(),
      onBatchSuccess: () => {
        this.dataTableController.loadDataList();
      },
    });
  };

  public doFinished = async () => {
    const status = this.dataStatus$.getValue();
    if (!_.isEmpty(status) && status[0] === 'error') {
      toastApi.error(status[1]);
      return;
    }
    // 如果不是动态引用，单独使用模块会有引用依赖报错（fill-form-controller -> onetablenew -> submit-confirm-controller -> fill-form-controller）
    const { doOneTableNewOperatorSubmitTask } = await import('../../shared/onetablenew');
    doOneTableNewOperatorSubmitTask({
      itemData: this.itemData,
      showSubmitBtn: true,
      showBackBtn: true,
      showSaveBtn: true,
      ...(this.submitConfirmOptionsFunc?.() || {}),
    });
  };

  public saveFormData = async (resetForm?: boolean) => {
    if (this.isSaving$.getValue()) return;

    try {
      this.isSaving$.next(true);
      this.dataStatus$.next(['processing', i18n.chain.proMicroModules.datapkg.saving]);

      const values = await this.formRef.current?.getValues();
      if (!values) {
        this.updateDataStatus();
        return;
      }

      const result = values.id ? await this.updateRow(values) : await this.addNewRow(values);

      // 如果未修改
      if (result.noChanged) {
        this.dataStatus$.next([]);
        if (resetForm) {
          this.createNewFormView({});
          toastApi.success(i18n.chain.comTip.optSuccess);
        } else {
          toastApi.warning(i18n.chain.comTip.formNoChanged);
        }
        return;
      }

      // 更新失败
      if (!result.row) {
        this.updateDataStatus();
        return;
      }

      // 更新表单数据和状态
      !resetForm && this.updateFormData(result.row);
      this.dataStatus$.next(['success', i18n.chain.proMicroModules.saveedTip]);

      // 处理重置表单
      if (resetForm) {
        this.createNewFormView({});
        toastApi.success(i18n.chain.comTip.optSuccess);
      }
    } catch (error) {
      this.dataStatus$.next(['error', i18n.chain.proMicroModules.notSaveTip]);
    } finally {
      this.isSaving$.next(false);
    }
  };

  public deleteData = async (reDel?: boolean) => {
    if (this.isSaving$.getValue()) return;

    const current = this.currentFormValues$.getValue();
    const enableDelete = this.getEnableDelete$().getValue();
    if (enableDelete) {
      await this.innerModifyItemData(current, reDel ? DataTableActionEnum.Redel : DataTableActionEnum.Delete);

      // 只有表单界面可以操作删除有两个地方（预览，移动端填报），删除数据即返回上一页
      this.onlyForm && this.submitConfirmOptionsFunc?.()?.deleteDataCallback?.();
    }
  };

  public copyCurrentRow() {
    const currentRow = this.currentFormValues$?.getValue?.();
    if (!this.getEnableCopy$().getValue()) {
      console.error(`error copy current row:${currentRow}, check row status`);
      return;
    }
    this.doCopyRow(currentRow);
  }

  public itemsToActions(items: any[]) {
    const actions = _.map(
      _.filter(items, (item) => item.type !== 'divider'),
      (item) => ({
        text: item.label,
        key: item.key,
        onClick: () => {
          item.onClick?.();
          this.setActionVisible(false);
        },
        danger: item.danger,
      }),
    );
    if (items.length > 0) {
      actions.push({
        text: i18n.chain.comButton.cancel,
        key: 'cancel',
        onClick: () => this.setActionVisible(false),
        danger: false,
      });
    }
    return actions;
  }

  public getMoreItems(enableCopy: boolean, enableDelete: boolean, isDeleted: boolean) {
    const moreItems: any[] = [];
    if (enableCopy) {
      moreItems.push({
        label: i18n.chain.proMicroModules.copyData,
        key: DataTableActionEnum.COPY,
        onClick: () => this.copyCurrentRow(),
      });
    }
    if (enableDelete) {
      moreItems.push({
        type: 'divider',
      });
      if (isDeleted) {
        moreItems.push({
          label: i18n.chain.proMicroModules.btnReverDel,
          key: DataTableActionEnum.Redel,
          onClick: () => this.deleteData(true),
        });
      } else {
        moreItems.push({
          label: i18n.chain.comButton.delete,
          key: DataTableActionEnum.Delete,
          danger: true,
          onClick: () => this.deleteData(),
        });
      }
    }
    return moreItems;
  }

  // 保存数据到服务器
  private async saveData(values: Record<string, any>): Promise<ISaveResult> {
    return values.id ? this.updateRow(values) : this.addNewRow(values);
  }

  private async updateRow(row: Record<string, any>) {
    const result: ISaveResult = {};
    const { updateRows } = processRows(
      [this.orginFormValues$.getValue()],
      [row],
      getProcessRowsOptions(this.isFormManageLevelUser),
    );
    if (!updateRows.length) {
      result.noChanged = true;
      return result;
    }
    const updateRow = await this.Model.updateRow(this.itemData.pkgId, updateRows[0]);
    if (!updateRow) return result;
    this.dataTableController.editDataInList(updateRow);
    result.row = updateRow;
    return result;
  }

  private async addNewRow(row: Record<string, any>) {
    const { addRows } = processRows([], [row], getProcessRowsOptions(this.isFormManageLevelUser));
    const result: ISaveResult = {};
    if (!addRows.values.length) {
      result.noChanged = true;
      return result;
    }
    const newRow = await this.Model.addRow(this.itemData.pkgId, addRows);
    if (!newRow) return result;
    const addIndex = this.getAddindex();
    this.currendIndex$.next(addIndex);
    this.dataTableController.addDataToListByIndex(newRow, addIndex, true);
    result.row = newRow;
    return result;
  }

  private updateFormData(formData: Record<string, any>) {
    this.orginFormValues$.next(formData);
    this.currentFormValues$.next(formData);
    this.formRef.current?.setValues(formData);
  }

  private getAddindex() {
    const index = this.currendIndex$.getValue();
    return index < 0 ? 0 : index;
  }

  private changeFormValueToNextIndex(ntIndex: number) {
    const listData = this.dataTableController.getDataListValue();
    let newIndex = Math.max(0, ntIndex);
    newIndex = Math.min(newIndex, listData.length - 1);
    const formData = listData[newIndex];
    if (_.isEqual(formData.id, this.currentFormValues$.getValue().id)) return;
    this.currendIndex$.next(newIndex);
    this.createNewFormView(formData);
  }

  private createNewFormView(formData: Record<string, any>, resetOrigin?: boolean) {
    this.orginFormValues$.next(resetOrigin ? {} : formData);
    this.currentFormValues$.next(formData);
    const oldProps = this.formProps$.getValue();
    this.formProps$.next({ ...oldProps, formData, key: Math.random() });
  }

  private doCopyRow(row: Record<string, any>) {
    const businessKeys = _.chain(_.keys(row))
      .map((name) => ({ name }))
      .thru(ignoreFrontendColumns)
      .map('name')
      .value();
    const newRow = _.omit(_.pick(row, businessKeys), ['id', 'rows']);
    this.currendIndex$?.next?.(-1);
    this.createNewFormView?.(newRow, true);
  }

  private innerModifyItemData = async (item: Record<string, any>, action: string, noToast = false) => {
    if (action === DataTableActionEnum.Edit) {
      const index = _.findIndex(this.dataTableController.getDataListValue(), { id: item.id });
      this.changeFormValueToNextIndex(index);
      !noToast && toastApi.success(i18n.chain.proMicroModules.editRegeonTip);
      return;
    }
    if (action === DataTableActionEnum.COPY) {
      this.doCopyRow(item);
      return;
    }
    const flag = DataTableActionEnum.Delete === action ? BooleanEnum.True : BooleanEnum.False;
    const { updateRows } = processRows(
      [item],
      [{ ...item, [COLUMN_FLAG]: flag }],
      getProcessRowsOptions(this.isFormManageLevelUser, { ignoreUpdateRowActionStatus: true }),
    );
    const updateRow = await this.Model.updateRow(this.itemData.pkgId, updateRows[0]);
    if (!updateRow) return;
    toastApi.success(i18n.chain.comTip.optSuccess);
    this.dataTableController.editDataInList(updateRow);
    // 对数据操作后重新计算状态（删除等）
    this.updateFormData(updateRow);
  };

  private cacluateStatus() {
    // 订阅 currentFormValues$、currendIndex$、dataStatus$，计算 enableDelete 和 enableCopy
    combineLatest([this.currentFormValues$, this.currendIndex$, this.dataStatus$]).subscribe(
      ([currentRow, currentIndex, dataStatus]) => {
        const isRowValid = _.isPlainObject(currentRow) && !_.isEmpty(currentRow);
        const isNotError = !(dataStatus?.[0] === 'error');
        const isDeleted = currentRow?.[COLUMN_FLAG] === BooleanEnum.True;
        const isEdit = currentIndex > -1;
        const enableDelete = isRowValid && isEdit && isNotError;
        const enableCopy = isRowValid && isEdit;
        this.enableDelete$.next(enableDelete);
        this.enableCopy$.next(enableCopy);
        this.deleteStatus$.next(isDeleted);
      },
    );
  }

  private initReletion(value?: Record<string, any>, directCopy?: boolean) {
    this.currentFormValues$.pipe(debounceTime(300)).subscribe(() => {
      if (!this.isSaving$.getValue()) {
        this.updateDataStatus();
      }
    });

    const tableCtrl = this.dataTableController;
    tableCtrl
      .getDataListLoading$()
      .pipe(skip(1))
      .subscribe((loading) => {
        if (!loading && value) {
          directCopy ? this.doCopyRow(value) : this.innerModifyItemData(value, DataTableActionEnum.Edit, true);
          return;
        }
        loading && this.currendIndex$.next(-1);
      });
    tableCtrl
      .getLoadingNext$()
      .pipe(skip(1))
      .subscribe((loading) => {
        const block = this.blockUi$.getValue();
        if (!loading && block.loading && block.callback) {
          block.callback();
          this.blockUi$.next({ loading: false });
        }
      });
    const pageSize = tableCtrl.getPageSize();
    combineLatest([this.currendIndex$, tableCtrl.getCurrentPage$(), tableCtrl.getPageTotal$()]).subscribe(
      ([index, currentPage, total]) => {
        const realIndex = currentPage * pageSize + index;
        this.hasPre$.next(realIndex > 0);
        this.hasNext$.next(realIndex + 1 < total);
      },
    );
    // 监听列表高亮改色
    this.orginFormValues$.subscribe((value) => {
      tableCtrl.setHighlightRows(value.id ? [value.id] : []);
    });
  }

  private updateDataStatus() {
    const currentValues = this.currentFormValues$.getValue();
    const originValues = this.orginFormValues$.getValue();

    if (!_.isEqualWith(originValues, currentValues, customizer)) {
      this.dataStatus$.next(['error', i18n.chain.proMicroModules.notSaveTip]);
    } else {
      this.dataStatus$.next([]);
    }
  }
}

function formatterVal(val: any) {
  if (!_.isPlainObject(val)) return val;
  let rslt: any = {};
  let hasNil = false;
  _.forEach(val, (kv, key) => {
    if (
      kv === undefined ||
      kv === '' ||
      kv === false ||
      kv === null ||
      ((_.isArray(kv) || _.isObjectLike(kv)) && _.isEmpty(kv))
    ) {
      hasNil = true;
    } else {
      rslt[key] = kv;
    }
  });
  return hasNil ? rslt : val;
}

// 自定义比较器函数
function customizer(objValue: any, othValue: any) {
  const fobj = formatterVal(objValue);
  const foth = formatterVal(othValue);
  if (objValue === fobj && othValue === foth) {
    return _.isEqual(fobj, foth);
  }
  return _.isEqualWith(fobj, foth, customizer);
}
