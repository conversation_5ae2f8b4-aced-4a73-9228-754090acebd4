import { FC } from 'react';
import { onFormMount } from '@formily/core';
import { ChevronDown, ChevronUp, MoreHoriz } from '@metro/icons';
import { ActionSheet } from '@metro/mobile-components/dist/esm/action-sheet';
import { Badge } from '@metroDesign/badge';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Flex } from '@metroDesign/flex';
import { type PopconfirmProps, Popconfirm } from '@metroDesign/popconfirm';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Spin } from '@metroDesign/spin';
import { Splitter } from '@metroDesign/splitter';
import classnames from 'classnames';
import { LoadingWrapper } from '@mdtBsComm/components/loading-wrapper';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { FormView } from '../../components/form-view';
import i18n from '../../languages';
import { OneTableNewDataTableStatics } from '../one-table-new-data-table-statics';
import { OneTableNewFillFormController } from './OneTableNewFillFormController';
import './index.less';

interface IProps {
  controller: OneTableNewFillFormController;
}

const TableBlock: FC<IProps> = ({ controller }) => {
  return (
    <div className="table-block">
      <OneTableNewDataTableStatics controller={controller.getDataTableController()} />
    </div>
  );
};

const FormBlockHeader: FC<IProps> = ({ controller }) => {
  const dataStatus = useObservableState(controller.getDataStatus$());
  const hasPre = useObservableState(controller.getHasPre$());
  const hasNext = useObservableState(controller.getHasNext$());
  const { id: currentId } = useObservableState(controller.getCurrentEditData$());
  const onlyForm = controller.getOnlyForm();

  const dsEle = dataStatus.length ? <Badge status={dataStatus[0] as 'default'} text={dataStatus[1]} /> : null;
  const editing = dataStatus?.[0] === 'error';

  const commonPopProps: PopconfirmProps = {
    title: i18n.chain.proMicroModules.notSaveTip,
    description: i18n.chain.proMicroModules.notSaveTipText,
    cancelText: i18n.chain.proMicroModules.noSave,
    okText: i18n.chain.comButton.save,
    type: 'warning',
    needConfirm: editing,
    noConfirmAction: 'onCancel',
  };

  const dt = currentId ? (
    <div className="data-tip">
      {i18n.chain.proMicroModules.editCurrentDataId}
      <span>{currentId}</span>
    </div>
  ) : null;

  return (
    <div className="header">
      <div className="title">{i18n.chain.proMicroModules.fillFormData}</div>
      {dt}
      <div
        className={classnames('tip', {
          'tip-only-form': onlyForm,
        })}
      >
        {dsEle}
      </div>
      <div>
        <Popconfirm
          {...commonPopProps}
          disabled={!hasPre}
          onCancel={() => controller.editPreData()}
          onConfirm={async () => controller.saveEditPreData()}
        >
          <Button ghost icon={<ChevronUp />} disabled={!hasPre}>
            {i18n.chain.proMicroModules.previousRow}
          </Button>
        </Popconfirm>
        <Popconfirm
          {...commonPopProps}
          disabled={!hasNext}
          onCancel={() => controller.editNextData()}
          onConfirm={async () => controller.saveEditNextData()}
        >
          <Button ghost icon={<ChevronDown />} disabled={!hasNext}>
            {i18n.chain.proMicroModules.nextRow}
          </Button>
        </Popconfirm>
      </div>
    </div>
  );
};

const FormBlockForm: FC<IProps> = ({ controller }) => {
  const formProps = useObservableState(controller.getFormProps$());
  const onlyForm = controller.getOnlyForm();

  return (
    <div className={classnames('form', { 'only-form': onlyForm })}>
      <Scrollbar style={{ height: '100%' }}>
        <FormView
          {...formProps}
          effects={() => {
            onFormMount((form) => {
              controller.changeFormValues(form.values);
            });
          }}
          ref={controller.getFormRef()}
          onChange={controller.changeFormValues}
        />
      </Scrollbar>
    </div>
  );
};

const MoreOperation: FC<IProps> = ({ controller }) => {
  const enableDelete = useObservableState(controller.getEnableDelete$());
  const enableCopy = useObservableState(controller.getEnableCopy$());
  const isDeleted = useObservableState(controller.getDeleteStatus$());
  const actionVisible = useObservableState(controller.getActionVisible$());

  const moreItems = controller.getMoreItems(enableCopy, enableDelete, isDeleted);
  const actions = controller.itemsToActions(moreItems);

  return moreItems.length ? (
    isPc() ? (
      <Dropdown menu={{ items: moreItems }}>
        <Button ghost>{i18n.chain.proMicroModules.moreOperation}</Button>
      </Dropdown>
    ) : (
      <>
        <div />
        <Button
          className="footer-more-btn"
          ghost
          onlyIcon
          icon={<MoreHoriz />}
          onClick={() => controller.setActionVisible(true)}
        />
        <ActionSheet visible={actionVisible} actions={actions} onClose={() => controller.setActionVisible(false)} />
      </>
    )
  ) : (
    <div />
  );
};

const FormFooter: FC<IProps> = ({ controller }) => {
  const isSaving = useObservableState(controller.getIsSaving$());
  const completeBtnEle = controller.getShowCompleteBtn() ? (
    <Button onClick={controller.doFinished} disabled={isSaving}>
      {i18n.chain.proMicroModules.doFinished}
    </Button>
  ) : null;

  return (
    <Flex className="footer" justify="space-between">
      <MoreOperation controller={controller} />
      <LoadingWrapper>
        <Flex gap="small">
          <Button type="primary" onClick={async () => controller.saveFormData()} disabled={isSaving}>
            {i18n.chain.comButton.save}
          </Button>
          <Button onClick={async () => controller.saveFormData(true)} disabled={isSaving}>
            {i18n.chain.proMicroModules.saveAndAdd}
          </Button>
          {completeBtnEle}
        </Flex>
      </LoadingWrapper>
    </Flex>
  );
};

const FormBlock: FC<IProps> = ({ controller }) => {
  return (
    <div className="form-block">
      <FormBlockHeader controller={controller} />
      <FormBlockForm controller={controller} />
      <FormFooter controller={controller} />
    </div>
  );
};

const LoadingBlock: FC<IProps> = ({ controller }) => {
  const block = useObservableState(controller.getBlockUi$());
  return block.loading ? <Spin className="block-ui" spinning brand /> : null;
};

export const OneTableNewFillForm: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getDataTableController().getDataListLoading$());
  const onlyForm = controller.getOnlyForm();

  const View = (
    <div className="one-table-new-fill-form">
      <Splitter style={{ height: '100%' }} lazy>
        {onlyForm ? null : (
          <Splitter.Panel defaultSize="60%">
            <TableBlock controller={controller} />
          </Splitter.Panel>
        )}
        <Splitter.Panel>
          <FormBlock controller={controller} />
        </Splitter.Panel>
      </Splitter>
      <LoadingBlock controller={controller} />
    </div>
  );

  return onlyForm ? <Spin spinning={onlyForm && loading}>{View}</Spin> : View;
};
