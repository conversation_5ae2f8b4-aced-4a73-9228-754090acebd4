import _ from 'lodash';
import { combineLatest, from, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { map, takeWhile } from 'rxjs/operators';
import { IFloworkTask, IOneTableNotices, IOneTableNoticesBody } from '@mdtApis/interfaces';
import {
  FLOWORK_TASK_DATA_WITH_EXECUTOR,
  IWorkflowModel,
  ONE_TABLE_FLOW_TASK_DATA_WITH_EXECUTOR,
  WORKFLOW_DATA,
  WORKFLOW_GENEALOGY_GROUP_DATA,
} from '@mdtBsBffServices/services';
import { WORKFLOW_SPEC_SIMPLE_DATA } from '@mdtBsBffServices/workflow-spec';
import { DATE_FORMATTER_1, DATE_FORMATTER_2, formateDateWithoutMillisecond } from '@mdtBsComm/utils/dayUtil';
import { postOneTableNoticesAsync } from '@mdtBsServices/datlasbff';
import { WorkflowSpecBffService } from '@mdtProComm/bff-services';
import { TaskBffService, WorkflowBffService } from '@mdtProComm/bff-services';
import { improveChildrenSchema } from '../../../components/form-view/util';
import { ONE_TABLE_EXPEDITE_TITLE_EXTRA } from '../../../datlas/datlasConfig';
import i18n from '../../../languages';
import { parseDataFromXml } from '../../../pages/one-table-report-create/util';
import { WorkFlowFormModelBff } from '../../../pages-in-micro/workflow/_util/WorkFlowFormModelBff';
import { FlowTypeEnum, getFlowSpecId, getTaskStageName } from '../../../utils/oneTableUtil';
import { ApplyStatusEnum } from '../../workflow-application-list';
import { IReportDetailInfoState } from '../OneTableReportDetailModelBff';
import { FlowNodeTypeEnum, IFlowNode } from './FlowInfo';

export const STATUS_LABEL_MAP: Record<ApplyStatusEnum, [string, string]> = {
  [ApplyStatusEnum.COMPLETED]: [i18n.chain.proMicroModules.workflow.status.done, 'success'],
  [ApplyStatusEnum.TO_PROCESS]: [i18n.chain.proMicroModules.workflow.status.pending, 'processing'],
  [ApplyStatusEnum.REJECTED]: [i18n.chain.proMicroModules.workflow.status.reject, 'error'],
  [ApplyStatusEnum.CANCELLED]: [i18n.chain.proMicroModules.workflow.status.cancelled, 'warining'],
  [ApplyStatusEnum.ENGINE_ERROR]: [i18n.chain.proMicroModules.workflow.status.engine_error, 'error'],
};

export interface IReportFlowInfoTableData {
  workflowId: string;
  departId: string;
  rootWorkflowId: string;
  rootName: string;
  departName: string;
  status: string;
  // statusDisplay: string[];
  dealType: string;
  dealTime: string;
  approveStatus: string[];
  initialWorkflowId: string;
  appId: number;
  userId: number;
  assignee: number;
  assignees?: number[];
}

export interface IQueryFlowsOptions {
  flowType: FlowTypeEnum;
  // flowType 为REPORT_MANAGEMENT和CHILD_WORKFLOW时传workflowId,其它情况传taskId
  workflowId?: string;
  taskId?: string;
}

export class FlowInfoBffModel {
  public static getReportInfo(id: string) {
    return from(
      WorkflowSpecBffService.queryWorkflowSpecDetail({
        id,
        params: { with_xml: true },
        respData: WORKFLOW_SPEC_SIMPLE_DATA,
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const result: IReportDetailInfoState = { success: resp.success };
        if (resp.success) {
          const { reportInfo, configData, formEditorSchema, datapkgId } = parseDataFromXml(resp.page_data.bpmn_xml);
          formEditorSchema &&
            (formEditorSchema.formilySchema = {
              ...improveChildrenSchema({
                schema: formEditorSchema.formilySchema.schema!,
                allSettingValues: formEditorSchema.allSettingValues,
              }),
              form: formEditorSchema.formilySchema.form,
            });
          result.data = {
            ...reportInfo,
            startDate: configData.startDate,
            endDate: configData.endDate,
            datapkgId,
            formEditorSchema,
          };
        }
        return result;
      }),
    );
  }

  public static async queryFlows(options: IQueryFlowsOptions): Promise<IFlowNode[]> {
    const { flowType, workflowId } = options;
    if (flowType === FlowTypeEnum.REPORT_MANAGEMENT) {
      return this.queryReportManagementFlows(workflowId!);
    }
    return this.queryOneTableFlowTasks(options);
  }

  public static queryWorkflowGenealogy(wfIds: string[]): any {
    if (_.isEmpty(wfIds)) return of([0, []]);
    return from(
      WorkflowBffService.batchQueryWorkflowGenealogy({
        data: {
          ids: wfIds,
        },
        respData: WORKFLOW_GENEALOGY_GROUP_DATA,
      }),
    ).pipe(
      switchMap((resp) => {
        const respData = resp.success ? resp.page_data : [];
        if (!respData.length) return of([0, []]);
        let genealogyWfs = _.flatten(_.map(respData, 'genealogy[0].targetWorkflows'));
        // 只保留specId为flowSpecId的列表
        genealogyWfs = _.filter(genealogyWfs, (it) => !!it && _.eq(it.spec.id, getFlowSpecId())).sort(
          (a, b) => b.update_time - a.update_time,
        );
        const data = _.map(genealogyWfs, (it) => this.transformToFlowTableData(it, {}));
        return of([0, _.uniqBy(data, 'initialWorkflowId')]);
      }),
    );
  }

  public static async queryReadyTask(workflowId: string): Promise<IFloworkTask | undefined> {
    const tasksResp = await WorkflowBffService.queryWorkflowTasks({
      id: workflowId,
      params: { with_data: true },
      respData: FLOWORK_TASK_DATA_WITH_EXECUTOR,
    });
    const tasks = tasksResp.success ? tasksResp.page_data : [];
    // 深拷贝的原因：此处task列表和报表列表处的任务流转列表是同一个对象（bff对相同请求进行了节流，并共享响应），在任务列表处会修改schema.
    return _.cloneDeep(_.find(tasks, (it) => it.status === 'ready'));
  }

  public static getWorkflowFormModel() {
    return WorkFlowFormModelBff;
  }

  public static async queryWorkflowDetail(wfId: string): Promise<IWorkflowModel> {
    const resp = await WorkflowBffService.queryWorkflowDetail({
      id: wfId,
      params: { with_spec: 'detail', with_form_data: true, with_data: true },
      respData: WORKFLOW_DATA,
    });
    return resp.success ? resp.page_data : ({} as any);
  }

  public static async postGeneralNotice(
    body: Omit<IOneTableNoticesBody, 'notices'> & {
      notices: (Omit<IOneTableNotices, 'message'> & {
        status?: string;
      })[];
    },
    msgInfo: {
      title?: string;
      userName?: string;
      endDate?: string;
      [key: string]: any;
    },
  ): Promise<boolean> {
    const { notices, ...rest } = body;
    const { userName, title, endDate } = msgInfo;

    const defaultMsg = (status?: string) =>
      `
报表名称：${title}
截止日期：${endDate}
任务状态：${status}
`;
    const overridedBody: IOneTableNoticesBody = {
      ...rest,
      notices: notices.map((it) => {
        const { status, ...rest } = it;
        return {
          ...rest,
          title: `${userName}的催办 ${ONE_TABLE_EXPEDITE_TITLE_EXTRA}`,
          message: defaultMsg(status),
        };
      }),
    };
    const resp = await postOneTableNoticesAsync(overridedBody);
    return resp.success;
  }

  private static transformToFlowTableData(flow: IWorkflowModel, map: any): IReportFlowInfoTableData {
    const departId = flow.data?.deliver_depart;
    const assignee = flow.data?.assignee;
    const assignees = flow.data?.assignees;
    const status = flow.data?.handling_state;
    return {
      workflowId: flow.workflow_id,
      departId,
      rootWorkflowId: flow.data?.root_workflow_id,
      rootName: flow.data?.form_name,
      departName: map[departId] || `${departId}`,
      status,
      // statusDisplay: STATUS_LABEL_MAP[flow.status as any as ApplyStatusEnum] || [],
      dealType: flow.data?.handling_way,
      dealTime: formateDateWithoutMillisecond(flow.update_time, DATE_FORMATTER_2),
      approveStatus: flow.data?.approval_status,
      initialWorkflowId: flow.data?.initial_workflow_id,
      appId: flow.initiator_app,
      userId: flow.initiator,
      // TODO: 此处给到的用户默认为本机构用户
      assignee,
      assignees,
    };
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  private static async queryReportManagementFlows(wfId: string) {
    const [wfDetail, tasksResp] = await combineLatest([
      this.queryWorkflowDetail(wfId),
      WorkflowBffService.queryWorkflowTasks({
        id: wfId,
        params: { with_data: true },
        respData: FLOWORK_TASK_DATA_WITH_EXECUTOR,
        disableBffProxy: true,
      }),
    ]).toPromise();

    const isWfComplete = wfDetail.status === ApplyStatusEnum.COMPLETED || wfDetail.status === ApplyStatusEnum.CANCELLED;
    const userTasks = _.filter(tasksResp.success ? tasksResp.page_data : [], (t) => t.status === 'completed');

    const approvalList: IFlowNode[] = _.map(userTasks, (t) => {
      return {
        name: t.bpmn_name,
        date: formateDateWithoutMillisecond(t.update_time, DATE_FORMATTER_1),
        type: t.name as any,
        excutor: t.executorObj?.name || '',
        formData: t.data,
        formSpec: { spec: t.form_spec?.form },
      };
    });
    approvalList.push({
      name: i18n.chain.proMicroModules.oneTable.firstDispatch,
      date: formateDateWithoutMillisecond(wfDetail.create_time, DATE_FORMATTER_1),
      type: FlowNodeTypeEnum.START,
      excutor: wfDetail.initiatorObj?.name || '',
      formData: {},
      formSpec: { spec: {} },
    });
    isWfComplete &&
      approvalList.unshift({
        name: i18n.chain.comText.copyLink,
        type: FlowNodeTypeEnum.END,
        formData: {},
        formSpec: { spec: {} },
      });
    return _.reverse(approvalList);
  }

  private static async queryOneTableFlowTasks(options: IQueryFlowsOptions): Promise<IFlowNode[]> {
    const { flowType, workflowId, taskId } = options;
    const queryWorkflowResource = flowType === FlowTypeEnum.CHILD_WORKFLOW || flowType === FlowTypeEnum.FILL_TASK;
    const resp = await TaskBffService.queryOneTableFlowTasks({
      resourceId: (queryWorkflowResource ? workflowId : taskId) as string,
      params: {
        resource_type: queryWorkflowResource ? 'workflow' : 'task',
      },
      respData: ONE_TABLE_FLOW_TASK_DATA_WITH_EXECUTOR,
    });
    if (!resp.success) return [];

    return _.map(resp.page_data, (it) => {
      const task = it.task_info;
      return {
        name: it.bpmn_name || getTaskStageName(it.stage),
        date: formateDateWithoutMillisecond(it.update_time, DATE_FORMATTER_1),
        type: (it.name === 'Start' ? it.stage : it.name) as any,
        excutor: it.executorObj?.name || '',
        formData: task.data,
        formSpec: { spec: it.form_spec?.form },
        workflowId: it.workflow_id,
      };
    });
  }
}

export type IFlowInfoModel = typeof FlowInfoBffModel;
