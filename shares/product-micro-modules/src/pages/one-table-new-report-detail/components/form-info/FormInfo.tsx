import { FC } from 'react';
import { EditOutlined } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Flex } from '@metroDesign/flex';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Spin } from '@metroDesign/spin';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { FormView } from '../../../../components/form-view';
import i18n from '../../../../languages';
import type { IProps } from '../../OneTableNewReportDetail';

const EditBtn: FC<IProps> = ({ controller }) => {
  const formInfoController = controller.getFormInfoController();
  const showEditBtn = useObservableState(formInfoController.getShowEditBtn$());
  return showEditBtn ? (
    <Flex justify="flex-end">
      <Dropdown
        menu={{
          items: [
            {
              key: '1',
              label: i18n.chain.proMicroModules.oneTable.editFormConfig,
              onClick: () => {
                formInfoController.edit();
              },
            },
            { type: 'divider' },
            {
              key: '2',
              label: i18n.chain.proMicroModules.oneTable.editForm,
              onClick: () => {
                formInfoController.editForm();
              },
            },
          ],
        }}
      >
        <Button.Link primary icon={<EditOutlined />}>
          {i18n.chain.proMicroModules.oneTable.editReport}
        </Button.Link>
      </Dropdown>
    </Flex>
  ) : null;
};

const formStyle = { height: 'calc(100% - 30px)', marginTop: 8 };
export const FormInfo: FC<IProps> = ({ controller }) => {
  const formInfoController = controller.getFormInfoController();
  const loading = useObservableState(formInfoController.getLoading$());
  const formSpec = formInfoController.getFormSpec();
  const flowSpec = formInfoController.getFlowSpec();
  const formData = useObservableState(formInfoController.getFormData$());
  const flowData = useObservableState(formInfoController.getFlowData$());

  return (
    <Spin spinning={loading} fillParent>
      <EditBtn controller={controller} />
      <Scrollbar style={formStyle}>
        <div className="form-info">
          <FormView {...flowSpec} formData={flowData} readonly />
          <FormView {...formSpec} formData={formData} readonly />
        </div>
      </Scrollbar>
    </Spin>
  );
};
