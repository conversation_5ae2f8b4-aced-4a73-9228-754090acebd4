import _ from 'lodash';
import { drawerApi } from '@metroDesign/drawer';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { toastApi } from '@metroDesign/toast';
import { Typography } from '@metroDesign/typography';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { saveCompressValueToUrl } from '@mdtBsComm/utils/urlUtil';
import { DbColumnTypeEnum, ModuleIdEnum, OneTableNewDataStateEnum } from '@mdtProComm/constants';
import { OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import { modifyParamsOfUrl } from '@mdtProComm/utils/urlUtil';
import { ModalReason } from '../../../../components/modal-reason';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../../components/transform-id-to-name';
import { DatlasAppController } from '../../../../datlas/app/DatlasAppController';
import type { IOneTableNewOperatorDataComm } from '../../../../interfaces';
import i18n from '../../../../languages';
import { CommandEnum, doOneTableNewOperatorCancelTask } from '../../../../shared/onetablenew';
import { DataTableTypeEnum, getDisplayFormName } from '../../../../utils/oneTableNewUtil';
import {
  IOpenOneTableNewFlowTreeOptions,
  openOneTableNewFlowTreeDrawer,
} from '../../../one-table-new-flow-tree/drawer-one-table-new-flow-tree';
import { openOneTableNewIssuedFormDrawer } from '../../../one-table-new-issued-form';
import { DeliveryDetailDataTableController } from './DeliveryDetailDataTableController';
import { DeliveryManagementDetail } from './DeliveryManagement';
import type { IDeliveryManagementModel, ITableData } from './DeliveryManagementModel';

export interface IControllerOptions {
  Model: IDeliveryManagementModel;
  itemData: IOneTableNewOperatorDataComm;
  endDate?: string;
  detailDrawerWidth?: string;
  issueDataSuccess: () => void;
  approveDataSuccess: () => void;
  rejectDataSuccess: () => void;
  cancelTaskSuccess: () => void;
  runOperatorCallback?: () => void;
}

abstract class DeliveryManagementBaseController {
  protected listController: any;
  private Model: IDeliveryManagementModel;
  private dataTableController?: DeliveryDetailDataTableController;
  private showExpediteBtn$ = new BehaviorSubject<boolean>(false);
  private showDeliveryCancelOrRejectList$ = new BehaviorSubject<boolean>(false);
  private issueDataSuccess: IControllerOptions['issueDataSuccess'];
  private approveDataSuccess: IControllerOptions['approveDataSuccess'];
  private rejectDataSuccess: IControllerOptions['rejectDataSuccess'];
  private cancelTaskSuccess: IControllerOptions['cancelTaskSuccess'];
  private runOperatorCallback?: IControllerOptions['runOperatorCallback'];
  private itemData: IOneTableNewOperatorDataComm;
  private endDate?: string;
  private detailDrawerWidth?: string;
  private showApprovalBtn = true;

  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.itemData = options.itemData;
    this.endDate = options.endDate;
    this.detailDrawerWidth = options.detailDrawerWidth || '70vw';
    this.issueDataSuccess = options.issueDataSuccess;
    this.approveDataSuccess = options.approveDataSuccess;
    this.rejectDataSuccess = options.rejectDataSuccess;
    this.cancelTaskSuccess = options.cancelTaskSuccess;
    this.runOperatorCallback = options.runOperatorCallback;
    this.showDeliveryCancelOrRejectList$.pipe(skip(1)).subscribe(() => {
      this.listController.loadDataList();
    });
  }

  public destroy() {
    this.Model = null!;
    this.itemData = null!;
    this.issueDataSuccess = null!;
    this.approveDataSuccess = null!;
    this.rejectDataSuccess = null!;
    this.cancelTaskSuccess = null!;
    this.runOperatorCallback = null!;
    this.showExpediteBtn$.complete();
    this.showDeliveryCancelOrRejectList$.complete();
    this.dataTableController?.destroy();
    this.listController.destroy();
  }

  public getShowDeliveryCancelOrRejectList$() {
    return this.showDeliveryCancelOrRejectList$;
  }

  public changeShowDeliveryCancelOrRejectList(show: boolean) {
    this.showDeliveryCancelOrRejectList$.next(show);
  }

  public getShowExpediteBtn$() {
    return this.showExpediteBtn$;
  }

  public changeShowExpediteBtn(show?: boolean) {
    this.showExpediteBtn$.next(!!show);
  }

  public getDataTableController() {
    return this.dataTableController;
  }

  public modifyShowApprovalBtn(show: boolean) {
    this.showApprovalBtn = show;
  }

  public getFormName() {
    return getDisplayFormName(this.itemData);
  }

  public getIsNeedApproval() {
    return this.itemData.isNeedApproval;
  }

  public getFirstPageData = () => {
    const { itemData } = this;
    const showCancelOrRejectList = this.showDeliveryCancelOrRejectList$.getValue();
    return this.Model!.queryFirstPage(
      itemData.assignWfId,
      itemData.isNeedApproval,
      showCancelOrRejectList,
      itemData.isCheckGranted,
    );
  };

  // 全部催办
  public doOperatorExpediteAll = async () => {
    const allList = this.listController.getDataListValue();
    const expediteList = _.filter(allList, this.showItemExpedite);
    await this.doOperatorExpedite(expediteList);
  };

  // 查看下发图谱
  public doOperatorViewFlowMap = async (options: Partial<IOpenOneTableNewFlowTreeOptions> = {}) => {
    openOneTableNewFlowTreeDrawer({
      ...options,
      itemData: this.itemData,
      showDeliveryCancelOrRejectList: this.showDeliveryCancelOrRejectList$.getValue(),
    });
  };

  // 审批
  public async doOperatorApproval(item: ITableData, closeDrawer: () => void) {
    const resp = await this.Model.approvalTask(item.rootWfId, item.assignWfId, CommandEnum.APPROVE);
    if (!resp) return;
    toastApi.success(i18n.chain.comTip.optSuccess);
    this.listController.loadDataList();
    this.approveDataSuccess?.();
    closeDrawer();
  }

  // 驳回
  public doOperatorReject(item: ITableData, closeDrawer: () => void) {
    const okFunc = async (reason: string, onClose: () => void) => {
      const resp = await this.Model.approvalTask(item.rootWfId, item.assignWfId, CommandEnum.REJECT, reason);
      if (!resp) return;
      toastApi.success(i18n.chain.comTip.optSuccess);
      this.listController.loadDataList();
      this.rejectDataSuccess?.();
      onClose();
      closeDrawer();
    };

    ModalReason.open({
      title: i18n.chain.proMicroModules.oneTable.rejectReasons,
      okButtonProps: { danger: true },
      okText: i18n.chain.proMicroModules.oneTable.btnSureReject,
      reasonOptions: {
        component: 'Textarea',
        require: true,
      },
      onOk: okFunc,
    });
  }

  // 分配数据
  public doOperatorIssueData(item: ITableData) {
    const adminId = item.userIds[0];
    const { itemData } = this;
    openOneTableNewIssuedFormDrawer(
      {
        itemData: {
          ...itemData,
          // @ts-ignore
          formName: (
            <Space size={2}>
              <Typography.Text>{i18n.chain.proMicroModules.oneTable.userAdmin}</Typography.Text>
              <Tag color="primary" type="light" bordered={false}>
                <TransformIdToName id={adminId} type={DbColumnTypeEnum.USER_ID} />
                (<TransformUserIdToOrgName placement="right" id={adminId} />)
              </Tag>
            </Space>
          ),
        },
        onlyData: true,
        initIssuedUsers: [{ userId: `${adminId}`, orgId: `${adminId}` }],
        successCallback: this.issueDataSuccess,
      },
      { title: i18n.chain.proMicroModules.oneTable.btnIssueData },
    );
  }

  // 取消下发
  public doOperatorCancel(item: ITableData) {
    doOneTableNewOperatorCancelTask({
      itemData: { ...this.itemData, ...item },
      successCallback: () => {
        this.listController.loadDataList();
        this.cancelTaskSuccess();
      },
    });
  }

  // 取消后处理数据
  public doOperatorAfterCancel(item: ITableData) {
    console.log(item.id);
  }

  // 催办
  public async doOperatorExpedite(item: ITableData | ITableData[]) {
    const mergedItems = _.isArray(item) ? item : [item];
    const datlasIns = DatlasAppController.getInstance();
    const appId = datlasIns.getAppId();
    const userName = datlasIns.getUserName();

    const notices = _.map(mergedItems, (item) => ({
      url: this.getExpediteTaskUrl(item.id),
      status: item.dataStateDisplay[0],
      recipients: [{ user_id: item.userIds, app_id: appId }],
    }));
    if (_.isEmpty(notices)) return;
    const success = await this.Model.postGeneralNotice(
      { app: appId, notices },
      { title: this.itemData.formName, userName, endDate: this.endDate },
    );
    success && toastApi.success(i18n.chain.comTip.sendSuccess);
  }

  public showItemExpedite = (item: ITableData) => {
    const isNeedApproval = this.itemData.isNeedApproval;
    const dataState = item.dataState;
    const isUnsubmitted = dataState === OneTableNewDataStateEnum.UNSUBMITTED;
    const isRejected = dataState === OneTableNewDataStateEnum.REJECTED;
    const isRunning = item.running;
    const approvalExpediteStatus = isNeedApproval && isRunning && (isUnsubmitted || isRejected);
    const notApprovalExpediteStatus = !isNeedApproval && isRunning;
    return !!(approvalExpediteStatus || notApprovalExpediteStatus);
  };

  public async openDetailDrawer(item: ITableData, approvalAgain?: boolean) {
    const { itemData } = this;
    const dataTableController = new DeliveryDetailDataTableController({
      itemData: { ...itemData, assignWfId: item.id },
      dataTableType: DataTableTypeEnum.DETAIL,
      dataPreviewOptions: { pkgId: itemData.pkgId, hasDownload: false },
      queryDownstreamUsersOptions: { querySubData: true },
    });
    drawerApi.open({
      title: i18n.chain.proMicroModules.oneTable.issuedDetail,
      width: this.detailDrawerWidth,
      destroyOnClose: true,
      children: (onClose: any) => (
        <DeliveryManagementDetail
          item={item}
          controller={this}
          onClose={onClose}
          approvalAgain={approvalAgain}
          needApproval={itemData.isNeedApproval}
          isEndless={itemData.isEndless}
          isPeriodic={itemData.isPeriodic}
          showApprovalBtn={this.showApprovalBtn}
        />
      ),
      onClose: () => {
        dataTableController.destroy();
      },
      footer: null,
    });
    this.dataTableController = dataTableController;
  }

  public getDropdownItems = (item: ITableData, needIssueData?: boolean) => {
    const { dataState, running } = item;
    const dropdownItems: any[] = [];
    // 上级取消和下级驳回都不需要显示
    if (!running || dataState === OneTableNewDataStateEnum.REFUSED) {
      // 目前取消后自动将assgin_user设置为parent, 其在填报中处理
      // dropdownItems.push({
      //   key: 'afterCancel',
      //   label: i18n.chain.proMicroModules.oneTable.btnAftercancel,
      //   onClick: () => this.doOperatorAfterCancel(item),
      // });
    } else {
      needIssueData &&
        dropdownItems.push({
          key: 'issueData',
          label: i18n.chain.proMicroModules.oneTable.btnIssueData,
          onClick: () => {
            this.runOperatorCallback?.();
            this.doOperatorIssueData(item);
          },
        });
      dropdownItems.push({
        key: 'cancel',
        danger: true,
        label: i18n.chain.proMicroModules.oneTable.btnCancelIssued,
        onClick: () => {
          this.runOperatorCallback?.();
          this.doOperatorCancel(item);
        },
      });
    }
    return dropdownItems;
  };

  // 需要审批时按钮
  public getNeedApprovalItemBtns = (item: ITableData) => {
    const { dataState } = item;
    let result: any[] = [];

    const isRejected = dataState === OneTableNewDataStateEnum.REJECTED;
    if (this.showItemExpedite(item)) {
      result.push({
        text: i18n.chain.proMicroModules.oneTable.expedite,
        onClick: () => {
          this.runOperatorCallback?.();
          this.doOperatorExpedite(item);
        },
        key: 'ex',
      });
    }

    // 审核
    if (dataState === OneTableNewDataStateEnum.SUBMITTED) {
      result.push({
        text: i18n.chain.proMicroModules.oneTable.btnApproval,
        onClick: () => {
          this.runOperatorCallback?.();
          this.openDetailDrawer(item);
        },
        key: 'ap',
      });
    }
    // 重新审核
    if (dataState === OneTableNewDataStateEnum.APPROVED || isRejected) {
      result.push({
        text: i18n.chain.proMicroModules.oneTable.btnApprovalAgain,
        onClick: () => {
          this.runOperatorCallback?.();
          this.openDetailDrawer(item, true);
        },
        key: 'apa',
      });
    }
    return result;
  };

  // 不需要审批按钮
  public getNotNeedApprovalItemBtns = (item: ITableData) => {
    const result: any[] = [];
    if (this.showItemExpedite(item)) {
      result.push({
        text: i18n.chain.proMicroModules.oneTable.expedite,
        onClick: () => {
          this.runOperatorCallback?.();
          this.doOperatorExpedite(item);
        },
        key: 'ex',
      });
    }
    return result;
  };

  private getExpediteTaskUrl(id: string) {
    const ctrl = DatlasAppController.getInstance().getRouterController();
    return modifyParamsOfUrl(
      ctrl.getJumpUrl(OneTableRoutePathEnum.REPORT_DETAIL_TASK_H5, ModuleIdEnum.ONE_TABLE),
      'q',
      saveCompressValueToUrl({ assignWfId: id, headerless: true, sidemenuless: true }),
    );
  }
}

export { DeliveryManagementBaseController };
