import _ from 'lodash';
import { combineLatest, from, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { IOnetableDownstreamStat } from '@mdtApis/interfaces';
import { formateDateByUnix } from '@mdtBsComm/utils/dayUtil';
import { postOneTableNoticesAsync } from '@mdtBsServices/datlasbff';
import {
  batchQueryWorkflowGenealogyAsync,
  queryOnetableDownstreamStatAsync,
  queryWorkflowsByPostAsync,
} from '@mdtBsServices/flowork';
import { IOneTableNotices, IOneTableNoticesBody, IWorkflow, OneTableNewDataStateEnum } from '@mdtProComm/index';
import { ONE_TABLE_EXPEDITE_TITLE_EXTRA } from '../../../../datlas/datlasConfig';
import i18n from '../../../../languages';
import { CommandEnum, doOneTableNewExcuteCommands } from '../../../../shared/onetablenew';
import { getOperatorFilterNotDelete } from '../../../../utils/oneTableNewFilterUtil';
import {
  getAssignees,
  getFlowStatusLable,
  getPrimaryAssignee,
  getReasonWithAssignWfData,
  getWorkflowIsRuning,
} from '../../../../utils/oneTableNewUtil';

export interface IDataDeliveryInfo extends IOnetableDownstreamStat {
  percent?: string;
}

export interface ITableData {
  id: string;
  rootWfId: string;
  assignWfId: string;
  userIds: number[];
  primaryAssignee: number;
  dataState: string;
  dataStateDisplay: string[];
  submitStatusDisplay: string[];
  dealTime: string;
  approvalStatusDisplay: string[];
  running?: boolean;
  reason?: string;
  dataDeliveryInfo?: IDataDeliveryInfo & { root: IDataDeliveryInfo };
  isCancelledOrRefused: boolean;
}

export class DeliveryManagementModel {
  public static queryFirstPage(
    wfId: string,
    needApproval?: boolean,
    showCancelOrRejectList?: boolean,
    isCheckGranted?: boolean,
  ) {
    return combineLatest([
      batchQueryWorkflowGenealogyAsync({ ids: [wfId], ignore_canceled: false, down: true, max_level: 1 }),
      queryOnetableDownstreamStatAsync({
        assign_workflow_id: wfId,
        max_depth: 20,
        data_filter: getOperatorFilterNotDelete(),
        row_permission_params: { check_data_submitted: false, permission_scope: 'all', check_granted: isCheckGranted },
      }),
    ]).pipe(
      switchMap(([resp, statResp]) => {
        const ids: string[] = _.get(resp, 'data[0].genealogy[0].targets', []);
        if (_.isEmpty(ids)) {
          return of([0, []] as [number, ITableData[]]);
        }
        const rootStat = _.find(statResp?.data || [], (it) => it.assign_workflow_id === wfId) as IDataDeliveryInfo;
        const statDataMap: Record<string, IDataDeliveryInfo & { root: IDataDeliveryInfo }> = _.reduce(
          statResp?.data || [],
          (result: Record<string, IDataDeliveryInfo & { root: IDataDeliveryInfo }>, stat) => {
            result[stat.assign_workflow_id] = {
              ...stat,
              percent:
                rootStat?.stat_include_downstream?.total_assign_rows > 0
                  ? (
                      ((stat.stat_include_downstream?.total_assign_rows || 0) /
                        rootStat?.stat_include_downstream?.total_assign_rows) *
                      100
                    ).toFixed(1)
                  : '0.0',
              root: rootStat,
            };
            return result;
          },
          {},
        );

        return from(
          queryWorkflowsByPostAsync(
            { ids, with_data: true, initiator_is_me: false, orderby: '-create_time' },
            { params: { page_size: ids.length } },
          ),
        ).pipe(
          map((result) => {
            const data = _.map(result.data, (it) => this.transformToTableData(it, needApproval, statDataMap));
            return [0, _.filter(data, (it) => (showCancelOrRejectList ? true : !it.isCancelledOrRefused))] as [
              number,
              ITableData[],
            ];
          }),
        );
      }),
    );
  }

  public static async approvalTask(rootWfId: string, assignWfId: string, command: string, comment?: string) {
    return doOneTableNewExcuteCommands({
      root_workflow_id: rootWfId,
      commands: [{ command: command as CommandEnum, assign_workflow_ids: [assignWfId], comment }],
    });
  }

  public static transformToTableData(
    item: IWorkflow,
    needApproval?: boolean,
    statDataMap?: Record<string, IOnetableDownstreamStat & { root: IOnetableDownstreamStat }>,
  ): ITableData {
    const data = item.data!;
    const [cc, ...dsp] = getFlowStatusLable(item);
    const { data_state: ds } = data;
    const reason = getReasonWithAssignWfData(data, cc);
    const primaryAssignee = getPrimaryAssignee(data);

    let dsd: string[] = dsp;
    const runing = getWorkflowIsRuning(item.status);
    if ((needApproval && runing) || ds === OneTableNewDataStateEnum.REFUSED) {
      // 已结束或被取消, 不需要进一步展示状态
      dsd = [...this.getDataStateDisplay(ds), ...dsp];
    }
    return {
      id: item.workflow_id,
      assignWfId: item.workflow_id,
      rootWfId: data.root_workflow_id,
      // 确保负责人在第一位
      userIds: _.uniq([primaryAssignee, ...getAssignees(data)]),
      primaryAssignee: primaryAssignee,
      dataState: ds,
      dataStateDisplay: dsd,
      running: runing,
      submitStatusDisplay: this.getSubmitStatusLable(ds),
      dealTime: formateDateByUnix(item.create_time, 2),
      approvalStatusDisplay: this.getApproveStatusLable(ds),
      reason,
      dataDeliveryInfo: statDataMap?.[item.workflow_id],
      isCancelledOrRefused: item.status === 'cancelled' || ds === OneTableNewDataStateEnum.REFUSED,
    };
  }

  public static async postGeneralNotice(
    body: Omit<IOneTableNoticesBody, 'notices'> & {
      notices: (Omit<IOneTableNotices, 'message'> & {
        status?: string;
      })[];
    },
    msgInfo: {
      title?: string;
      userName?: string;
      endDate?: string;
      [key: string]: any;
    },
  ): Promise<boolean> {
    const { notices, ...rest } = body;
    const { userName, title, endDate } = msgInfo;

    const defaultMsg = (status?: string) =>
      `
报表名称：${title}
截止日期：${endDate}
任务状态：${status}
`;
    const overridedBody: IOneTableNoticesBody = {
      ...rest,
      notices: notices.map((it) => {
        const { status, ...rest } = it;
        return {
          ...rest,
          title: `${userName}的催办 ${ONE_TABLE_EXPEDITE_TITLE_EXTRA}`,
          message: defaultMsg(status),
        };
      }),
    };
    const resp = await postOneTableNoticesAsync(overridedBody);
    return resp.success;
  }

  private static getDataStateDisplay(state: string) {
    if (state === OneTableNewDataStateEnum.UNSUBMITTED) {
      return [i18n.chain.dataStateLabel.unsubmitted, 'primary'];
    }
    if (state === OneTableNewDataStateEnum.SUBMITTED) {
      return [i18n.chain.taskStatusLabel.unapproved2, 'warning'];
    }
    if (state === OneTableNewDataStateEnum.APPROVED) {
      return [i18n.chain.dataStateLabel.approved, 'success'];
    }
    if (state === OneTableNewDataStateEnum.REJECTED) {
      return [i18n.chain.dataStateLabel.rejected, 'error'];
    }
    if (state === OneTableNewDataStateEnum.REFUSED) {
      return [i18n.chain.dataStateLabel.refused, 'error'];
    }
    return [];
  }

  private static getSubmitStatusLable(state: string) {
    return state === OneTableNewDataStateEnum.UNSUBMITTED
      ? [i18n.chain.dataActionStatus.unsubmit, 'primary']
      : [i18n.chain.dataActionStatus.submit, 'success'];
  }

  private static getApproveStatusLable(state: string) {
    if (state === OneTableNewDataStateEnum.APPROVED) {
      return [i18n.chain.proMicroModules.workflow.status.completed, 'success'];
    }
    if (state === OneTableNewDataStateEnum.REJECTED) {
      return [i18n.chain.proMicroModules.workflow.status.reject, 'error'];
    }
    if (state !== OneTableNewDataStateEnum.UNSUBMITTED) {
      return [i18n.chain.comText.approval, 'warning'];
    }
    return [];
  }
}

export type IDeliveryManagementModel = typeof DeliveryManagementModel;
