import _ from 'lodash';
import { VisibilityOn } from '@metro/icons';
import { But<PERSON> } from '@metroDesign/button';
import { ONE_TABLE_INFO } from '../../datlas/datlasConfig';
import i18n from '../../languages';
import { getFastViewOperatorFilter } from '../../utils/oneTableNewFilterUtil';
import { FastViewTypeEnum } from '../../utils/oneTableNewUtil';
import { OneTableNewDataTableStaticsController } from '../one-table-new-data-table-statics';

class OneTableNewReportDetailDataTableController extends OneTableNewDataTableStaticsController {
  public getFastSelectFilterProps() {
    const allOptions = [
      {
        value: FastViewTypeEnum.All,
        label: i18n.chain.proMicroModules.oneTable.fastView.all,
        realVal: '',
      },
      {
        value: FastViewTypeEnum.Add,
        label: i18n.chain.proMicroModules.oneTable.fastView.add,
        realVal: this.getRealVal(FastViewTypeEnum.Add),
      },
      {
        value: FastViewTypeEnum.Update,
        label: i18n.chain.proMicroModules.oneTable.fastView.update,
        realVal: this.getRealVal(FastViewTypeEnum.Update),
      },
      {
        value: FastViewTypeEnum.Delete,
        label: i18n.chain.proMicroModules.oneTable.fastView.delete,
        realVal: this.getRealVal(FastViewTypeEnum.Delete),
      },
      {
        value: FastViewTypeEnum.Untouch,
        label: i18n.chain.proMicroModules.oneTable.fastView.untouch,
        realVal: this.getRealVal(FastViewTypeEnum.Untouch),
      },
      {
        value: FastViewTypeEnum.AllWithSubordinates,
        label: i18n.chain.proMicroModules.oneTable.fastView.allWithSubordinates,
        extraParams: { row_permission_params: { permission_scope: 'all' }, operator_filter: {} },
      },
    ];
    const includeFastSelectKeys = ONE_TABLE_INFO.includeFastSelectKeys;
    const excludeFastSelectKeys = ONE_TABLE_INFO.excludeFastSelectKeys;
    let validValues: FastViewTypeEnum[] = [
      FastViewTypeEnum.All,
      FastViewTypeEnum.Add,
      FastViewTypeEnum.Update,
      FastViewTypeEnum.Delete,
      FastViewTypeEnum.Untouch,
    ];

    if (!_.isEmpty(includeFastSelectKeys)) {
      validValues = _.union(validValues, includeFastSelectKeys) as FastViewTypeEnum[];
    }

    if (excludeFastSelectKeys && !_.isEmpty(excludeFastSelectKeys)) {
      validValues = _.difference(validValues, excludeFastSelectKeys) as FastViewTypeEnum[];
    }

    const filteredOptions = _.filter(allOptions, (option) => _.includes(validValues, option.value));

    return {
      options: filteredOptions,
      defaultValue: filteredOptions?.[0] ?? null,
      width: 170,
    };
  }

  protected renderPreviewOperation(row: any) {
    const fastSelect = this.getFastSelectFilterValue$().getValue();
    const isAllWithSubordinates = fastSelect?.value === FastViewTypeEnum.AllWithSubordinates;
    if (isAllWithSubordinates) {
      return [
        <Button
          key="preview"
          ghost
          icon={<VisibilityOn />}
          onlyIcon
          showTooltip
          tooltipTitle={i18n.chain.proMicroModules.oneTable.allWithSubordinatesTip}
          disabled
        />,
      ];
    }
    return super.renderPreviewOperation(row);
  }

  private getRealVal(key: FastViewTypeEnum) {
    const filterFunc = getFastViewOperatorFilter(key);
    return filterFunc?.();
  }
}

export { OneTableNewReportDetailDataTableController };
