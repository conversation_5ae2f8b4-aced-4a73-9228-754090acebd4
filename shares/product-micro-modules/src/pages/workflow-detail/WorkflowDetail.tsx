import _ from 'lodash';
import { FC } from 'react';
import Clear from '@metro/icons/dist/esm/react/Clear';
import ClockCountdown from '@metro/icons/dist/esm/react/ClockCountdown';
import SuccessOutlined from '@metro/icons/dist/esm/react/SuccessOutlined';
import ToolBarShapeCircle from '@metro/icons/dist/esm/react/ToolBarShapeCircle';
import { Button as MetroButton } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Tag } from '@metroDesign/tag';
import { Typography } from '@metroDesign/typography';
import { useBoolean } from 'ahooks';
import { FormSpecTypeEnum } from '@mdtBpmnPropertiesPanel/properties/FormSpecProps';
import { If } from '@mdtBsComm/components/if';
import { ToggleSectionList } from '@mdtBsComm/components/toggle-section-list';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { deepOmit } from '@mdtBsComm/utils/deepOmitUtil';
import Button, { IconButton, LinkButton } from '@mdtDesign/button';
import { RadioGroup } from '@mdtDesign/radio';
import Scrollbar from '@mdtDesign/scrollbar';
import Spin from '@mdtDesign/spin';
import Timeline, { TimelineItem } from '@mdtDesign/timeline';
import { NameCacheEnum } from '@mdtProComm/controllers/NameCacheController';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { FormSpec } from '../../components/form-spec';
import { FormView } from '../../components/form-view';
import {
  ShowFullGroupIdToName,
  ShowFullOrgIdToName,
  ShowRoleIdToName,
  TransformIdToName,
} from '../../components/transform-id-to-name';
import { CommentInputContainer, CommentListContainer, CommentWrapper } from '../../containers/comment';
import i18n from '../../languages';
import { DrawerWorkflowForm } from '../../pages-in-micro/workflow/components/workflow-form/drawer-workflow-form';
import { WorkflowDetailCustom } from './custom-page/WorkflowDetailCustom';
import {
  DetailModuleEnum,
  detailModuleOptions,
  FlowNodeTypeEnum,
  IAssignInfo,
  IFlowNode,
  WorkflowDetailController,
} from './WorkflowDetailController';
import './index.less';

export interface IProps {
  controller: WorkflowDetailController;
}

const CancelBtn: FC<IProps> = ({ controller }) => {
  const isCancelable = controller.canShowCancelButton();
  if (!isCancelable) return null;
  return (
    <MetroButton.Link danger onClick={() => controller.handleCancel()}>
      {i18n.chain.proMicroModules.workflow.cancel}
    </MetroButton.Link>
  );
};
const ApprovalTools: FC<IProps> = ({ controller }) => {
  const tools = controller.getIsApprovalOfTaskType() ? (
    <>
      <Button status="danger" type="primary" onClick={() => controller.handleApproval('rejected')}>
        {i18n.chain.proMicroModules.workflow.rejected}
      </Button>
      <Button className="confirm-btn" type="primary" onClick={() => controller.handleApproval('approved')}>
        {i18n.chain.proMicroModules.workflow.approved}
      </Button>
    </>
  ) : (
    <Button className="confirm-btn" type="primary" onClick={() => controller.handleApproval('approved')}>
      {controller.getInfoBtnText()}
    </Button>
  );
  return <div className="approval-tools">{tools}</div>;
};

const ApplyInfo: FC<IProps> = ({ controller }) => {
  const formSpecData = controller.getRootFormSpecOptions();
  const { formSpec, formData } = formSpecData;

  const formInfo = _.isEmpty(formSpec) ? (
    i18n.chain.proMicroModules.workflow.noInitiate
  ) : formSpec?.type === FormSpecTypeEnum.FORMILY_V2 ? (
    <FormView
      formilySchema={formSpec.spec}
      formData={formData}
      readonly
      allSettingValues={deepOmit(_.get(formSpec.spec, 'allSettingValues'), [MetroSettingProp.dynamic])}
    />
  ) : (
    <FormSpec {...formSpecData} />
  );
  return (
    <Scrollbar>
      {/* <div className="apply-user">发起人: {controller.getApplyUserName()}</div> */}
      {formInfo}
    </Scrollbar>
  );
};

export interface IApprovalInfoItemProps {
  node: IFlowNode;
  controller: WorkflowDetailController;
}

const AssigneesByType: FC<{
  assignments: IAssignInfo[] | undefined;
  type: NameCacheEnum;
  taskType: string;
}> = ({ assignments, type, taskType }) => {
  let filteredAssignments = (assignments || []).filter((assign) => assign.type === type);
  if (filteredAssignments.length === 0) return null;

  const prefixType =
    taskType === 'approval' ? i18n.chain.proMicroModules.workflow.approval : i18n.chain.proMicroModules.workflow.handle;

  let suffix = '';
  if (type === NameCacheEnum.USER) {
    suffix = i18n.chain.comText.user;
  } else if (type === NameCacheEnum.ROLE) {
    suffix = i18n.chain.comText.role;
  } else if (type === NameCacheEnum.USERORG) {
    suffix = i18n.chain.comText.org;
  } else if (type === NameCacheEnum.GROUP) {
    suffix = i18n.chain.comText.group;
  }
  const prefix = `${prefixType}${suffix}：`;
  return (
    <Flex>
      <span style={{ marginRight: '8px', whiteSpace: 'nowrap' }}>{prefix}</span>
      {filteredAssignments.map((assign) => (
        <Flex key={assign.type} wrap>
          {assign.ids.map((id: number, idIndex: number) => (
            <span key={`${id}_${idIndex}`}>
              {type === NameCacheEnum.USER && <TransformIdToName id={id} type={type} />}
              {type === NameCacheEnum.GROUP && <ShowFullGroupIdToName id={id} />}
              {type === NameCacheEnum.ROLE && <ShowRoleIdToName id={id} />}
              {type === NameCacheEnum.USERORG && <ShowFullOrgIdToName id={id} />}
              {idIndex < assign.ids.length - 1 && '、'}
            </span>
          ))}
        </Flex>
      ))}
    </Flex>
  );
};

const AssigneesList: FC<{ applyInfo: IFlowNode['applyInfo'] }> = ({ applyInfo }) => {
  if (!applyInfo || !(applyInfo.assignments || []).length) return null;

  const taskType = applyInfo.taskType || '';
  const assignments = applyInfo.assignments || [];
  const types = [NameCacheEnum.USER, NameCacheEnum.USERORG, NameCacheEnum.GROUP, NameCacheEnum.ROLE];

  const actionText =
    taskType === 'approval' ? i18n.chain.proMicroModules.workflow.approval : i18n.chain.proMicroModules.workflow.handle;

  return (
    <Flex vertical>
      <Typography.Text type="secondary" strong style={{ padding: '8px 0 4px' }}>
        {i18n.chain.proMicroModules.workflow.approvalOrHandleTitle(actionText)}
      </Typography.Text>
      {types.map(
        (type) =>
          assignments.some((assign) => assign.type === type) && (
            <AssigneesByType key={type} assignments={assignments} type={type} taskType={taskType} />
          ),
      )}
    </Flex>
  );
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const ApprovalInfoItem: FC<IApprovalInfoItemProps> = ({ node, controller }) => {
  const [open, { toggle }] = useBoolean(false);

  let nodeIcon;
  if (node.type === FlowNodeTypeEnum.APPROVAL_FAIL) {
    nodeIcon = <Clear style={{ color: 'var(--metro-danger-default)', fontSize: '20px' }} />;
  } else if (node.type === FlowNodeTypeEnum.ONGOING) {
    nodeIcon = <ClockCountdown style={{ color: 'var(--metro-info-default)', fontSize: '20px' }} />;
  } else if (node.type === FlowNodeTypeEnum.END || node.type === FlowNodeTypeEnum.CANCELLED) {
    nodeIcon = <ToolBarShapeCircle style={{ fontSize: '16px' }} />;
  } else {
    nodeIcon = <SuccessOutlined style={{ color: 'var(--metro-success-default)', fontSize: '20px' }} />;
  }
  const formSpec = node.formSpec;
  let formContent = null;
  if (formSpec?.type === FormSpecTypeEnum.FORMILY_V2) {
    formContent = (
      <FormView
        formilySchema={formSpec.spec}
        formData={node.formData}
        readonly
        allSettingValues={_.get(formSpec.spec, 'allSettingValues')}
      />
    );
  } else if (formSpec?.type === FormSpecTypeEnum.FORMILY) {
    formContent = <FormSpec formSpec={node.formSpec} formData={node.formData} readonly />;
  }

  const [icon, text] = open ? ['arrow-up', i18n.chain.comText.close] : ['arrow-down', i18n.chain.comText.showFullInfo];
  const renderToggleBtn = (showBtn: boolean) => {
    return showBtn ? (
      <LinkButton leftIcon={icon} onClick={toggle}>
        {text}
      </LinkButton>
    ) : null;
  };

  const excutor = node.excutor ? `${node.excutor}` : '';
  let approvalStatus: any = '';
  if (node.type === FlowNodeTypeEnum.APPROVAL_PASS) {
    approvalStatus = (
      <Tag color="success" type="light">
        {i18n.chain.proMicroModules.workflow.approved}
      </Tag>
    );
  } else if (node.type === FlowNodeTypeEnum.APPROVAL_FAIL) {
    approvalStatus = (
      <Tag color="error" type="light">
        {i18n.chain.proMicroModules.workflow.refuse}
      </Tag>
    );
  }

  const content =
    node.type === FlowNodeTypeEnum.END ? null : (
      <div className="content-wrap">
        {node.type === FlowNodeTypeEnum.CANCELLED ? null : (
          <div className="excutor">
            {node.type === FlowNodeTypeEnum.ONGOING && node.applyInfo ? (
              <AssigneesList applyInfo={node.applyInfo} />
            ) : (
              <span>{excutor || i18n.chain.proMicroModules.workflow.anonymous}</span>
            )}
            {approvalStatus}
          </div>
        )}
        <ToggleSectionList maxHeight={200} section={open} renderToggleBtn={renderToggleBtn}>
          {formContent}
        </ToggleSectionList>
      </div>
    );
  const cls = node.type === FlowNodeTypeEnum.APPROVAL_FAIL ? 'node-fail' : 'node-success';
  return (
    <TimelineItem dot={nodeIcon} key={`${node.name}_${node.excutor}`} className={cls}>
      <div className="node-name-wrap">
        <span className="node-name">{node.name}</span>
        <Flex className="excute-date" align="center" gap={6}>
          {node.date}
          <If data={!!node.taskId}>
            <CommentWrapper
              controllerOptions={{
                id: controller.getWorkflowId(),
                extraParams: {
                  task_id: node.taskId,
                },
              }}
              title={`${i18n.chain.proMicroModules.comment.comment} ${node.name}`}
            />
          </If>
        </Flex>
      </div>
      {content}
    </TimelineItem>
  );
};

const ApprovalInfo: FC<IProps> = ({ controller }) => {
  const aList = controller.getApprovalList();
  const commentController = controller.getCommentController();
  const list = _.map(aList, (item, index) => <ApprovalInfoItem node={item} key={index} controller={controller} />);
  const content = _.isEmpty(aList) ? i18n.chain.comNoData : list;
  return (
    <Flex vertical style={{ height: '100%', overflow: 'hidden' }}>
      <Flex vertical flex={1} style={{ overflow: 'hidden' }}>
        <Scrollbar className="workflow-node-list">
          <Timeline className="workflow-graph-timeline">{content}</Timeline>
          <CommentListContainer controller={commentController} />
        </Scrollbar>
      </Flex>
      <Flex style={{ flexShrink: 0 }}>
        <CommentInputContainer controller={commentController} />
      </Flex>
    </Flex>
  );
};

const ActionButtons: FC<IProps> = ({ controller }) => {
  const isToApproval = controller.getIsToApproval();
  return isToApproval ? <ApprovalTools controller={controller} /> : null;
};

const PcView: FC<IProps> = ({ controller }) => {
  const closeBtn = controller.showCloseBtn() ? (
    <IconButton className="wf-close-btn" icon="close" ghost onClick={controller.handleClose} />
  ) : null;

  return (
    <div className="workflow-detail-page">
      <div className="name-tool-wrap">
        <div className="wf-name-wrap">
          {closeBtn}
          <div className="wf-name">{controller.getWfName()}</div>
        </div>
        <Flex gap={8}>
          <CancelBtn controller={controller} />
          <ActionButtons controller={controller} />
        </Flex>
      </div>
      <div className="graph-wrap">
        <div className="module-title">{i18n.chain.proMicroModules.workflow.circulationInfo}</div>
        <ApprovalInfo controller={controller} />
      </div>
      <div className="apply-info">
        <div className="module-title">{i18n.chain.proMicroModules.workflow.initiateInfo}</div>
        <ApplyInfo controller={controller} />
      </div>
    </div>
  );
};

const MobileView: FC<IProps> = ({ controller }) => {
  const module = useObservableState(controller.getModule$());
  const content =
    DetailModuleEnum.APPROVAL === module ? (
      <ApprovalInfo controller={controller} />
    ) : (
      <ApplyInfo controller={controller} />
    );
  const closeBtn = controller.showCloseBtn() ? (
    <IconButton className="wf-close-btn" icon="close" ghost onClick={controller.handleClose} />
  ) : null;

  return (
    <div className="workflow-detail-page workflow-detail-page-h5">
      <div className="wf-name-wrap">
        {closeBtn}
        <div className="wf-name">{controller.getWfName()}</div>
      </div>
      <ActionButtons controller={controller} />
      <RadioGroup
        className="module-select"
        radioType="button"
        onChange={controller.handleModuleChange}
        value={module}
        options={detailModuleOptions}
      />
      {content}
    </div>
  );
};

const WorkflowDetailInner: FC<IProps> = ({ controller }) => {
  const view = isPc() ? <PcView controller={controller} /> : <MobileView controller={controller} />;

  return (
    <>
      {view}
      <DrawerWorkflowForm controller={controller.getApprovalController()} />
    </>
  );
};

export const WorkflowDetail: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getLoading$());

  return loading ? (
    <Spin className="workflow-detail-loading" spinning={true} tip={i18n.chain.comDataLoading} />
  ) : (
    <WorkflowDetailCustom DefaultView={WorkflowDetailInner} controller={controller} />
  );
};
