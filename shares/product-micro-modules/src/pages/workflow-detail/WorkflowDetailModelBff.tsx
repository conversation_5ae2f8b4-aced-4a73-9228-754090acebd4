import _ from 'lodash';
import { combineLatest } from 'rxjs';
import { FormSpecTypeEnum } from '@mdtBpmnPropertiesPanel/properties/FormSpecProps';
import { FLOWORK_TASK_DATA_WITH_EXECUTOR, WORKFLOW_NODE_DATA } from '@mdtBsBffServices/services';
import { DATE_FORMATTER_1, formateDateWithoutMillisecond } from '@mdtBsComm/utils/dayUtil';
import { WorkflowBffService } from '@mdtProComm/bff-services';
import { NameCacheEnum } from '@mdtProComm/controllers/NameCacheController';
import { IFloworkTask, IFormSpec } from '@mdtProComm/interfaces';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { WorkFlowFormModelBff } from '../../pages-in-micro/workflow/_util/WorkFlowFormModelBff';
import { getDefaultValue } from '../../utils/bpmnUtil';
import { ApplyStatusEnum } from '../workflow-application-list';
import { FlowNodeTypeEnum, IAssignInfo, IDrawerWorkflowUiData, IFlowNode } from './WorkflowDetailController';

export const isAssignToMe = (task: IFloworkTask) => {
  const app = DatlasAppController.getInstance();
  const userId = app.getUserId();
  const userRoles = app.getUserRoles();
  return (
    _.includes(task.assign_users, userId) ||
    !!_.intersection(userRoles, task.assign_groups).length ||
    !!_.intersection(userRoles, task.assign_roles).length
  );
};

export class WorkflowDetailModelBff {
  // eslint-disable-next-line sonarjs/cognitive-complexity, max-params
  public static async queryUiData(
    workflowId: string,
    userTaskXmlId?: string,
    userTaskId?: string,
    specifiedRootXmlId?: string,
    showNickname?: boolean,
  ): Promise<IDrawerWorkflowUiData> {
    const nodesResp = await WorkflowBffService.queryWorkflowNodes({
      id: workflowId,
      params: { with_data: true },
      respData: WORKFLOW_NODE_DATA,
      disableBffProxy: true,
    });
    const nodes = nodesResp.success ? nodesResp.page_data : [];
    const nodeIds = _.map(nodes, 'spec.id');
    const nodeSpecId2InfoMap: Record<string, { specName: string; data: any }> = {};
    _.forEach(nodes, (n) => {
      nodeSpecId2InfoMap[n.id] = {
        specName: n.spec.name,
        data: n.data,
      };
    });

    const [formInfo, tasksResp] = await combineLatest([
      WorkFlowFormModelBff.getFormSpecByWfId(workflowId, nodeIds, true, showNickname),
      WorkflowBffService.queryWorkflowTasks({
        id: workflowId,
        params: { with_data: true },
        respData: FLOWORK_TASK_DATA_WITH_EXECUTOR,
        disableBffProxy: true,
      }),
    ]).toPromise();
    const {
      applyUserName,
      rootFormSpec,
      rootFormData,
      wfSpecType,
      allSettingValues,
      detailPageMap,
      createTime,
      wfName,
      wfStatus,
      contextData,
      initiator,
      ...nodeSpecs
    } = formInfo;
    const isWfComplete = wfStatus === ApplyStatusEnum.COMPLETED;
    const isWfCancelled = wfStatus === ApplyStatusEnum.CANCELLED;
    const userTasks = _.filter(
      tasksResp.success ? tasksResp.page_data : [],
      (t) => t.status === 'completed' || t.status === 'ready',
    );

    let toApprovalTask: IFloworkTask = null!;
    // node.spec.name是节点的展示名字
    // node.data 当前节点form填写的内容以及上个节点传过来的内容（其值是离它最近的一个userTask节点的form data）
    // task.name 等于 node.spec.id
    // task.task_id 等于 node.id
    const approvalList: IFlowNode[] = _.map(userTasks, (t) => {
      let type: FlowNodeTypeEnum;
      let applyInfo: IFlowNode['applyInfo'];
      if (t.status === 'ready') {
        type = FlowNodeTypeEnum.ONGOING;
        if (isAssignToMe(t)) {
          if (userTaskId && t.task_id === userTaskId) {
            toApprovalTask = t;
          }
          if (!toApprovalTask && !userTaskId) {
            toApprovalTask = t;
          }
        }

        const assignments: IAssignInfo[] = _.filter(
          [
            { ids: _.get(t, 'assign_users', []), type: NameCacheEnum.USER },
            { ids: _.get(t, 'assign_groups', []), type: NameCacheEnum.GROUP },
            { ids: _.get(t, 'assign_roles', []), type: NameCacheEnum.ROLE },
            { ids: _.get(t, 'assign_orgs', []), type: NameCacheEnum.USERORG },
          ],
          (item) => !_.isEmpty(item.ids),
        );

        applyInfo = {
          assignments,
          taskType: t.task_type,
        };
      } else if (t.task_type === 'approval') {
        if (t.approval_result === 'rejected') {
          type = FlowNodeTypeEnum.APPROVAL_FAIL;
        } else {
          type = FlowNodeTypeEnum.APPROVAL_PASS;
        }
      } else {
        type = FlowNodeTypeEnum.NORMAL_PASS;
      }
      const nodeInfo = nodeSpecId2InfoMap[t.task_id];
      const executor = showNickname ? t.executorObj?.nickname || t.executorObj?.name || '' : t.executorObj?.name || '';

      return {
        name: nodeInfo?.specName || i18n.chain.proMicroModules.workflow.anonymousNode,
        date: formateDateWithoutMillisecond(t.update_time, DATE_FORMATTER_1),
        type,
        excutor: executor,
        formData: nodeInfo?.data,
        formSpec: nodeSpecs[t.name],
        taskId: t.task_id,
        applyInfo,
      };
    });
    approvalList.push({
      name: i18n.chain.proMicroModules.workflow.fillStart,
      date: createTime,
      type: FlowNodeTypeEnum.START,
      excutor: applyUserName,
      formData: {},
      formSpec: { spec: {} },
    });

    isWfCancelled &&
      approvalList.unshift({
        name: i18n.chain.proMicroModules.workflow.cancelled,
        type: FlowNodeTypeEnum.CANCELLED,
        formData: {
          workflow_cancel_comment: contextData?.workflow_cancel_comment,
        },
        formSpec: {
          type: FormSpecTypeEnum.FORMILY_V2,
          spec: {
            schema: {
              properties: {
                workflow_cancel_comment: {
                  type: 'string',
                  title: i18n.chain.proMicroModules.workflow.cancelledReason,
                  'x-component': 'Input',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    layout: 'vertical',
                    colon: false,
                    style: { marginTop: 10 },
                  },
                  'x-component-props': {
                    placeholder: i18n.chain.proMicroModules.workflow.cancelled,
                  },
                },
              },
            },
          },
        },
      });
    isWfComplete &&
      approvalList.unshift({
        name: i18n.chain.proMicroModules.workflow.flowEnd,
        type: FlowNodeTypeEnum.END,
        formData: {},
        formSpec: { spec: {} },
      });
    const sortedApprovalList = _.reverse(approvalList);

    let approvalSpec: IFormSpec | undefined;
    let approvalFormDefaultValue = {};
    const toApprovalNodeId = toApprovalTask?.name;
    let detailPageKey = userTaskXmlId || toApprovalNodeId;
    if (toApprovalNodeId) {
      approvalSpec = nodeSpecs[toApprovalNodeId];
      approvalSpec &&
        (approvalFormDefaultValue = getDefaultValue({
          formSpec: approvalSpec,
          contextData,
        }));
    }
    const detailPage = detailPageKey ? detailPageMap[detailPageKey] : detailPageMap.rootDetailPage;
    const rootFormInfo = { rootFormSpec, rootFormData };
    const sprt = specifiedRootXmlId && nodeSpecs[specifiedRootXmlId];
    if (sprt) {
      rootFormInfo.rootFormSpec = sprt;
      rootFormInfo.rootFormData = getDefaultValue({ formSpec: sprt, contextData });
    }

    return {
      applyUserName,
      ...rootFormInfo,
      wfSpecType,
      allSettingValues,
      approvalList: sortedApprovalList,
      approvalSpec,
      approvalFormDefaultValue,
      toApprovalTask,
      wfName,
      wfStatus,
      detailPage,
      initiator,
    };
  }

  public static async cancelWorkflow(workflowId: string, reason?: string) {
    const resp = await WorkflowBffService.cancelWorkflow({
      id: workflowId,
      disableBffProxy: true,
      data: { cancel_comment: reason },
    });
    return resp.success;
  }

  public static getWorkflowFormModel() {
    return WorkFlowFormModelBff;
  }
}

export type IWorkflowDetailModel = typeof WorkflowDetailModelBff;
