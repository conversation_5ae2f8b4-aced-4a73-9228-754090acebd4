import { BehaviorSubject } from 'rxjs';
import { IFloworkTask, IIFloworkTaskType } from '@mdtApis/interfaces';
import { getParamValueFromUrl } from '@mdtBsComm/utils/urlUtil';
import { RequestController } from '@mdtBsControllers/request-controller';
import { WfSpecTypeEnum, WorkflowDetailPageTypeEnum } from '@mdtProComm/constants';
import { NameCacheEnum } from '@mdtProComm/controllers/NameCacheController';
import { IEmptyFunc, IIFloworkApprovalResult, IWorkflowDetailPageConfig } from '@mdtProComm/interfaces';
import { isInsideIframe } from '@mdtProComm/utils/commonUtil';
import { getShowNicknameFromUrl } from '@mdtProComm/utils/urlUtil';
import { ModalReason } from '../../components/modal-reason';
import { CommentController } from '../../containers/comment';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { DrawerWorkflowFormController } from '../../pages-in-micro/workflow/components/workflow-form/drawer-workflow-form';
import type { IFormSpec } from '../../utils/bpmn-xml-util';
import { ApplyStatusEnum } from '../workflow-application-list/WorkflowApplicationListController';
import { IWorkflowDetailModel } from './WorkflowDetailModelBff';

export enum DetailModuleEnum {
  APPLY = 'apply',
  APPROVAL = 'approval',
}

export const detailModuleOptions = [
  {
    label: i18n.chain.proMicroModules.workflow.applyInfo,
    value: DetailModuleEnum.APPLY,
  },
  {
    label: i18n.chain.proMicroModules.workflow.flowInfo,
    value: DetailModuleEnum.APPROVAL,
  },
];

// export interface IApprovalDetailData {
//   status: string;
//   workflowId: string;
//   workflowName: string;
//   workflowDesc?: string;
//   wfSpecName: string;
//   applyTime: string;
//   statusDisplay: string[];
//   // 我发起的下面三个字段没值
//   nodeId?: string;
//   taskId?: string;
//   taskType?: IIFloworkTaskType;
// }

// 流转节点类型
export enum FlowNodeTypeEnum {
  START = 'start',
  NORMAL_PASS = 'normal_pass',
  APPROVAL_PASS = 'approval_pass',
  APPROVAL_FAIL = 'approval_fail',
  ONGOING = 'ongoing',
  END = 'end',
  CANCELLED = 'cancelled',
}

export interface IAssignInfo {
  ids: number[];
  type: NameCacheEnum;
}

export interface IFlowNode {
  name: string;
  date?: string;
  type: FlowNodeTypeEnum;
  excutor?: string;
  formData: any;
  formSpec: IFormSpec;
  taskId?: string;
  applyInfo?: {
    taskType: IIFloworkTaskType;
    assignments: IAssignInfo[];
  };
}

export interface IDrawerWorkflowUiData {
  applyUserName: string;
  rootFormSpec: IFormSpec;
  rootFormData: any;
  approvalSpec?: IFormSpec;
  approvalFormDefaultValue?: any;
  approvalList: IFlowNode[];
  wfSpecType: WfSpecTypeEnum;
  allSettingValues?: Record<string, any>;
  toApprovalTask?: IFloworkTask;
  wfName: string;
  detailPage?: IWorkflowDetailPageConfig;
  wfStatus?: string;
  initiator?: number;
}

export interface IApprovalDetailController {
  Model: IWorkflowDetailModel;
  workflowId: string;
  userTaskXmlId?: string;
  userTaskId?: string;
  specifiedRootXmlId?: string;
}

export class WorkflowDetailController extends RequestController {
  private approvalController: DrawerWorkflowFormController;
  private commentController: CommentController;
  private Model: IWorkflowDetailModel;
  private loading$ = new BehaviorSubject<boolean>(true);
  private module$ = new BehaviorSubject<DetailModuleEnum>(DetailModuleEnum.APPLY);
  private onSuccessCb?: IEmptyFunc;
  private onCancelCb?: IEmptyFunc;
  private workflowId: string;
  private approvalSpec?: IFormSpec;
  private applyUserName?: string;
  private approvalList?: IFlowNode[];
  private approvalFormDefaultValue?: any;
  private toApprovalTask?: IFloworkTask;
  private rootFormSpecOptions?: any;
  private wfName?: string;
  private userTaskXmlId?: string;
  private userTaskId?: string;
  private specifiedRootXmlId?: string;
  private detailPage?: IWorkflowDetailPageConfig;
  private wfStatus?: string;
  private initiator?: number;

  public constructor(
    { Model, workflowId, userTaskXmlId, userTaskId, specifiedRootXmlId }: IApprovalDetailController,
    onSuccessCb?: IEmptyFunc,
    onCancelCb?: IEmptyFunc,
  ) {
    super();
    this.onSuccessCb = onSuccessCb;
    this.onCancelCb = onCancelCb;
    this.Model = Model;
    this.userTaskId = userTaskId;
    this.userTaskXmlId = userTaskXmlId;
    this.specifiedRootXmlId = specifiedRootXmlId;
    this.workflowId = workflowId;
    this.approvalController = new DrawerWorkflowFormController();
    this.commentController = new CommentController({
      id: workflowId,
    });
    // this.userTaskId = 'e1584c41-05a8-4076-b06a-914debc49619';
    // this.workflowId = '36cc2d09-558f-4d05-9f36-a23b22eb0df7';
    this.init();
  }

  public destroy() {
    super.destroy();
    this.approvalController.destroy();
    this.commentController.destroy();
    this.Model = null!;
    this.module$.complete();
    this.loading$.complete();
    this.onSuccessCb = undefined;
    this.onCancelCb = undefined;
    this.workflowId = null!;
    this.approvalSpec = null!;
    this.approvalList = null!;
    this.approvalFormDefaultValue = undefined;
    this.detailPage = undefined;
    this.initiator = undefined;
  }

  public getCommentController() {
    return this.commentController;
  }

  public getCustomDetailPage() {
    return this.detailPage || { pageType: WorkflowDetailPageTypeEnum.DEFAULT };
  }

  public getWfName() {
    return this.wfName;
  }

  public getWorkflowId() {
    return this.workflowId;
  }

  public showCloseBtn() {
    return !!this.onCancelCb;
  }

  public handleClose = () => {
    this.onCancelCb?.();
  };

  public getModule$() {
    return this.module$;
  }

  public getLoading$() {
    return this.loading$;
  }

  public handleModuleChange = (val: string) => {
    this.module$.next(val as DetailModuleEnum);
  };

  public getApprovalController() {
    return this.approvalController;
  }

  public getRootFormSpecOptions() {
    return this.rootFormSpecOptions;
  }

  public getApprovalList() {
    return this.approvalList;
  }

  public getApplyUserName() {
    return this.applyUserName;
  }

  public getIsToApproval() {
    return !!this.toApprovalTask;
  }

  public getIsApprovalOfTaskType() {
    return this.toApprovalTask?.task_type === 'approval';
  }

  public getInfoBtnText() {
    return getParamValueFromUrl('infoBtn') || i18n.chain.proMicroModules.workflow.inputInfo;
  }

  public getInfoTitleText() {
    return getParamValueFromUrl('infoTitle') || i18n.chain.proMicroModules.workflow.inputFormInfo;
  }

  public canShowCancelButton() {
    const isInitiator = DatlasAppController.getInstance().getUserId() === this.initiator;
    return this.wfStatus === ApplyStatusEnum.TO_PROCESS && isInitiator;
  }

  public handleCancel = () => {
    if (!this.workflowId) return;

    ModalReason.error({
      title: i18n.chain.proMicroModules.workflow.cancelConfirm,
      onOk: async (reason: string) => await this.deleteDataToService(reason),
      okCancel: true,
      okButtonProps: { danger: true },
      reasonOptions: {
        require: true,
        tipText: i18n.chain.proMicroModules.workflow.cancelRequired,
        componentProps: {
          placeholder: i18n.chain.proMicroModules.workflow.cancelConfirmDesc,
        },
      },
    });
  };

  public handleApproval = async (status: IIFloworkApprovalResult) => {
    const taskId = this.toApprovalTask?.task_id;
    const title =
      this.toApprovalTask?.task_type === 'approval'
        ? `${
            status === 'approved'
              ? i18n.chain.proMicroModules.workflow.approved
              : i18n.chain.proMicroModules.workflow.rejected
          } ${taskId}`
        : this.getInfoTitleText();
    const formSpec = this.approvalSpec;

    if (!formSpec) {
      const success = await this.Model!.getWorkflowFormModel().approval(taskId!, status, {});
      success && this.onSuccessCb?.();
      return;
    }
    this.approvalController
      .openModal({
        title,
        formSpec,
        formData: this.approvalFormDefaultValue,
        onSubmit: async (data: any) => {
          const success = await this.Model!.getWorkflowFormModel().approval(this.toApprovalTask!.task_id, status, data);
          return { success };
        },
      })
      .subscribe(async (val) => {
        val.success && this.onSuccessCb?.();
      });
  };

  private async init() {
    const {
      rootFormSpec,
      rootFormData,
      approvalSpec,
      approvalFormDefaultValue,
      approvalList,
      applyUserName,
      wfSpecType,
      allSettingValues,
      toApprovalTask,
      wfName,
      detailPage,
      wfStatus,
      initiator,
    } = await this.Model.queryUiData(
      this.workflowId,
      this.userTaskXmlId,
      this.userTaskId,
      this.specifiedRootXmlId,
      getShowNicknameFromUrl(),
    );

    this.rootFormSpecOptions = {
      formSpec: rootFormSpec,
      formData: rootFormData,
      readonly: true,
      wfSpecType,
      allSettingValues,
    };
    this.approvalSpec = approvalSpec;
    this.approvalFormDefaultValue = approvalFormDefaultValue;
    this.approvalList = approvalList;
    this.applyUserName = applyUserName;
    this.toApprovalTask = toApprovalTask;
    this.wfName = wfName;
    this.detailPage = detailPage;
    this.wfStatus = wfStatus;
    this.initiator = initiator;

    if (!isInsideIframe()) {
      document.title = this.wfName;
    }

    this.loading$.next(false);
  }

  private deleteDataToService = async (reason: string) => {
    if (!this.workflowId) return { success: false };
    const success = await this.Model.cancelWorkflow(this.workflowId, reason);
    success && this.onSuccessCb?.();
  };
}
