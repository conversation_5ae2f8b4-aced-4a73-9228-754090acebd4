.page_datapkg-data-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .dmc-datatable {
    &::-webkit-scrollbar {
      display: block !important;
    }
  }

  &_tool-bar {
    display: flex;
    align-items: center;
    background-color: var(--dmc-primary-panel-3);
  }

  &_secret-title {
    padding: 0 15px;

    &-icon {
      position: relative;
      bottom: 15px;
    }
  }

  &_table {
    flex-grow: 1;
  }

  &_tip {
    .dmc-text-alert-close {
      display: none;
    }
  }

  &_blur-search-h5 {
    margin-bottom: 10px;
  }
}

.page_datapkg-data-preview.enable-flat {
  height: unset;
}
