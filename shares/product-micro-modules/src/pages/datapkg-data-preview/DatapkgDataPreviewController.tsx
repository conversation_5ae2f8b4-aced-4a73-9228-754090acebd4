import _ from 'lodash';
import { ReactElement, ReactNode } from 'react';
import { AsyncSubject, BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { deepClean } from '@mdtBsComm/utils/deepCleanUtil';
import type { IClickItemFunc, IControllerPaginationOptions } from '@mdtBsComponents/data-list-comp';
import {
  ICondition,
  IDatapkgColumn as IDColumn,
  IDatapkgDownloadPost,
  IDatapkgRows,
  IDatapkgRowsQuery,
  IDatapkgRowsQueryCondition,
  IDatapkgRowsQueryOrderBy,
  IOperatorFilter,
  IRequestCancelToken,
} from '@mdtBsServices/interfaces';
import { SortTypeEnum } from '@mdtDesign/data-table';
import { ColumnOperatorFilterEnum } from '@mdtProComm/constants';
import { addCreateDownloadDatapkgTask } from '@mdtProTasks/util';
import {
  DataTablePkgPreviewController,
  IDataTableProps,
  IPaginationParams,
} from '../../containers/data-table-pkg-preview';
import { DatapkgColumnSettingController } from '../../containers/datapkg-colomn-setting';
import { DrawerPreviewGeometryDataController } from '../../containers/drawer-preview-geometry-data';
import { type IFilterRuleFormOptionsFunc, FilterListController } from '../../containers/filter-list';
import { IDownloadPkgOptions, PopoverPkgDownloadController } from '../../containers/popover-pkg-download';
import type { IColumnItem } from '../../containers/table-pkg-column';
import { ENABLE_DATA_TABLE_COLUMN_SETTING } from '../../datlas/datlasConfig';
import i18n from '../../languages';
import { SecretTitle } from './DatapkgDataPreview';
import type { IDatapkgDataPreviewModel } from './DatapkgDataPreviewModel';

export type ILoadPkgPreviewRslt = Observable<[number, IDatapkgRows]>;
export type ILoadPkgPreviewFunc = (pkgId: string, data?: IDatapkgRowsQuery) => ILoadPkgPreviewRslt;

export type ILoadNextPagePkgPreviewFunc = (pkgId: string, data: IDatapkgRowsQuery) => Observable<IDatapkgRows>;

export type IDatapkgColumn = IDColumn & { title?: string };

export interface IControllerOptions extends IControllerPaginationOptions {
  pkgId: string;
  pkgName?: string;
  hasSearch?: boolean;
  hasBlurSearch?: boolean;
  hiddenPreviewData?: boolean;
  noSearchTip?: string;
  hasDownload?: boolean;
  hasFilter?: boolean;
  hasPreviewGeometryData?: boolean;
  enableQueryName?: boolean; // 只有本机构数据包才展示id对应的name
  downloadExcludes?: string[];
  columns?: IDatapkgColumn[];
  previewGeometryUrl?: string;
  conditions?: IDatapkgRowsQueryCondition[];
  useRuleForm?: boolean;
  gridHeight?: number;
  enableFlat?: boolean;
  secretVisible?: boolean;
  hasColumnSetting?: boolean;
  excludeSettingColumns?: string[];
  excludeSettingColumnsKey?: string;
  autoAddDefaultCondition?: boolean;
  defaultCondition?: any;
  ruleFormOptions?: IFilterRuleFormOptionsFunc;
  modifyColumns?: (columns: IDatapkgColumn[]) => IDatapkgColumn[];
  modifyTableColumns?: (columns: IColumnItem[]) => IColumnItem[];
  modifyFilterParams?: (filterParams: IDatapkgRowsQuery) => IDatapkgRowsQuery;
  onClickRow?: IClickItemFunc;
  renderOtherHeaderBtns?: () => ReactNode;
  renderDownloadBtn?: (active: boolean) => ReactElement;
}

interface IInitOptions {
  pkgId: string;
  pkgName?: string;
  hasSearch?: boolean;
  hasDownload?: boolean;
  hasFilter?: boolean;
  hasColumnSetting?: boolean;
  excludeSettingColumns?: string[];
  excludeSettingColumnsKey?: string;
  // 用于快速搜索文本列的搜索框，默认为false, 前置条件为hasSearch, 如果hasSearch为false, 则hasBlurSearch无效
  hasBlurSearch?: boolean;
  hasPreviewGeometryData?: boolean;
  columns: IDatapkgColumn[];
  previewGeometryUrl?: string;
  conditions?: IDatapkgRowsQueryCondition[];
  operatorFilter?: IOperatorFilter;
}

class DatapkgDataPreviewController extends DataTablePkgPreviewController {
  private Model: IDatapkgDataPreviewModel;
  private filterController: FilterListController;
  private downloadController?: PopoverPkgDownloadController;
  private columnSettingController$ = new BehaviorSubject<DatapkgColumnSettingController | undefined>(undefined);
  private previewGeometryDataController?: DrawerPreviewGeometryDataController;
  private dataTableProps$?: AsyncSubject<IDataTableProps>;
  private hasSearch$ = new BehaviorSubject<boolean>(true);
  private hasBlurSearch$ = new BehaviorSubject<boolean>(false);
  private hasDownload$ = new BehaviorSubject<boolean>(false);
  private hasFilter$ = new BehaviorSubject<boolean>(false);
  private hasColumnSetting$ = new BehaviorSubject<boolean>(false);
  private excludeSettingColumnsKey = 'key';
  private excludeSettingColumns?: string[];
  private hasPreviewGeometryData$ = new BehaviorSubject<boolean>(false);
  private pkgId = '';
  private pkgName = '';
  private previewGeometryUrl = '';
  private filterListener?: Subscription;
  private sorterItem?: IDatapkgRowsQueryOrderBy;
  private columns: IDatapkgColumn[] = [];
  private noSearchTip = i18n.chain.proMicroModules.datapkg.noSearchDesc;
  private modifyColumns?: (columns: IDatapkgColumn[]) => IDatapkgColumn[];
  private modifyTableColumns?: (columns: IColumnItem[]) => IColumnItem[];
  private modifyFilterParams?: (filterParams: IDatapkgRowsQuery) => IDatapkgRowsQuery;
  private renderOtherHeaderBtns?: () => ReactNode;
  private renderDownloadBtn?: (active: boolean) => ReactElement;
  private aliasColumns: Record<string, string> = {};
  private originPkgColumns: IDatapkgColumn[] = [];
  private hiddenPreviewData?: boolean;
  private downloadExcludes?: string[];
  private blurSearchVal$ = new BehaviorSubject<IOperatorFilter | undefined>(undefined);
  // 初始化和可见列更新触发数据加载可能是同时的，所以需要一个状态来控制不同时加载
  private isInitializing = false;

  public constructor(Model: IDatapkgDataPreviewModel, options?: IControllerOptions) {
    super({
      loadPkgPreviewFunc: () => this.loadFirstPageData(),
      loadNextPagePkgPreviewFunc: (params: IPaginationParams) => this.loadNextPageData(params),
      tableOptions: () => this.dataTableProps$!,
      getBackendFilterParams: () => this.getFilterParams(),
      enableDownload: options?.hasSearch,
      enableQueryName: options?.enableQueryName,
      onClickRow: options?.onClickRow,
      pagination: options?.pagination,
      paginationAtBefore: options?.paginationAtBefore,
      paginationProps: options?.paginationProps,
      initialPageNum: options?.initialPageNum,
      gridHeight: options?.gridHeight,
      enableFlat: options?.enableFlat,
      secretVisible: options?.secretVisible,
    });
    this.Model = Model;
    this.filterController = new FilterListController({
      useRuleForm: options?.useRuleForm,
      autoAddDefaultCondition: options?.autoAddDefaultCondition,
      defaultCondition: options?.defaultCondition,
      ruleFormOptions: options?.ruleFormOptions,
    });
    if (options) {
      this.downloadExcludes = options.downloadExcludes;
      this.hiddenPreviewData = options.hiddenPreviewData;
      this.renderOtherHeaderBtns = options.renderOtherHeaderBtns;
      this.renderDownloadBtn = options.renderDownloadBtn;
      this.modifyColumns = options.modifyColumns;
      this.modifyTableColumns = options.modifyTableColumns;
      this.modifyFilterParams = options.modifyFilterParams;
      options.noSearchTip && (this.noSearchTip = options.noSearchTip);
      this.loadData(options);
    }
  }

  public getNoSearchTip() {
    return this.noSearchTip;
  }

  public getColumns(): IDatapkgColumn[] {
    return this.columns;
  }

  public onBlurSearch(value: string) {
    if (!value) {
      this.blurSearchVal$.next(undefined);
      return;
    }
    const canBlurSearchColumns = _.filter(this.columns, (column) => column.type === 'str');
    const result: ICondition[] = [];
    _.forEach(canBlurSearchColumns, (column) => {
      result.push({ column: column.name, operator: ColumnOperatorFilterEnum.ILIKE, param: `%${value}%` });
    });
    this.blurSearchVal$.next({ $or: result });
  }

  public getHasSearch$() {
    return this.hasSearch$;
  }

  public getHasBlurSearch$() {
    return this.hasBlurSearch$;
  }

  public getHasDownload$() {
    return this.hasDownload$;
  }

  public getHasFilter$() {
    return this.hasFilter$;
  }

  public getHasColumnSetting$() {
    return this.hasColumnSetting$;
  }

  public getHasPreviewGeometryData$() {
    return this.hasPreviewGeometryData$;
  }

  public getFilerController() {
    return this.filterController;
  }

  public getDownloadController() {
    return this.downloadController!;
  }

  public getColumnSettingController$() {
    return this.columnSettingController$;
  }

  public getPreviewGeometryDataController() {
    return this.previewGeometryDataController!;
  }

  public handlePreviewGeometryData = () => {
    this.previewGeometryDataController!.openModal({
      url: this.previewGeometryUrl,
    });
  };

  public getPreviewGeometryUrl() {
    return this.previewGeometryUrl;
  }

  public destroy() {
    super.destroy();
    this.filterController.destroy();
    this.downloadController?.destroy();
    this.columnSettingController$.getValue()?.destroy();
    this.downloadController = undefined;
    this.columnSettingController$.complete();
    this.previewGeometryDataController?.destroy();
    this.previewGeometryDataController = undefined;
    this.dataTableProps$?.unsubscribe();
    this.dataTableProps$ = undefined;
    this.hasSearch$.complete();
    this.hasBlurSearch$.complete();
    this.blurSearchVal$.complete();
    this.hasDownload$.complete();
    this.hasFilter$.complete();
    this.hasColumnSetting$.complete();
    this.hasPreviewGeometryData$.complete();
    this.filterListener?.unsubscribe();
    this.filterListener = undefined;
    this.sorterItem = undefined;
    this.columns = [];
    this.Model = null!;
    this.originPkgColumns = [];
    this.modifyColumns = undefined;
    this.modifyTableColumns = undefined;
    this.modifyFilterParams = undefined;
    this.renderOtherHeaderBtns = undefined;
    this.renderDownloadBtn = undefined;
    this.downloadExcludes = undefined;
  }

  public renderExtraHeaderBtns() {
    return this.renderOtherHeaderBtns?.();
  }

  public reflushColumn() {
    this.initColumns(this.originPkgColumns);
  }

  public getFilterParams() {
    const filters = this.filterController.getFilterList$().getValue();
    const filterOperatorFilter = this.filterController.getOperatorFilter$().getValue();
    const blurOperatorFilter = this.blurSearchVal$.getValue();
    const operatorFilter = _.isEmpty(blurOperatorFilter)
      ? filterOperatorFilter
      : deepClean({ $and: [blurOperatorFilter, filterOperatorFilter] });
    const pp = this.getPaginationParams();
    const params = {
      condition: _.size(filters) ? filters : undefined,
      operator_filter: !_.isEmpty(operatorFilter) ? operatorFilter : undefined,
      orderby: this.sorterItem ? [this.sorterItem] : undefined,
      ...pp,
    };
    return this.modifyFilterParams ? this.modifyFilterParams(params) : params;
  }

  public getCancelToken(): IRequestCancelToken | undefined {
    return undefined;
  }

  private init(options: IInitOptions) {
    const {
      pkgId,
      pkgName = '',
      hasSearch = false,
      hasDownload = false,
      hasFilter = true,
      hasPreviewGeometryData = false,
      hasBlurSearch,
      columns,
      conditions,
      previewGeometryUrl = '',
      hasColumnSetting = ENABLE_DATA_TABLE_COLUMN_SETTING,
    } = options;
    this.isInitializing = true;
    this.pkgId = pkgId;
    this.pkgName = pkgName;
    this.hasSearch$.next(hasSearch);
    this.hasBlurSearch$.next(!!(hasSearch && hasBlurSearch));
    this.hasDownload$.next(hasDownload);
    this.hasFilter$.next(hasFilter);
    this.hasColumnSetting$.next(hasColumnSetting);
    this.excludeSettingColumns = options.excludeSettingColumns;
    this.initColumns(columns);
    hasPreviewGeometryData && (this.previewGeometryDataController = new DrawerPreviewGeometryDataController());
    this.previewGeometryUrl = previewGeometryUrl;
    this.hasPreviewGeometryData$.next(hasPreviewGeometryData);
    this.filterListener?.unsubscribe();
    this.filterController.init(this.columns, conditions);
    this.filterListener = this.listenBackendFilter(
      this.filterController.getFilterList$(),
      this.filterController.getOperatorFilter$(),
    );
    this.loadDataList();
    this.isInitializing = false;
  }

  private loadFirstPageData() {
    if (!this.hasSearch$.getValue()) {
      return this.Model.getDatapkgPreview(this.pkgId, this.hiddenPreviewData);
    }
    return this.Model.getDatapkgDataListFirstPage(this.pkgId, this.getFilterParams(), this.getCancelToken());
  }

  private loadNextPageData(params: IPaginationParams) {
    return this.Model.getDatapkgDataListNextPage(
      this.pkgId,
      { ...params, ...this.getFilterParams() },
      this.getCancelToken(),
    );
  }

  private downloadDataPkg = async ({
    downloadType,
    exportGeoField,
    exportFilterResult,
    geoFormat,
    columns,
    column_mapping,
  }: IDownloadPkgOptions) => {
    const { condition: paramCondition, operator_filter: parmaOperatorFilter } = this.getFilterParams();
    const condition = paramCondition || this.filterController.getFilterList$().getValue();
    const operatorFilter = parmaOperatorFilter || this.filterController.getOperatorFilter$().getValue();
    const params: IDatapkgDownloadPost = {
      file_type: downloadType,
      geo: exportGeoField,
      geometry_format: geoFormat,
      only: columns,
      excludes: _.isEmpty(this.downloadExcludes) ? undefined : this.downloadExcludes,
      column_mapping,
    };

    if (exportFilterResult) {
      if (!_.isEmpty(condition)) {
        params.condition = condition;
      }
      if (!_.isEmpty(operatorFilter)) {
        params.operator_filter = operatorFilter;
      }
    } else {
      const modifiedParamsOperatorFilter = this.modifyFilterParams?.({})?.operator_filter;
      modifiedParamsOperatorFilter && (params.operator_filter = modifiedParamsOperatorFilter);
    }
    addCreateDownloadDatapkgTask(this.pkgId, this.pkgName, params);
    return true;
  };

  private loadData(options: IControllerOptions) {
    const { pkgId, columns, previewGeometryUrl, conditions, ...pkgInfo } = options;
    combineLatest(
      this.Model.queryPkgInfo(pkgId, pkgInfo),
      this.Model.queryColumns(pkgId, columns),
      this.blurSearchVal$,
    ).subscribe((data) => {
      if (data[0].hasDownload) {
        this.downloadController = new PopoverPkgDownloadController({
          downloadPkgFunc: this.downloadDataPkg,
          renderDownloadBtn: this.renderDownloadBtn,
          showExportFilterResult: true,
          defaultExportFilterResult: true,
          loadColumnOptionsFunc: async () => this.columns || [],
          downloadExcludes: this.downloadExcludes,
        });
      }
      this.originPkgColumns = data[1] || [];
      this.init({
        pkgId,
        previewGeometryUrl,
        columns: this.originPkgColumns,
        conditions,
        ...data[0],
      });
    });
  }

  /**
   * 处理和转换列，提取共用逻辑
   * @param columns 原始列
   * @param visibleColumns 可见列配置
   * @returns 转换后的列数据
   */
  private processColumns = (columns: IDatapkgColumn[], visibleColumns: IColumnItem[]) => {
    // 根据可见列过滤和排序
    const filteredAndSortedColumns = _.chain(columns)
      .filter((col) => _.some(visibleColumns, (vCol) => vCol.name === col.name))
      .sortBy((col) => _.findIndex(visibleColumns, (vCol) => vCol.name === col.name))
      .value();

    // 设置别名
    this.setAliasColumns(visibleColumns);

    // 更新列
    return _.flow(
      this.modifyColumnsFunc,
      this.defaultModifyColumnsFunc,
      this.aliasColumnsFunc,
    )(filteredAndSortedColumns);
  };

  private initColumns = (columns: IDatapkgColumn[]) => {
    this.dataTableProps$ = new AsyncSubject<IDataTableProps>();
    let sortColumns: IColumnItem[] = _.map(columns, (c) => ({
      ...c,
      sortType: this.getColumnSortType(c.name),
      sorter: this.hasSearch$.getValue() ? this.getSorter(c.name) : undefined,
    }));
    if (this.modifyTableColumns) {
      sortColumns = _.flow(this.modifyTableColumns, this.defaultModifyColumnsFunc)(sortColumns);
    }
    this.columns = this.processColumns(columns, sortColumns);
    this.generateColumnSettingController(columns, sortColumns);
    this.defaultTableProps(sortColumns);
  };

  /**
   * 从原始列中提取被excludeSettingColumns排除的列
   * @param columns 原始列
   * @returns 被排除的列
   */
  private getExcludedColumns = (columns: IColumnItem[]): IColumnItem[] => {
    if (!this.hasColumnSetting$.getValue() || !this.excludeSettingColumns || this.excludeSettingColumns.length === 0) {
      return [];
    }
    return _.filter(columns, (c) => _.includes(this.excludeSettingColumns, (c as any)[this.excludeSettingColumnsKey]));
  };

  /**
   * 过滤掉被excludeSettingColumns排除的列
   * @param columns 原始列
   * @returns 过滤后的列
   */
  private filterExcludedColumns = (columns: IColumnItem[]): IColumnItem[] => {
    if (!this.hasColumnSetting$.getValue() || !this.excludeSettingColumns || this.excludeSettingColumns.length === 0) {
      return columns;
    }
    return _.filter(columns, (c) => !_.includes(this.excludeSettingColumns, (c as any)[this.excludeSettingColumnsKey]));
  };

  private generateColumnSettingController = (columns: IDatapkgColumn[], sortColumns: IColumnItem[]) => {
    if (this.columnSettingController$.getValue()) {
      this.columnSettingController$.getValue()!.destroy();
    }
    this.columnSettingController$.next(
      new DatapkgColumnSettingController({
        columns: this.filterExcludedColumns(sortColumns),
        pkgId: this.pkgId,
        onColumnChange: (visibleColumns: IColumnItem[]) => {
          const excludedColumns = this.getExcludedColumns(sortColumns);
          const mergedColumns = [...visibleColumns, ...excludedColumns];
          this.updateTableColumns(columns, mergedColumns);
        },
      }),
    );
  };

  /**
   * 更新表格列，不重新创建列设置控制器
   * @param columns 原始列
   * @param visibleColumns 可见列（已包含被排除的列）
   */
  private updateTableColumns = (columns: IDatapkgColumn[], visibleColumns: IColumnItem[]) => {
    this.columns = this.processColumns(columns, visibleColumns);
    this.dataTableProps$ = new AsyncSubject<IDataTableProps>();
    this.defaultTableProps(visibleColumns);

    if (!this.isInitializing) {
      this.loadDataList();
    }
  };

  // 给到默认的aliasColumns 映射
  private setAliasColumns = (columns: IColumnItem[]): void => {
    const maps: Record<string, string> = {};
    _.forEach(columns, (c: IColumnItem) => {
      c.title && (maps[c.name] = c.title);
    });
    this.aliasColumns = maps;
  };

  private modifyColumnsFunc = (columns: IDatapkgColumn[]) => {
    if (!_.isFunction(this.modifyColumns)) {
      return columns;
    }
    return this.modifyColumns(columns);
  };

  private defaultModifyColumnsFunc = (columns: IDatapkgColumn[]) => {
    return _.map(columns, (column) => {
      if (column.type === 'str' && column.view_format?.format === 'secret') {
        return {
          ...column,
          headerRenderer: () => <SecretTitle controller={this} column={column} />,
        };
      }
      return column;
    });
  };

  private aliasColumnsFunc = (columns: IDatapkgColumn[]) => {
    if (_.isEmpty(this.aliasColumns)) {
      return columns;
    }
    return _.map(columns, (column) => ({
      ...column,
      title: column.title ?? this.aliasColumns[column.name],
    }));
  };

  private getColumnSortType = (name: string) => {
    if (!this.sorterItem || this.sorterItem.field !== name) return SortTypeEnum.UNSET;
    if (this.sorterItem.asc) return SortTypeEnum.ASCEND;
    return SortTypeEnum.DESCEND;
  };

  private getSorter = (name: string) => {
    return (type: SortTypeEnum) => {
      if (SortTypeEnum.ASCEND === type) {
        this.sorterItem = { asc: true, field: name };
      } else if (SortTypeEnum.DESCEND === type) {
        this.sorterItem = { asc: false, field: name };
      } else {
        this.sorterItem = undefined;
      }
      // 列字段排序需要重新初始化
      this.initColumns(this.originPkgColumns);
      this.loadDataList();
    };
  };
}

export { DatapkgDataPreviewController };
