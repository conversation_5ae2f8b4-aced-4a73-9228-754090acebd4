import { ChangeEvent } from 'react';
import { VisibilityOff1, VisibilityOn } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Input as MetroInput } from '@metroDesign/input';
import classnames from 'classnames';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { LinkButton } from '@mdtDesign/button';
import { Icon } from '@mdtDesign/icon';
import Input from '@mdtDesign/input';
import TextAlert from '@mdtDesign/text-alert';
import { generateClass } from '@mdtProComm/utils/generateClassUtil';
import { DataTablePkgPreview } from '../../containers/data-table-pkg-preview';
import { DatapkgColumnSetting } from '../../containers/datapkg-colomn-setting';
import { DrawerPreviewGeometryData } from '../../containers/drawer-preview-geometry-data';
import { FilterList } from '../../containers/filter-list';
import { PopoverPkgDownload } from '../../containers/popover-pkg-download';
import i18n from '../../languages';
import { useDatapkgDataPreviewContext, useDatapkgDataPreviewProvider } from './datapkgDataPreviewContext';
import { DatapkgDataPreviewController, IDatapkgColumn } from './DatapkgDataPreviewController';
import './index.less';

interface IProps {
  controller: DatapkgDataPreviewController;
  modalUiData?: { className?: string };
  className?: string;
}
interface IInnerProps {
  className?: string;
  hasHeader?: boolean;
}

const styles = generateClass('page_datapkg-data-preview');

// 子组件--检索框=====================================================================================
export const SearchInput = ({ controller }: IProps) => {
  const value = useObservableState(() => controller.getSingleFilter$());

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    controller.changeSingleFilter(e.target.value);
  };

  return (
    <Input
      placeholder={i18n.chain.proMicroModules.datapkg.dataSearch}
      prefixIcon="search"
      onChange={handleChange}
      value={value}
      size="compact"
      allowClear={false}
    />
  );
};

const DownloadBtn = ({ controller }: IProps) => {
  const hasDownload = useObservableState(controller.getHasDownload$());
  return hasDownload ? <PopoverPkgDownload controller={controller.getDownloadController()} /> : null;
};

const FilterBtn = ({ controller }: IProps) => {
  const hasFilter = useObservableState(controller.getHasFilter$());
  return hasFilter ? <FilterList controller={controller.getFilerController()} /> : null;
};

const ColumnSettingBtn = ({ controller }: IProps) => {
  const hasColumnSetting = useObservableState(controller.getHasColumnSetting$());
  const ctrl = useObservableState(controller.getColumnSettingController$());
  return hasColumnSetting && ctrl ? <DatapkgColumnSetting controller={ctrl} /> : null;
};

export const SecretTitle = ({ controller, column }: IProps & { column: IDatapkgColumn }) => {
  const showAllSecret = useObservableState(controller.getShowAllSecret$());
  return (
    <Flex align="center" gap={6} className="page_datapkg-data-preview_secret-title">
      <Icon icon="text" size={13} className="page_datapkg-data-preview_secret-title-icon" />
      {column.title}
      <Button onlyIcon ghost size="small" focus={false} onClick={() => controller.setShowAllSecret(!showAllSecret)}>
        {showAllSecret ? <VisibilityOn /> : <VisibilityOff1 />}
      </Button>
    </Flex>
  );
};

// 在数据详情页外部有设置overflow=hidden(不能改为auto), 需要提供手动添加tool的方式。
export const TableDatapkgDataPreviewTool = ({ controller }: IProps) => {
  const hasSearch = useObservableState(controller.getHasSearch$());
  const hasPreviewGeometry = useObservableState(controller.getHasPreviewGeometryData$());

  const searchBtns = hasSearch ? (
    <Flex align="center" gap={12}>
      <DownloadBtn controller={controller} />
      <FilterBtn controller={controller} />
      <ColumnSettingBtn controller={controller} />
      {isPc() ? <BlurSearch controller={controller} /> : null}
      {controller.renderExtraHeaderBtns()}
    </Flex>
  ) : null;

  const previewBtn = hasPreviewGeometry ? (
    <LinkButton
      leftIcon="map-2"
      onClick={controller.handlePreviewGeometryData}
      disabled={!controller.getPreviewGeometryUrl()}
    >
      {i18n.chain.proMicroModules.datapkg.geoPreview}
    </LinkButton>
  ) : null;

  return (
    <div className={classnames(styles('tool-bar'), 'datapkg-data-preview-tool')}>
      {previewBtn}
      {searchBtns}
    </div>
  );
};

const Tip = () => {
  const { datapkgDataPreviewController: controller } = useDatapkgDataPreviewContext();
  const hasSearch = useObservableState(controller.getHasSearch$());
  return hasSearch ? null : <TextAlert className={styles('tip')} message={controller.getNoSearchTip()} />;
};

const BlurSearch = ({ controller }: IProps) => {
  const hasBlurSearch = useObservableState(controller.getHasBlurSearch$());
  const handleChange = (value: string) => {
    controller.onBlurSearch(value);
  };

  return hasBlurSearch ? (
    <MetroInput.Search
      style={{ width: isPc() ? 160 : '100%' }}
      className={styles(`${isPc() ? 'blur-search' : 'blur-search-h5'}`)}
      placeholder={i18n.chain.proMicroModules.datapkg.blurSearchTip}
      allowClear
      size="small"
      onSearch={handleChange}
    />
  ) : null;
};

const TablePreviewContainerInner = ({ className }: IInnerProps) => {
  const { datapkgDataPreviewController: controller } = useDatapkgDataPreviewContext();
  const enableFlat = controller.getEnableFlat();
  useObservableState(controller.getShowAllSecret$());
  return (
    <div className={classnames(styles(), enableFlat ? 'enable-flat' : '', className)}>
      <TableDatapkgDataPreviewTool controller={controller} />
      <Tip />
      {!isPc() ? <BlurSearch controller={controller} /> : null}
      <DataTablePkgPreview controller={controller} className={styles('table')} />
    </div>
  );
};

const PreviewView = ({ controller }: IProps) => {
  const hasPreviewGeometry = useObservableState(controller.getHasPreviewGeometryData$());
  return hasPreviewGeometry ? (
    <DrawerPreviewGeometryData controller={controller.getPreviewGeometryDataController()} />
  ) : null;
};

const DatapkgDataPreview = ({ controller, className, modalUiData }: IProps) => {
  const Provider = useDatapkgDataPreviewProvider();
  const value = { datapkgDataPreviewController: controller };
  const cls = className || modalUiData?.className;

  return (
    <Provider value={value}>
      <TablePreviewContainerInner className={cls} />
      <PreviewView controller={controller} />
    </Provider>
  );
};

export { DatapkgDataPreview };
