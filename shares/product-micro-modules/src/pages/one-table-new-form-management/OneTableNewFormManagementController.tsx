import _ from 'lodash';
import { Menu } from '@metroDesign/menu';
import { Modal } from '@metroDesign/modal';
import { toastApi } from '@metroDesign/toast';
import copy from 'copy-to-clipboard';
import { BehaviorSubject } from 'rxjs';
import { assignIfNotEmpty } from '@mdtBsComm/utils/assignUtil';
import { deepMerge } from '@mdtBsComm/utils/deepMergeUtil';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFloworkFormsPostQuery, IOnetableManagedFormsQueryPost, IOperatorFilter } from '@mdtProComm/interfaces';
import { OneTableNewFormFilterToolController } from '../../components/one-table-new-form-filter-tool';
import { CardCurdWithSimpleSearchController, IPaginationParams } from '../../containers/card-curd-with-simple-search';
import { DatapkgSelector } from '../../containers/datapkg-selector';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import {
  doOneTableNewOperatorAddManageCollaborate,
  doOneTableNewOperatorCompleteFlow,
  doOneTableNewOperatorCopyCreate,
  doOneTableNewOperatorCreateReport,
  doOneTableNewOperatorDeleteDraft,
  doOneTableNewOperatorDeleteWorkflow,
  doOneTableNewOperatorDraftEdit,
  doOneTableNewOperatorEditReportType,
  doOneTableNewOperatorExportToMyData,
  doOneTableNewOperatorFillForm,
  doOneTableNewOperatorIssueTask,
  doOneTableNewOperatorPublish,
  doOneTableNewOperatorTransferManage,
  doOneTableNewOperatorViewDetail,
  FORM_TYPE_ITEMS,
} from '../../shared/onetablenew';
import {
  DetailFromPageEnum,
  FormTypeEnum,
  getDraftFormMetaQuery,
  getNewRootWfspecId,
  getQueryWorkflowStatus,
} from '../../utils/oneTableNewUtil';
import type { IOpenOneTableNewReportCreateOptions } from '../one-table-new-report-create';
import { CardItem } from './_util/CardItem';
import { CardDataStatusEnum, CreateFormEnum } from './_util/constants';
import { HeaderTool, TabTool, TotalTool } from './_util/HeaderTool';
import type { ICardItemData, IOneTableNewFormManagementModel } from './OneTableNewFormManagementModel';

interface IControllerOptions {
  Model: IOneTableNewFormManagementModel;
}

export class OneTableNewFormManagementController extends RequestController {
  private Model: IOneTableNewFormManagementModel;
  private cardController: CardCurdWithSimpleSearchController;
  private cardDataStatus$ = new BehaviorSubject<CardDataStatusEnum>(CardDataStatusEnum.DONING);
  private filterToolController: OneTableNewFormFilterToolController;

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    this.cardController = new CardCurdWithSimpleSearchController<any>({
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: () => this.queryFirstPageData(),
            loadNextPageDataListFunc: (params: IPaginationParams) => this.queryNextPageData(params),
            getBackendFilterParams: () => this.getQueryParams(),
          },
          compOptions: () => this.initListOptions(),
        },
        cardItemViewController: () => this,
      },
      headerOptions: {
        title: i18n.chain.proMicroModules.oneTable.menu.formCreateManagement,
        hideInput: true,
        renderExtendOthers: () => {
          return <HeaderTool controller={this} />;
        },
        renderExtendHeader: () => (
          <>
            <TabTool controller={this} />
            <TotalTool controller={this} />
          </>
        ),
      },
    });

    this.filterToolController = new OneTableNewFormFilterToolController({
      onChange: this.onFilterValueChange,
    });

    this.cardDataStatus$.subscribe(() => {
      this.filterToolController.resetAll();
    });

    this.cardController.loadDataList();
  }

  public destroy() {
    super.destroy();
    this.Model = null!;
    this.cardController.destroy();
    this.cardDataStatus$.complete();
    this.filterToolController.destroy();
  }

  public getFilterToolController() {
    return this.filterToolController;
  }

  public openWorkItemIdentifier = async (identifier: Record<string, any>) => {
    const { fromPage, rootWfId } = identifier;
    if (!rootWfId || fromPage !== DetailFromPageEnum.MANAGEMENT) return;
    let item = _.find(this.cardController.getDataListValue(), { rootWfId });
    if (!item) {
      item = await this.Model.queryItemById(rootWfId).toPromise();
    }
    item && this.viewDetail(item);
  };

  public getCardDataStatus$() {
    return this.cardDataStatus$;
  }

  public changeCardDataStatus(status: CardDataStatusEnum) {
    this.cardDataStatus$.next(status);
  }

  public completeReport(item: ICardItemData) {
    doOneTableNewOperatorCompleteFlow({
      itemData: item,
      successCallback: this.reloadCardData,
    });
  }

  // 删除流程
  public cleanWf(item: ICardItemData) {
    doOneTableNewOperatorDeleteWorkflow({
      itemData: item,
      successCallback: () => {
        this.reloadCardData();
        toastApi.success(i18n.chain.comTip.deleteSuccess);
      },
    });
  }

  public fillTaskForm(item: ICardItemData) {
    doOneTableNewOperatorFillForm({
      itemData: item,
      submitConfirmOptionsFunc: (onClose?: () => void) => {
        return {
          onlySaveCallBack: () => {
            onClose?.();
            this.reloadCardData();
          },
          submitSuccessCallback: () => {
            onClose?.();
            this.reloadCardData();
          },
        };
      },
    });
  }

  public issuedTask(item: ICardItemData) {
    doOneTableNewOperatorIssueTask({ itemData: item });
  }

  public deleteForm(item: ICardItemData) {
    doOneTableNewOperatorDeleteDraft({ formId: item.formId, successCallback: this.reloadCardData });
  }

  public editForm(item: ICardItemData) {
    doOneTableNewOperatorDraftEdit({
      formType: item.formType,
      formId: item.formId,
      onCancelCb: this.reloadCardData,
      onSuccessCb: this.reloadCardData,
    });
  }

  public async publishForm(item: ICardItemData) {
    doOneTableNewOperatorPublish({ formId: item.formId, onSuccessCallback: this.onCreateSuccessCb });
  }

  public addMoreUser(item: ICardItemData) {
    doOneTableNewOperatorAddManageCollaborate({ itemData: item });
  }

  public createByCopy(item: ICardItemData) {
    doOneTableNewOperatorCopyCreate({
      formType: item.formType,
      fromFormId: item.formId,
      onSuccessCb: this.onCreateSuccessCb,
      onCancelCb: this.onCreateCancelCb,
    });
  }

  public editReportType(item: ICardItemData) {
    doOneTableNewOperatorEditReportType({ itemData: item, successCallback: this.reloadCardData });
  }

  public exportToMyData(item: ICardItemData) {
    doOneTableNewOperatorExportToMyData({ itemData: item, successCallback: this.reloadCardData });
  }

  public copyFillLink(item: ICardItemData) {
    const rootWfId = item.rootWfId;
    const routerCtrl = DatlasAppController.getInstance().getRouterController();
    if (rootWfId) {
      const link = routerCtrl.getFillFormSingleUrl(rootWfId);
      copy(link);
      toastApi.success(i18n.chain.comTip.copySuccess);
    }
  }

  public transferManage(item: ICardItemData) {
    doOneTableNewOperatorTransferManage({
      itemData: item,
      successCallback: this.reloadCardData,
    });
  }

  public createForm(key: CreateFormEnum, options?: { needChoose?: boolean }) {
    const { needChoose = true } = options || {};
    if (needChoose) {
      this.openPeriodicModal(key);
      return;
    }
    this.handleFormCreateFunc(key);
  }

  public getCardController() {
    return this.cardController;
  }

  public handlePkgCreate(options: IOpenOneTableNewReportCreateOptions) {
    DatapkgSelector.open({
      auth: 'search',
      onChange: (pkgId) => {
        doOneTableNewOperatorCreateReport({ ...options, fromPkgId: pkgId });
      },
    });
  }

  public viewDetail(item: ICardItemData) {
    const onReloadExtra = item.rootWfId
      ? () => {
          this.cardDataStatus$.next(CardDataStatusEnum.DRAFT);
        }
      : () => {
          this.cardDataStatus$.next(CardDataStatusEnum.DONING);
        };

    doOneTableNewOperatorViewDetail({
      fromPage: DetailFromPageEnum.MANAGEMENT,
      formId: item.formId,
      rootWfId: item.rootWfId,
      onReloadExtra: onReloadExtra,
      onReload: this.reloadCardData,
    });
  }

  private reloadCardData = () => {
    this.cardController.loadDataList();
  };

  private onCreateSuccessCb = (onClose?: any) => {
    this.cardDataStatus$.next(CardDataStatusEnum.DONING);
    onClose?.();
  };

  private onCreateCancelCb = (onClose?: any) => {
    this.cardDataStatus$.next(CardDataStatusEnum.DRAFT);
    onClose?.();
  };

  private openPeriodicModal = (formKey: CreateFormEnum) => {
    const modal = Modal.open({
      title: i18n.chain.proMicroModules.oneTable.createForm,
      footer: null,
      centered: true,
      content: (
        <div className="form-header-tool-modal">
          <Menu
            onClick={({ key }) => {
              this.handleFormCreateFunc(formKey, key as FormTypeEnum);
              modal.destroy();
            }}
            items={FORM_TYPE_ITEMS}
          />
        </div>
      ),
    });
  };

  private handleFormCreateFunc = (key: CreateFormEnum, formType = FormTypeEnum.NORMAL) => {
    const options = { formType, onSuccessCb: this.onCreateSuccessCb, onCancelCb: this.onCreateCancelCb };
    switch (key) {
      case CreateFormEnum.EMPTY:
        doOneTableNewOperatorCreateReport(options);
        break;
      case CreateFormEnum.PKG:
        this.handlePkgCreate(options);
        break;
    }
  };

  private queryFirstPageData() {
    const query = this.getQueryParams();
    return this.Model.queryFirstPage(query.dataStatus, query.params, { fetch_total_count: true }, query.cancelToken);
  }

  private queryNextPageData(params: IPaginationParams) {
    const query = this.getQueryParams();
    return this.Model.queryNextPage(query.dataStatus, query.params, params, query.cancelToken);
  }

  private getQueryParams() {
    const cancelToken = this.getCancelToken();
    const dataStatus = this.cardDataStatus$.getValue();
    const filterValue = this.filterToolController.getTransformedFilterValue() || {};
    const orderby = '-create_time';
    if (dataStatus === CardDataStatusEnum.DRAFT) {
      const params: IFloworkFormsPostQuery = {
        process_type: 'one_table',
        ...filterValue,
        orderby,
        meta_query: deepMerge(getDraftFormMetaQuery(), filterValue.meta_query as IOperatorFilter),
      };
      return { cancelToken, params, dataStatus };
    }
    const status = getQueryWorkflowStatus(dataStatus === CardDataStatusEnum.COMPLETE);
    const params: IOnetableManagedFormsQueryPost = {
      root_workflow_spec_id: getNewRootWfspecId(),
      workflow_status: status,
      orderby,
      with_assign_workflow_info: true,
      with_form_info: true,
    };
    assignIfNotEmpty(params, 'bind_form_query', filterValue);
    return { cancelToken, params, dataStatus };
  }

  private onFilterValueChange = () => {
    this.cardController.loadDataList();
  };

  private initListOptions() {
    return {
      itemGap: 16,
      itemHeight: 180,
      itemWidth: 270,
      useVirtual: true,
      CardItemView: CardItem,
    };
  }
}
