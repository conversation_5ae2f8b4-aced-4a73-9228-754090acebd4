import _ from 'lodash';
import { FC } from 'react';
import { Card } from '@metroDesign/card';
import { Flex } from '@metroDesign/flex';
import type { MenuProps } from '@metroDesign/menu';
import type { ItemType } from '@metroDesign/menu/hooks/useItems';
import { Typography } from '@metroDesign/typography';
import { OneTableNewActions } from '../../../components/one-table-new-actions';
import { OneTableNewCardIcon } from '../../../components/one-table-new-card-icon';
import { OneTableNewCollaborateView } from '../../../components/one-table-new-collaborate-view';
import i18n from '../../../languages';
import { CardDataStatusEnum } from '../_util/constants';
import { OneTableNewFormManagementController } from '../OneTableNewFormManagementController';
import type { ICardItemData } from '../OneTableNewFormManagementModel';

const EDIT_CONFIG_KEY = 'edit-config';

interface IProps {
  controller: OneTableNewFormManagementController;
  item: ICardItemData;
}

const DraftAction: FC<IProps> = ({ controller, item }) => {
  let dropdownMenus: MenuProps['items'] = [
    {
      key: 'copy',
      label: i18n.chain.proMicroModules.oneTable.btnCopyCreate,
      onClick: () => controller.createByCopy(item),
    },
    {
      key: EDIT_CONFIG_KEY,
      label: i18n.chain.proMicroModules.oneTable.editFormType,
      onClick: () => controller.editReportType(item),
    },
    {
      key: 'del',
      label: i18n.chain.proMicroModules.oneTable.deleteForm,
      onClick: () => controller.deleteForm(item),
    },
  ];

  if (item.isPeriodic) {
    dropdownMenus = _.filter(dropdownMenus, (val) => val?.key !== EDIT_CONFIG_KEY);
  }

  const actionProps: any[] = [
    { children: i18n.chain.proMicroModules.oneTable.reportEdit, onClick: () => controller.editForm(item) },
    { children: i18n.chain.proMicroModules.oneTable.publishJob, onClick: () => controller.publishForm(item) },
    { disabled: true },
    dropdownMenus,
  ];

  return <OneTableNewActions actionProps={actionProps} />;
};

const DoingAction: FC<IProps> = ({ controller, item }) => {
  // 新增协同
  let collectorItem: ItemType | null = {
    key: 'collector',
    label: i18n.chain.proMicroModules.oneTable.btnCollector,
    onClick: () => controller.addMoreUser(item),
    tooltip: i18n.chain.proMicroModules.oneTable.btnCollectorTooltip2,
  };

  // 复制
  let copyItem: ItemType | null = {
    key: 'copy',
    label: i18n.chain.proMicroModules.oneTable.btnCopyCreate,
    onClick: () => controller.createByCopy(item),
  };

  // 修改报表类型
  let editItem: ItemType | null = {
    key: EDIT_CONFIG_KEY,
    label: i18n.chain.proMicroModules.oneTable.editFormType,
    onClick: () => controller.editReportType(item),
  };

  // 复制填报链接
  let copyFillLinkItem: ItemType | null = {
    key: 'copyFillLink',
    label: i18n.chain.proMicroModules.oneTable.btnCopyFillLink,
    onClick: () => controller.copyFillLink(item),
  };

  // 导出到我的数据
  let exportToMyDataItem: ItemType | null = {
    key: 'exportToMyData',
    label: i18n.chain.proMicroModules.oneTable.exportToMyData.title,
    onClick: () => controller.exportToMyData(item),
  };

  // 转交报表
  let transferItem: ItemType | null = {
    key: 'transfer',
    label: i18n.chain.proMicroModules.oneTable.btnTransferForm,
    tooltip: i18n.chain.proMicroModules.oneTable.btnTransferFormTip,
    onClick: () => controller.transferManage(item),
  };

  // 结束报表
  let completItem: ItemType | null = {
    key: 'complet',
    danger: true,
    label: i18n.chain.proMicroModules.oneTable.btnFinish,
    onClick: () => controller.completeReport(item),
  };

  if (item.isCollaborate) {
    collectorItem = null;
    completItem = null;
    transferItem = null;
  }

  // TODO: 如果周期都可以和其他报表互转，可以放开
  if (item.isPeriodic) {
    editItem = null;
  }

  const dropdownMenus: MenuProps['items'] = [
    collectorItem,
    copyItem,
    editItem,
    copyFillLinkItem,
    exportToMyDataItem,
    transferItem,
    completItem,
  ].filter(Boolean);
  const actionProps: any[] = [
    { onClick: () => controller.fillTaskForm(item) },
    { onClick: () => controller.issuedTask(item) },
    { disabled: true },
    dropdownMenus,
  ];

  return <OneTableNewActions actionProps={actionProps} />;
};

const CompleteAction: FC<IProps> = ({ controller, item }) => {
  const delItem: (ItemType | null)[] = item.isCollaborate
    ? [null]
    : [
        {
          key: 'copy',
          label: i18n.chain.proMicroModules.oneTable.btnCopyCreate,
          onClick: () => controller.createByCopy(item),
        },
        {
          key: 'exportToMyData',
          label: i18n.chain.proMicroModules.oneTable.exportToMyData.title,
          onClick: () => controller.exportToMyData(item),
        },
        {
          key: 'clean',
          danger: true,
          label: i18n.chain.proMicroModules.oneTable.btnCleanWf,
          onClick: () => controller.cleanWf(item),
        },
      ];
  const dropdownMenus: MenuProps['items'] = delItem.filter(Boolean);
  const actionProps: any[] = [{ disabled: true }, { disabled: true }, { disabled: true }, dropdownMenus];

  return <OneTableNewActions actionProps={actionProps} />;
};

const actionViewMap: Record<string, FC<IProps>> = {
  [CardDataStatusEnum.DRAFT]: DraftAction,
  [CardDataStatusEnum.DONING]: DoingAction,
  [CardDataStatusEnum.COMPLETE]: CompleteAction,
};

export const CardItem: FC<IProps> = ({ item, controller }) => {
  const { isEndless, isPeriodic, isCollaborate } = item;
  const cardCls = `one-table-new-form-management-card-item`;
  const View = actionViewMap[item.status] as FC<IProps>;

  const statusMap = {
    [CardDataStatusEnum.DRAFT]: `${item.createTime}${i18n.chain.proMicroModules.oneTable.dateStatus.create}`,
    [CardDataStatusEnum.DONING]: `${item.createTime}${i18n.chain.proMicroModules.oneTable.dateStatus.publish}`,
    [CardDataStatusEnum.COMPLETE]: `${item.createTime}${i18n.chain.proMicroModules.oneTable.dateStatus.publish}`,
  };

  return (
    <Card size="small" className={cardCls} actions={[<View key="0" item={item} controller={controller} />]}>
      <div className="item-wrap" onClick={() => controller.viewDetail(item)}>
        <OneTableNewCardIcon isEndless={isEndless} isPeriodic={isPeriodic} />
        <div className="item-label">{item.name}</div>
        <Flex justify="space-between">
          <Typography.Text type="secondary" className="date">
            {statusMap[item.status]}
          </Typography.Text>
          {isCollaborate ? <OneTableNewCollaborateView /> : null}
        </Flex>
      </div>
    </Card>
  );
};
