import _ from 'lodash';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, distinctUntilChanged, skip } from 'rxjs/operators';
import { DebounceTimeEnum } from '@mdtBsComm/constants';
import { ITableCurdColumn, IVirtualizedTableCurdProps } from '@mdtBsComponents/data-list-comp-table-curd';
import { DISABLE_SELECT_KEY } from '@mdtBsControllers/data-list-controller';
import Tag from '@mdtDesign/tag';
import Tooltip from '@mdtDesign/tooltip';
import { PERSONAL_PKG_PERMISSIONS, ResourceBackendPermissionEnum, ResourceSelectTypeEnum } from '@mdtProComm/constants';
import { IPkgPermissions, IResourceTableData } from '@mdtProComm/interfaces';
import {
  getResourcePermissionLabel,
  getResourcePermissionOptions,
  resourceTypeKeyMap,
} from '@mdtProComm/utils/resourceUtil';
import {
  ICurdExtendControllerOptions,
  ITableCurdExtendData,
  TableCurdSearchExtendController,
} from '../../containers/table-curd-search-extend';
import i18n from '../../languages';

const permissionsStyle = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap' as any,
};

class ResourceShareBaseController extends TableCurdSearchExtendController<IResourceTableData> {
  protected isSearching = false;
  protected searchVal$ = new BehaviorSubject('');
  protected filterVal$ = new BehaviorSubject<ResourceSelectTypeEnum>(ResourceSelectTypeEnum.ALL);

  public constructor(options: ICurdExtendControllerOptions) {
    super(options);
    this.listenSearchChange();
  }

  public destroy() {
    super.destroy();
    this.searchVal$.complete();
    this.filterVal$.complete();
  }

  public getSearchVal$() {
    return this.searchVal$;
  }

  public changeSearchVal$(val: string) {
    return this.searchVal$.next(val);
  }

  public getFilterValVal$() {
    return this.filterVal$;
  }

  public changeFilterValVal$(val: ResourceSelectTypeEnum) {
    return this.filterVal$.next(val);
  }

  // 渲染表头所需信息
  protected initTableProps = (
    appPkgPermissions: IPkgPermissions,
    enableEditPermission: boolean,
    showShareDate = true,
  ): IVirtualizedTableCurdProps => {
    const shareDateColumn = showShareDate
      ? [{ name: i18n.chain.proMicroModules.resource.shareTime, code: 'shareDate' }]
      : [];
    const permissionColumn: ITableCurdColumn = {
      name: i18n.chain.proMicroModules.resource.auth,
      code: 'permissions',
      width: 360,
    };
    if (enableEditPermission) {
      permissionColumn.editOptions = {
        type: 'select',
        selectProps: (r: IResourceTableData) => {
          const disableReadPermissions: IPkgPermissions = {
            ...appPkgPermissions,
            [ResourceBackendPermissionEnum.READ]: false,
            // 机构数据包不分享下载权
            // [ResourceBackendPermissionEnum.DOWNLOAD]: r.isOwner,
          };
          const pkgPermissions = r.isPersonalPkg ? PERSONAL_PKG_PERMISSIONS : disableReadPermissions;
          const noPermission = r[DISABLE_SELECT_KEY];

          return {
            options: getResourcePermissionOptions({
              ...r,
              pkgPermissions,
            }),
            mode: 'multiple',
            maxTagCount: 3,
            disabled: noPermission,
            placeholder: noPermission ? i18n.chain.proMicroModules.resource.noAuth : i18n.chain.comPlaceholder.select,
          };
        },
      };
    } else {
      permissionColumn.render = (val: string[], r: IResourceTableData) => {
        const ps = _.compact(_.map(val, (p) => getResourcePermissionLabel(p, r.type)));
        const pst = _.map(_.take(ps, 3), (p) => <Tag key={p} tag={p} color="blue-700" />);
        const oPs = _.drop(ps, 3);
        if (!_.isEmpty(oPs)) {
          const tips = _.map(oPs, (p) => <div key={p}>{p}</div>);
          const opst = (
            <Tooltip placement="topLeft" title={tips} key={r.id}>
              <Tag tag={`+${_.size(oPs)}`} color="blue-700" />
            </Tooltip>
          );
          pst.push(opst);
        }
        return <div style={permissionsStyle}>{pst}</div>;
      };
    }

    return {
      columns: [
        { name: i18n.chain.proMicroModules.resource.fileName, code: 'name', width: 360 },
        { name: i18n.chain.proMicroModules.resource.fileType, code: 'typeDisplay' },
        { name: i18n.chain.proMicroModules.resource.creator, code: 'creator' },
        ...shareDateColumn,
        permissionColumn,
      ],
      type: 'page-bg',
      primaryKey: 'id',
      withVerticalBorder: false,
      showColumnSetting: false,
    };
  };

  protected loadHeaderOptions() {
    return {
      createBtnLabel: i18n.chain.proMicroModules.resource.addResource,
      title: ' ',
    };
  }

  private listenSearchChange() {
    combineLatest(this.searchVal$, this.filterVal$)
      .pipe(skip(1), debounceTime(DebounceTimeEnum.LARGER), distinctUntilChanged())
      .subscribe(([searchVal, filterVal]) => {
        if (!searchVal && filterVal === ResourceSelectTypeEnum.ALL) {
          if (this.isSearching) {
            this.isSearching = false;
            this.changeDataList(this.getAllData());
          }
          return;
        }

        this.isSearching = true;
        const allData: ITableCurdExtendData<IResourceTableData>[] = this.getAllExtendData(true);
        this.handleSearchFilter(searchVal, filterVal, allData);
      });
  }

  // eslint-disable-next-line complexity, sonarjs/cognitive-complexity
  private handleSearchFilter(
    searchVal: string,
    filterVal: ResourceSelectTypeEnum,
    allData: ITableCurdExtendData<IResourceTableData>[],
  ) {
    let filterData: ITableCurdExtendData<IResourceTableData>[] = [];
    const filterKey = resourceTypeKeyMap[filterVal];

    // Tip 新加筛选条件时更新resourceTypeKeyMap
    if (searchVal && filterVal === ResourceSelectTypeEnum.ALL) {
      filterData = _.filter(allData, (item) => _.includes(item.name, searchVal));
    } else if (!searchVal && filterVal !== ResourceSelectTypeEnum.ALL) {
      filterData = _.filter(allData, (item) => item[filterKey] as boolean);
    } else if (searchVal && filterVal !== ResourceSelectTypeEnum.ALL) {
      filterData = _.filter(allData, (item) => (item[filterKey] as boolean) && _.includes(item.name, searchVal));
    }
    this.changeDataList(_.uniqBy(filterData, 'resourceId'));
  }
}

export { ResourceShareBaseController };
