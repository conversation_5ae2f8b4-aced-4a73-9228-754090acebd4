import _ from 'lodash';
import { combineLatest, from, Observable, of } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { DATAPKG_COLLABORATE_DATA, DATAPKG_SIMPLE_DATA } from '@mdtBsBffServices/services';
import { randomUuid } from '@mdtBsComm/utils/stringUtil';
import { DatapkgDetailBffService } from '@mdtProComm/bff-services';
import { ResourceBackendPermissionEnum } from '@mdtProComm/constants';
import { ColumnOperatorEnum } from '@mdtProComm/constants/columnConstant';
import {
  IBusinessResult,
  IDatapkgCollaborateInfo,
  IDatapkgCollaboratePermission,
  IDatapkgColumn,
  ILabelValue,
} from '@mdtProComm/interfaces';
import { getColumnOperatorLabel } from '@mdtProComm/utils/columnUtil';
import {
  getResourcePermissionLabel,
  getResourcePermissionOptions,
  transformFrontPermissions,
} from '@mdtProComm/utils/resourceUtil';
import { IUserLazySelectorModel, UserLazySelectorModel } from '../../containers/user-lazy-selector';
import { AbstractAppController } from '../../controllers/AbstractAppController';
import i18n from '../../languages';
import { IDatapkgDataPreviewModel } from '../datapkg-data-preview';

export enum PermissionEnum {
  READ = 'read',
  VIEW_DETAIL = 'view_detail',
  UPDATE = 'update',
  INSERT = 'insert',
  DELETE = 'delete',
}
export const PermissionLabelValueMap = {
  [PermissionEnum.READ]: i18n.chain.proMicroModules.collaborate.read,
  [PermissionEnum.VIEW_DETAIL]: i18n.chain.proMicroModules.collaborate.viewDetail,
  [PermissionEnum.UPDATE]: i18n.chain.proMicroModules.collaborate.update,
  [PermissionEnum.INSERT]: i18n.chain.proMicroModules.collaborate.insert,
  [PermissionEnum.DELETE]: i18n.chain.proMicroModules.collaborate.delete,
};
// 合并permission时使用的类型，作为中间过渡使用; 作为表单类型。
export interface ICollaborate {
  conditions: ICondition[];
  permissions: string[];
  userIds: string[];
  roleIds: string[];
}
export interface ICondition {
  column: string;
  operator: string;
  values: string[];
}
export interface ITableData {
  id: string;
  conditions: ICondition[];
  conditionsDisplay: string[];
  permissions: string[];
  permissionTags: string[];
  userIds: string[];
  roleIds: string[];
  usersDisplay: string;
}

export interface IModifyUiData {
  permissionOptions: ILabelValue[];
  // userOptions: TreeLabelValueItemInterface[];
  columns: IDatapkgColumn[];
}

export interface IDatapkgInfo {
  isCollaborateDatapkg: boolean;
}

export interface ICollaborateListModel {
  getUserSelectorModel: () => IUserLazySelectorModel;
  getDataPreviewModel: () => IDatapkgDataPreviewModel;
  // 查询创建、更新弹窗需要的ui数据
  queryModifyUiData: (tableData: ITableData[], rest?: ITableData) => Observable<IModifyUiData>;

  // 加载页面table数据
  queryCollaborateList: () => Observable<ITableData[]>;

  // 创建数据
  createCollaborate: (createData: ICollaborate) => Observable<IBusinessResult<ITableData>>;

  // 修改数据
  updateCollaborate: (
    updateData: ICollaborate,
    originalUpdateData: ITableData,
  ) => Observable<IBusinessResult<ITableData>>;

  // 删除数据
  deleteCollaborate: (deleteData: ITableData) => Observable<IBusinessResult<ITableData>>;
}

interface IModelOptions {
  dataPreviewModel: IDatapkgDataPreviewModel;
  pkgId: string;
  appId: number;
  userId: number; // 数据包负责人id
  columns: IDatapkgColumn[];
  isExternalAppPkg: boolean;
  isCollaborateDatapkg: boolean;
  isOutTable: boolean;
  isOutSql: boolean;
  app: AbstractAppController;
}

export class CollaborateListModelBff implements ICollaborateListModel {
  private pkgId: string;
  // 缓存的userOptions
  private isCollaborateDatapkg = false;
  private columns: IDatapkgColumn[];
  private isOutTable = false;
  private isOutSql = false;
  private isExternalAppPkg = false;
  private app: AbstractAppController;
  private userSelectorModel: IUserLazySelectorModel;
  private dataPreviewModel: IDatapkgDataPreviewModel;

  public constructor(options: IModelOptions) {
    this.pkgId = options.pkgId;
    this.columns = options.columns;
    this.isCollaborateDatapkg = options.isCollaborateDatapkg;
    this.isOutSql = options.isOutSql;
    this.isOutTable = options.isOutTable;
    this.isExternalAppPkg = options.isExternalAppPkg;
    this.app = options.app;
    this.userSelectorModel = new UserLazySelectorModel({
      appId: options.appId,
    });
    this.dataPreviewModel = options.dataPreviewModel;
  }

  public destroy() {
    this.columns = [];
    this.app = null!;
    this.userSelectorModel = null!;
    this.dataPreviewModel = null!;
  }

  public getUserSelectorModel() {
    return this.userSelectorModel;
  }

  public getDataPreviewModel() {
    return this.dataPreviewModel;
  }

  // 查询创建、更新弹窗需要的ui数据
  public queryModifyUiData(): Observable<IModifyUiData> {
    const ps = this.app!.getUserPermissionController()!;
    const { enableApprovalUse, enableApprovalDataUpdate, enableApprovalSearch } = ps.getApprovalManagePermission();
    const permissionOptions = getResourcePermissionOptions({
      isExternalAppPkg: this.isExternalAppPkg,
      isAppPkg: !this.isExternalAppPkg,
      isAppSqlPkg: this.isOutSql,
      isAppTablePkg: this.isOutTable,
      pkgPermissions: {
        [ResourceBackendPermissionEnum.READ]: enableApprovalUse,
        [ResourceBackendPermissionEnum.VIEW_DETAIL]: enableApprovalSearch,
        [ResourceBackendPermissionEnum.PKG_UPDATE]: enableApprovalDataUpdate,
        [ResourceBackendPermissionEnum.META_UPDATE]: enableApprovalDataUpdate,
      },
    });
    return combineLatest(this.queryColumns()).pipe(
      map(([columns]) => {
        return {
          permissionOptions,
          columns,
        };
      }),
    );
  }

  // 加载页面table数据
  public queryCollaborateList(): Observable<ITableData[]> {
    return combineLatest(this.queryCollaborateData()).pipe(
      map((data) => {
        const list = data[0];
        const mergedList = this.mergeList(list);
        return _.map(mergedList, (item) => this.transformToTableData(item));
      }),
    );
  }

  // 发起协作编辑
  public createCollaborate(createData: ICollaborate): Observable<IBusinessResult<ITableData>> {
    if (this.isCollaborateDatapkg) {
      return this.updateCollaborateToService(createData);
    }
    return from(
      DatapkgDetailBffService.createDatapkgCollaborate({
        pkgId: this.pkgId,
        data: this.transformToBackendData([createData]),
        respData: DATAPKG_SIMPLE_DATA,
      }),
    ).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? this.transformToTableData(createData) : undefined,
      })),
    );
  }

  // 修改协作编辑
  public updateCollaborate(
    updateData: ICollaborate,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    return this.deleteCollaborate(originalUpdateData).pipe(
      mergeMap((delSuccess) => {
        if (!delSuccess) return of({ success: false });
        return this.updateCollaborateToService(updateData, originalUpdateData.id);
      }),
    );
  }

  // 删除协作
  public deleteCollaborate(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(
      DatapkgDetailBffService.deleteDatapkgCollaborate({
        pkgId: this.pkgId,
        data: this.transformToBackendData([deleteData], false),
        respData: DATAPKG_SIMPLE_DATA,
      }),
    ).pipe(map((resp) => ({ success: resp.success, result: deleteData })));
  }

  // 合并permission、userIds、roleIds
  private mergeList(list: ICollaborate[]) {
    const mergedList: ICollaborate[] = [];
    _.forEach(list, (item) => {
      const sortedItem = {
        ...item,
        conditions: _.sortBy(item.conditions, 'column'),
      };
      const temp = _.find(mergedList, (mergedItem) => {
        const tempMergedItem = JSON.stringify(_.pick(mergedItem, ['conditions']));
        const tempItem = JSON.stringify(_.pick(sortedItem, ['conditions']));
        return _.eq(tempMergedItem, tempItem);
      });
      if (temp) {
        temp.permissions = _.concat(temp.permissions, item.permissions);
        if (!_.isEmpty(item.userIds)) {
          temp.userIds = item.userIds;
        }
        !_.isEmpty(item.userIds) && (temp.userIds = item.userIds);
        !_.isEmpty(item.roleIds) && (temp.roleIds = item.roleIds);
      } else {
        mergedList.unshift(sortedItem);
      }
    });
    return _.map(mergedList, (item) => {
      item.userIds = _.map(item.userIds, (id) => `${id}`);
      item.permissions = transformFrontPermissions(_.uniq(item.permissions));
      return item;
    });
  }

  // 新建、更新、删除时转换为后端需要的格式
  private transformToBackendData(value: ICollaborate[], needConditions = true) {
    const result: IDatapkgCollaborateInfo[] = [];
    _.forEach(value, (c) => {
      const ps = _.flatten(_.map(c.permissions, (p) => _.split(p, ',')));
      const conditions = _.map(c.conditions, (condition) => _.pick(condition, ['column', 'operator', 'values']));
      const temp: IDatapkgCollaborateInfo[] = _.map(ps, (p) => {
        const result: IDatapkgCollaborateInfo = {
          permission: p as IDatapkgCollaboratePermission,
          user_ids: _.map(c.userIds, (id) => id) as any,
          role_ids: _.map(c.roleIds, _.parseInt),
        };
        if (needConditions) {
          result.conditions = conditions;
        }
        return result;
      });
      result.push(...temp);
    });

    return {
      collaborate_info: result,
    };
  }

  // 协同条件列表
  private queryCollaborateData() {
    return from(
      DatapkgDetailBffService.queryDatapkgCollaborate({
        pkgId: this.pkgId,
        respData: DATAPKG_COLLABORATE_DATA,
      }),
    ).pipe(
      map((resp) => {
        if (!resp.success) return [] as ICollaborate[];
        return _.map(resp.page_data.collaborate_info, (item) => ({
          conditions: item.conditions,
          permissions: [item.permission],
          userIds: _.map(item.user_ids, (id) => `${id}`),
          roleIds: _.map(item.role_ids, (id) => `${id}`),
        })) as ICollaborate[];
      }),
    );
  }

  private transformToTableData(item: ICollaborate) {
    const conditionsDisplay = _.map(item.conditions, (item) => {
      return `${item.column} ${getColumnOperatorLabel(item.operator!, item.operator)} ${
        item.operator === ColumnOperatorEnum.IN || item.operator === ColumnOperatorEnum.NOT_IN
          ? item.values.join(',')
          : item.values
      }`;
    });
    const userRoles = this.userSelectorModel.transformToSelectorData({ userIds: item.userIds, roleIds: item.roleIds });
    const tableData: ITableData = {
      userIds: item.userIds,
      roleIds: item.roleIds,
      id: randomUuid(),
      conditions: item.conditions,
      conditionsDisplay: conditionsDisplay,
      permissions: item.permissions,
      permissionTags: _.map(item.permissions, (p) => getResourcePermissionLabel(p)),
      usersDisplay: _.join(_.map(userRoles, 'name'), '、'),
    };
    return tableData;
  }

  private queryColumns(): Observable<IDatapkgColumn[]> {
    return of(this.columns);
  }

  private updateCollaborateToService(updateData: ICollaborate, id?: string): Observable<IBusinessResult<ITableData>> {
    return from(
      DatapkgDetailBffService.updateDatapkgCollaborate({
        pkgId: this.pkgId,
        data: this.transformToBackendData([updateData]),
        respData: DATAPKG_SIMPLE_DATA,
      }),
    ).pipe(
      map((resp) => {
        let data;
        if (resp.success) {
          data = this.transformToTableData(updateData);
          id && (data.id = id);
        }
        return {
          success: resp.success,
          result: data,
        };
      }),
    );
  }
}
