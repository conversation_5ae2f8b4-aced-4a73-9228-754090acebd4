import { FC } from 'react';
import { Select } from '@metroDesign/select';
import { isPc } from '@mdtBsComm/utils';

export const Bool: FC<{ value: boolean; onChange: (value: boolean) => void }> = ({ value, onChange, ...rest }) => (
  <Select value={value} onSelect={(val) => onChange?.(val)} allowClear={false} {...rest} />
);

export const ActionStatus: FC<{ value: number; onChange: (value: number) => void }> = ({
  value,
  onChange,
  ...rest
}) => (
  <Select value={value} onSelect={(val) => onChange?.(val)} width={120} dropdownMatchSelectWidth={isPc()} {...rest} />
);
