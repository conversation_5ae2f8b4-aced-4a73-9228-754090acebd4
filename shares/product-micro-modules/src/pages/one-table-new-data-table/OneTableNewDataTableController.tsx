import _ from 'lodash';
import { MouseE<PERSON>, ReactNode } from 'react';
import { Edit2, More<PERSON><PERSON>z, VisibilityOn } from '@metro/icons';
import type { RuleCondition, RuleField, RuleFormWidgets } from '@metro/rule-form';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { type SelectProps } from '@metroDesign/select';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { type IDatapkgRowsQuery, IDatapkgColumn } from '@mdtApis/interfaces';
import { DATE_FORMATTER_2, endOf, formateDate, startOf } from '@mdtBsComm/utils/dayUtil';
import { deepMerge } from '@mdtBsComm/utils/deepMergeUtil';
import { parseFormatToPicker } from '@mdtBsComm/utils/formatUtil';
import { LinkButton } from '@mdtDesign/button';
import { ColumnOperatorFilterEnum, FieldTypeEnum, QuestionCompEnum } from '@mdtProComm/constants';
import { getSystemColumnTitle } from '@mdtProComm/utils/columnUtil';
import { findFieldByKey } from '@mdtProComm/utils/formilyUtil';
import type { IColumn } from '../../components/filter-rule-form';
import type { IFilterRuleFormOptions } from '../../containers/filter-list';
import type { IColumnItem } from '../../containers/table-pkg-column';
import { ENABLE_DATA_TABLE_COLUMN_SETTING } from '../../datlas/datlasConfig';
import type { IOneTableNewOperatorDataComm } from '../../interfaces';
import i18n from '../../languages';
import {
  DatapkgDataPreviewController,
  DatapkgDataPreviewModel,
  IControllerOptions as IDatapkgDataPreviewControllerOptions,
} from '../../pages/datapkg-data-preview';
import {
  BooleanEnum,
  COLUMN_ACTION,
  COLUMN_CREATE_TIME,
  COLUMN_FLAG,
  COLUMN_STATUS,
  COLUMN_UPDATE_TIME,
  COLUMN_UPDATE_USER_ID,
  DataStatusEnum,
  DataTableTypeEnum,
  getDataStatusTagsComm,
  getHasDownload,
  getNeedApproveDataStatusTags,
  getNotNeedApproveDataStatusTags,
  ignoreFrontendColumns,
  ONE_TABLE_NEW_COLUMN_LIST,
} from '../../utils/oneTableNewUtil';
import { ActionStatus, Bool } from './OneTableNewDataTable';

interface IComponentColumn {
  component?: string;
  componentProps?: Record<string, any>;
  dataSource?: any[];
  type: string;
  name: string;
  format?: string;
  title?: ReactNode;
}

export interface IControllerOptions {
  itemData: IOneTableNewOperatorDataComm;
  dataPreviewOptions: IDatapkgDataPreviewControllerOptions;
  dataTableType: DataTableTypeEnum;
  modifyItemData?: (item: any, action: string) => void;
  // 默认true, onetablenew会提供一套默认的规则表单配置，如果为false，则使用接收的ruleForm
  overrideControllerOptions?: boolean;
  // 数据状态的数据源
  actionOptions?: SelectProps['options'];
  // 修改action的contdition翻译逻辑
  transformActionCondition?: (node: RuleCondition) => RuleCondition;
  // 数据状态固定在右侧
  dataStatusFixedRight?: boolean;
  dataIdFixedRight?: boolean;
  dataDateFixedLeft?: boolean;
  // 额外的请求参数在初始化的时候传递进来（始终生效）
  requestParams?: Record<string, any>;
  // 首次额外请求的参数（首次生效），注意传递空对象也视为传递了额外参数
  firstRequestParams?: Record<string, any>;
}

export const enum DataTableActionEnum {
  Edit = 'edit',
  Preview = 'preview',
  Delete = 'delete',
  Redel = 'redel',
  COPY = 'copy',
}

const renderDownloadBtn = () => {
  return (
    <LinkButton className="module_filter-list_tool-btn" leftIcon="download">
      {i18n.chain.proMicroModules.oneTable.downloadData}
    </LinkButton>
  );
};

export class OneTableNewDataTableController extends DatapkgDataPreviewController {
  protected itemData: IOneTableNewOperatorDataComm;
  protected dataTableType: DataTableTypeEnum;
  private modifyItemData?: (item: any, action: string) => void;
  private originTableColumns: IColumnItem[] = [];
  private dataStatusFixedRight?: boolean;
  private dataIdFixedRight?: boolean;
  private dataDateFixedLeft?: boolean;
  private requestParams?: Record<string, any>;
  private firstRequestParams?: Record<string, any>;
  private isFirstLoad = true;

  public constructor(options: IControllerOptions) {
    super(DatapkgDataPreviewModel, {
      noSearchTip: i18n.chain.proMicroModules.oneTable.tip.noSearchTip,
      enableQueryName: true,
      hiddenPreviewData: true,
      hasDownload: getHasDownload(options.itemData, options.dataPreviewOptions.hasDownload),
      hasSearch: true,
      hasPreviewGeometryData: false,
      pkgName: options.itemData.formName,
      useRuleForm: options.dataPreviewOptions?.useRuleForm ?? true,
      downloadExcludes: ONE_TABLE_NEW_COLUMN_LIST,
      excludeSettingColumns: options.dataTableType === DataTableTypeEnum.PRIVIEW ? ['__operate_column__'] : [],
      hasColumnSetting: ENABLE_DATA_TABLE_COLUMN_SETTING && options.dataTableType === DataTableTypeEnum.PRIVIEW,
      modifyColumns: (columns) => this.innerModifyColumns(columns),
      modifyTableColumns: (columns) => this.innerModifyTableColumns(columns),
      modifyFilterParams: (params) => this.innerModifyFilterParams(params),
      renderDownloadBtn: renderDownloadBtn,
      ...options.dataPreviewOptions,
      ruleFormOptions: () =>
        this.getRuleFormProps({
          options: options.dataPreviewOptions?.ruleFormOptions?.() || {},
          override: options.overrideControllerOptions ?? true,
          actionOptions: options.actionOptions,
          transformActionCondition: options.transformActionCondition,
          itemData: options.itemData,
        }),
    });
    this.itemData = options.itemData;
    this.dataTableType = options.dataTableType;
    this.modifyItemData = options.modifyItemData;
    this.requestParams = options.requestParams;
    this.firstRequestParams = options.firstRequestParams;
    this.dataStatusFixedRight = options.dataStatusFixedRight;
    this.dataIdFixedRight = options.dataIdFixedRight;
    this.dataDateFixedLeft = options.dataDateFixedLeft;
  }

  public destroy() {
    super.destroy();
    this.itemData = null!;
    this.originTableColumns = [];
    this.requestParams = undefined;
    this.firstRequestParams = undefined;
    this.modifyItemData = undefined;
  }

  public getRequestParams() {
    return this.requestParams;
  }

  public getFirstRequestParams() {
    return this.firstRequestParams;
  }

  public getOriginTableColumns() {
    return this.originTableColumns;
  }
  protected renderOperation(row: any) {
    const handleOpt = (e: MouseEvent, action: string) => {
      e.stopPropagation();
      this.modifyItemData?.(row, action);
    };

    const btns = [
      <Button
        key={DataTableActionEnum.Edit}
        ghost
        icon={<Edit2 />}
        onlyIcon
        showTooltip
        tooltipTitle={i18n.chain.comButton.edit}
        onClick={(e) => handleOpt(e, DataTableActionEnum.Edit)}
      />,
    ];
    btns.push(
      <Dropdown trigger={['click']} menu={{ items: this.getMoreItems(row) }}>
        <Button onlyIcon icon={<MoreHoriz />} ghost />
      </Dropdown>,
    );

    return btns;
  }

  protected renderPreviewOperation(row: any) {
    const handleOpt = (e: MouseEvent, action: string) => {
      e.stopPropagation();
      this.modifyItemData?.(row, action);
    };

    return [
      <Button
        key={DataTableActionEnum.Preview}
        ghost
        icon={<VisibilityOn />}
        onlyIcon
        showTooltip
        tooltipTitle={i18n.chain.comButton.detail}
        onClick={(e) => handleOpt(e, DataTableActionEnum.Preview)}
      />,
    ];
  }

  private getMoreItems(row: any) {
    const moreItems: any[] = [
      {
        label: i18n.chain.proMicroModules.copyData,
        key: DataTableActionEnum.COPY,
        onClick: () => this.modifyItemData?.(row, DataTableActionEnum.COPY),
      },
      {
        type: 'divider',
      },
    ];
    if (row[COLUMN_FLAG] === BooleanEnum.True) {
      moreItems.push({
        label: i18n.chain.proMicroModules.btnReverDel,
        key: DataTableActionEnum.Redel,
        onClick: () => this.modifyItemData?.(row, DataTableActionEnum.Redel),
      });
    } else {
      moreItems.push({
        label: i18n.chain.comButton.delete,
        key: DataTableActionEnum.Delete,
        danger: true,
        onClick: () => this.modifyItemData?.(row, DataTableActionEnum.Delete),
      });
    }
    return moreItems;
  }

  private innerModifyColumns = (columns: IDatapkgColumn[]) => {
    let nc = ignoreFrontendColumns(columns);
    nc = this.addCommColumnsToLast(nc);
    nc = this.addDataStatusToColumns(nc);
    return nc;
  };

  private innerModifyFilterParams = (params: IDatapkgRowsQuery) => {
    params.orderby = params.orderby ?? [
      { field: COLUMN_UPDATE_TIME, asc: false },
      { field: COLUMN_CREATE_TIME, asc: false },
      { field: 'id', asc: true },
    ];
    if (this.isFirstLoad) {
      this.isFirstLoad = false;
      return _.merge({}, this.firstRequestParams ?? params, this.requestParams);
    }
    return _.merge({}, params, this.requestParams);
  };

  private addDataStatusToColumns = (columns: IColumnItem[]) => {
    return [this.getDataStausColumn(), ...columns];
  };

  // 在左侧的系统列有自己的顺序 id > action > updateTime
  private sortColumns = (columns: IColumnItem[]) => {
    const normalColumns = _.filter(
      columns,
      (column) => !['id', COLUMN_ACTION, COLUMN_UPDATE_TIME].includes(column.name),
    );

    const leftFixedColumns: IColumnItem[] = [];
    const rightFixedColumns: IColumnItem[] = [];

    const idColumn = _.find(columns, { name: 'id' });
    const actionColumn = _.find(columns, { name: COLUMN_ACTION });
    const updateTimeColumn = _.find(columns, { name: COLUMN_UPDATE_TIME });

    if (idColumn) {
      this.dataIdFixedRight ? rightFixedColumns.push(idColumn) : leftFixedColumns.push(idColumn);
    }

    if (actionColumn) {
      this.dataStatusFixedRight ? rightFixedColumns.push(actionColumn) : leftFixedColumns.push(actionColumn);
    }

    if (updateTimeColumn && this.dataDateFixedLeft) {
      leftFixedColumns.push(updateTimeColumn);
    } else if (updateTimeColumn) {
      normalColumns.push(updateTimeColumn);
    }

    return [...leftFixedColumns, ...normalColumns, ...rightFixedColumns];
  };

  private innerModifyTableColumns = (columns: IColumnItem[]) => {
    const { allSettingValues } = this.itemData.formSpec;
    let fcs = ignoreFrontendColumns(columns);
    _.forEach(fcs, (col: any) => {
      // 数据包展示的列表有可能和表单标题不一致，需要做个映射
      col.title = _.get(allSettingValues, `${col.name}.title`, col.name);
    });
    // 保存原始列信息
    this.originTableColumns = [...fcs];
    fcs = this.addCommColumnsToLast(fcs as IDatapkgColumn[]);
    fcs = this.addDataStatusToColumns(fcs);
    fcs = this.sortColumns(fcs);
    // 如果是填报表格
    if (this.dataTableType === DataTableTypeEnum.FILL) {
      fcs.push({
        name: i18n.chain.comText.operation,
        type: 'text',
        key: '__operate_column__',
        width: 120,
        minWidth: 120,
        frozen: true,
        formatter: (data: any) => {
          return <Space size="small">{this.renderOperation(data.row)}</Space>;
        },
      });
    }
    // 如果是预览表格
    if (this.dataTableType === DataTableTypeEnum.PRIVIEW) {
      fcs.push({
        name: i18n.chain.comText.operation,
        type: 'text',
        key: '__operate_column__',
        width: 100,
        minWidth: 100,
        frozen: true,
        formatter: (data: any) => {
          return <Space size="small">{this.renderPreviewOperation(data.row)}</Space>;
        },
      });
    }
    return fcs as IColumnItem[];
  };

  private getDataStausColumn(): any {
    const { isFormManageLevelUser, isNeedApproval } = this.itemData;
    const getDataStatusTags = isFormManageLevelUser
      ? getDataStatusTagsComm
      : isNeedApproval
      ? getNeedApproveDataStatusTags
      : getNotNeedApproveDataStatusTags;
    return {
      name: COLUMN_ACTION,
      type: 'number',
      title: getSystemColumnTitle(i18n.chain.proMicroModules.oneTable.dataStatus),
      width: 130,
      minWidth: 130,
      align: 'center',
      formatter: (data: Record<string, any>) => {
        const tags = getDataStatusTags(data.row);
        return _.map(tags, (it, index) => (
          <Tag key={index} color={it[1]} type="light" size="small">
            {it[0]}
          </Tag>
        ));
      },
    };
  }

  private addCommColumnsToLast(columns: IDatapkgColumn[]) {
    const commColumns: any[] = [
      {
        name: COLUMN_UPDATE_USER_ID,
        type: 'user_id',
        title: getSystemColumnTitle(i18n.chain.proMicroModules.oneTable.lastedUser),
      },
      {
        name: COLUMN_UPDATE_TIME,
        type: 'date',
        title: getSystemColumnTitle(i18n.chain.proMicroModules.oneTable.lastedTime),
        formatter: (data: Record<string, any>) => {
          let date = data.row[COLUMN_UPDATE_TIME];
          date = date ? formateDate(date, DATE_FORMATTER_2) : null;
          return <div>{date}</div>;
        },
      },
    ];
    const fixIdColums = _.map(columns as any[], (column) => {
      if (column.name === 'id') {
        column.title = getSystemColumnTitle('ID');
        column.width = 80;
        column.minWidth = 80;
      }
      return column;
    });
    return _.concat(fixIdColums, commColumns);
  }

  private getCustomWidgets(columns: IComponentColumn[]): RuleFormWidgets {
    const widgets: RuleFormWidgets = {};
    _.forEach(columns, ({ type, component, componentProps }) => {
      widgets[type] = [component, componentProps];
    });
    return widgets;
  }

  private getDateTimeMapper(columns: IComponentColumn[]): Record<PropertyKey, string[]> {
    const mapper: Record<PropertyKey, string[]> = {};
    _.forEach(columns, ({ type }) => {
      mapper[type] = [
        ColumnOperatorFilterEnum.EQ,
        ColumnOperatorFilterEnum.NE,
        ColumnOperatorFilterEnum.GT,
        ColumnOperatorFilterEnum.GE,
        ColumnOperatorFilterEnum.LT,
        ColumnOperatorFilterEnum.LE,
      ];
    });
    return mapper;
  }

  private getUserSelectMapper(columns: IComponentColumn[]): Record<PropertyKey, string[]> {
    const mapper: Record<PropertyKey, string[]> = {};
    _.forEach(columns, ({ type }) => {
      const [, , withoutOrg] = type.split('_');
      const withoutOrgBool = withoutOrg === 'true';
      mapper[type] = withoutOrgBool
        ? [ColumnOperatorFilterEnum.INTERSECT]
        : [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE, ColumnOperatorFilterEnum.CONTAIN];
    });
    return mapper;
  }

  private getCustomTypes(columns: IComponentColumn[]) {
    const types: any[] = [];
    _.forEach(columns, ({ type }) => {
      types.push({
        value: type,
        label: type,
      });
    });
    return types;
  }

  private getActionTypes() {
    return _.map([{ type: COLUMN_ACTION }], ({ type }) => ({ value: type, label: type }));
  }

  private getCustomFields(columns: IComponentColumn[]) {
    const fields: any[] = [];
    _.forEach(columns, ({ type, name, title }) => {
      fields.push({
        type,
        name,
        title,
      });
    });
    return fields;
  }

  private getDateTimeList(itemData: IOneTableNewOperatorDataComm, columns: IDatapkgColumn[]): IComponentColumn[] {
    return _.compact(
      _.map(
        _.filter(columns, ({ type }) => _.eq(type, FieldTypeEnum.DATETIME)),
        ({ name, view_format, title }) => {
          const field = findFieldByKey(itemData.formSpec.formilySchema.schema, name);
          if (field) {
            const component = field['x-component'];
            const componentProps = field['x-component-props'];
            return {
              name,
              component,
              componentProps,
              type: `${component}_${name}`,
              format: componentProps?.format ?? view_format?.format,
              title,
            };
          }
          return null;
        },
      ),
    );
  }

  private getUserSelectList(itemData: IOneTableNewOperatorDataComm, columns: IDatapkgColumn[]): IComponentColumn[] {
    return _.compact(
      _.map(
        _.filter(columns, ({ type }) => _.eq(type, FieldTypeEnum.JSON)),
        ({ name, title }) => {
          const field = findFieldByKey(itemData.formSpec.formilySchema.schema, name);
          if (field) {
            const component = field['x-component'];
            const componentProps = field['x-component-props'];
            const withoutOrg = !!componentProps.withoutOrg;
            if (component === QuestionCompEnum.USER_SELECT_INPUT) {
              return {
                name,
                component: 'UserSelect',
                componentProps,
                type: `${component}_${name}_${withoutOrg}`,
                title,
              };
            }
          }
          return null;
        },
      ),
    );
  }
  private getRuleFormProps(options: {
    options: IFilterRuleFormOptions;
    override: boolean;
    actionOptions: SelectProps['options'];
    transformActionCondition?: (node: RuleCondition) => RuleCondition;
    // 在这里构造的rule-form 需要用到schema去确定datetime的具体类型
    itemData: IOneTableNewOperatorDataComm;
  }) {
    const {
      actionOptions = [
        { value: DataStatusEnum.Insert, label: i18n.chain.dataActionStatus.insert },
        { value: DataStatusEnum.Update, label: i18n.chain.dataActionStatus.update },
        { value: DataStatusEnum.Submitted, label: i18n.chain.dataActionStatus.submit },
        { value: DataStatusEnum.UnSubmitted, label: i18n.chain.dataActionStatus.unsubmit },
        { value: DataStatusEnum.Rejected, label: i18n.chain.dataActionStatus.rejected },
        { value: DataStatusEnum.Delete, label: i18n.chain.dataActionStatus.delete },
        { value: DataStatusEnum.NoDelete, label: i18n.chain.dataActionStatus.noDelete },
        { value: DataStatusEnum.ACTION_NULL, label: i18n.chain.dataActionStatus.empty },
      ],
      options: controllerOptions,
      override,
      itemData,
    } = options || {};
    const dateTimeColumns = this.getDateTimeList(itemData, this.getColumns());
    const userSelectColumns = this.getUserSelectList(itemData, this.getColumns());
    const defaultProps: IFilterRuleFormOptions = {
      // datetime放到当前层处理
      fieldTypeExclude: [FieldTypeEnum.DATETIME, FieldTypeEnum.JSON],
      ruleFormProps: {
        notExclude: [COLUMN_ACTION],
        types: [
          ...this.getActionTypes(),
          ...this.getCustomTypes(dateTimeColumns),
          ...this.getCustomTypes(userSelectColumns),
        ],
        mapper: {
          bool: [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE],
          array_str: [
            ColumnOperatorFilterEnum.EQ,
            ColumnOperatorFilterEnum.NE,
            ColumnOperatorFilterEnum.IS,
            ColumnOperatorFilterEnum.INTERSECT,
            ColumnOperatorFilterEnum.SUPERSET,
            ColumnOperatorFilterEnum.SUBSET,
          ],
          [COLUMN_ACTION]: [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE],
          ...this.getDateTimeMapper(dateTimeColumns),
          ...this.getUserSelectMapper(userSelectColumns),
        },
        fields: [...this.getCustomFields(dateTimeColumns), ...this.getCustomFields(userSelectColumns)],
        widgets: {
          bool: 'Bool',
          [COLUMN_ACTION]: 'ActionStatus',
          array_str: {
            default: 'Multiple',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          ...this.getCustomWidgets(dateTimeColumns),
          ...this.getCustomWidgets(userSelectColumns),
        },
        components: {
          Bool: (props: any) => (
            <Bool
              {...props}
              options={[
                { label: i18n.chain.proMicroModules.filter.false, value: false },
                { label: i18n.chain.proMicroModules.filter.true, value: true },
              ]}
            />
          ),
          ActionStatus: (props: any) => <ActionStatus {...props} options={actionOptions} />,
        },
      },
      handleCustomCondition: (node: RuleCondition) => {
        const overrideActionFunc = options.transformActionCondition ?? this.handleColumnActionCondition;
        return _.flow(overrideActionFunc.bind(this), this.handleDateTimeCondition.bind(this))(_.cloneDeep(node));
      },
      transformColumnToFields: (columns: IColumn[]): RuleField[] => {
        return _.map(columns, ({ type, name, title, dataSource, component }) => {
          if (name === COLUMN_ACTION) {
            return {
              type: COLUMN_ACTION,
              name,
              title,
              validator: [{ required: true, message: `${i18n.chain.comPlaceholder.select}${title}` }],
            };
          }
          const mergedType = component === 'Cascader' && type === 'array' ? 'arrayCascader' : type;
          // 给到options就代表当前筛选项需要根据选择来填写，这里需要过滤部分组件
          const options = !_.includes(
            [
              QuestionCompEnum.INPUT,
              QuestionCompEnum.AUTO_COMPLETE,
              QuestionCompEnum.INPUT_TEXTAREA,
              QuestionCompEnum.NUMBER_PICKER,
            ],
            component,
          )
            ? dataSource
            : undefined;
          return {
            type: mergedType,
            name,
            title,
            options,
            validator: {
              default: [{ required: true, message: `${title}${i18n.chain.comWarning.noEmpty}` }],
              [ColumnOperatorFilterEnum.IS]: [],
            },
          };
        });
      },
    };
    return override ? deepMerge(defaultProps, controllerOptions) : controllerOptions ?? defaultProps;
  }

  private handleDateTimeCondition(node: RuleCondition): RuleCondition {
    const dateTimeColumns = this.getDateTimeList(this.itemData, this.getColumns());
    const isDateTimeType = _.includes(_.map(dateTimeColumns, 'name'), node.name);
    if (isDateTimeType) {
      const format = _.find(dateTimeColumns, (item) => item.name === node.name)?.format as string;
      const unit = parseFormatToPicker(format) || 'day';
      const [start, end] = [startOf(node.value, unit), endOf(node.value, unit)];

      if (node.operator === ColumnOperatorFilterEnum.EQ || node.operator === ColumnOperatorFilterEnum.NE) {
        node.operator = ColumnOperatorFilterEnum.BETWEEN;
        node.value = [start, end];
      } else if (node.operator === ColumnOperatorFilterEnum.GT) {
        node.value = end;
      } else if (node.operator === ColumnOperatorFilterEnum.GE) {
        node.value = start;
      } else if (node.operator === ColumnOperatorFilterEnum.LT) {
        node.value = start;
      } else if (node.operator === ColumnOperatorFilterEnum.LE) {
        node.value = end;
      }
    }
    return node;
  }

  private handleColumnActionCondition(node: RuleCondition): RuleCondition {
    if (node.type === COLUMN_ACTION) {
      let value = node.value;
      let name = node.name;
      const flagArr = [DataStatusEnum.NoDelete, DataStatusEnum.Delete];
      const statusArr = [DataStatusEnum.UnSubmitted, DataStatusEnum.Submitted, DataStatusEnum.Rejected];
      if (_.eq(value, DataStatusEnum.ACTION_NULL)) {
        value = null;
      }
      if (_.includes(flagArr, value) || _.eq(value, DataStatusEnum.FLAG_NULL)) {
        name = COLUMN_FLAG;
        const vIndex = _.findIndex(flagArr, (item) => item === value);
        value = vIndex >= 0 ? vIndex : null;
      }
      if (_.includes(statusArr, value)) {
        name = COLUMN_STATUS;
        value = _.findIndex(statusArr, (item) => item === value);
      }
      return { ...node, name, value };
    }
    return node;
  }
}
