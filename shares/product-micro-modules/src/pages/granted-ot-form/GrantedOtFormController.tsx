import _ from 'lodash';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { IResourceAuthorizationPut, IResourceAuthorizationPutGranteeWithPrivilege } from '@mdtApis/interfaces';
import { RequestController } from '@mdtBsControllers/request-controller';
import { OrgManageTreeListController, OrgManageTreeListModel } from '../../containers/org-manage-tree-list';
import { TransferPanelController } from '../../containers/transfer-panel';
import { type ISelectorItem, UserLazySelectorModel } from '../../containers/user-lazy-selector';
import { AbstractAppController } from '../../controllers/AbstractAppController';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import type { IGrantedOtFormModel } from './GrantedOtFormModel';

interface IControllerOptions {
  Model: IGrantedOtFormModel;
}

interface ISelectOrgItem {
  isRoot: boolean;
  value: number | string;
}

export enum StepEnum {
  AUTH_LIST = 'auth-list',
  ADMIN_MANAGE = 'admin-manage',
}

export class GrantedOtFormController extends RequestController {
  protected Model: IGrantedOtFormModel;
  protected app: AbstractAppController;
  // 部门数据加载状态
  protected isOrgLoading$ = new BehaviorSubject<boolean>(false);
  // 部门
  protected orgController?: OrgManageTreeListController;
  // 选择的部门
  protected selectedOrg$: BehaviorSubject<ISelectOrgItem>;
  // 右侧controller
  protected usersController: TransferPanelController;
  protected showModifyBtn$ = new BehaviorSubject<boolean>(false);
  protected orgReleationLoading$ = new BehaviorSubject<boolean>(false);
  protected currentOrgReleation$ = new BehaviorSubject<ISelectorItem[]>([]);
  protected orgIdNameMap: Record<string, string> = {};
  protected appId: number;
  protected rootKey: string;
  private rootLoading$ = new BehaviorSubject<boolean>(true);
  private userChangeLoading$ = new BehaviorSubject<boolean>(false);
  private step$ = new BehaviorSubject<StepEnum>(StepEnum.ADMIN_MANAGE);
  private authManageUsers$ = new BehaviorSubject<number[]>([]);
  private hasPermissionToAuth$ = new BehaviorSubject<boolean>(false);

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    const app = DatlasAppController.getInstance();
    this.app = app;
    this.appId = app.getAppId();
    this.rootKey = `app_${this.appId}`;
    this.selectedOrg$ = new BehaviorSubject<ISelectOrgItem>({ isRoot: true, value: this.rootKey });
    this.usersController = new TransferPanelController({
      userIds: [],
      userSelectorModel: new UserLazySelectorModel({ appId: this.appId }),
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.Model = null!;
    this.app = null!;
    this.currentOrgReleation$.complete();
    this.currentOrgReleation$.next([]);
    this.showModifyBtn$.complete();
    this.isOrgLoading$.complete();
    this.selectedOrg$.complete();
    this.orgReleationLoading$.complete();
    this.step$.complete();
    this.rootLoading$.complete();
    this.authManageUsers$.complete();
    this.hasPermissionToAuth$.complete();
    this.orgController?.destroy();
    this.orgController = null!;
    this.usersController?.destroy();
    this.usersController = null!;
    this.orgIdNameMap = {};
  }

  public getStep$() {
    return this.step$;
  }

  public changeStep(step: StepEnum) {
    this.step$.next(step);
  }

  public getRootLoading$() {
    return this.rootLoading$;
  }

  public getAuthManageUsers$() {
    return this.authManageUsers$;
  }

  public getHasPermissionToAuth$() {
    return this.hasPermissionToAuth$;
  }

  public async changeAuthManageUsers(users: number[]) {
    const authManageUsers = this.authManageUsers$.getValue();
    const addUsers = _.difference(users, authManageUsers);
    const removeUsers = _.difference(authManageUsers, users);
    if (addUsers.length || removeUsers.length) {
      this.userChangeLoading$.next(true);
    }
    if (addUsers.length) {
      await this.Model.postAppAuthOrization(addUsers, this.appId, this.getCancelToken()).toPromise();
    }
    if (removeUsers.length) {
      await this.Model.removeAppAuthOrization(removeUsers, this.appId, this.getCancelToken()).toPromise();
    }
    this.authManageUsers$.next(users);
    this.userChangeLoading$.next(false);
  }

  public getOrgName(orgId: number | string): string {
    return this.orgIdNameMap[orgId] || '';
  }

  public getShowModifyBtn$() {
    return this.showModifyBtn$;
  }

  public getOrgLoading$() {
    return this.isOrgLoading$;
  }

  public getOrgReleationLoading$() {
    return this.orgReleationLoading$;
  }

  public getSelectedOrg$() {
    return this.selectedOrg$;
  }

  public onOrgSelect = (orgId: string, isRoot: boolean) => {
    this.selectedOrg$.next({ isRoot, value: orgId });
  };

  public cancelModify() {
    const oldVal = this.currentOrgReleation$.getValue();
    this.usersController.getSelectedItems$().next(oldVal);
  }

  public async sureModify() {
    const newVal = this.usersController.getSelectedItems$().getValue();
    const oldVal = this.currentOrgReleation$.getValue();
    const adds = _.differenceBy(newVal, oldVal, 'id');
    const removes = _.differenceBy(oldVal, newVal, 'id');
    const st = this.selectedOrg$.getValue();
    // 新增的
    const gwp: IResourceAuthorizationPutGranteeWithPrivilege[] = _.map(adds, (item) => {
      return { grantee_id: _.toNumber(item.id), grantee_app_id: this.appId, privilege_list: ['manage'] };
    });
    // 删除的
    _.forEach(removes, (item) => {
      gwp.push({ grantee_id: _.toNumber(item.id), grantee_app_id: this.appId, privilege_list: [] });
    });
    const data = (
      st.isRoot
        ? { resource_type: 'all_app_form', resource_ids: `${this.appId}`, grantee_with_privilege: gwp }
        : { resource_type: 'all_org_form', resource_ids: `${st.value}`, grantee_with_privilege: gwp }
    ) as IResourceAuthorizationPut;
    this.Model.modifyGrantedData(data).subscribe((resp) => {
      if (!resp) return;
      this.currentOrgReleation$.next(newVal);
      toastApi.success(i18n.chain.comTip.saveSuccess);
    });
  }

  public getUsersController() {
    return this.usersController;
  }

  public getOrgController() {
    if (!this.orgController) {
      this.orgController = new OrgManageTreeListController(
        {
          appId: this.app.getAppId(),
          appName: this.app.getAppName(),
          onOrgSelectFunc: this.onOrgSelect,
          onlyRead: true,
        },
        OrgManageTreeListModel,
      );
    }
    return this.orgController;
  }

  protected async init() {
    await this.handleAdminManageFunc();
    this.rootLoading$.next(false);
    this.initAuthListFunc();
  }

  protected initModify() {
    combineLatest(this.currentOrgReleation$, this.usersController.getSelectedItems$())
      .pipe(
        map(([v1, v2]) => !_.isEqual(v1, v2)),
        distinctUntilChanged(),
      )
      .subscribe((val) => {
        this.showModifyBtn$.next(val);
      });
  }

  // 获取设置角色，群组，用户
  protected getSelectedOrgReleationData() {
    this.selectedOrg$.pipe(distinctUntilChanged((pre, next) => _.isEqual(pre, next))).subscribe((org) => {
      this.orgReleationLoading$.next(true);
      const cancelToken = this.getCancelToken();
      const [resourceType, resourceIds] = (org.isRoot ? ['all_app_form', this.appId] : ['all_org_form', org.value]) as [
        string,
        string,
      ];
      this.Model.queryGrantedData(resourceType, resourceIds, cancelToken).subscribe((data) => {
        this.orgReleationLoading$.next(false);
        this.currentOrgReleation$.next(data);
        this.usersController.getSelectedItems$().next([...data]);
      });
    });
  }

  // 初始化组织和角色列表
  protected initOrgAndRoleList = () => {
    this.isOrgLoading$.next(true);
    return this.Model.queryRolesAndOrgs(this.appId, this.rootKey)
      .pipe(
        map((value) => {
          if (!this.orgController) return;
          this.orgIdNameMap = value.orgIdNameMap;
          this.orgController.init(value.orgList);
          this.isOrgLoading$.next(false);
          return value;
        }),
      )
      .subscribe();
  };

  private initAuthListFunc() {
    this.initOrgAndRoleList();
    this.getSelectedOrgReleationData();
    this.initModify();
  }

  private async handleAdminManageFunc() {
    const authManageResp = await this.Model.getAppAuthOrization(this.app.getAppId(), this.getCancelToken()).toPromise();
    if (authManageResp.success) {
      this.authManageUsers$.next(authManageResp.data);
      if (_.some(authManageResp.data, (user) => _.isEqual(user, this.app.getUserId()))) {
        this.step$.next(StepEnum.AUTH_LIST);
      }
    }
    this.listenCurrentUserHasPermissionToAuth();
  }

  private listenCurrentUserHasPermissionToAuth() {
    this.authManageUsers$.subscribe((users) => {
      const result = _.some(users, (user) => _.isEqual(user, this.app.getUserId()));
      this.hasPermissionToAuth$.next(result);
    });
  }
}
