import { FC, memo, useMemo } from 'react';
import { FolderUser } from '@metro/icons';
import { Breadcrumb } from '@metroDesign/breadcrumb';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Spin } from '@metroDesign/spin';
import { Tag } from '@metroDesign/tag';
import { Typography } from '@metroDesign/typography';
import { LoadingWrapper } from '@mdtBsComm/components/loading-wrapper';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import UserSelectInput from '../../components/user-select-input';
import { OrgManageTreeList } from '../../containers/org-manage-tree-list';
import { TransferPanel } from '../../containers/transfer-panel';
import i18n from '../../languages';
import { GrantedOtFormController, StepEnum } from './GrantedOtFormController';
import './index.less';

interface IProps {
  controller: GrantedOtFormController;
  className?: string;
}

const ModifyButton: FC<IProps> = memo(({ controller }) => {
  const show = useObservableState(controller.getShowModifyBtn$());

  const onCancel = () => {
    controller.cancelModify();
  };

  const onSureModify = async () => {
    controller.sureModify();
  };

  return show ? (
    <LoadingWrapper>
      <Flex className="modify-btn-wrapper" gap={12}>
        <Button onClick={onCancel}>{i18n.chain.comButton.cancel}</Button>
        <Button type="primary" onClick={onSureModify}>
          {i18n.chain.proMicroModules.grantedOtForm.sureOk}
        </Button>
      </Flex>
    </LoadingWrapper>
  ) : null;
});

// 子组件-右侧表格====================================================================================
const RightContent: FC<IProps> = ({ controller }) => {
  const selected = useObservableState(controller.getSelectedOrg$());
  const loading = useObservableState(controller.getOrgReleationLoading$());

  const ele = useMemo(() => {
    const ele = (
      <Tag color="error" type="light">
        {selected.isRoot ? i18n.chain.proMicroModules.grantedOtForm.scopeApp : controller.getOrgName(selected.value)}
      </Tag>
    );
    const tip = i18n.chain.proMicroModules.grantedOtForm.whoTip(
      <Typography.Text type="primary">{i18n.chain.proMicroModules.grantedOtForm.manageForm}</Typography.Text>,
      ele,
    );
    return (
      <div className="users-wrap">
        {tip}
        <TransferPanel controller={controller.getUsersController()} />
      </div>
    );
  }, [selected, controller]);

  return (
    <div className="page_granted_ot_form-right">
      <Spin spinning={loading}>
        {ele}
        {loading ? null : <ModifyButton controller={controller} />}
      </Spin>
    </div>
  );
};

// 子组件-左侧列表====================================================================================
const LeftContent: FC<IProps> = ({ controller }) => {
  const isLoading = useObservableState(() => controller.getOrgLoading$());
  const orgController = controller.getOrgController();

  return (
    <div className="page_granted_ot_form-left">
      <Spin spinning={isLoading} fillParent>
        <OrgManageTreeList controller={orgController} />
      </Spin>
    </div>
  );
};

export const GrantedOtForm: FC<IProps> = ({ controller, className }) => {
  const rootLoading = useObservableState(controller.getRootLoading$());
  const step = useObservableState(controller.getStep$());
  if (rootLoading) {
    return <Spin spinning={rootLoading} fillParent />;
  }
  if (step === StepEnum.ADMIN_MANAGE) {
    return <AuthManagePage controller={controller} />;
  }
  return <AuthPage controller={controller} className={className} />;
};

const AuthPage: FC<IProps> = ({ controller, className }) => {
  return (
    <div className={`page_granted_ot_form ${className || ''}`}>
      <LeftContent controller={controller} />
      <Flex vertical gap={16} className="page_granted_ot_form_layout">
        <BreadcrumbView controller={controller} />
        <RightContent controller={controller} />
      </Flex>
    </div>
  );
};

const BreadcrumbView: FC<IProps> = ({ controller }) => {
  return (
    <Breadcrumb
      items={[
        {
          title: (
            <Button.Link icon={<FolderUser />}>{i18n.chain.proMicroModules.grantedOtForm.adminManage}</Button.Link>
          ),
          onClick: () => {
            controller.changeStep(StepEnum.ADMIN_MANAGE);
          },
        },
        {
          title: i18n.chain.proMicroModules.grantedOtForm.manageList,
        },
      ]}
    />
  );
};

const AuthManagePage: FC<IProps> = ({ controller }) => {
  const users = useObservableState(controller.getAuthManageUsers$());
  const hasPermissionToAuth = useObservableState(controller.getHasPermissionToAuth$());
  return (
    <div className="page_granted_ot_form-auth-manage">
      <Typography.Title level={4}>{i18n.chain.proMicroModules.grantedOtForm.adminManage}</Typography.Title>

      <Flex vertical gap={8} className="page_granted_ot_form-auth-manage-user-select">
        <Flex gap={16} align="center">
          <Flex align="center" gap={4} style={{ flex: '1 0 auto' }}>
            <Typography.Text>{i18n.chain.proMicroModules.grantedOtForm.adminTitle1}</Typography.Text>
            <Tag color="error" type="light" style={{ marginRight: 0 }}>
              {i18n.chain.proMicroModules.grantedOtForm.scopeApp}
            </Tag>
            <Typography.Text>{i18n.chain.proMicroModules.grantedOtForm.adminTitle2}</Typography.Text>
          </Flex>
          <UserSelectInput
            btnLabel={`+ ${i18n.chain.proMicroModules.oneTable.addUsers}`}
            value={users}
            withoutOrg
            onChange={async (v) => controller.changeAuthManageUsers(v)}
          />
        </Flex>
        <Flex justify="flex-end" style={{ marginTop: 16 }}>
          <Button
            type="primary"
            disabled={!hasPermissionToAuth}
            showTooltip={!hasPermissionToAuth}
            tooltipTitle={i18n.chain.proMicroModules.grantedOtForm.noGranted}
            onClick={() => {
              controller.changeStep(StepEnum.AUTH_LIST);
            }}
          >
            {i18n.chain.proMicroModules.grantedOtForm.manageForm}
          </Button>
        </Flex>
      </Flex>
    </div>
  );
};
