import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import type { IRequestCancelToken, IResourceAuthorizationPut } from '@mdtApis/interfaces';
import {
  deleteAuthorizationPermissionAsync,
  getAuthorizationAsync,
  postResourceAuthorizationAsync,
  queryResourceAuthorizationAsync,
} from '@mdtBsServices/auth';
import { postOneTableUpdateAuthorizationOfGrantedFormAsync } from '@mdtBsServices/datlasbff';
import { ISelectorItem, transformBackendTypeToFrontendType } from '../../containers/user-lazy-selector';
import { UserModel } from '../../models/UserModel';

export class GrantedOtFormModel {
  public static getAppAuthOrization(appId: number, cancelToken?: IRequestCancelToken) {
    return from(
      getAuthorizationAsync(
        {
          resourceType: 'all_app_form',
        },
        {
          resourceIds: [appId],
          privilegeType: 'manage',
        },
        { cancelToken },
      ),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return {
          ...resp,
          data: _.map(resp.data, 'grantee'),
        };
      }),
    );
  }

  public static postAppAuthOrization(userId: number[], appId: number, cancelToken?: IRequestCancelToken) {
    return from(
      postResourceAuthorizationAsync(
        {
          resourceType: 'all_app_form',
        },
        {
          resource_ids: [`${appId}`],
          grantee_with_privilege: userId.map((it) => ({
            grantee_id: it,
            grantee_app_id: appId,
            privilege_list: ['manage'],
          })),
        },
        { cancelToken },
      ),
    );
  }

  public static removeAppAuthOrization(userId: number[], appId: number, cancelToken?: IRequestCancelToken) {
    return from(
      deleteAuthorizationPermissionAsync(
        { resourceType: 'all_app_form' },
        {
          resource_ids: [`${appId}`],
          grantee_with_privilege: userId.map((it) => ({
            grantee_id: it,
            grantee_app_id: appId,
            privilege_list: ['manage'],
          })),
        },
        { cancelToken },
      ),
    );
  }
  // 获取角色和部门数据
  public static queryRolesAndOrgs(appId: number, rootId: string) {
    return from(UserModel.queryRolesAndOrgs(appId, rootId)).pipe(
      map((value) => {
        const orgIdNameMap: Record<string, string> = {};
        _.reduce(
          value.orgIdNameMap,
          (prev, curr) => {
            prev[curr.key] = curr.title;
            return prev;
          },
          orgIdNameMap,
        );
        return { orgList: value.orgList, orgIdNameMap };
      }),
    );
  }

  public static queryGrantedData(resourceType: string, resourceIds: string, cancelToken?: IRequestCancelToken) {
    return from(
      queryResourceAuthorizationAsync(resourceType, {
        params: { resource_ids: resourceIds, privilege_type: 'manage' },
        cancelToken,
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return _.map(resp.data, (it) => {
          const st = transformBackendTypeToFrontendType(it.grantee_type);
          return st ? { id: it.grantee, name: it.name, type: st } : null;
        }).filter(Boolean) as unknown as ISelectorItem[];
      }),
    );
  }

  public static modifyGrantedData(data: IResourceAuthorizationPut) {
    return from(postOneTableUpdateAuthorizationOfGrantedFormAsync(data)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.success),
    );
  }
}

export type IGrantedOtFormModel = typeof GrantedOtFormModel;
