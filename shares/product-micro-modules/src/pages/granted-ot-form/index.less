.page_granted_ot_form_layout {
  width: 100%;

  .metro-breadcrumb {
    padding: 20px 20px 0;
  }
}

.page_granted_ot_form {
  display: flex;
  width: 100%;
  height: 100%;

  .table-menu {
    padding: 16px 26px 0;

    .dmc-radio-nav {
      font-size: 16px;
    }
  }

  &.without-root-node .component_organization-root {
    display: none !important;
  }
}

.page_granted_ot_form-right {
  width: 100%;
  padding: 0 20px;

  .users-wrap {
    padding-top: 20px;
  }

  .modify-btn-wrapper {
    justify-content: flex-end;
    margin-top: 12px;
    padding-right: 20px;
  }

  .user-label {
    display: flex;
    align-items: center;
    height: 32px;
    padding-bottom: 8px;
    color: var(--metro-text-1);
    font-size: 14px;

    .metro-tag {
      margin: 0 8px;
    }
  }
}

.page_granted_ot_form-left {
  height: 100%;
  background-color: var(--dmc-org-list-bg-color);
  border-right: 1px solid var(--dmc-org-border-color);
}

.page_granted_ot_form-auth-manage {
  width: 100%;
  padding: 20px;

  &-user-select {
    margin: 16px 0;
    padding: 48px 24px 24px;
    font-size: 14px;
    background-color: var(--dmc-primary-panel-3);
    border-radius: 6px;
  }
}
