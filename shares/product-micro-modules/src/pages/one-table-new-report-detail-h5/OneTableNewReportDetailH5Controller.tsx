import { ONE_TABLE_INFO } from '../../datlas/datlasConfig';
import { DataTableTypeEnum } from '../../utils/oneTableNewUtil';
import { OneTableNewDataTableStaticsController } from '../one-table-new-data-table-statics/OneTableNewDataTableStaticsController';
import { openOneTableNewPeriodicManagementDrawer } from '../one-table-new-periodic-management/drawer-one-table-new-periodic-management';
import {
  DeliveryManagementH5,
  DeliveryManagementH5Controller,
} from '../one-table-new-report-detail/components/delivery-management-h5';
import {
  IControllerOptions as IProps,
  OneTableNewReportDetailController,
} from '../one-table-new-report-detail/OneTableNewReportDetailController';

export interface IControllerOptions extends IProps {
  onUpdate?: (data: Record<string, any>) => void;
}

export class OneTableNewReportDetailH5Controller extends OneTableNewReportDetailController {
  private onUpdate?: (data: Record<string, any>) => void;
  public constructor(options: IControllerOptions) {
    const { onUpdate, ...restOptions } = options;
    super({
      ...restOptions,
      fillOnlyForm: true,
      showFormInfoEditBtn: false,
      excludeOperators: ['copyCreate', 'editFormType'],
    });
    this.onUpdate = onUpdate;
  }

  public destroy() {
    super.destroy();
    this.deliveryManagementController?.destroy();
  }

  public getDeliveryManagement() {
    return DeliveryManagementH5;
  }

  public getDeliveryManagementController() {
    if (!this.deliveryManagementController) {
      const options = this.getDeliverManageMentOptions();
      this.deliveryManagementController = new DeliveryManagementH5Controller(options);
    }
    return this.deliveryManagementController;
  }

  public getDataTableController() {
    if (!this.dataTableController) {
      const itemData = this.getFormData()!;
      this.dataTableController = new OneTableNewDataTableStaticsController({
        itemData,
        dataPreviewOptions: {
          pkgId: itemData.pkgId,
          hasDownload: ONE_TABLE_INFO.hasH5DetailDownload,
          hasFilter: ONE_TABLE_INFO.hasH5DetailFilter,
          enableFlat: ONE_TABLE_INFO.hasH5EnableFlat ?? true,
          hasBlurSearch: ONE_TABLE_INFO.hasH5BlurSearch ?? true,
          onClickRow: (item) => {
            this.innerModifyItemData(item, DataTableTypeEnum.PRIVIEW);
          },
        },
        hideDataStatistic: ONE_TABLE_INFO.hideH5DataStatistic,
        dataStatusFixedRight: ONE_TABLE_INFO.hasH5DataStatusFixedRight,
        dataIdFixedRight: ONE_TABLE_INFO.hasH5DataIdFixedRight,
        dataDateFixedLeft: ONE_TABLE_INFO.hasH5DataDateFixedLeft,
        dataTableType: DataTableTypeEnum.DETAIL,
      });
    }
    return this.dataTableController;
  }

  public viewPeriodicHistory = () => {
    openOneTableNewPeriodicManagementDrawer({
      itemData: this.getFormData()!,
      fromPage: this.getFromPage(),
      onUpdate: (data) => this.onUpdate?.(data),
    });
  };
}
