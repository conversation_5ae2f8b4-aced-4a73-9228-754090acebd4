import _ from 'lodash';
import { RequestDataTypeEnum } from '.';

export const getFieldMap = (dataRequire: Record<string, any>, userSetting: Record<string, string>) => {
  const fieldMap: Record<string, string> = Object.create(null);
  if (dataRequire.type === RequestDataTypeEnum.ARRAY) {
    _.forEach(dataRequire.items.properties, (val, key) => {
      fieldMap[key] = userSetting[key] || key;
    });
  } else if (dataRequire.type === RequestDataTypeEnum.OBJECT) {
    _.forEach(dataRequire.properties, (val, key) => {
      fieldMap[key] = userSetting[key] || key;
    });
  }
  return fieldMap;
};

export const checkDataType = (dataType: string, data: any) => {
  if (!dataType || dataType === RequestDataTypeEnum.ANY) return true;
  if (_.isArray(data)) {
    return dataType === RequestDataTypeEnum.ARRAY;
  }
  return typeof data === dataType;
};

export const mappingObject = (obj: Record<string, any>, fieldMap: Record<string, string>) => {
  if (_.isEmpty(fieldMap)) return obj;
  const c_obj = { ...obj };
  _.forEach(fieldMap, (map, key) => {
    const val = _.get(obj, map);
    c_obj[key] = !_.isNil(val) ? val : !_.isNil(_.get(obj, key)) ? _.get(obj, key) : '';
  });
  return c_obj;
};
