import _ from 'lodash';
import { CSSProperties, FC, memo, useRef, useState } from 'react';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ArrayField as ArrayFieldType } from '@formily/core';
import { ArrayField, Field, observer, useField } from '@formily/react';
import Add from '@metro/icons/dist/esm/react/Add';
import AppList from '@metro/icons/dist/esm/react/AppList';
import Delete from '@metro/icons/dist/esm/react/Delete';
import Edit2Filled from '@metro/icons/dist/esm/react/Edit2Filled';
import { Button } from '@metroDesign/button';
import { Checkbox } from '@metroDesign/checkbox';
import { Collapse } from '@metroDesign/collapse';
import { Input } from '@metroDesign/input';
import { Modal } from '@metroDesign/modal';
import { Popconfirm } from '@metroDesign/popconfirm';
import { Select } from '@metroDesign/select';
import { Space } from '@metroDesign/space';
import { toastApi } from '@metroDesign/toast';
import { useMemoizedFn } from 'ahooks';
import { nanoid } from 'nanoid';
import { BehaviorSubject } from 'rxjs';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import i18n from '../../../../../languages';
import { useEmmitter } from '../../../hooks/useEmmitter';
import { IEditor, IMonaco, MonacoEditor } from '../../monaco-editor';

const EditName: FC<{ name: string; changeName: (value: string) => void }> = ({ name, changeName }) => {
  const [editing, setEditing] = useState(false);

  const finishedEdit = (e: any) => {
    let v = _.trim(e.target.value);
    setEditing(false);
    v && v !== name && changeName(v);
  };

  if (editing) {
    return (
      <Input width={160} autoFocus size="small" defaultValue={name} onBlur={finishedEdit} onPressEnter={finishedEdit} />
    );
  }

  return (
    <Space className="title" size={4}>
      <span>{name}</span>
      <Button
        size="small"
        className="hover-icon"
        ghost
        icon={<Edit2Filled />}
        onlyIcon
        onClick={() => setEditing(true)}
      />
    </Space>
  );
};

const SortableItem: FC<any> = observer((props) => {
  const { id, item, codeEmmitter$, removeItem, updateList, ...resetProps } = props;
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });
  const editorRef = useRef<IEditor>();

  const style: CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 })?.replace(
      /translate3d\(([^,]+),/,
      'translate3d(0,',
    ),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const genExtra = () => {
    return (
      <Popconfirm
        overlayClassName="designer-filter-item-popconfirm"
        title={i18n.chain.proMicroModules.workflow.edit.delHandler}
        description={i18n.chain.proMicroModules.workflow.edit.delHandlerDesc}
        type="warning"
        onConfirm={() => removeItem(id)}
      >
        <Button size="small" className="hover-icon" ghost icon={<Delete />} onlyIcon />
      </Popconfirm>
    );
  };

  const getHeader = () => {
    return (
      <div className="item-header">
        <Space size="small">
          <div ref={setActivatorNodeRef} style={{ touchAction: 'none', cursor: 'move' }} {...listeners}>
            <AppList />
          </div>
          <Checkbox
            checked={item.checked}
            onChange={(e) => {
              item.checked = e.target.checked;
              updateList();
            }}
          />
          <EditName
            name={item.name}
            changeName={(v: string) => {
              item.name = v;
            }}
          />
        </Space>
      </div>
    );
  };

  const finishedEdit = () => {};

  const editCode = () => {
    codeEmmitter$.next({
      open: true,
      code: item.code,
      finishedEdit: (value: string) => {
        editorRef.current?.setValue(value);
        item.code = value;
        updateList();
      },
    });
  };

  const onMount = (editor: IEditor) => {
    editorRef.current = editor;
  };

  return (
    <div className="designer-filter-item" id={id} {...resetProps} ref={setNodeRef} style={style} {...attributes}>
      <Collapse
        ghost
        expandIconPosition="end"
        collapsible="icon"
        items={[
          {
            key: item.id,
            label: getHeader(),
            extra: genExtra(),
            children: (
              <>
                <MonacoEditor
                  language="javascript"
                  finishedEdit={finishedEdit}
                  height="280px"
                  disableFull
                  fnName="format(data)"
                  value={item.code}
                  readOnly
                  onMount={onMount}
                />
                <Button
                  size="small"
                  className="modal-edit-icon"
                  ghost
                  icon={<Edit2Filled />}
                  onlyIcon
                  onClick={editCode}
                />
              </>
            ),
          },
        ]}
      />
    </div>
  );
});

const ModalEditor = memo<{ codeEmmitter$: BehaviorSubject<any> }>(({ codeEmmitter$ }) => {
  const value = useObservableState(codeEmmitter$);
  const editorRef = useRef<IEditor>();
  const monacoRef = useRef<IMonaco>();

  const onMount = (editor: IEditor, monaco: IMonaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
  };

  const closeModal = () => {
    codeEmmitter$.next({});
  };

  const saveEdit = () => {
    if (!editorRef.current || !monacoRef.current) {
      closeModal();
      return;
    }
    const editor = editorRef.current!;
    const monaco = monacoRef.current!;
    const model = editor.getModel()!;
    const newValue = _.trim(editor.getValue());
    const markers = monaco.editor.getModelMarkers({ resource: model.uri });
    // 如果有错误，则不保存
    if (markers.length || !newValue) {
      toastApi.error(i18n.chain.proMicroModules.workflow.edit.codeEmptyOrGrammerError);
      return;
    }
    closeModal();
    value.finishedEdit(newValue);
  };

  return (
    <Modal
      centered
      wrapClassName="fake-code-editor-dialog"
      open={value.open}
      title={i18n.chain.proMicroModules.workflow.edit.codeEdit}
      width="80vw"
      onCancel={closeModal}
      onOk={saveEdit}
      destroyOnClose
    >
      <MonacoEditor
        finishedEdit={() => {}}
        height="100%"
        disableFull
        disableCopy
        fnName="format(data)"
        language="javascript"
        value={value.code}
        onMount={onMount}
      />
    </Modal>
  );
});

const FilterList = observer((props: any) => {
  const filed = useField<ArrayFieldType>();
  const { value = [], disabled, onChange } = props;

  const codeEmmitter$ = useEmmitter({});

  const removeItem = useMemoizedFn((id: string) => {
    const index = _.indexOf(keys, id);
    filed.remove(index);
  });

  const updateList = useMemoizedFn(() => {
    onChange([...filed.value]);
  });

  const items: any[] = [];
  const keys: string[] = _.map([...value], (it) => {
    items.push(
      <SortableItem
        key={it.id}
        id={it.id}
        item={it}
        updateList={updateList}
        removeItem={removeItem}
        codeEmmitter$={codeEmmitter$}
      />,
    );
    return it.id;
  });

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = _.indexOf(keys, active.id);
      const overIndex = _.indexOf(keys, over?.id);
      filed.move(activeIndex, overIndex);
    }
  };

  const addLocalFilter = () => {
    codeEmmitter$.next({
      open: true,
      code: 'return data',
      finishedEdit: (value: string) => {
        filed.push({
          id: nanoid(10),
          name: i18n.chain.proMicroModules.workflow.edit.createHandler,
          code: value,
          checked: true,
        });
      },
    });
  };

  return (
    <>
      <div className={`designer-filter-list ${disabled ? 'disabled' : ''}`}>
        <DndContext onDragEnd={onDragEnd}>
          <SortableContext items={keys} strategy={verticalListSortingStrategy}>
            {items}
          </SortableContext>
        </DndContext>
        <div className="designer-filter-create">
          <Select placeholder={i18n.chain.proMicroModules.workflow.edit.addGlobalHandler} />
          <Button type="primary" icon={<Add />} onlyIcon onClick={addLocalFilter} />
        </div>
      </div>
      <ModalEditor codeEmmitter$={codeEmmitter$} />
    </>
  );
});

export const FilterData = observer(() => {
  return (
    <>
      <Field
        name="useFilter"
        decorator={['FormItem']}
        component={['Checkbox', { children: i18n.chain.proMicroModules.workflow.edit.handler }]}
        reactions={(field) => {
          const value = field.value;
          const vf = field.form.fields[field.query('.filters')['addresses']];
          vf?.setComponentProps({ disabled: !value });
        }}
      />
      <ArrayField name="filters" component={[FilterList, { disabled: true }]} />
    </>
  );
});
