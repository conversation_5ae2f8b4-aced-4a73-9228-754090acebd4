.designer-formate-data-setting {
  .metro-toast-container-bg {
    padding: 0 10px;
  }

  .monaco-editor-container {
    background: var(--metro-fill-0);
    border: none;
  }

  .designer-filter-table {
    .metro-table-thead {
      display: none;
    }
  }

  .designer-filter-list {
    position: relative;
    margin: 0;
    border: 1px solid var(--metro-border-0);
    border-radius: 6px;

    &.disabled {
      &::before {
        position: absolute;
        inset: 0;
        z-index: 10;
        background: var(--metro-secondary-disabled);
        opacity: 0.6;
        content: '';
      }
    }
  }

  .designer-filter-item {
    position: relative;
    border-bottom: 1px solid var(--metro-border-0);

    .hover-icon {
      visibility: hidden;
    }

    .modal-edit-icon {
      position: absolute;
      right: 26px;
      bottom: 2px;
    }

    &:hover {
      .hover-icon {
        visibility: visible;
      }
    }

    .metro-collapse-header {
      cursor: auto;
    }

    .item-header {
      display: flex;
      align-items: center;
      height: 24px;
    }

    .title {
      display: flex;
      align-items: center;
      font-size: 13px;
    }
  }

  .designer-filter-create {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 47px;
    padding: 15px;
  }
}

.fake-code-editor-dialog {
  .metro-modal-body {
    height: calc(100vh - 220px);
  }

  .monaco-editor-container {
    height: 100%;
    border: none;
  }
}

.designer-filter-item-popconfirm.metro-popover {
  max-width: 350px;
}

.designer-formate-data-mapping {
  position: relative;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
  }
}

.mapping-field-select .metro-select-selector {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
