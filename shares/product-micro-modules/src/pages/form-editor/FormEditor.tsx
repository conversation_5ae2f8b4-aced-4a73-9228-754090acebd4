import _ from 'lodash';
import React, { FC } from 'react';
import {
  CompositePanel, // 左侧组合布局面板
  Designer, // 设计器根组件，主要用于下发上下文
  DesignerToolsWidget, // 画板工具挂件
  ICompositePanelItemProps,
  IDesignerComponents,
  ResourceWidget, // 拖拽源挂件
  StudioPanel, // 主布局面板
  ToolbarPanel, // 工具栏布局面板
  ViewPanel, // 视图布局面板
  ViewportPanel, // 视口布局面板
  ViewToolsWidget, // 视图切换工具挂件
  Workspace, // 工作区组件，核心组件，用于管理工作区内的拖拽行为，树节点数据等等...
  WorkspacePanel, // 工作区布局面板
} from '@designable/react';
import { useCreation } from 'ahooks';
import classnames from 'classnames';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import {
  ArrayCards,
  ArrayItems,
  ArrayTable,
  AutoComplete,
  Cascader,
  Checkbox,
  DatePicker,
  DescriptionText,
  Field,
  Form,
  FormCollapse,
  FormGrid,
  FormLayout,
  Input,
  NumberPicker,
  ObjectContainer,
  Password,
  Radio,
  Rate,
  Select,
  Space,
  Switch,
  Text,
  TextArea,
  TimePicker,
  TreeSelect,
} from '@mdtProFormEditor/metro-form-design/components';
import { SettingsForm } from '@mdtProFormEditor/metro-form-settings';
import { CompWithLayout } from '../../components/comp-with-layout';
import { GeometryInput } from '../../components/geometry-input/GeometryInputDesign';
import { Upload } from '../../components/upload/UploadDesign';
import { UserSelectInput } from '../../components/user-select-input/UserSelectInputDesign';
import { DatapkgSelectorComp as PkgSelect } from '../../containers/datapkg-selector';
import { FormilyUserSelector } from '../../containers/formily-user-selector';
import { UserSelector } from '../../containers/formily-user-selector/UserSelectorDesign';
import i18n from '../../languages';
import formViewComponentMiddleware from '../../utils/form-view-component-middleware';
import { DataSource } from './formily-externals/data-source';
import { DataInnerApiCascader } from './formily-externals/data-source/config-setting/data-inner-api-cascader';
import { DefaultValue } from './formily-externals/default-value';
import { Dynamic } from './formily-externals/dynamic';
import { ReactionsSetter } from './formily-externals/reactions-setter';
import { VisibleSetter } from './formily-externals/visible-setter/VisibleSetter';
import { ComponentTreeWidget } from './widgets/ComponentTreeWidget';
import { SubmitStyleConfig } from './widgets/submit-style-config';
import { useFormEditorProvider } from './formEditorContext';
import { FormEditorController } from './FormEditorController';
// import { transformSettingProps } from './transformer';
import { ActionsWidget, LogoWidget, PreviewWidget } from './widgets';
import 'antd/dist/antd.less';
import './index.less';

const metroComponents = {
  ArrayItems,
  ArrayCards,
  ArrayTable,
  Form,
  Field,
  Input,
  TextArea,
  Select,
  Radio,
  Rate,
  Checkbox,
  NumberPicker,
  Password,
  DatePicker,
  TimePicker,
  Upload,
  Switch,
  Text,
  DescriptionText,
  Space,
  FormCollapse,
  FormGrid,
  FormLayout,
  GeometryInput,
  UserSelectInput,
  UserSelector,
  ObjectContainer,
  Cascader,
  TreeSelect,
  AutoComplete,
};
const settingComponents = {
  DataSource,
  DefaultValue,
  VisibleSetter,
  Dynamic,
  ReactionsSetter,
  PkgSelect,
  SubmitStyleConfig,
  CompWithLayout,
  DataInnerApiCascader,
  UserSelectInput,
  NumberPicker,
  UserSelector: FormilyUserSelector,
};

interface IFormEditorProps {
  className?: string;
  headerLess?: boolean;
  controller: FormEditorController;
  customerDesignerComponents?: {
    // default unshift
    method?: 'push' | 'unshift';
    components: IDesignerComponents;
  };
}

const defaultSources = [
  UserSelectInput,
  Radio,
  Checkbox,
  DatePicker,
  Input,
  AutoComplete,
  GeometryInput,
  Upload,
  Switch,
  Rate,
  ArrayCards,
  ArrayTable,
  Cascader,
  DescriptionText,
  // UserSelector,
];

const FormEditorInner: FC<IFormEditorProps> = ({ controller, className, headerLess, customerDesignerComponents }) => {
  useObservableState(controller.initSchemaUpdate$);
  const engine = controller.engine;
  const CompositePanelItem = CompositePanel.Item as React.FC<ICompositePanelItemProps>;
  const cls = classnames('page_metro-design', { 'header-less': headerLess }, className);
  let sources = defaultSources;
  if (customerDesignerComponents) {
    const { method, components } = customerDesignerComponents;
    const processedComponents = formViewComponentMiddleware.processFullComponents(components);
    const customComps: any = _.values(processedComponents);
    sources = method === 'push' ? _.concat(defaultSources, customComps) : _.concat(customComps, defaultSources);
    _.assign(metroComponents, processedComponents);
  }
  return (
    <Designer engine={engine} theme={controller.theme}>
      <StudioPanel logo={<LogoWidget />} actions={<ActionsWidget controller={controller} />} className={cls}>
        <CompositePanel>
          <CompositePanelItem title={i18n.chain.proMicroModules.fillDesign.fieldTyle}>
            <ResourceWidget className="metro-resource-widget" title="sources.Inputs" sources={sources} />
          </CompositePanelItem>
        </CompositePanel>
        <Workspace id="form">
          <WorkspacePanel>
            <ToolbarPanel>
              <DesignerToolsWidget use={['SCREEN_TYPE']} />
              <ViewToolsWidget use={['DESIGNABLE', 'PREVIEW']} />
            </ToolbarPanel>
            <ViewportPanel>
              <ViewPanel type="DESIGNABLE">{() => <ComponentTreeWidget components={metroComponents} />}</ViewPanel>
              {/* <ViewPanel type="JSONTREE" scrollable={false}>
                {(tree, onChange) => <SchemaEditorWidget tree={tree} onChange={onChange} />}
              </ViewPanel> */}
              <ViewPanel type="PREVIEW">
                {() => (
                  <PreviewWidget
                    editorSchema={controller.getEditorSchemaToSave(true)}
                    dataSource={controller.getDataSource()}
                  />
                )}
              </ViewPanel>
            </ViewportPanel>
          </WorkspacePanel>
        </Workspace>
        <SettingsForm
          components={settingComponents}
          allValues={controller.getAllSettingValues()}
          // transformer={transformSettingProps}
          optionsMap={controller.optionsMap}
          onChange={(node, values) => {
            controller.refreshNodeTree(node, values);
            // controller.initNodeForm(node.id, values);
          }}
          onTypeChange={(node, newComponentName) => {
            controller.replaceNodeType(node, newComponentName);
          }}
          onFormInit={(id, form) => {
            controller.initNodeForm(id, form.values);
          }}
        />
      </StudioPanel>
    </Designer>
  );
};

const FormEditor: FC<IFormEditorProps> = (props) => {
  const Provider = useFormEditorProvider();

  const value = useCreation(() => {
    return { formEditorController: props.controller };
  }, [props.controller]);

  return (
    <Provider value={value}>
      <FormEditorInner {...props} />
    </Provider>
  );
};

export { FormEditor };
