.workflow-edit-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.workflow-edit {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-shrink: 1;
  padding: 0 20px;
}

.bpmn-panel-wrap {
  width: 100%;
  height: 100%;
}

.dm-spec-import-btns-wrap {
  display: flex;
  align-items: center;
  margin-right: 12px;

  .dmc-btn {
    padding-right: 12px;
    border-radius: 6px 0 0 6px;

    .dmc-btn-bg {
      border-right: none;
      border-radius: 6px 0 0 6px;
    }
  }

  .dm-spec-template_upload-btn {
    border-radius: 0 6px 6px 0;
  }
}

.js-properties-panel {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  width: 300px;
  border-left: 1px solid var(--dmc-split-page-color);
}

.workflow-edit-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 52px;
  background: var(--dmc-page-header-bg-color);
  border-bottom: 1px solid var(--dmc-page-header-border-color);

  .leftBox {
    display: flex;
    align-items: center;
    padding-left: 12px;

    .nameInput {
      margin-left: 6px;
    }
  }

  .rightBox {
    display: flex;
    align-items: center;
    padding-right: 12px;

    .splitLine {
      width: 1px;
      height: 19px;
      margin: auto 12px;
      background-color: var(--dmc-split-page-color);
    }

    .giveUpBtn {
      margin-right: 8px;
    }
  }
}

.timer-group-setting {
  display: flex;

  .timer-group-title {
    flex-direction: row-reverse;
    flex-shrink: 0;
    width: 120px;
    padding-right: 8px;
  }

  .timer-group-content {
    flex-grow: 1;
  }

  .timer-group-content-horizontal {
    display: flex;

    .metro-formily-item:not(:last-child) {
      margin-right: 10px;
    }
  }
}

.customer-radio-button-group {
  .metro-formily-item-control-content-component {
    line-height: 1 !important;
  }
}

.vars-wrap {
  .metro-radio-button-wrapper-checked::after {
    position: absolute;
    right: 0;
    bottom: -1px;
    left: 0;
    height: 1px;
    background-color: var(--metro-bg-0);
    content: '';
  }

  .metro-formily-item-control .metro-formily-item-control-content .metro-formily-item-control-content-component {
    border-bottom: 1px solid var(--metro-border-0);
  }

  .metro-radio-button-wrapper-checked:not(.metro-radio-button-wrapper-disabled) {
    background-color: transparent;
  }

  .metro-radio-button-wrapper-checked:not(.metro-radio-button-wrapper-disabled):focus-within {
    box-shadow: none;
  }

  .metro-radio-group-button {
    padding: 0;
    border-bottom: none;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  .metro-space > .metro-space-item:first-child {
    border-right: 1px solid var(--metro-border-0);
  }
}

.wf-spec-form {
  padding-bottom: 0;
}

.common-vars-only-one {
  .metro-card-head {
    display: none;
  }
}

.config-inline-script {
  height: 100%;
  padding: 12px;

  .monaco-editor-container {
    height: 100%;
  }
}

.config-inline-script-variable-menu {
  max-width: 600px;
}
