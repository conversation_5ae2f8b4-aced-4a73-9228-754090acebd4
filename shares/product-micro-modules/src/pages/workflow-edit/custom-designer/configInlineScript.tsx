import React from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { drawerApi } from '@metroDesign/drawer';
import { Popover } from '@metroDesign/popover';
import { Table } from '@metroDesign/table';
import { getWfGlobalVariables } from '@mdtProComm/utils/wfTmplUtil';
import i18n from '../../../languages';
import { MonacoEditor } from '../../../pages/form-editor/formily-externals/monaco-editor/MonacoEditor';

const ScriptEditor = ({ value, onChange }: { value: string; onChange: (value: string) => void }) => {
  const editorRef = React.useRef<any>();
  const sysVars = React.useMemo(() => getWfGlobalVariables(), []);
  const [popoverOpen, setPopoverOpen] = React.useState(false);

  // 插入变量到编辑器光标处
  const insertVariable = (variableId: string) => {
    if (!editorRef.current) return;
    const selection = editorRef.current.getSelection();
    editorRef.current.executeEdits('', [
      {
        range: selection,
        text: variableId,
        forceMoveMarkers: true,
      },
    ]);
    editorRef.current.focus();
    setPopoverOpen(false);
  };

  // 系统变量表格列定义
  const columns: any[] = [
    { title: i18n.chain.proMicroModules.workflow.edit.varName, dataIndex: 'name', key: 'name', width: 120 },
    { title: i18n.chain.proMicroModules.workflow.edit.varID, dataIndex: 'id', key: 'id', width: 140 },
    {
      title: i18n.chain.proMicroModules.workflow.edit.varType,
      dataIndex: 'typeDisplay',
      key: 'typeDisplay',
      width: 80,
    },
    { title: i18n.chain.proMicroModules.workflow.edit.varDesc, dataIndex: 'desc', key: 'desc', width: 180 },
  ];

  // 系统变量下拉内容
  const variableMenu = (
    <Table
      columns={columns}
      dataSource={Array.isArray(sysVars) ? sysVars : []}
      size="small"
      pagination={false}
      rowKey="id"
      style={{ minWidth: 520, maxHeight: 320, overflow: 'auto', cursor: 'pointer' }}
      onRow={(record: any) => ({
        onClick: () => insertVariable(record.id),
        style: { cursor: 'pointer' },
      })}
    />
  );

  return (
    <div className="config-inline-script" style={{ position: 'relative' }}>
      <div style={{ marginBottom: 8, display: 'flex' }}>
        <Popover
          content={variableMenu}
          trigger="click"
          placement="bottomLeft"
          rootClassName="config-inline-script-variable-menu"
          open={popoverOpen}
          onOpenChange={setPopoverOpen}
        >
          <Button size="small" type="primary" ghost>
            {i18n.chain.proMicroModules.workflow.edit.insertSysVar}
          </Button>
        </Popover>
      </div>
      <MonacoEditor
        language="python"
        value={value || ''}
        height="100%"
        options={{
          minimap: { enabled: false },
          lineNumbers: 'on',
          scrollBeyondLastLine: false,
          automaticLayout: true,
        }}
        onMount={(editor) => {
          editorRef.current = editor;
        }}
        finishedEdit={(editor) => {
          const newValue = editor.getValue();
          onChange(newValue);
        }}
      />
    </div>
  );
};

export const openInlineScriptConfig = async (value: string, callback: (value: string) => void) => {
  let currentValue = value;

  drawerApi.open({
    title: i18n.chain.proMicroModules.bpmn.inlineScript,
    width: 800,
    okText: i18n.chain.comButton.confirm,
    cancelText: i18n.chain.comButton.cancel,
    closable: true,
    destroyOnClose: true,
    onConfirm: (onClose?: () => void) => {
      callback(currentValue);
      onClose?.();
    },
    children: () => (
      <ScriptEditor
        value={value}
        onChange={(newValue) => {
          currentValue = newValue;
        }}
      />
    ),
  });
};
