import { drawerApi } from '@metroDesign/drawer';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { FormView } from '../../../components/form-view';
import i18n from '../../../languages';

export const openServiceConfig = async (value: string, callback: any) => {
  const schema = {
    form: { colon: false, layout: 'vertical' },
    schema: {
      properties: {
        internal_request: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: i18n.chain.comPlaceholder.input,
                showSearch: true,
                allowClear: true,
                optionFilterProp: 'label',
              },
              required: true,
              enum: [
                { label: i18n.chain.proMicroModules.workflow.edit.specOwner, value: 'spec_owner' },
                { label: i18n.chain.proMicroModules.workflow.edit.initiator, value: 'initiator' },
                { label: i18n.chain.proMicroModules.workflow.edit.taskExecutor, value: 'task_executor' },
              ],
              default: 'spec_owner',
              title: i18n.chain.proMicroModules.workflow.edit.specOwner,
              name: 'type',
              'x-decorator-props': { layout: 'vertical', colon: false },
              'x-designable-id': 'lftq86as6cf',
              'x-index': 0,
            },
            task_id: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              title: i18n.chain.proMicroModules.workflow.edit.taskExecutor,
              'x-component-props': { placeholder: i18n.chain.comPlaceholder.input },
              name: 'task_id',
              'x-validator': "{{(value)=> { if (!value) return '';\n     }}} ",
              'x-decorator-props': { layout: 'vertical', colon: false },
              'x-designable-id': 'ic8thsivd5t',
              'x-index': 1,
              'x-reactions': {
                dependencies: [{ property: 'value', type: 'string', source: 'type', name: 'type' }],
                fulfill: { state: { visible: '{{$deps.type === "task_executor"}}' } },
              },
            },
            allow_missing: {
              type: 'boolean',
              'x-decorator': 'FormItem',
              'x-component': 'Switch',
              default: true,
              title: i18n.chain.proMicroModules.workflow.edit.allowMissing,
              name: 'allow_missing',
              'x-component-props': {},
              'x-decorator-props': {
                tooltip: i18n.chain.proMicroModules.workflow.edit.allowMissingTooltip,
                layout: 'vertical',
                colon: false,
              },
              'x-designable-id': '59ge4em6udi',
              'x-index': 2,
              'x-formily-field-props': {},
            },
          },
        },
      },
    },
  };

  drawerApi.open({
    title: i18n.chain.proMicroModules.workflow.edit.configServiceConfig,
    width: 600,
    okButtonProps: { style: { display: 'none' } },
    cancelButtonProps: { style: { display: 'none' } },
    closable: true,
    destroyOnClose: true,
    children: (onClose) => (
      <FormView
        formilySchema={schema}
        formData={parseStrToObj(value)}
        onSubmit={async (values: any) => {
          onClose();
          callback(JSON.stringify(values));
          return { success: true };
        }}
      />
    ),
  });
};
