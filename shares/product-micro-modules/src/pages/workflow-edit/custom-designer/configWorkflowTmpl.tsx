import _ from 'lodash';
import { drawerApi } from '@metroDesign/drawer';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { getTmplGlobalValues, transformBackend2Html, transformHtml2Backend } from '@mdtProComm/utils/wfTmplUtil';
import { FormView } from '../../../components/form-view';
import i18n from '../../../languages';
import { getUserTaskNodeOptions } from '../../../utils/bpmn-xml-util';
import WorkflowEditController from '../WorkflowEditController';

const getTmplOptions = (rootFormOptions: ILabelValue[]) => {
  const fieldNames = _.map(rootFormOptions, 'label');
  return _.concat(getTmplGlobalValues(), {
    label: i18n.chain.proMicroModules.workflow.edit.startFormField,
    children: fieldNames,
  } as any);
};

export const openWorkflowTmplConfig = async (
  title: string,
  value: any,
  callback: any,
  controller: WorkflowEditController,
) => {
  const { xml } = await controller.getEditXml();
  // 此处的 ignoreFrontendKey 为true，是为处理options中表单字段为空的情况
  const rootFormOptions = _.get(getUserTaskNodeOptions(xml, true, false, false, true), '[0].fieldOptions');

  const properties = {
    value: {
      type: 'string',
      title,
      'x-decorator': 'FormItem',
      'x-component': 'TagInputFormily',
      'x-component-props': {
        options: getTmplOptions(rootFormOptions),
      },
    },
  };

  const schema = {
    form: { colon: false, layout: 'vertical' },
    schema: { properties },
  };
  const formData = { value: transformBackend2Html(value, rootFormOptions) };

  drawerApi.open({
    title: i18n.chain.proMicroModules.workflow.edit.configWfTemplate,
    width: 600,
    okButtonProps: { style: { display: 'none' } },
    cancelButtonProps: { style: { display: 'none' } },
    closable: true,
    destroyOnClose: true,
    children: (onClose) => (
      <FormView
        formilySchema={schema}
        formData={formData}
        onSubmit={async (values: any) => {
          onClose();
          callback(transformHtml2Backend(values.value, rootFormOptions));
          return { success: true };
        }}
      />
    ),
  });
};
