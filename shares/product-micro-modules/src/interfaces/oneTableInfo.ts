/**
 * 格式化一表通配置信息，添加类型定义
 */
export interface IOneTableInfo {
  // 流程配置
  /** 流程类型，用于选择默认配置 */
  flowType?: string;
  /** 是否启用流程类型调试模式 */
  flowTypeDebug?: boolean;
  /** 是否启用分布式图形模式树 */
  distributeGraphModeTree?: boolean;
  /** 新的根工作流规范ID */
  newRootWfspecId?: string;
  /** 新的分配工作流规范ID */
  newAssignWfspecId?: string;
  /** 启动规范ID */
  startSpecId?: string;
  /** 流程规范ID */
  flowSpecId?: string;
  /** 周期性最小间隔（分钟） */
  periodicMinInterval?: number;
  /** 所有权包ID */
  ownershipPkgId?: string;

  // 报表筛选条件工具配置
  /** 是否启用名称筛选 */
  formEnableNameSearch?: boolean;
  /** 是否启用高级筛选 */
  formEnableAdvancedFilter?: boolean;
  /** 是否启用日期筛选 */
  formEnableDateSearch?: boolean;

  // 报表配置
  /** 报表相关配置 */
  reportConfig: {
    formSpec: Record<string, any>;
    periodFormSpec: Record<string, any>;
    flowSpec: Record<string, any>;
    flowEndlessSpec: Record<string, any>;
    flowPeriodicSpec: Record<string, any>;
    selectedReinforceUsersSpec: Record<string, any>;
    selectedTransferUserSpec: Record<string, any>;
    exportToMyDataSpec: Record<string, any>;
    advancedFilterSpec: Record<string, any>;
    enableAdvanceFilter?: boolean;
  };

  // 用户选择配置
  /** 用户选择角色列表 */
  userSelectRoles?: Array<{ roleId: string; roleName: string }>;

  // 报表标签配置
  /** 报表标签列表 */
  reportTags?: Array<any>;
  /** 报表任务类型 */
  reportTaskType?: Array<any>;
  /** 报表区域 */
  reportRegion?: Array<any>;
  /** 报表级别 */
  reportLevel?: Array<any>;
  /** 报表频率 */
  reportFrequency?: Array<any>;
  /** 报表多级别 */
  reportLevelMuti?: Array<any>;
  /** 报表范围 */
  reportRange?: Array<any>;

  // 任务配置
  /** 报表规范到启动规范字段映射 */
  reportSpecToStartSpecFieldMap?: Record<string, any>;
  /** 接受任务名称 */
  acceptTaskName?: string;
  /** 填写任务名称 */
  fillingTaskName?: string;
  /** 提交任务名称 */
  submitTaskName?: string;
  /** 首次处理变量 */
  firstHandlingVar?: string;
  /** 受让人变量 */
  assigneeVar?: string;
  /** 结算方式键 */
  settleWayKey?: string;
  /** 启用完成工作流程键 */
  enableCompleteWorkflowKey?: string;
  /** 接受任务方法映射 */
  acceptTaskMethodMap?: Record<string, any>;
  /** 审批任务提交键 */
  approvalTaskSubmitKey?: string;
  /** 审批任务部门键 */
  approvalTaskDepartKey?: string;

  // 列配置
  /** 忽略下载列 */
  ignoreDownloadColumns?: Array<string>;
  /** 新忽略下载列 */
  newIgnoreDownloadColumns?: Array<string>;
  /** 新忽略显示列 */
  newIgnoreDisplayColumns?: Array<string>;

  // 高级过滤器配置
  /** 包含高级过滤器键 */
  includeAdvancedFilterKey?: Array<string>;
  /** 排除高级过滤器键 */
  excludeAdvancedFilterKey?: Array<string>;
  /** 包含覆盖高级过滤器键 */
  includeOverrideAdvancedFilterKey?: boolean;
  /** 包含快速选择键 */
  includeFastSelectKeys?: Array<string>;
  /** 排除快速选择键 */
  excludeFastSelectKeys?: Array<string>;

  // 表单变量配置
  /** 表单变量配置 */
  formVarsConfig?: Record<string, any>;

  // 定时任务选项
  /** 快速crontab选项 */
  fastCronTabOptions?: Array<any>;
  /** 快速crontab额外选项 */
  fastCronTabExtraOptions?: Array<any>;

  // H5配置
  /** 是否有H5详情下载 */
  hasH5DetailDownload?: boolean;
  /** 是否有H5详情过滤器 */
  hasH5DetailFilter?: boolean;
  /** 是否启用H5扁平化 */
  hasH5EnableFlat?: boolean;
  /** 是否有H5模糊搜索 */
  hasH5BlurSearch?: boolean;
  /** 是否隐藏H5数据统计 */
  hideH5DataStatistic?: boolean;
  /** 是否有H5数据状态固定在右侧 */
  hasH5DataStatusFixedRight?: boolean;
  /** 是否有H5数据ID固定在右侧 */
  hasH5DataIdFixedRight?: boolean;
  /** 是否有H5数据日期固定在左侧 */
  hasH5DataDateFixedLeft?: boolean;
}
