declare module '*.less';
declare module '*.svg';
declare module '*.bmp';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.png';
declare module '*.mp4';
declare module '@syncpoint/wkx';
declare module 'tinycolor2';
declare module 'query-extensions';
declare module 'inherits';
declare module 'bpmn-js*';
declare module 'js-base64';
declare module '*.webp';

// eslint-disable-next-line @typescript-eslint/naming-convention
declare interface Window {
  microApp?: any;
  rawWindow?: any;
  __MICRO_APP_NAME__?: string;
  __MICRO_APP_ENVIRONMENT__?: boolean;
  __MICRO_APP_PUBLIC_PATH__?: string;
  __MICRO_APP_BASE_ROUTE__?: string;
  __dm_memory_leak_list?: Function[];
  wx?: any;
}
