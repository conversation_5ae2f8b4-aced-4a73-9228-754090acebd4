import _ from 'lodash';
import { getRunQlang } from '@sql-generator/sql-editor/dist/utils/pgsql.js';
import { DATAPKGS_URL, FILES_URL, QLANGS } from '@mdtApis/config';
import { getFileName } from '@mdtBsComm/utils/fileUtil';
import { AccessLevelEnum, TaskStatusEnum } from '@mdtProComm/constants';
import { executeJsCodeWithContext, stripJsComments } from '@mdtProComm/utils/codeUtil';
import { transformDatapkgRowsToTable } from '@mdtProComm/utils/pkgdataUtil';
import { CreateUploadFileTask } from '@mdtProTasks/CreateUploadFileTask';
import { getValidUploadFileType } from '../components/upload/utils';
import { DatlasAppController } from '../datlas/app/DatlasAppController';
import { PkgActionEnum, RequestTypeEnum } from './constants';

const responseAdaptor = (response: any) => {
  const rslt = response?.data || {};
  if (rslt.rc === 0) {
    // 参数归一
    const value = { ...(rslt.metadata || { page_num: 0, total_count: 0, page_size: 10 }), result: rslt.result };
    // 格式化数据
    const data = {
      total: value.total_count,
      // 页码从1开始
      page: value.page_num + 1,
      perPage: value.page_size,
      items: value.result,
    };
    // eslint-disable-next-line no-param-reassign
    response = { ...response, data };
  }
  return response;
};

const transformAmisPaginationToMdt = (api: any) => {
  const { context = {}, config = {} } = api;
  const { page = 1, perPage = 10 } = context;
  // 如果前端分页，page_size设置为10w
  return config.loadDataOnce ? { page_size: 100000, page_num: 0 } : { page_size: perPage, page_num: page - 1 };
};

// 执行自定义代码
const executeCustomCode = async (api: any, axios: any, proxy: any) => {
  return executeJsCodeWithContext(proxy.code, { axios, api, _ });
};

// 执行qlang
const executeQLang = async (api: any, axios: any, proxy: any) => {
  const params = transformAmisPaginationToMdt(api);
  // 拼接qlang执行参数
  let config = {
    method: 'post',
    url: `${QLANGS}/temp/execution`,
    data: { result_type: 'record', qlang: getRunQlang(proxy.editor.sql) },
    params: params,
  };
  const cleanedCode = stripJsComments(proxy.request_adapt);
  if (cleanedCode) {
    config = await executeJsCodeWithContext(cleanedCode, { config, api, _ });
  }
  return axios.request(config).then(responseAdaptor);
};

// 获取查询配置
const omtKeys = ['page', 'perPage', 'items', 'total', 'pageDir'];
const getQueryConfig = (api: any, proxy: any, preUrl: string) => {
  const queryParams = _.get(proxy, 'request_params.query', {});
  const context = Object.hasOwn(api.context, '__step') ? {} : api.context;
  const resetKeys = _.difference(Object.keys(context), omtKeys);
  const params = transformAmisPaginationToMdt(api);
  const conditions: any[] = [];
  _.forEach(resetKeys, (key) => {
    if (!_.isEmpty(context[key]) && key !== 'id') {
      // TODO 需要想办法处理不同类型的查询条件
      const params = _.isArray(context[key]) ? context[key] : context[key].split(',');
      const isMutiple = params.length > 1;
      const operator = isMutiple ? 'in' : 'ilike';
      const param = isMutiple ? params : `%${params[0]}%`;
      conditions.push({ column: key, operator, param });
    }
  });
  if (conditions.length) {
    return {
      params: { ...params, fetch_total_count: true },
      url: `${preUrl}/query`,
      method: 'post',
      data: { operator_filter: { [`$${queryParams.conditions ?? 'and'}`]: conditions } },
    };
  }
  return { params: { ...params, fetch_total_count: true }, url: `${preUrl}/rows`, method: 'get' };
};
// 获取新增配置
const getAddConfig = (api: any, proxy: any, preUrl: string) => {
  let data = api.data || api.body;
  const columns = _.keys(data);
  const values = _.map(columns, (column) => {
    return data[column] ?? null;
  });
  data = { columns, values: [values] };
  return { url: `${preUrl}/rows`, method: 'post', data };
};
// 获取更新配置
const getUpdateConfig = (api: any, proxy: any, preUrl: string) => {
  const { uniqueKey } = _.get(proxy, 'request_params.update', {});
  const ownContextUniqueValue = uniqueKey && Object.hasOwn(api.context, uniqueKey) ? api.context[uniqueKey] : undefined;
  const ids = _.split(ownContextUniqueValue || api.context.ids || api.context.id, ',');
  return {
    url: `${preUrl}/rows`,
    method: 'patch',
    data: { values: api.context.diff, condition: { column: uniqueKey || 'id', operator: 'in', param: ids } },
  };
};
// 获取删除配置
const getDeleteConfig = (api: any, proxy: any, preUrl: string) => {
  const { uniqueKey } = _.get(proxy, 'request_params.delete', {});
  const ownContextUniqueValue = uniqueKey && Object.hasOwn(api.context, uniqueKey) ? api.context[uniqueKey] : undefined;
  const ids = _.split(ownContextUniqueValue || api.context.ids || api.context.id, ',');
  return { url: `${preUrl}/rows`, method: 'delete', data: { row_ids: ids } };
};
const transformMdtResultToAmis = (responen: any) => {
  const items = _.get(responen, 'data.items', {});
  if (items.columns) {
    responen.data.items = transformDatapkgRowsToTable(items);
  }
  return responen;
};
const actionMethodMap: Record<string, Function> = {
  [PkgActionEnum.QUERY]: getQueryConfig,
  [PkgActionEnum.ADD]: getAddConfig,
  [PkgActionEnum.UPDATE]: getUpdateConfig,
  [PkgActionEnum.DELETE]: getDeleteConfig,
};
// 执行数据包api
const executeRequest = async (api: any, axios: any, proxy: any) => {
  const preUrl = `${DATAPKGS_URL}/${proxy.pkgId}`;
  let config = actionMethodMap[proxy.action](api, proxy, preUrl);
  const cleanedCode = stripJsComments(proxy.request_adapt);
  if (cleanedCode) {
    config = await executeJsCodeWithContext(cleanedCode, { config, api, _ });
  }
  const resp = axios.request(config).then(responseAdaptor);
  return proxy.action === PkgActionEnum.QUERY ? resp.then(transformMdtResultToAmis) : resp;
};

// 是否是mdtApi
export const isMdtApi = (url: string) => {
  return url.includes('mdt_api_proxy_');
};

// mdtApi代理请求
export const mdtApiProxyFetcher = async (api: any, axiosIns?: any) => {
  const axios = axiosIns || DatlasAppController.getInstance().getAxiosInstance();
  const { mdtApiProxy } = api;
  switch (mdtApiProxy.request_type) {
    case RequestTypeEnum.CUSTOM:
      return executeCustomCode(api, axios, mdtApiProxy);
    case RequestTypeEnum.QLANG:
    case RequestTypeEnum.LOW_CODE_QLANG:
      return executeQLang(api, axios, mdtApiProxy);
    case RequestTypeEnum.DATAPKG:
      return executeRequest(api, axios, mdtApiProxy);
    default:
      return new Error('unknown request type');
  }
};

// 上传文件
export const mdtUploadFile = async (file: File, cancelTokenSource: any, onUploadProgress: any) => {
  return new Promise<{ status: string; ok: boolean; data?: any }>((resolve) => {
    const fileType = getValidUploadFileType(_.split(file.type, '/')[0] as 'image');
    const uploadTask = new CreateUploadFileTask(
      file,
      fileType,
      { quietProcess: true, quietNotify: true, accessLevel: AccessLevelEnum.ANYONE },
      { needRealFileName: true, cancelTokenSource, onUploadProgress },
    );
    const uploadFinished = (value?: any) => {
      uploadTask.destroy();
      const rslt = value
        ? { status: '0', ok: true, data: value }
        : { status: '1', ok: false, msg: 'file upload failed' };
      resolve(rslt);
    };
    uploadTask.taskResp$.subscribe((resp) => {
      const status = resp.status;
      if (status === TaskStatusEnum.SUCCESSFUL) {
        uploadFinished({ value: { id: resp.result, type: fileType, value: resp.result, name: getFileName(file) } });
      } else if (status === TaskStatusEnum.FAILED) {
        uploadFinished();
      }
    });
  });
};

// mdt getFileUrl
export const FILE_ID_FLAG = 'mdt_file_id_';
export const mdtFileUrlProxyFetch = async (url: string, axiosIns?: any) => {
  if (!_.startsWith(url, FILE_ID_FLAG)) return url;
  const fileId = url.slice(FILE_ID_FLAG.length);
  if (!fileId) return '';
  const axios = axiosIns || DatlasAppController.getInstance().getAxiosInstance();
  const resp = await axios.request({
    method: 'get',
    url: `${FILES_URL}/${fileId}/content`,
    params: { redirect: false },
  });
  return _.get(resp, 'data.result.sign_url.url', '');
};
