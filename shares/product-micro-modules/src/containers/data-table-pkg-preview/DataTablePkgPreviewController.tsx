import _ from 'lodash';
import { CSSProperties } from 'react';
import { Tag } from '@metroDesign/tag';
import { AsyncSubject, BehaviorSubject, forkJoin, from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { DATE_FORMATTER_2, formatDayjsValue } from '@mdtBsComm/utils/dayUtil';
import type { IClickItemFunc, IControllerPaginationOptions } from '@mdtBsComponents/data-list-comp';
import { DataListCompDataTableController, IDataTableProps } from '@mdtBsComponents/data-list-comp-data-table';
import type { IGetBackendFilterParams, IPaginationParams } from '@mdtBsControllers/data-list-controller';
import { TooltipText } from '@mdtDesign/tooltip';
import SecretPreview from '@mdtProComm/components/secret-preview';
import type { IDatapkgRows } from '@mdtProComm/interfaces';
import {
  isArrayColumn,
  isDatetimeColumn,
  isIdNameColumn,
  isMediaFileColumn,
  transformDbTypeToFrontType,
} from '@mdtProComm/utils/columnUtil';
import { FilesPreviewBtn } from '../../components/files-preview';
import { TransformIdToName } from '../../components/transform-id-to-name';
import type { IColumnItem } from '../table-pkg-column';
import './index.less';

export const FIT_CONTENT = 'fit-content';

export type ILoadPkgPreviewRslt = Observable<[number, IDatapkgRows]>;
export type ILoadPkgPreviewFunc = (params?: any) => ILoadPkgPreviewRslt;
export type ILoadNextPagePkgPreviewFunc = (params: IPaginationParams) => Observable<IDatapkgRows>;
export interface IControllerOptions extends IControllerPaginationOptions {
  tableOptions: () => AsyncSubject<IDataTableProps>;
  loadPkgPreviewFunc?: ILoadPkgPreviewFunc;
  loadNextPagePkgPreviewFunc?: ILoadNextPagePkgPreviewFunc;
  getBackendFilterParams?: IGetBackendFilterParams;
  // 父节点的高度不能确定，子节点无法动态计算高度，需要指定高度
  // fit-content表示根据子节点的高度和计算
  // 如果父类的高度能确定，则自适应父类高度，无需指定
  gridHeight?: number | typeof FIT_CONTENT;
  enableDownload?: boolean;
  enableQueryName?: boolean;
  onClickRow?: IClickItemFunc;
  enableFlat?: boolean;
  secretVisible?: boolean;
}

// 处理表格数据
const dealDatapkgRows = (datapkgRows: IDatapkgRows) => {
  const { columns, values } = datapkgRows;
  return _.map(values, (row) => {
    return _.reduce(
      columns,
      (val, it, index) => {
        const v = row[index];
        val[it] = v;
        return val;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {} as Record<string, any>,
    );
  });
};

/**
 * 出于用户会动态调整列顺序的目的，所以没有对列处理，由用户自己传入，
 * 这边只对列的值做转换处理
 */
export class DataTablePkgPreviewController extends DataListCompDataTableController<any> {
  private loadPkgPreviewFunc: ILoadPkgPreviewFunc;
  private loadNextPagePkgPreviewFunc: ILoadNextPagePkgPreviewFunc;
  // 此处修改为动态获取，主要为了应对动态切换数据包的场景。
  private tableOptions?: () => AsyncSubject<IDataTableProps>;
  private tableOptionsValue?: IDataTableProps;
  private gridHeight?: number | typeof FIT_CONTENT;
  private enableDownload?: boolean;
  private enableQueryName?: boolean;
  private showAllSecret$ = new BehaviorSubject<boolean>(false);

  public constructor(options: IControllerOptions) {
    super({
      dataListControllerOptions: {
        loadDataListFunc: (params: any) => this.loadPkgPreview(params),
        loadNextPageDataListFunc: (params: IPaginationParams) => this.dealNextPageDataList(params),
        getBackendFilterParams: options.getBackendFilterParams,
      },
      compOptions: () => this.initTableProps(),
      onClickItem: options.onClickRow,
      pagination: options.pagination,
      paginationAtBefore: options.paginationAtBefore,
      paginationProps: options.paginationProps,
      initialPageNum: options.initialPageNum,
      enableFlat: options.enableFlat,
    });
    this.tableOptions = options.tableOptions;
    this.gridHeight = options.gridHeight;
    this.enableDownload = options.enableDownload;
    this.enableQueryName = options.enableQueryName;
    this.showAllSecret$.next(options.secretVisible ?? false);
    this.loadPkgPreviewFunc = options.loadPkgPreviewFunc || this.defaultLoadPkgPreviewFunc;
    this.loadNextPagePkgPreviewFunc = options.loadNextPagePkgPreviewFunc || this.defaultLoadNextPagePkgPreviewFunc;
  }

  public destroy() {
    super.destroy();
    this.loadPkgPreviewFunc = this.defaultLoadPkgPreviewFunc;
    this.loadNextPagePkgPreviewFunc = this.defaultLoadNextPagePkgPreviewFunc;
    this.tableOptions = undefined;
    this.tableOptionsValue = undefined;
    this.enableDownload = undefined;
    this.showAllSecret$.complete();
  }

  public setShowAllSecret(show: boolean) {
    this.showAllSecret$.next(show);
  }

  public getShowAllSecret$() {
    return this.showAllSecret$;
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  public defaultTableProps(v: IColumnItem[]) {
    const columns = _.map(v, ({ type, name, sorter, sortType, ...resetProps }) => {
      return {
        name,
        key: name,
        type: transformDbTypeToFrontType(type),
        formatter: (data: any) => {
          let text = data.row[data.column.name] ?? '';
          if (isArrayColumn(type) && _.isArray(text)) {
            const ts = _.map(text, (it, index) => (
              <Tag key={index} color="processing">
                {it}
              </Tag>
            ));
            return <TooltipText text={ts as any} placement="right" />;
          }
          if (isMediaFileColumn(type, text)) {
            return <FilesPreviewBtn listStr={text} enableDownload={this.enableDownload} />;
          }
          if (isIdNameColumn(type) && this.enableQueryName) {
            return text ? <TransformIdToName id={text} type={type} /> : null;
          }
          // 密文formatter， 这里应该再判断一下类型
          if (resetProps?.view_format?.format === 'secret') {
            return <SecretPreview value={text} showToggle={false} visible={this.getShowAllSecret$().getValue()} />;
          }
          // 时间formatter
          if (resetProps?.view_format?.format || isDatetimeColumn(type)) {
            return text ? (
              <TooltipText
                text={formatDayjsValue(text, resetProps?.view_format?.format || DATE_FORMATTER_2)}
                placement="right"
              />
            ) : null;
          }
          // 做个最后的保底, 如果有些列的值是对象, 经过前面的值没有正确识别
          _.isObject(text) && (text = JSON.stringify(text));
          _.isBoolean(text) && (text = _.toUpper(text as any));
          return <TooltipText text={text} placement="right" />;
        },
        sortType,
        sorter,
        headerCellClass: 'data-search-sorter-cell',
        resizable: true,
        ...resetProps,
      };
    });
    const opts = {
      columns: columns,
      // 170来源于format一个时间到时分秒可以展示的宽度
      defaultColumnOptions: { minWidth: 170 },
    };
    if (!this.tableOptions) return;
    const subject = this.tableOptions();
    subject.next(opts as IDataTableProps);
    subject.complete();
  }

  private initTableProps = (): IDataTableProps => {
    return this.tableOptionsValue!;
  };

  // 对数据进行转换
  private dealNextPageDataList(params: IPaginationParams) {
    return from(this.loadNextPagePkgPreviewFunc(params)).pipe(
      map((datapkgRows) => {
        return dealDatapkgRows(datapkgRows);
      }),
    );
  }

  // 请求首页数据
  private loadPkgPreview(params: any) {
    const ph = this.gridHeight || 0;
    return forkJoin(this.loadPkgPreviewFunc(params), this.tableOptions!()).pipe(
      map(([[total, list], v2]) => {
        // 转换成行数据
        const data = dealDatapkgRows(list);
        // 计算列信息
        const tps = { ...(v2 || {}) };
        const style: CSSProperties = v2.style || {};
        // 如果未指定高度
        if (!('height' in style) && ph) {
          // 则动态计算父类高度
          if (ph === FIT_CONTENT) {
            const rl = data.length;
            const hrh = tps.headerRowHeight || 45;
            const rh = tps.rowHeight || 36;
            // 如果没有值，则空状态的高度为45
            const th = hrh + (rl ? rl * rh : 50);
            tps.headerRowHeight = hrh;
            tps.rowHeight = rh;
            // 防止出现滚动条，预留10像素
            style.height = th + 10;
          } else {
            style.height = ph;
          }
          tps.style = style;
        }
        this.tableOptionsValue = tps;
        return [total, data] as [number, any[]];
      }),
    );
  }

  private defaultLoadPkgPreviewFunc() {
    return of([0, { columns: [], values: [] }] as [number, IDatapkgRows]);
  }

  private defaultLoadNextPagePkgPreviewFunc() {
    return of({ columns: [], values: [] });
  }
}
