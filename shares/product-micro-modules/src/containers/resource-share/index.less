.module_resource-share-modal {
  .dmc-dialog-content {
    background-color: var(--metro-bg-2);
  }

  .dmc-dialog-header {
    border-bottom: none;

    .dmc-dialog-title {
      color: var(--metro-text-0);
      font-weight: 500;
      font-size: 18px;
    }
  }

  .dmc-dialog-body {
    padding: 0 16px 20px;
  }

  .dmc-dialog-footer {
    display: none;
  }

  .module_resource-share-inner {
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-title {
        color: var(--metro-text-2);
        font-weight: 400;
        font-size: 14px;
      }
    }

    .module_resource-share-curd_list-wrap {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 420px;
      margin-top: 8px;
      overflow: hidden;
      border: 1px solid var(--metro-border-0);
      border-radius: 8px;

      &-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: #fff;
        border-top: 1px solid var(--metro-border-0);

        &-title {
          color: var(--metro-text-2, rgb(36 39 47 / 55%));
          font-weight: 400;
          font-size: 13px;
          line-height: 145%;
          text-align: justify;
        }

        &-btns {
          display: flex;
          gap: 12px;
        }
      }
    }

    .module_card-curd-with-simple-search {
      flex: 1;
      height: auto;
      overflow: hidden;

      .card-curd_header {
        padding: 6px 12px;
        border-bottom: 1px solid var(--metro-border-0);
      }

      > .dmc-checkbox {
        position: absolute;
        top: 10px;
        left: 12px;
        width: 60px;
      }

      .option-card {
        margin-top: 8px;
        padding: 0 12px;
        color: var(--metro-text-1);
      }
    }

    .module_resource-share-header-title {
      display: flex;
      align-items: center;
      padding-left: 30px;
      // justify-content: center;
      color: var(--metro-text-1, rgb(36 39 47 / 85%));
      font-weight: 400;
      font-size: 14px;
      font-style: normal;
      text-align: justify;
    }
  }
}

.module_user-select-permission {
  margin-top: 16px;

  .module_user-select-permission-title {
    margin-bottom: 8px;
    color: var(--metro-text-1, rgb(36 39 47 / 85%));
    font-weight: 500;
    font-size: 14px;
  }
}
