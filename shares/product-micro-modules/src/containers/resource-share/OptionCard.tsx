import { FC } from 'react';
import { Flex } from '@metroDesign/flex';
import Checkbox from '@mdtDesign/checkbox';
import Icon from '@mdtDesign/icon';
import Select from '@mdtDesign/select';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { TransformIdToName } from '../../components/transform-id-to-name';
import { ISelectorItem, SelectorTypeIconMap } from './constant';
import { ResourceShareController } from './ResourceShareController';

interface IOptionCardProps {
  item: ISelectorItem;
  selected: boolean;
  controller: ResourceShareController;
}

export const OptionCard: FC<IOptionCardProps> = ({ item, selected, controller }: IOptionCardProps) => {
  const isDisabled = controller.getOptionsController()!.checkItemDisabled(item);

  const handleChange = (checked: boolean) => {
    if (isDisabled) return;
    controller.handleClickOption(item, checked);
  };

  const handleChangePermission = (value: any) => {
    if (isDisabled) return;
    controller.handleChangePermission(item, value);
  };

  return (
    <Flex className="option-card" justify="space-between" align="center">
      <Flex align="center" gap={4}>
        <Checkbox checked={selected} disabled={isDisabled} onChange={handleChange} />
        <Flex align="center" gap={4}>
          <Icon icon={SelectorTypeIconMap[item.type]} size={18} />
          <TransformIdToName id={item.id} type={DbColumnTypeEnum.USER_ID} />
        </Flex>
      </Flex>

      <Select
        showArrow
        maxTagCount={2}
        value={item.permissions}
        mode="multiple"
        options={controller.getPermissionOptions()}
        onChange={handleChangePermission}
      />
    </Flex>
  );
};
