import _ from 'lodash';
import { Result } from '@metroDesign/result';
import { ModalWithBtnsCompDialogController } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ResourceReceiverEnum, ResourceSelectTypeEnum } from '@mdtProComm/constants';
import { AbstractAppController } from '../../controllers/AbstractAppController';
import i18n from '../../languages';
import { CardCurdWithSimpleSearchController } from '../card-curd-with-simple-search';
import { UserLazySelectorModel } from '../user-lazy-selector';
import { ISelectorItem } from './constant';
import { OptionCard } from './OptionCard';
import { ResourceShareInner } from './ResourceShareInner';
import { IResourceShareModel } from './ResourceShareModel';
import SelectAll from './SelectAll';
import { UserPermissionSelectController } from './UserPermissionSelectController';
import { UserPermissionSelectModalController } from './UserPermissionSelectModalController';
import { UserSelectorControllerWithPermission } from './UserSelectorControllerWithPermission';
import { getPermissionOptions } from './util';
export interface IResourceShareControllerOptions {
  app: AbstractAppController;
  Model: IResourceShareModel;
}

export interface IResourceShareData {
  resourceId: string;
  resourceType: ResourceSelectTypeEnum;
}

export class ResourceShareController extends ModalWithBtnsCompDialogController {
  private app: AbstractAppController;
  private Model: IResourceShareModel;
  private optionsController?: CardCurdWithSimpleSearchController<ISelectorItem, ResourceShareController>;
  private addUserController?: UserSelectorControllerWithPermission;
  private editPermissionModalController?: ModalWithBtnsCompDialogController;
  private editPermissionController?: UserPermissionSelectController;
  public constructor(options: IResourceShareControllerOptions) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initModalOptions(),
        beforeOpenFunc: async (data?: IResourceShareData) => {
          console.log('data...', data);
          if (this.optionsController) {
            this.optionsController.destroy();
          }
          this.optionsController = new CardCurdWithSimpleSearchController<ISelectorItem, ResourceShareController>({
            dataListCompCardCurdControllerOptions: {
              dataListCompControllerOptions: {
                dataListControllerOptions: {
                  loadDataListFunc: () =>
                    this.Model.getResourceShareData(this.app?.getUserId(), this.getPermissionOptions()),
                },
                compOptions: () => this.initListOptions(),
                EmptyView: () => (
                  <div className="module_resource-share-empty-view">
                    <Result status="noContent" subTitle={i18n.chain.proMicroModules.resourceShare.noContent()} />
                  </div>
                ),
              },
              cardItemViewController: () => this,
              curdOptions: {
                enableAllSelect: true,
              },
            },
            headerOptions: this.initHeaderOptions(),
          });
          this.optionsController.listenFrontFilter();
          this.optionsController.loadDataList();
        },
      },
      InnerView: ResourceShareInner,
    });
    this.Model = options.Model;
    this.app = options.app;
    const appId = this.app.getAppId();
    this.addUserController = new UserSelectorControllerWithPermission({
      app: this.app,
      Model: new UserLazySelectorModel({ appId }),
      permissionOptions: this.getPermissionOptions(),
    });
    this.editPermissionModalController = new UserPermissionSelectModalController({
      permissionOptions: this.getPermissionOptions(),
    });
  }

  public getOptionsController() {
    return this.optionsController;
  }

  public getAddUserController() {
    return this.addUserController;
  }

  public closeModal() {
    super.closeModal();
  }

  public destroy() {
    super.destroy();
    this.optionsController?.destroy();
    this.optionsController = null!;
    this.Model = null!;
  }

  public handleClickOption(item: ISelectorItem, checked: boolean) {
    checked
      ? this.optionsController?.addToSelectedDataList(item)
      : this.optionsController?.removeFromSelectedDataList(item);
  }

  public onClickAddUser = () => {
    const disabledUserIds = this.optionsController
      ?.getAllData()
      .filter((item) => item.type === ResourceReceiverEnum.USER)
      .map((item) => item.id);
    disabledUserIds?.push(this.app?.getUserId());
    const disabledRoleIds = this.optionsController
      ?.getAllData()
      .filter((item) => item.type !== ResourceReceiverEnum.USER)
      .map((item) => item.id);

    this.addUserController
      ?.openModal({
        disabledUserIds: disabledUserIds?.map((t) => String(t)),
        disabledRoleIds: disabledRoleIds?.map((t) => String(t)),
      })
      .subscribe((res) => {
        if (res.success) {
          const addData: ISelectorItem[] = [];
          res.result.values?.forEach((t: any) => {
            const item = {
              id: t.id,
              name: t.name,
              type: t.type,
              permissions: res.result.selectedPermissions,
            };
            addData.push(item);
            this.optionsController?.addDataToList(item);
          });
          this.postDataToServer(addData);
        }
      });
  };

  public getPermissionOptions() {
    return getPermissionOptions({
      resourceType: this.Model.getResourceType(),
      isOwner: this.Model.getIsOwner(),
      userPermissionController: this.app.getUserPermissionController()!,
    });
  }

  public handleChangePermission(item: ISelectorItem, value: any) {
    const removedItems = _.difference(item.permissions, value);
    const addItems = _.difference(value, item.permissions);
    this.optionsController?.editDataInList({
      ...item,
      permissions: value,
    });
    const addData = _.size(addItems)
      ? [
          {
            ...item,
            permissions: addItems,
          },
        ]
      : undefined;
    const removeData = _.size(removedItems)
      ? [
          {
            ...item,
            permissions: removedItems,
          },
        ]
      : undefined;
    this.postDataToServer(addData, removeData);
  }

  // 删除用户
  public onClickDeleteItems = () => {
    const data = this.optionsController?.getSelectedDataListValue();
    data?.forEach((item) => {
      this.optionsController?.deleteDataFromList(item);
    });
    this.postDataToServer(undefined, data);
  };

  // 修改权限, 弹出权限选择框
  public onClickEditItems = () => {
    const selectedValues = this.optionsController?.getSelectedDataListValue();
    const selectedPermissions = _.intersection(..._.map(selectedValues, 'permissions'));
    this.editPermissionModalController
      ?.openModal({
        selectedPermissions: selectedPermissions,
      })
      .subscribe((res) => {
        if (res.success) {
          const addItems: ISelectorItem[] = [];
          const removeItems: ISelectorItem[] = [];
          selectedValues?.forEach((item: any) => {
            const removedPermissions = _.difference(item.permissions, res.result.selectedPermissions);
            const addPermissions = _.difference(res.result.selectedPermissions, item.permissions);
            if (_.size(removedPermissions)) {
              removeItems.push({ ...item, permissions: removedPermissions });
            }
            if (_.size(addPermissions)) {
              addItems.push({ ...item, permissions: addPermissions });
            }
            this.optionsController?.editDataInList({ ...item, permissions: res.result.selectedPermissions });
          });
          this.postDataToServer(addItems, removeItems);
        }
      });
  };

  public getEditPermissionModalController() {
    return this.editPermissionModalController;
  }

  private postDataToServer(addData?: ISelectorItem[], removeData?: ISelectorItem[]) {
    this.Model.postResourceShareData(this.app.getAppId(), addData, removeData);
  }

  private initModalOptions() {
    return {
      rootClassName: 'module_resource-share-modal',
      title: i18n.chain.proMicroModules.resourceShare.title,
      width: 600,
      footer: null,
    } as any;
  }

  private initListOptions() {
    return {
      itemGap: 0,
      itemHeight: 75,
      itemWidth: '100%',
      useVirtual: false,
      itemKey: 'id',
      CardItemView: OptionCard,
    };
  }

  private initHeaderOptions() {
    return {
      title: <SelectAll />,
    };
  }
}
