import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { createDate, Dayjs, transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { ITicketResolution, ITicketResolutionPut } from '@mdtBsServices/interfaces';
import { LinkButton } from '@mdtDesign/button';
import toastApi from '@mdtDesign/toast';
import { ApplyTicketTypeEnum, DatapkgPermissionEnum } from '@mdtProComm/constants';
import { applyPermissionTypes, ApplyTypeEnum } from '@mdtProComm/models/ApprovalTicketModel';
import i18n from '../../languages';
import { EmotionExpireDateController } from '../emotion-expire-date';
import {
  ILoadDataListRslt,
  IPaginationParams,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '../table-curd-with-simple-search';
import { OperationBtns } from './TableApprovalManage';
import { IDownloadTableData, IQueryParams, ITableApprovalManageModel, ITableData } from './TableApprovalManageModel';

interface IFilterParams {
  params: IQueryParams;
}

export interface IControllerOptions {
  ticketType: BehaviorSubject<ApplyTicketTypeEnum>;
  applyType: BehaviorSubject<ApplyTypeEnum>;
  Model: ITableApprovalManageModel;
  clickDetailFunc: (id: string) => void;
}

class TableApprovalManageController {
  private Model: ITableApprovalManageModel;
  private tableController: TableCurdWithSimpleSearchController<ITableData>;
  private downloadApprovalController: EmotionExpireDateController<IDownloadTableData>;
  private ticketType$: BehaviorSubject<ApplyTicketTypeEnum>;
  private applyType$;
  private clickDetailFunc;

  public constructor(options: IControllerOptions) {
    this.ticketType$ = options.ticketType;
    this.applyType$ = options.applyType;
    this.clickDetailFunc = options.clickDetailFunc;
    this.Model = options.Model;
    this.tableController = new TableCurdWithSimpleSearchController<ITableData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryFirstPageData,
          loadNextPageDataListFunc: this.queryNextPageData,
          getBackendFilterParams: this.getBackendFilterParams,
        },
        tableOptions: this.initTableOptions,
        curdOptions: {},
      },
      headerOptions: this.initHeaderOptions,
    });
    this.downloadApprovalController = new EmotionExpireDateController({
      title: i18n.chain.proMicroModules.approval.expireTitle,
      description: i18n.chain.proMicroModules.approval.expireDesc,
      confirmFunc: this.downloadApproval,
    });
    this.init();
  }

  public destroy() {
    this.tableController.destroy();
    this.downloadApprovalController.destroy();
    this.applyType$ = null!;
    this.ticketType$ = null!;
    this.clickDetailFunc = null!;
    this.Model = null!;
  }

  public getDownloadConfirmController() {
    return this.downloadApprovalController;
  }

  public getTableController() {
    return this.tableController;
  }

  public resolutionApproval = async (resolution: ITicketResolution, data: ITableData): Promise<boolean> => {
    const needExpire =
      ApplyTicketTypeEnum.USER_RESOURCE_PERMISSION === this.ticketType$.getValue() &&
      (this.applyType$.getValue() as any) === DatapkgPermissionEnum.DOWNLOAD &&
      resolution === 'approved';
    if (needExpire) {
      this.downloadApprovalController.openModal({ ...data, expire: createDate().add(24, 'hour') });
      return true;
    } else {
      data.isLoading = true;
      return this.approvalSubmit({ resolution }, data);
    }
  };

  // 加载首页列表
  private queryFirstPageData = (filterParams: IFilterParams): ILoadDataListRslt<ITableData> => {
    return this.Model.queryFirstPageTickets(filterParams.params);
  };

  // 加载下一页列表
  private queryNextPageData = (params: IPaginationParams) => {
    const filterParams = this.getBackendFilterParams();
    return this.Model.queryNextPageTickets({ ...params, ...filterParams.params });
  };

  // 获取参数
  private getBackendFilterParams = (): IFilterParams => {
    const search = this.tableController.getSingleFilterValue();
    const applyType: any = this.applyType$.getValue();
    const isApplyPermission = _.includes(applyPermissionTypes, applyType);
    const params: IQueryParams = {
      q: search || undefined,
      status: 'open',
      permission: isApplyPermission ? applyType : undefined,
      ticket_type: this.ticketType$.getValue(),
    };
    return { params };
  };

  // 查看数据包
  private viewDatapkg = (r: ITableData) => {
    this.clickDetailFunc(r.pkgId);
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    const crossAppColumn = [];
    const expireColumn = [];
    if (this.ticketType$.getValue() === ApplyTicketTypeEnum.CROSS_APP_RESOURCE_PERMISSION) {
      crossAppColumn.push({ name: i18n.chain.proMicroModules.approval.applyApp, code: 'appName', width: 160 });
      expireColumn.push({ name: i18n.chain.proMicroModules.approval.expireDate, code: 'expireDate', width: 160 });
    }
    return {
      columns: [
        {
          name: i18n.chain.proMicroModules.approval.pkgName,
          code: 'name',
          render: (value: string, r: ITableData) => (
            <LinkButton onClick={() => this.viewDatapkg(r)}>{value}</LinkButton>
          ),
        },
        ...crossAppColumn,
        { name: i18n.chain.proMicroModules.approval.applier, code: 'initiator', width: 120 },
        { name: i18n.chain.proMicroModules.approval.applyDate, code: 'createTime', width: 120 },
        ...expireColumn,
        {
          name: '',
          code: 'id',
          width: 120,
          align: 'right',
          render: (value: string, r: ITableData) => {
            return value ? <OperationBtns controller={this} data={r} /> : null;
          },
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
      showColumnSetting: false,
    };
  };

  // 渲染头部所需信息
  private initHeaderOptions = () => {
    return {
      createBtnLabel: '',
      inputPlaceholder: i18n.chain.proMicroModules.approval.searchApply,
      title:
        this.ticketType$.getValue() === ApplyTicketTypeEnum.CROSS_APP_RESOURCE_PERMISSION
          ? i18n.chain.proMicroModules.approval.crossApp
          : i18n.chain.proMicroModules.approval.selpApp,
    };
  };

  private downloadApproval = async (expire: Dayjs, rest: IDownloadTableData) => {
    const remainTime = transformDateToUnix(expire) - transformDateToUnix(Date.now());
    if (remainTime < 300) {
      toastApi.error(i18n.chain.proMicroModules.approval.downloadTimeOutsid);
      return { success: false };
    }
    const success = await this.approvalSubmit({ resolution: 'approved', expire: remainTime }, rest);
    return { success };
  };

  private approvalSubmit = async (resolution: ITicketResolutionPut, data: ITableData) => {
    if (!data.id) return true;
    const result = await this.Model.resolveTicket(data.id, resolution).toPromise();
    if (result) {
      toastApi.success(i18n.chain.proMicroModules.approval.applyFinished(data.name));
      this.tableController.deleteDataFromList(data);
    }
    data.isLoading = false;
    return result;
  };

  // 初始请求
  private init() {
    const tc = this.tableController;
    tc.listenBackendFilter(tc.getSingleFilter$(), this.applyType$, this.ticketType$);
    this.ticketType$.getValue() && tc.loadDataList(this.getBackendFilterParams());
  }
}

export { TableApprovalManageController };
