import _ from 'lodash';
import { Tag } from '@metroDesign/tag';
import classnames from 'classnames';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsComp } from '@mdtBsComponents/modal-with-btns-comp';
import { LinkButton } from '@mdtDesign/button';
import Icon from '@mdtDesign/icon';
import Scrollbar from '@mdtDesign/scrollbar';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { TransformIdToName } from '../../components/transform-id-to-name';
import i18n from '../../languages';
import { SelectorTypeEnum, SelectorTypeIconMap } from '../user-lazy-selector';
import type { TransferPanelController } from './TransferPanelController';
import './index.less';

interface IProps {
  controller?: TransferPanelController;
  className?: string;
  disabled?: boolean;
  btnLabel?: string;
}

const ItemList = ({ controller, disabled }: IProps) => {
  const items = useObservableState(controller!.getSelectedItems$());

  const itemsComp = _.map(items, (item) => {
    const transformIdToNameType = controller?.getTransformIdToNameType
      ? controller.getTransformIdToNameType()
      : item.type === SelectorTypeEnum.USER
      ? DbColumnTypeEnum.USER_ID
      : DbColumnTypeEnum.ROLE_ID;
    return (
      <Tag
        key={item.id}
        closable={!disabled}
        style={{ display: 'inline-flex', alignItems: 'center' }}
        icon={
          <Icon
            size={14}
            style={{ display: 'inline-flex', verticalAlign: 'text-top', marginRight: 4 }}
            icon={SelectorTypeIconMap[item.type]}
          />
        }
        onClose={() => controller!.handleDeleteItem(item.id as string)}
      >
        {item.name || <TransformIdToName id={item.id} type={transformIdToNameType} />}
      </Tag>
    );
  });

  const content = itemsComp.length ? itemsComp : <span className="transfer-placeholder">--</span>;

  return (
    <div className="transfer-panel_panel">
      <Scrollbar>{content}</Scrollbar>
    </div>
  );
};

const TransferPanel = ({ controller, className, disabled, btnLabel }: IProps) => {
  const ctrl = controller!;

  const addBtn = disabled ? null : (
    <LinkButton className={classnames('transfer-panel_add-btn', 'add-btn')} onClick={() => ctrl.openUserSelector()}>
      {btnLabel || i18n.chain.proMicroModules.chooseUser.addUser}
    </LinkButton>
  );

  return (
    <div className={classnames('module-transfer-panel', className)}>
      {addBtn}
      <ItemList controller={ctrl} disabled={disabled} />
      <ModalWithBtnsComp controller={ctrl.getUserSelectorController()} />
    </div>
  );
};

export { TransferPanel };
