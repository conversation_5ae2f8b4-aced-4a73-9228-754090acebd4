import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import {
  ISelectorItem,
  IUserLazySelectorControllerOptions,
  IUserLazySelectorData,
  IUserLazySelectorModel,
  UserLazySelectorController,
  UserLazySelectorModel,
} from '../user-lazy-selector';

const transformIdToNameTypeMap = {
  id: DbColumnTypeEnum.USER_ID,
  uuid: 'user_uuid',
};

// TransferPanel 是UserSelector打开弹窗前后的条件都已确定的情况，所以此处options的值是两种条件下的并集。
export type ITransferPanelControllerOptions = IUserLazySelectorData &
  Omit<IUserLazySelectorControllerOptions, 'Model' | 'app'> & {
    userSelectorModel: IUserLazySelectorModel;
  };
export class TransferPanelController {
  private selectedItems$ = new BehaviorSubject<ISelectorItem[]>([]);
  private userSelectorController: UserLazySelectorController;
  private userSelectorModel: IUserLazySelectorModel;
  private disabledUserIds?: string[];
  private disabledRoleIds?: string[];

  public constructor({ userSelectorModel, ...rest }: ITransferPanelControllerOptions) {
    this.userSelectorModel = userSelectorModel;
    this.userSelectorController = new UserLazySelectorController({
      Model: userSelectorModel
        ? userSelectorModel
        : new UserLazySelectorModel({ appId: DatlasAppController.getInstance().getAppId() }),
      app: DatlasAppController.getInstance(),
      ...rest,
    });
    this.disabledUserIds = rest.disabledUserIds;
    this.disabledUserIds = rest.disabledRoleIds;
    (async () => {
      const users = userSelectorModel.transformToSelectorData(rest);
      users.length && this.selectedItems$.next(users);
    })();
  }

  public destroy() {
    this.selectedItems$.complete();
    this.userSelectorController?.destroy();
    this.userSelectorController = null!;
    this.userSelectorModel = null!;
    this.disabledUserIds = [];
    this.disabledRoleIds = [];
  }

  public getTransformIdToNameType() {
    return transformIdToNameTypeMap[this.userSelectorModel.getUserKey()] ?? DbColumnTypeEnum.ROLE_ID;
  }

  public getUserSelectorController() {
    return this.userSelectorController;
  }

  public getSelectedItems$() {
    return this.selectedItems$;
  }

  public openUserSelector() {
    this.userSelectorController
      .openModal({
        data: this.selectedItems$.getValue(),
        disabledUserIds: this.disabledUserIds,
        disabledRoleIds: this.disabledRoleIds,
      })
      .subscribe((resp) => {
        resp.success && this.selectedItems$.next(resp.result);
      });
  }

  public handleDeleteItem(id: string) {
    const items = _.reject(this.selectedItems$.getValue(), { id });
    this.selectedItems$.next(items);
  }
}
