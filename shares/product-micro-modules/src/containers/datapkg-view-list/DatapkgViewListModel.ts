import { from, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { getDatapkgViewsAsync, queryDatapkgsAsync } from '@mdtBsServices/datapkgs';

class DatapkgViewListModel {
  public static queryFirstPageData(pkgId: string) {
    return from(getDatapkgViewsAsync(pkgId)).pipe(
      switchMap((resp) => {
        if (!resp.success || !resp.data || resp.data.length === 0) return of([] as any);
        return from(queryDatapkgsAsync({ id: resp.data })).pipe(
          map((pkgResp) => {
            if (!pkgResp.success) return [0, []];
            return [0, pkgResp.data];
          }),
        );
      }),
    );
  }
}

export { DatapkgViewListModel };
