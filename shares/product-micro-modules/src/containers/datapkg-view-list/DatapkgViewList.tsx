import { FC } from 'react';
import { ArrowSquareOut, ChevronDown } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Popover } from '@metroDesign/popover';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Tag } from '@metroDesign/tag';
import { Typography } from '@metroDesign/typography';
import { IDatapkg } from '@mdtApis/interfaces';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPersonalPkg } from '@mdtProComm/utils/datapkgUtil';
import i18n from '../../languages';
import { CardCurdWithSimpleSearch } from '../card-curd-with-simple-search';
import { DatapkgViewListController } from './DatapkgViewListController';
import { DatapkgViewListModel } from './DatapkgViewListModel';
import './index.less';

export interface IProps {
  pkgId: string;
}

const DatapkgViewList: FC<IProps> = ({ pkgId }) => {
  const [controller] = useController(() => {
    const ctrl = new DatapkgViewListController({ Model: DatapkgViewListModel, pkgId });
    return [ctrl, null];
  });

  const listCtrl = controller.getListController();
  const listData = useObservableState(listCtrl.getDataList$());

  return listData.length ? (
    <Popover
      className="one-table-new-export-to-my-data-popover"
      rootClassName="one-table-new-export-to-my-data-popover-root"
      trigger={['click']}
      placement="topLeft"
      content={
        <Scrollbar style={{ height: 320, width: 300 }}>
          <CardCurdWithSimpleSearch controller={listCtrl} className="my-fixed-height-list" />
        </Scrollbar>
      }
    >
      <Button
        ghost
        icon={<ChevronDown />}
        iconDirection="right"
      >{`${i18n.chain.proMicroModules.oneTable.exportToMyData.exported}${listData.length}`}</Button>
    </Popover>
  ) : (
    <Button ghost disabled className="one-table-new-export-to-my-data-popover">
      {`${i18n.chain.proMicroModules.oneTable.exportToMyData.exported}${listData.length}`}
    </Button>
  );
};

interface IOptionCardProps {
  item: IDatapkg;
  controller: DatapkgViewListController;
}

export const CardView: FC<IOptionCardProps> = ({ item, controller }) => {
  const isPersonal = isPersonalPkg(item.ownership);
  return (
    <Flex className="one-table-new-export-to-my-data-item" vertical gap={8}>
      <Flex justify="space-between" align="center">
        {isPersonal ? (
          <Tag color="cyan-light">{i18n.chain.proMicroModules.oneTable.exportToMyData.personalPkg}</Tag>
        ) : (
          <Tag color="violet-light">{i18n.chain.proMicroModules.oneTable.exportToMyData.appPkg}</Tag>
        )}
        <Button.Link
          primary
          icon={<ArrowSquareOut />}
          iconDirection="right"
          onClick={() => controller.gotoMyData(item)}
        >
          {i18n.chain.proMicroModules.oneTable.exportToMyData.goto}
        </Button.Link>
      </Flex>
      <Typography.Text strong>{item.name}</Typography.Text>
      <Typography.Text type="secondary">
        {item.row_permission_strategy
          ? i18n.chain.proMicroModules.oneTable.exportToMyData.subbmitted
          : i18n.chain.proMicroModules.oneTable.exportToMyData.all}
      </Typography.Text>
    </Flex>
  );
};

export { DatapkgViewList };
