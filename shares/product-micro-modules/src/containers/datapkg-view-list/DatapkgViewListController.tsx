import { IDatapkg } from '@mdtApis/interfaces';
import { isPersonalPkg } from '@mdtProComm/utils/datapkgUtil';
import { CardCurdWithSimpleSearchController } from '../card-curd-with-simple-search';
import { CardView } from './DatapkgViewList';
import { DatapkgViewListModel } from './DatapkgViewListModel';

export interface IControllerProps {
  Model: typeof DatapkgViewListModel;
  pkgId: string;
}

class DatapkgViewListController {
  private Model: typeof DatapkgViewListModel;
  private pkgId: string;
  private listController: CardCurdWithSimpleSearchController<IDatapkg>;

  public constructor(props: IControllerProps) {
    this.Model = props.Model;
    this.pkgId = props.pkgId;
    this.listController = new CardCurdWithSimpleSearchController({
      headerOptions: {
        hideHeader: true,
      },
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: () => this.Model.queryFirstPageData(this.pkgId),
          },
          compOptions: () => this.initListOptions(),
        },
        cardItemViewController: () => this,
      },
    });
    this.listController.loadDataList();
  }

  public getListController() {
    return this.listController;
  }

  public destroy() {
    this.listController.destroy();
  }

  public gotoMyData(item: IDatapkg) {
    const isPersonal = isPersonalPkg(item.ownership);
    const subRoute = isPersonal ? 'personal-data' : 'self-app-data';
    window.open(`/onetable/mydata/${subRoute}?openWorkItemIdentifier=${item.id}`);
  }

  private initListOptions() {
    return {
      itemGap: 8,
      itemHeight: 92,
      itemWidth: '100%',
      useVirtual: false,
      itemKey: 'value',
      CardItemView: CardView,
    };
  }
}

export { DatapkgViewListController };
