import { BehaviorSubject } from 'rxjs';

/**
 * 为 DataListCompController 创建一个适配器，添加 DataListCompTableController 所需的方法
 * 这样可以在不修改原控制器的情况下，让它能用于 DataListCompTable 组件
 */
export function createTableControllerAdapter(controller: any) {
  const proxy = Object.create(controller);
  const columnsUpdated$ = new BehaviorSubject(controller.getCompOptions()?.columns || []);

  proxy.getColumnSettingController = function () {
    return {
      destroy: () => {},
      columns: controller.getCompOptions()?.columns || [],
      onColumnChange: () => {},
    };
  };

  proxy.getColumnsUpdated$ = function () {
    return columnsUpdated$;
  };

  proxy.getShowColumnSetting = function () {
    return false;
  };

  // 确保在销毁时也清理适配器资源
  const originalDestroy = proxy.destroy;
  proxy.destroy = function () {
    if (typeof originalDestroy === 'function') {
      originalDestroy.call(controller);
    }
    columnsUpdated$.complete();
  };

  return proxy;
}
