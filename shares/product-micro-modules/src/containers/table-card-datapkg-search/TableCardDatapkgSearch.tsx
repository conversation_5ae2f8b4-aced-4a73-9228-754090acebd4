import _ from 'lodash';
import { FC, ReactNode, useMemo } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DataListCompCard } from '@mdtBsComponents/data-list-comp-card';
import { DataListCompTable } from '@mdtBsComponents/data-list-comp-table';
import { LinkButton } from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import i18n from '../../languages';
import { createTableControllerAdapter } from './utils/ControllerAdapter';
import { useTableCardDatapkgSearchContext, useTableCardDatapkgSearchProvider } from './tableCardDatapkgSearchContext';
import { IConditionListItem, TableCardDatapkgSearchController } from './TableCardDatapkgSearchController';
import { ToolHeader } from './tool-header';
import './index.less';

// 子组件--筛选条件提示组件============================================================================
const ConditionList = () => {
  const { tableCardDatapkgSearchController: controller } = useTableCardDatapkgSearchContext();
  const list = useObservableState(() => controller.getConditionList$());
  const groups = _.groupBy(list, 'group');

  const closeTag = (item: IConditionListItem) => {
    controller.delConditionListItem(item);
  };

  const clearAll = () => {
    controller.clearAllConditionList();
  };

  let comps: ReactNode[] = [];
  _.forEach(groups, (values) => {
    const prefix = values[0].group;
    comps.push(
      <div className="pkg-search_condition-list-prefix" key={prefix}>
        {prefix + ':'}
      </div>,
    );
    const tags = _.map(values, (it) => (
      <Tag key={it.id} tag={it.name} color="blue-700" closable onClickClose={() => closeTag(it)} />
    ));
    comps = comps.concat(tags);
  });

  return comps.length ? (
    <div className="pkg-search_condition-list">
      {comps}
      <LinkButton onClick={clearAll} status="plain" size="compact" className="pkg-search_condition-list-clear-btn">
        {i18n.chain.proMicroModules.datapkg.cleatFilter}
      </LinkButton>
    </div>
  ) : null;
};

// 子组件--展示样式切换部分=============================================================================
const ShapeContainer = () => {
  const { tableCardDatapkgSearchController: controller } = useTableCardDatapkgSearchContext();
  const isCard = useObservableState(() => controller.getShapeIsCard$());

  const adaptedController = useMemo(() => {
    return isCard ? controller : createTableControllerAdapter(controller);
  }, [controller, isCard]);

  const View = (isCard ? DataListCompCard : DataListCompTable) as FC<any>;

  return <View key={isCard} controller={adaptedController} className="pkg-search_pkg-list-container" />;
};

// 数据包筛选模块=====================================================================================
interface IProps {
  controller: TableCardDatapkgSearchController;
  className?: string;
}
const TableCardDatapkgSearch: FC<IProps> = ({ controller, className }) => {
  const Provider = useTableCardDatapkgSearchProvider();
  const value = { tableCardDatapkgSearchController: controller };

  return (
    <Provider value={value}>
      <div className={`module_table-card-datapkg-search ${className || ''}`}>
        <ToolHeader />
        <ConditionList />
        <ShapeContainer />
      </div>
    </Provider>
  );
};

export { TableCardDatapkgSearch };
