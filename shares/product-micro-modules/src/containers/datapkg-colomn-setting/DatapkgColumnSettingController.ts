import { ColumnSettingController, ColumnSettingModel } from '@mdtBsComponents/column-setting';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { IColumnItem } from '../table-pkg-column';

export type ColumnChangeCallback = (visibleColumns: IColumnItem[]) => void;

interface IControllerOptions {
  columns: IColumnItem[];
  pkgId: string;
  onColumnChange?: ColumnChangeCallback;
}

/**
 * 数据包列设置控制器
 * 继承通用的 ColumnSettingController
 */
class DatapkgColumnSettingController extends ColumnSettingController {
  public constructor(options: IControllerOptions) {
    super({
      Model: ColumnSettingModel,
      columns: options.columns,
      id: `${options.pkgId}_${DatlasAppController.getInstance().getUserId()}`,
      onColumnChange: options.onColumnChange as any,
    });
  }
}

export { DatapkgColumnSettingController };
