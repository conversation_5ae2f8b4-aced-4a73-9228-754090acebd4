import _ from 'lodash';
import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { LinkButton } from '@mdtDesign/button';
import Checkbox from '@mdtDesign/checkbox';
import Icon from '@mdtDesign/icon';
import Scrollbar from '@mdtDesign/scrollbar';
import TooltipText from '@mdtDesign/tooltip/TooltipText';
import { TransformIdToName } from '../../components/transform-id-to-name';
import i18n from '../../languages';
import { CardCurdWithSimpleSearch } from '../card-curd-with-simple-search';
import { ISelectorItem, SelectorController, SelectorTypeEnum, SelectorTypeIconMap } from './SelectorController';
import './index.less';

// 子组件--类型选择
interface IProps {
  controller: SelectorController;
}

const SelectMe = ({ controller }: IProps) => {
  const selectMe = useObservableState(controller.getSelectMe$());
  const showMe = useObservableState(controller.getShowMe$());
  if (!showMe) return null;

  return (
    <Checkbox
      checked={selectMe}
      onChange={controller.changeSelectMe}
      title={i18n.chain.proMicroModules.chooseUser.me}
    />
  );
};

const TypeList = (props: IProps) => {
  const controller = props.controller!;
  const selectedType = useObservableState(controller.getSelectedType$());
  if (selectedType) return null;

  const list = _.map(controller.getTypeList(), (typeObj: ILabelValue) => (
    <div
      className="resource-type-item"
      onClick={() => controller.handleClickType(typeObj.value as SelectorTypeEnum)}
      key={typeObj.value}
    >
      <div className="type-name">
        <Icon icon={SelectorTypeIconMap[typeObj.value]} size={18} />
        <span>{typeObj.label}</span>
      </div>
      <Icon icon="chevron-right" size={18} />
    </div>
  ));
  return (
    <div className="types-container">
      <SelectMe controller={controller} />
      {list}
    </div>
  );
};

// 子组件--选项
interface IOptionCardProps {
  item: ISelectorItem;
  selected: boolean;
  controller: SelectorController;
}

export const OptionCard: FC<IOptionCardProps> = ({ item, controller, selected }) => {
  const isDisabled = controller.getOptionsController().checkItemDisabled(item);
  const showNextBtn =
    (item.type !== SelectorTypeEnum.USER && controller.showUser()) ||
    (!controller.showUser() && item.type === SelectorTypeEnum.ORGANIZATION);

  const nextBtn = showNextBtn ? (
    <LinkButton
      className="next-level-btn"
      onClick={(e) => {
        e.stopPropagation();
        controller.handleClickNextLevel(item);
      }}
      disabled={selected}
    >
      {item.type === SelectorTypeEnum.ORGANIZATION
        ? i18n.chain.proMicroModules.chooseUser.sub
        : i18n.chain.proMicroModules.chooseUser.people}
    </LinkButton>
  ) : null;

  const handleClickOption = () => {
    if (isDisabled) return;
    controller.handleClickOption(item, selected);
  };

  return (
    <div className="option-card" onClick={handleClickOption}>
      <Checkbox checked={selected} disabled={isDisabled} />
      <Icon icon={SelectorTypeIconMap[item.type]} size={18} />
      <TooltipText text={item.name} className="option-name" placement="right" />
      {nextBtn}
    </div>
  );
};

const OptionsContainer = (props: IProps) => {
  const controller = props.controller!;

  return (
    <div className="options-container">
      <CardCurdWithSimpleSearch className="card-curd_list-wrap" controller={controller.getOptionsController()} />
    </div>
  );
};

export const BreadMenu = (props: IProps) => {
  const controller = props.controller!;
  const items = useObservableState(controller.getBreadItems$());
  const lastIndex = _.size(items) - 1;
  const content = _.map(items, (it, index) => {
    const prefix = index === 0 ? null : <span className="divider">/</span>;
    const item =
      lastIndex !== 0 && index < lastIndex ? (
        <LinkButton status="plain" onClick={() => controller.handleClickBreadMenu(it, index)}>
          {it.label}
        </LinkButton>
      ) : (
        <span>{it.label}</span>
      );
    return (
      <span key={it.value}>
        {prefix}
        {item}
      </span>
    );
  });

  return <div className="bread-menu">{content}</div>;
};

// 子组件--选中内容
const SelectedList = (props: IProps) => {
  const controller = props.controller!;
  const values = useObservableState(controller.getValues$());
  const count = _.size(values);
  const transformIdToNameType = controller.getTransformIdToNameType();
  const list = _.map(values, (item) => {
    return (
      <div className="selected-item" key={item.id}>
        <div className="seleted-title">
          <Icon icon={SelectorTypeIconMap[item.type]} size={18} />
          <div className="selected-title-value">
            <TransformIdToName id={item.id} type={transformIdToNameType} />
          </div>
        </div>
        <Icon className="unselect-btn" icon="close" size={18} onClick={() => controller.removeSelectedItem(item)} />
      </div>
    );
  });

  const clearBtn = count ? (
    <LinkButton onClick={controller.handleClear}>{i18n.chain.proMicroModules.chooseUser.clear}</LinkButton>
  ) : null;

  return (
    <div className="selected-container">
      <div className="selected-tip">
        <div className="selected-count">
          {i18n.chain.proMicroModules.chooseUser.selected} <span className="selected-count-value">{count}</span>
        </div>
        {clearBtn}
      </div>
      <Scrollbar className="selected-list">{list}</Scrollbar>
    </div>
  );
};

// 用户选择器
const Selector = (props: IProps) => {
  const controller = props.controller!;
  const renderFooterExtra = controller.getRenderFooterExtra();
  return (
    <div className="module_user-lazy-selector-container">
      <div className="module_user-lazy-selector">
        <TypeList controller={controller} />
        <OptionsContainer controller={controller} />
        <SelectedList controller={controller} />
      </div>
      {renderFooterExtra ? renderFooterExtra() : null}
    </div>
  );
};

export { Selector };
