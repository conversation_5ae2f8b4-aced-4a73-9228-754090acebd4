import _ from 'lodash';
import { FC, ReactNode } from 'react';
import {
  DataListCompTableCurdController,
  IControllerOptions as IDataListCompTableCurdControllerOptions,
  IDynamicOptions,
  ITableCurdColumn,
  IVirtualizedTableCurdProps,
} from '@mdtBsComponents/data-list-comp-table-curd';
import { ENABLE_TABLE_COLUMN_SETTING } from '../../datlas/datlasConfig';
import { Header } from './TableCurdWithSimpleSearch';

export interface IHeaderOptions {
  createBtnLabel?: string;
  inputPlaceholder?: string;
  hideInput?: boolean;
  title?: ReactNode;
  renderExtendOthers?: () => ReactNode;
  hideHeader?: boolean;
}

export interface IControllerOptions<V> {
  dataListCompTableCurdControllerOptions: IDataListCompTableCurdControllerOptions<V>;
  headerOptions: IDynamicOptions<IHeaderOptions>;
}

class TableCurdWithSimpleSearchController<V> extends DataListCompTableCurdController<V> {
  private headerOptions: IDynamicOptions<IHeaderOptions>;

  public constructor(options: IControllerOptions<V>) {
    const tableOptions = options.dataListCompTableCurdControllerOptions.tableOptions;

    const mergedTableOptions: IDynamicOptions<IVirtualizedTableCurdProps<V>> = (params?: any) => {
      const origOptions = tableOptions
        ? _.isFunction(tableOptions)
          ? tableOptions(params)
          : tableOptions
        : { columns: [] as ITableCurdColumn<V>[] };

      return {
        headerRender: (ColumnSetting: FC, FolderBreadView: FC) =>
          this.defaultHeaderRender(ColumnSetting, FolderBreadView),
        showColumnSetting: ENABLE_TABLE_COLUMN_SETTING,
        ...origOptions,
      };
    };

    super({
      ...options.dataListCompTableCurdControllerOptions,
      tableOptions: mergedTableOptions,
    });
    this.headerOptions = options.headerOptions;
  }

  public destroy() {
    super.destroy();
    this.headerOptions = null!;
  }

  public getHeaderOptions(): IHeaderOptions {
    return (_.isFunction(this.headerOptions) ? this.headerOptions() : this.headerOptions) || {};
  }

  // 复写最上层父类的方法
  public changeSingleFilter(value: string) {
    this.getSingleFilter$().next(_.trimStart(value));
  }

  private defaultHeaderRender(ColumnSetting: FC, FolderBreadView: FC) {
    return <Header controller={this} ColumnSetting={ColumnSetting} FolderBreadView={FolderBreadView} />;
  }
}

export { TableCurdWithSimpleSearchController };
