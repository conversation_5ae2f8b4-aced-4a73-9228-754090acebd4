import { ChangeEvent, FC } from 'react';
import { Flex } from '@metroDesign/flex';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DataListCompTableWithCurd } from '@mdtBsComponents/data-list-comp-table-curd';
import Button, { ButtonProps } from '@mdtDesign/button';
import Input from '@mdtDesign/input';
import i18n from '../../languages';
import {
  useTableCurdWithSimpleSearchContext,
  useTableCurdWithSimpleSearchProvider,
} from './tableCurdWithSimpleSearchContext';
import { TableCurdWithSimpleSearchController } from './TableCurdWithSimpleSearchController';
import './index.less';

// 子组件--检索输入框==================================================================================
interface IInputSearchProps {
  placeholder: string;
}
const InputSearch: FC<IInputSearchProps> = ({ placeholder }) => {
  const { tableCurdWithSimpleSearchController: controller } = useTableCurdWithSimpleSearchContext();
  const value = useObservableState(() => controller.getSingleFilter$());

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    controller.changeSingleFilter(e.target.value);
  };

  return (
    <Input
      className="table-curd-search_user-input"
      value={value}
      placeholder={placeholder}
      prefixIcon="search"
      onChange={handleChange}
      allowClear={false}
    />
  );
};

// 子组件--检索及按钮=================================================================================
export const Header: FC<{
  controller: TableCurdWithSimpleSearchController<any>;
  ColumnSetting: FC<any>;
  FolderBreadView: FC;
}> = ({ controller, ColumnSetting, FolderBreadView }) => {
  const Provider = useTableCurdWithSimpleSearchProvider();
  const { enableCreate, createProps } = controller.getCurdOptions();
  const {
    createBtnLabel,
    title,
    inputPlaceholder = i18n.chain.comPlaceholder.input,
    hideInput,
    renderExtendOthers,
    hideHeader,
  } = controller.getHeaderOptions();

  const onClickCreateBtn = () => {
    controller.onClickCreateBtn();
  };

  const btnComp = enableCreate ? (
    <Button
      leftIcon="add"
      type="primary"
      className="table-curd-search_create-btn"
      onClick={onClickCreateBtn}
      {...(createProps as ButtonProps)}
    >
      {createBtnLabel}
    </Button>
  ) : null;

  const input = hideInput ? null : <InputSearch placeholder={inputPlaceholder} />;

  return hideHeader ? null : (
    <Provider value={{ tableCurdWithSimpleSearchController: controller }}>
      <div className="table-curd-search_header">
        <div className="table-curd-search_title">{title}</div>
        <Flex align="center" gap={8}>
          <ColumnSetting />
          {input}
          {btnComp}
        </Flex>
        {renderExtendOthers ? renderExtendOthers() : null}
      </div>
      <FolderBreadView />
    </Provider>
  );
};

/**
 * 表格样式，有标题，搜索和一个按钮组合的界面
 * 适用数据量不大，前端检索的增删改查的界面
 */
// 表格列表页面=======================================================================================
interface IProps<T> {
  controller: TableCurdWithSimpleSearchController<T>;
  className?: string;
}
export function TableCurdWithSimpleSearch<T>({ controller, className }: IProps<T>) {
  return (
    <div className={`modules_table-curd-with-simple-search ${className}`}>
      <DataListCompTableWithCurd className="table-curd-search_table" controller={controller} />
    </div>
  );
}
