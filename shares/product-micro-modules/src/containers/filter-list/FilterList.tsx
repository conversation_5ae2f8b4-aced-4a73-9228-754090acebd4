import { FC, ReactElement } from 'react';
import { Close } from '@metro/icons';
import { Popup } from '@metro/mobile-components/dist/esm/popup';
import { Button } from '@metroDesign/button';
import { CreateFilter } from '@metroDesign/create-filter';
import { Flex } from '@metroDesign/flex';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { BadgesMemo } from '@mdtDesign/badges';
import { LinkButton } from '@mdtDesign/button';
import { FilterRuleForm } from '../../components/filter-rule-form';
import i18n from '../../languages';
import { FilterListController } from './FilterListController';
import { FilterPanel } from './FilterPanel';
import './index.less';

interface IProps {
  controller: FilterListController;
  children?: ReactElement;
}

const cancelButtonStyle = { ghost: true, withoutBorder: true };

export const FilterList: FC<IProps> = ({ controller, children }) => {
  useObservableState(controller.getColumns$());
  const filterSize = useObservableState(controller.getFilterSize$());
  const panelController = controller.getPanelController();
  const filterRuleFormController = controller.getFilterRuleFormController();

  const defaultFilterBtn = (
    <LinkButton
      className={`module_filter-list_tool-btn ${filterSize ? 'has-filter' : ''}`}
      leftIcon="filter"
      style={{ display: 'flex' }}
    >
      <BadgesMemo count={filterSize}>{i18n.chain.proMicroModules.filter.advancedFilter}</BadgesMemo>
    </LinkButton>
  );
  const popoverBtn = children ? children : defaultFilterBtn;
  const content = panelController ? <FilterPanel controller={panelController} /> : null;
  const ruleFormContent = filterRuleFormController ? <FilterRuleForm controller={filterRuleFormController} /> : null;

  if (!isPc()) {
    return <FilterListH5 controller={controller} />;
  }

  return (
    <CreateFilter
      cancelButtonLabel={<Close />}
      cancelButtonProps={cancelButtonStyle}
      overlayClassName="module_filter-list-overlay"
      overlay={
        <>
          {content}
          {ruleFormContent}
        </>
      }
    >
      {popoverBtn}
    </CreateFilter>
  );
};

const FilterListH5: FC<IProps> = ({ controller, children }) => {
  useObservableState(controller.getColumns$());
  const filterSize = useObservableState(controller.getFilterSize$());
  const popupVisible = useObservableState(controller.getPopupVisible$());
  const filterRuleFormController = controller.getFilterRuleFormController();

  const onPopupClose = () => controller.onPopupClose();
  const defaultFilterBtn = (
    <LinkButton className={`module_filter-list_tool-btn ${filterSize ? 'has-filter' : ''}`} leftIcon="filter">
      <BadgesMemo count={filterSize}>{i18n.chain.proMicroModules.filter.advancedFilter}</BadgesMemo>
    </LinkButton>
  );
  const popoverBtn = children ? children : defaultFilterBtn;
  const ruleFormContent = filterRuleFormController ? <FilterRuleForm controller={filterRuleFormController} /> : null;
  return (
    <>
      <div onClick={() => controller.onPopupOpen()}>{popoverBtn}</div>
      <Popup
        visible={popupVisible}
        className="module_filter-list-overlay-h5"
        destroyOnClose
        onMaskClick={onPopupClose}
        onClose={onPopupClose}
        bodyStyle={{ height: '80vh', padding: '20px', width: '100%' }}
      >
        <Flex vertical gap="small">
          <Button onClick={onPopupClose} ghost onlyIcon icon={<Close />} className="close" />
          {ruleFormContent}
        </Flex>
      </Popup>
    </>
  );
};
