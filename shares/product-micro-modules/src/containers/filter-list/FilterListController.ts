import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { IDatapkgColumn, IDatapkgRowsQueryCondition, IOperatorFilter } from '@mdtProComm/interfaces';
import {
  type IControllerOptions as IRuleFormOptions,
  FilterRuleFormController,
} from '../../components/filter-rule-form';
import { FilterPanelController } from './FilterPanelController';

export type IFilterRuleFormOptions = Omit<IRuleFormOptions<IOperatorFilter>, 'columns' | 'onSubmitCallback'>;
export type IFilterRuleFormOptionsFunc = () => IFilterRuleFormOptions;
interface IControllerOptions {
  useRuleForm?: boolean;
  ruleFormOptions?: IFilterRuleFormOptionsFunc;
  // 是否自动添加默认条件（默认为 true）
  autoAddDefaultCondition?: boolean;
  // 自定义默认条件，如果不提供且 autoAddDefaultCondition 为 true，则使用标准默认条件
  defaultCondition?: any;
}

class FilterListController {
  private readonly columns$ = new BehaviorSubject<IDatapkgColumn[]>([]);
  private filterList$: BehaviorSubject<IDatapkgRowsQueryCondition[]> = new BehaviorSubject(
    [] as IDatapkgRowsQueryCondition[],
  );
  private operatorFilter$ = new BehaviorSubject<IOperatorFilter | undefined>(undefined);
  private filterSize$ = new BehaviorSubject(0);
  private panelController?: FilterPanelController;
  private ruleFormController?: FilterRuleFormController<IOperatorFilter>;
  private ruleFormOptions?: IFilterRuleFormOptionsFunc;
  private useRuleForm?: boolean;
  private popupVisible$ = new BehaviorSubject(false);
  private autoAddDefaultCondition: boolean;
  private defaultCondition?: any;

  public constructor(options?: IControllerOptions) {
    this.useRuleForm = options?.useRuleForm;
    this.ruleFormOptions = options?.ruleFormOptions;
    this.autoAddDefaultCondition = options?.autoAddDefaultCondition ?? true;
    this.defaultCondition = options?.defaultCondition;
  }

  public destroy() {
    this.columns$.complete();
    this.columns$.next([]);
    this.filterList$.complete();
    this.operatorFilter$.complete();
    this.filterList$.next([]);
    this.filterSize$.complete();
    this.panelController?.destroy();
    this.ruleFormController?.destroy();
    this.popupVisible$.complete();
    this.panelController = undefined;
    this.ruleFormOptions = undefined;
    this.defaultCondition = undefined;
  }

  public init(columns: IDatapkgColumn[], initConditions?: IDatapkgRowsQueryCondition[]) {
    if (this.useRuleForm) {
      this.ruleFormController = new FilterRuleFormController<IOperatorFilter>({
        maxHeight: '60vh',
        ...(this.ruleFormOptions?.() || {}),
        columns,
        onSubmitCallback: (values?: IOperatorFilter) => {
          this.operatorFilter$.next(values);
          // 移动端关闭面板
          this.popupVisible$.next(false);
        },
      });
      this.operatorFilter$.subscribe((values) => {
        this.filterSize$.next(countColumns(values));
      });
      if (this.autoAddDefaultCondition && !this.defaultCondition) {
        this.buildDefaultCondition();
      }
      this.applyDefaultConditionIfNeeded(initConditions);
    } else {
      this.panelController = new FilterPanelController({
        columns,
        initConditions,
        clickSearchFunc: (queryList: IDatapkgRowsQueryCondition[]) => {
          this.filterList$.next(queryList);
        },
      });
      this.filterList$.next(initConditions || []);
      this.filterList$.subscribe((value) => {
        const oldVal = this.filterSize$.getValue();
        const newVal = _.size(value);
        oldVal !== newVal && this.filterSize$.next(newVal);
      });
    }

    this.columns$.next(columns);
  }

  public getPopupVisible$() {
    return this.popupVisible$;
  }

  public onPopupClose() {
    this.popupVisible$.next(false);
  }

  public onPopupOpen() {
    this.popupVisible$.next(true);
  }

  public getFilterSize$ = () => {
    return this.filterSize$;
  };

  public getPanelController() {
    return this.panelController;
  }

  public getFilterRuleFormController() {
    return this.ruleFormController;
  }

  public getColumns$() {
    return this.columns$;
  }

  public getFilterList$() {
    return this.filterList$;
  }

  public getOperatorFilter$() {
    return this.operatorFilter$;
  }

  public getFilterListValue() {
    return this.filterList$.getValue();
  }

  private applyDefaultConditionIfNeeded(initConditions?: IDatapkgRowsQueryCondition[]) {
    if (
      !this.autoAddDefaultCondition ||
      !_.isEmpty(initConditions) ||
      !this.ruleFormController ||
      !this.defaultCondition
    ) {
      return;
    }

    this.ruleFormController.onChange(this.defaultCondition);
  }

  private buildDefaultCondition() {
    if (!this.ruleFormController) {
      return;
    }

    const ruleFormProps = this.ruleFormController.getRuleFormProps();
    const fields = ruleFormProps.fields || [];

    if (fields.length > 0) {
      const firstField = fields[0];
      this.defaultCondition = {
        relation: 'and',
        conditions: [
          {
            name: firstField.name,
            operator: 'eq', // 等于操作符
            type: firstField.type,
            value: null,
          },
        ],
      };
    }
  }
}

export { FilterListController };

// 计算条件数量
// eslint-disable-next-line sonarjs/cognitive-complexity
function countColumns(data?: IOperatorFilter): number {
  let count = 0;

  function traverse(obj: any) {
    if (_.isArray(obj)) {
      obj.forEach(traverse);
    } else if (_.isObject(obj)) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (key === 'column') {
            count += 1;
          } else {
            traverse((obj as any)[key]);
          }
        }
      }
    }
  }

  if (data) {
    traverse(data);
  }

  return count;
}
