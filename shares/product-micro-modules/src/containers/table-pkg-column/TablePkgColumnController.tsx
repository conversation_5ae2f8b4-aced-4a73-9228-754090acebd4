import _ from 'lodash';
import { from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { DataListCompTableController, IVirtualizedTableProps } from '@mdtBsComponents/data-list-comp-table';
import type { SortTypeEnum } from '@mdtDesign/data-table';
import { getDbColumnTypeLabel } from '@mdtProComm/utils/columnUtil';
import i18n from '../../languages';

export interface IColumnItem {
  name: string;
  type: string;
  comment?: string;
  view_format?: Record<string, any>;
  description?: string;
  sortType?: SortTypeEnum;
  title?: string;
  formatter?: any;
  sorter?: (type: SortTypeEnum) => void;
  key?: string;
  width?: number;
  minWidth?: number;
  frozen?: boolean;
}
export type ILoadColumnsFunc<V> = (params?: any) => Observable<V[]>;
interface IControllerOptions<V> {
  loadColumnsFunc?: ILoadColumnsFunc<V>;
  maxHeight?: number;
}

export class TablePkgColumnController<V extends IColumnItem> extends DataListCompTableController<IColumnItem> {
  private loadColumnsFunc: ILoadColumnsFunc<V>;
  private maxHeight: number | string;

  public constructor(options: IControllerOptions<V>) {
    super({
      dataListControllerOptions: {
        loadDataListFunc: (params: any) => this.loadPkgColumn(params),
      },
      compOptions: () => this.initTableColumns(),
    });
    this.maxHeight = options.maxHeight || 'auto';
    this.loadColumnsFunc = options.loadColumnsFunc || this.defaultLoadColumnsFunc;
  }

  public destroy() {
    super.destroy();
    this.loadColumnsFunc = this.defaultLoadColumnsFunc;
  }

  private initTableColumns = (): IVirtualizedTableProps => {
    return {
      columns: [
        { name: i18n.chain.proMicroModules.datapkg.name, code: 'name', width: 140 },
        { name: i18n.chain.proMicroModules.datapkg.type, code: 'type', width: 130 },
        { name: i18n.chain.proMicroModules.datapkg.typeDesc, code: 'description', width: 120 },
        { name: i18n.chain.proMicroModules.datapkg.nameDesc, code: 'comment' },
      ],
      type: 'page-bg',
      primaryKey: 'name',

      style: { height: this.maxHeight },
    };
  };

  private loadPkgColumn = (params: any) => {
    return from(this.loadColumnsFunc(params)).pipe(
      map((v) => {
        const data = _.map(v, (it) => {
          return {
            name: it.name,
            type: it.type,
            description: getDbColumnTypeLabel(it.type),
            comment: it.comment,
          };
        });
        return [0, data] as [number, IColumnItem[]];
      }),
    );
  };

  private defaultLoadColumnsFunc() {
    return of([]);
  }
}
