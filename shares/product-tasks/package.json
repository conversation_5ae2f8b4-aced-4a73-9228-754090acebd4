{"name": "@mdt/product-tasks", "version": "1.25.18", "private": false, "description": "脉策产品级别任务通用", "keywords": ["mdt", "product", "comm", "tasks"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@i18n-chain/react": "2.0.1", "@mdt/business-services": "^1.35.14", "@mdt/product-comm": "^1.28.18", "jszip": "^3.7.1", "observable-webworker": "^3.4.0"}}