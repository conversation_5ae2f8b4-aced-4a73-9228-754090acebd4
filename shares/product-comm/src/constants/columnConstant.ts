export enum FieldTypeEnum {
  TEXT = 'text',
  STR = 'str',
  NUMBER = 'number',
  INT = 'int',
  BIGINT = 'bigint',
  FLOAT = 'float',
  LNGLAT = 'lnglat',
  DATETIME = 'datetime',
  DATE = 'date',
  BOOL = 'bool',
  FORMULA = 'formula',
  GEOMETRY = 'geometry',
  IMAGE = 'image',
  JSON = 'json',
  ARRAY_STR = 'array_str',
  USER_ID = 'user_id',
}

export enum ColumnOperatorFilterEnum {
  EQ = 'eq', // 等于
  NE = 'ne', // 不等于
  GT = 'gt', // 大于
  GE = 'ge', // 大于等于
  LT = 'lt', // 小于
  LE = 'le', // 小于等于
  IS = 'is', // eq类似，但是仅用于判断True, False和NULL
  BETWEEN = 'between', // 在某个区间
  IN = 'in', // 等于集合中的任意一个值
  START_WITH = 'startswith', // 以某些文本开头
  END_WITH = 'endswith', // 以某些文本结尾
  LIKE = 'like', // 文本满足指定的模式，以%为占位符
  ILIKE = 'ilike', // 与like类型，但是忽略大小写
  MATCH = 'match', // 满足某个正则表达式
  CONTAIN = 'contain', // 文本包含某个子文本
  INTERSECT = 'intersect', // 两个集合是否有交集
  SUPERSET = 'superset', // 一个集合是否是另一个集合的超集
  SUBSET = 'subset', // 一个集合是否是另一个集合的子集
}

// 操作符 (逐步废弃)
export enum ColumnOperatorEnum {
  EQ = 'eq',
  NE = 'ne',
  GT = 'gt',
  GE = 'ge',
  LT = 'lt',
  LE = 'le',
  CONTAIN = 'contain',
  NOT_CONTAIN = 'not_contain',
  START_WITH = 'start_with',
  END_WITH = 'end_with',
  BETWEEN = 'between',
  // TODO row有
  EMPTY = 'empty',
  NOT_EMPTY = 'not_empty',
  EQUAL = 'equal',
  NOT_EQUAL = 'not_equal',
  NOT_BETWEEN = 'not_between',
  // TODO cont有
  IN = 'in',
  NOT_IN = 'not_in',
  TRUE = 'true',
  FALSE = 'false',
  LIKE = 'like',
  NOT_LIKE = 'not_like',
  MATCH = 'match',
  NOT_MATCH = 'not_match',
  NULL = 'null',
  NOT_NULL = 'not_null',
}

// 监控类型
export enum ConstraintTypeEnum {
  COLUMN = 'column',
  FORMULA = 'formula',
  SQL = 'sql',
}

export const COLUMN_OPERATOR_COMM_TYPE = 'comm';

export const COLUMN_OPERATOR_NO_VALUE_LIST: string[] = [
  ColumnOperatorEnum.EMPTY,
  ColumnOperatorEnum.NOT_EMPTY,
  ColumnOperatorEnum.NULL,
  ColumnOperatorEnum.NOT_NULL,
];

export const COLUMN_OPERATOR_DOUBLE_DATE_INPUT_LIST: string[] = [
  ColumnOperatorEnum.BETWEEN,
  ColumnOperatorEnum.NOT_BETWEEN,
];

export const COLUMN_OPERATOR_EQUAL_OR_NOT: string[] = [
  // ColumnOperatorEnum.EQUAL,
  // ColumnOperatorEnum.NOT_EQUAL,
  ColumnOperatorEnum.EQ,
  ColumnOperatorEnum.NE,
];
export const RANGE_OPERATORS: string[] = [ColumnOperatorEnum.BETWEEN, ColumnOperatorEnum.NOT_BETWEEN];
export const LIST_OPERATORS: string[] = [ColumnOperatorEnum.EQUAL, ColumnOperatorEnum.NOT_EQUAL];

export enum FrontendColumnTypeEnum {
  NUMBER = 'number', // 数值
  TEXT = 'text', // 文本
  BOOL = 'bool', // 布尔
  DATE = 'date', // 时间
  ARRAY = 'array', // 数组（整数、文本）
}

export enum DbColumnTypeEnum {
  INT = 'int',
  BIGINT = 'bigint',
  FLOAT = 'float',
  STR = 'str',
  BOOL = 'bool',
  DATE = 'date', // 使用少,一般用datatime代替
  TIME = 'time',
  DATETIME = 'datetime',
  TIMESTAMP = 'timestamp', // 使用少,一般用datatime代替
  BIT = 'bit',
  GEOMETRY = 'geometry',
  JSON = 'json',
  MEDIA_JSON = 'mediajson',
  ARRAY_INT = 'array_int',
  ARRAY_STR = 'array_str',
  ARRAY_FLOAT = 'array_float',
  USER_ID = 'user_id',
  ORG_ID = 'org_id',
  GROUP_ID = 'group_id',
  ROLE_ID = 'role_id',
  GENERAL_ROLE_ID = 'general_role_id',
}

export const numberInput = [
  ColumnOperatorEnum.NE,
  ColumnOperatorEnum.EQ,
  ColumnOperatorEnum.GT,
  ColumnOperatorEnum.LT,
  ColumnOperatorEnum.GE,
  ColumnOperatorEnum.LE,
];

export enum ColumnDdlTypeEnum {
  PRIMARY_KEY = 'primary_key',
  UNIQUE_CONSTRAINT = 'unique_constraint',
  INDEX = 'index',
}

// 关键列类型
export enum KeyColumnEnum {
  SELECT_GEOMETRY = 'select_geometry', // 单、双地理使用
  DISPLAY_GEOMETRY = 'display_geometry', // 双地理使用
  LINK_GEOMETRY = 'link_geometry', // 点到点使用
  ID = 'id',
  NAME = 'name',
  ADDRESS = 'address',
  UPDATE_TIME = 'update_time',
  LNG = 'lng',
  LAT = 'lat',
}

export enum QuestionTypeEnum {
  GEOMETRY = 'geometry',
  TEXT = 'text',
  TEXTAREA = 'textarea',
  INTEGER = 'integer',
  DECIMAL = 'decimal',
  SWITCH = 'switch',
  TIME = 'time',
  DATE = 'date',
  DATETIME = 'datetime',
  TIMESTAMP = 'timestamp',
  FILE = 'file',
  ARRAY = 'array',
}
