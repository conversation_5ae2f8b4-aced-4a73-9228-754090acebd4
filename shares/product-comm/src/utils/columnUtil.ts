import _ from 'lodash';
import { randomUuid } from '@mdtBsComm/utils/stringUtil';
import {
  COLUMN_OPERATOR_EQUAL_OR_NOT,
  COLUMN_OPERATOR_NO_VALUE_LIST,
  ColumnOperatorEnum,
  ColumnOperatorFilterEnum,
  ConstraintTypeEnum,
  DbColumnTypeEnum,
  FieldTypeEnum,
  FrontendColumnTypeEnum,
  KeyColumnEnum,
  QuestionTypeEnum,
  RANGE_OPERATORS,
} from '../constants';
import { IColumnQueryItem, IDatapkgColumn, ILabelValue } from '../interfaces';
import i18n from '../languages';
import { getLabelValueOptions } from './commonUtil';

export const ICON_MAP: Record<string, string> = {
  [FieldTypeEnum.JSON]: 'text',
  [FieldTypeEnum.TEXT]: 'text',
  [FieldTypeEnum.STR]: 'text',
  [FieldTypeEnum.NUMBER]: 'num',
  [FieldTypeEnum.FLOAT]: 'num',
  [FieldTypeEnum.INT]: 'num',
  [FieldTypeEnum.DATETIME]: 'time',
  [FieldTypeEnum.DATE]: 'time',
  [FieldTypeEnum.LNGLAT]: 'geo-global',
  [FieldTypeEnum.IMAGE]: 'image',
};

export const TABLE_HEAD_TYPE_MAP: Record<string, string> = {
  [FieldTypeEnum.JSON]: FrontendColumnTypeEnum.TEXT,
  [FieldTypeEnum.TEXT]: FrontendColumnTypeEnum.TEXT,
  [FieldTypeEnum.ARRAY_STR]: FrontendColumnTypeEnum.TEXT,
  [FieldTypeEnum.STR]: FrontendColumnTypeEnum.TEXT,
  [FieldTypeEnum.NUMBER]: FrontendColumnTypeEnum.NUMBER,
  [FieldTypeEnum.BIGINT]: FrontendColumnTypeEnum.NUMBER,
  [FieldTypeEnum.FLOAT]: FrontendColumnTypeEnum.NUMBER,
  [FieldTypeEnum.INT]: FrontendColumnTypeEnum.NUMBER,
  [FieldTypeEnum.DATETIME]: 'datetime',
  [FieldTypeEnum.DATE]: 'datetime',
  [FieldTypeEnum.LNGLAT]: 'lnglat',
  [FieldTypeEnum.GEOMETRY]: 'lnglat',
  [FieldTypeEnum.IMAGE]: 'image',
  [DbColumnTypeEnum.TIMESTAMP]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.USER_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.ORG_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.GROUP_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.ROLE_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.GENERAL_ROLE_ID]: FrontendColumnTypeEnum.NUMBER,
};

export const DB_COLUMN_TYPE_TO_FRONTEND_MAP: Record<string, string> = {
  [DbColumnTypeEnum.INT]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.BIGINT]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.FLOAT]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.STR]: FrontendColumnTypeEnum.TEXT,
  [DbColumnTypeEnum.BOOL]: FrontendColumnTypeEnum.BOOL,
  [DbColumnTypeEnum.DATE]: FrontendColumnTypeEnum.DATE,
  [DbColumnTypeEnum.TIME]: FrontendColumnTypeEnum.DATE,
  [DbColumnTypeEnum.DATETIME]: FrontendColumnTypeEnum.DATE,
  [DbColumnTypeEnum.USER_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.ORG_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.GROUP_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.ROLE_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.GENERAL_ROLE_ID]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.TIMESTAMP]: FrontendColumnTypeEnum.NUMBER,
  [DbColumnTypeEnum.GEOMETRY]: FrontendColumnTypeEnum.TEXT,
  [DbColumnTypeEnum.JSON]: FrontendColumnTypeEnum.TEXT,
  [DbColumnTypeEnum.ARRAY_STR]: FrontendColumnTypeEnum.ARRAY,
  [DbColumnTypeEnum.ARRAY_INT]: FrontendColumnTypeEnum.ARRAY,
  [DbColumnTypeEnum.ARRAY_FLOAT]: FrontendColumnTypeEnum.ARRAY,
};

export const DB_COLUMN_TYPE_TO_QUESTION_MAP: Record<string, QuestionTypeEnum> = {
  [DbColumnTypeEnum.INT]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.BIGINT]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.FLOAT]: QuestionTypeEnum.DECIMAL,
  [DbColumnTypeEnum.STR]: QuestionTypeEnum.TEXT,
  [DbColumnTypeEnum.BOOL]: QuestionTypeEnum.SWITCH,
  [DbColumnTypeEnum.DATE]: QuestionTypeEnum.DATE,
  [DbColumnTypeEnum.TIME]: QuestionTypeEnum.TIME,
  [DbColumnTypeEnum.DATETIME]: QuestionTypeEnum.DATETIME,
  [DbColumnTypeEnum.USER_ID]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.ORG_ID]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.GROUP_ID]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.ROLE_ID]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.GENERAL_ROLE_ID]: QuestionTypeEnum.INTEGER,
  [DbColumnTypeEnum.TIMESTAMP]: QuestionTypeEnum.TIMESTAMP,
  [DbColumnTypeEnum.GEOMETRY]: QuestionTypeEnum.GEOMETRY,
  [DbColumnTypeEnum.JSON]: QuestionTypeEnum.FILE,
  [DbColumnTypeEnum.MEDIA_JSON]: QuestionTypeEnum.FILE,
  [DbColumnTypeEnum.ARRAY_INT]: QuestionTypeEnum.ARRAY,
  [DbColumnTypeEnum.ARRAY_STR]: QuestionTypeEnum.ARRAY,
  [DbColumnTypeEnum.ARRAY_FLOAT]: QuestionTypeEnum.ARRAY,
};

export const CONDITION_NEGATION_MAP: Record<string, string> = {
  [ColumnOperatorEnum.EMPTY]: ColumnOperatorEnum.NOT_EMPTY,
  [ColumnOperatorEnum.NOT_EMPTY]: ColumnOperatorEnum.EMPTY,
  [ColumnOperatorEnum.EQUAL]: ColumnOperatorEnum.NOT_EQUAL,
  [ColumnOperatorEnum.NOT_EQUAL]: ColumnOperatorEnum.EQUAL,
  [ColumnOperatorEnum.CONTAIN]: ColumnOperatorEnum.NOT_CONTAIN,
  [ColumnOperatorEnum.NOT_CONTAIN]: ColumnOperatorEnum.CONTAIN,
  [ColumnOperatorEnum.NE]: ColumnOperatorEnum.EQ,
  [ColumnOperatorEnum.EQ]: ColumnOperatorEnum.NE,
  [ColumnOperatorEnum.GT]: ColumnOperatorEnum.LE,
  [ColumnOperatorEnum.LT]: ColumnOperatorEnum.GE,
  [ColumnOperatorEnum.GE]: ColumnOperatorEnum.LT,
  [ColumnOperatorEnum.LE]: ColumnOperatorEnum.GT,
  [ColumnOperatorEnum.IN]: ColumnOperatorEnum.IN,
  [ColumnOperatorEnum.NOT_IN]: ColumnOperatorEnum.NOT_IN,
};

export const notOperators = [ColumnOperatorEnum.NOT_BETWEEN];
export const notOperatorsMap = {
  [ColumnOperatorEnum.NOT_BETWEEN]: ColumnOperatorEnum.BETWEEN,
};

export let dbColumnTypeDescMap: Record<string, string>;

let COLUMN_OPERATOR_LABEL_MAP: Record<string, string>;
let COLUMN_OPREATOR_FILTER_LABEL_MAP: Record<string, string>;

export let CONSTRAINT_TYPE_LABEL_MAP: Record<string, string>;

export const getDbColumnTypeOptions = () => {
  return _.map({ ...DbColumnTypeEnum }, (value) => ({
    label: value,
    value,
  }));
};

// 获取字段在数据库的类型到中文展示的映射. eg: int -> 数值(整数)
export const getDbColumnTypeLabel = (dbType: string) => {
  return dbColumnTypeDescMap[dbType] || i18n.chain.column.unknown;
};

// 获取数据包质量监控类型到中文展示的映射. eg: column -> 字段监控
export const getConstraintTypeLabel = (constraintType: string, defaultLabel = '') => {
  return CONSTRAINT_TYPE_LABEL_MAP[constraintType] || defaultLabel;
};

// 获取列操作符的类型到中文展示的映射. eg: eq -> 等于
export const getColumnOperatorLabel = (columnOperator: string, defaultLabel = '') => {
  return COLUMN_OPERATOR_LABEL_MAP[columnOperator] || defaultLabel;
};

export const getColumnOperatorFilterLabel = (columnOperator: string, defaultLabel = '') => {
  return COLUMN_OPREATOR_FILTER_LABEL_MAP[columnOperator] || defaultLabel;
};

// 将字段在数据库的类型转换为以表格列形式展示的类型. eg: date/datetime -> datetime
export const transformDbTypeToFrontType = (dbType: string) => {
  return TABLE_HEAD_TYPE_MAP[dbType] || FrontendColumnTypeEnum.TEXT;
};

// 判断是否是整数列
export const isIntColumn = (columnType: string) => {
  return columnType === FieldTypeEnum.INT;
};

// 判断是否是JSON列
export const isJSONColumn = (columnType: string) => {
  return columnType === FieldTypeEnum.JSON;
};

// 判断是否是日期
export const isDataColumn = (columnType: string) => {
  return columnType === FieldTypeEnum.DATE || columnType === FieldTypeEnum.DATETIME;
};

// 判断是否是数组列
export const isArrayColumn = (columnType: string) => {
  return columnType.startsWith('array_');
};

const columnTypeColorMap: Record<string, string> = {
  [DbColumnTypeEnum.STR]: '#6899ef',
  [DbColumnTypeEnum.INT]: '#ec7281',
  [DbColumnTypeEnum.FLOAT]: '#ec7281',
  [DbColumnTypeEnum.BIGINT]: '#ec7281',
  [DbColumnTypeEnum.BOOL]: '#6899ef',
  [DbColumnTypeEnum.DATE]: '#53c99a',
  [DbColumnTypeEnum.TIME]: '#53c99a',
  [DbColumnTypeEnum.DATETIME]: '#53c99a',
  [DbColumnTypeEnum.GEOMETRY]: '#ffbc39',
};
export const getColumnTypeColor = (columnType: string) => {
  return columnTypeColorMap[columnType] || '#6899ef';
};

const columnColorTypeMap: Record<string, DbColumnTypeEnum> = {
  '#6899ef': DbColumnTypeEnum.STR,
  '#ec7281': DbColumnTypeEnum.INT,
  '#53c99a': DbColumnTypeEnum.DATE,
};
export const getColumnTypeByColor = (color: string) => {
  return columnColorTypeMap[color] || DbColumnTypeEnum.STR;
};

// 将字段在数据库的类型转换为前端类型. eg: date/datetime -> date
export const transformDbColumnTypeToFrontend = (dbType: string, defaultType = FrontendColumnTypeEnum.TEXT) => {
  return DB_COLUMN_TYPE_TO_FRONTEND_MAP[dbType] || defaultType;
};

// 将字段在数据库的类型转换为题目类型.
export const transformDbColumnTypeToQuestion = (dbType: string, defaultType = QuestionTypeEnum.TEXT) => {
  return DB_COLUMN_TYPE_TO_QUESTION_MAP[dbType] || defaultType;
};

export const getColumnQueryEmptyItem = (): IColumnQueryItem => {
  return { id: randomUuid() };
};

export const transformDatapkgColumnToOptions = (columns: (IDatapkgColumn & { title?: string })[]) => {
  return _.map(columns, ({ name, type, title }) => ({
    label: title || name,
    value: name,
    columntype: transformDbColumnTypeToFrontend(type),
  }));
};

export const transformColumnQueryToCondition = (queryItem: IColumnQueryItem, needFormat = true) => {
  const { values, column, columntype, operator } = queryItem;
  if (!column || !operator) return;

  const isRange = _.includes(RANGE_OPERATORS, operator);
  const isNumColumnType = columntype === FrontendColumnTypeEnum.NUMBER;
  const validValues = isRange ? values : _.filter(values, (v) => !_.isEmpty(_.trim(v)));
  const needValue = !_.includes(COLUMN_OPERATOR_NO_VALUE_LIST, operator);
  if (_.isEmpty(validValues) && needValue) return;

  const isMultipleVal = _.includes(COLUMN_OPERATOR_EQUAL_OR_NOT, operator);
  if (isMultipleVal && isNumColumnType && needFormat) {
    return { column, operator, values: _.map(validValues, (v) => _.toNumber(v)) };
  }
  return { column, operator, values: validValues };
};

// 可在前端进行变更的后端类型
export const dbColumnModifiableTyps = [
  DbColumnTypeEnum.INT,
  DbColumnTypeEnum.FLOAT,
  DbColumnTypeEnum.STR,
  DbColumnTypeEnum.DATE,
  DbColumnTypeEnum.DATETIME,
  DbColumnTypeEnum.TIMESTAMP,
  DbColumnTypeEnum.BOOL,
  DbColumnTypeEnum.GEOMETRY,
  DbColumnTypeEnum.MEDIA_JSON,
  DbColumnTypeEnum.ARRAY_STR,
  DbColumnTypeEnum.ARRAY_INT,
  DbColumnTypeEnum.ARRAY_FLOAT,
  DbColumnTypeEnum.BIGINT,
  DbColumnTypeEnum.JSON,
  DbColumnTypeEnum.USER_ID,
  DbColumnTypeEnum.ORG_ID,
  DbColumnTypeEnum.GROUP_ID,
  DbColumnTypeEnum.ROLE_ID,
  DbColumnTypeEnum.GENERAL_ROLE_ID,
];

export const getDbColumnModifiableTypeOptions = () => {
  return _.map(dbColumnModifiableTyps, (value) => ({
    label: dbColumnTypeDescMap[value],
    value,
  }));
};

export const isNumberOfDbColumnType = (type: any) => {
  const numTypes = [DbColumnTypeEnum.INT, DbColumnTypeEnum.FLOAT];
  return _.includes(numTypes, type);
};

export let KEY_COLUMN_LABEL_MAP: Record<string, string>;

// 当字段类型为GEOMETRY时 可选的关键字段类型有[KeyColumnEnum.SELECT_GEOMETRY]
const KEY_COLUMN_LABEL_MAP_GEOMETRY = {
  [KeyColumnEnum.SELECT_GEOMETRY]: 'select_geometry',
  [KeyColumnEnum.DISPLAY_GEOMETRY]: 'display_geometry',
  [KeyColumnEnum.LINK_GEOMETRY]: 'link_geometry',
};

let KEY_COLUMN_LABEL_MAP_ID: Record<string, string>;

let KEY_COLUMN_LABEL_MAP_INT: Record<string, string>;

let KEY_COLUMN_LABEL_MAP_STR: Record<string, string>;

let KEY_COLUMN_LABEL_MAP_DATE: Record<string, string>;

let KEY_COLUMN_LABEL_MAP_FLOAT: Record<string, string>;

const typeOptionsMap: Record<string, any> = {
  [DbColumnTypeEnum.GEOMETRY]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_GEOMETRY),
  [DbColumnTypeEnum.INT]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_INT),
  [DbColumnTypeEnum.STR]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_STR),
  [DbColumnTypeEnum.DATE]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_DATE),
  [DbColumnTypeEnum.DATETIME]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_DATE),
  [DbColumnTypeEnum.FLOAT]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_FLOAT),
  [DbColumnTypeEnum.MEDIA_JSON]: () => [],
  [DbColumnTypeEnum.TIMESTAMP]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_INT),
  [DbColumnTypeEnum.USER_ID]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_ID),
  [DbColumnTypeEnum.ORG_ID]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_ID),
  [DbColumnTypeEnum.GROUP_ID]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_ID),
  [DbColumnTypeEnum.ROLE_ID]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_ID),
  [DbColumnTypeEnum.GENERAL_ROLE_ID]: () => getLabelValueOptions(KEY_COLUMN_LABEL_MAP_ID),
};

// 根据字段类型获取可选的关键列表
export const getKeyColumnOptions = (columnType?: DbColumnTypeEnum) => {
  const allOptions = getLabelValueOptions(KEY_COLUMN_LABEL_MAP);
  if (!columnType || !_.includes(dbColumnModifiableTyps, columnType)) return allOptions;
  const partOptions = typeOptionsMap[columnType];
  if (partOptions) return partOptions();

  return [];
};

// 通过操作符列表生产options
export const generateOperatorOptions = (list: any[]) => {
  return _.map(list, (opt) => ({
    label: COLUMN_OPERATOR_LABEL_MAP[opt],
    value: opt,
  }));
};

export const generateOperatorFilterOptions = (list: any[]) => {
  return _.map(list, (opt) => ({
    label: COLUMN_OPREATOR_FILTER_LABEL_MAP[opt],
    value: opt,
  }));
};

const COMMON_OPERATORS = [
  ColumnOperatorEnum.EMPTY,
  ColumnOperatorEnum.NOT_EMPTY,
  ColumnOperatorEnum.EQ,
  ColumnOperatorEnum.NE,
];

// 质量监控会额外添加in
// 列为数字类型对应的操作符列表
const NUMBER_OPERATORS = [
  ...COMMON_OPERATORS,
  ColumnOperatorEnum.GT,
  ColumnOperatorEnum.LT,
  ColumnOperatorEnum.GE,
  ColumnOperatorEnum.LE,
  ColumnOperatorEnum.BETWEEN,
  ColumnOperatorEnum.NOT_BETWEEN,
];
export let COLUMN_NUMBER_OPERATORS_OPTIONS: ILabelValue[];

// 质量监控会额外添加in
// 列为文本类型对应的操作符列表
const TEXT_OPERATORS = [
  ...COMMON_OPERATORS,
  ColumnOperatorEnum.CONTAIN,
  ColumnOperatorEnum.NOT_CONTAIN,
  ColumnOperatorEnum.START_WITH,
  ColumnOperatorEnum.END_WITH,
];
export let COLUMN_TEXT_OPERATORS_OPTIONS: ILabelValue[];

// 列为日期类型对应的操作符列表
const DATE_OPERATORS = [
  ...COMMON_OPERATORS,
  ColumnOperatorEnum.GT,
  ColumnOperatorEnum.LT,
  ColumnOperatorEnum.GE,
  ColumnOperatorEnum.LE,
  ColumnOperatorEnum.BETWEEN,
  ColumnOperatorEnum.NOT_BETWEEN,
];
export let COLUMN_DATE_OPERATORS_OPTIONS: ILabelValue[];

// 列为bool类型对应的操作符列表
const BOOL_OPERATORS = [...COMMON_OPERATORS];
export let COLUMN_BOOL_OPERATORS_OPTIONS: ILabelValue[];

export const getColumnOperatorOptions = (columnType?: string) => {
  const columnTypeOperatorsMap = {
    [FrontendColumnTypeEnum.TEXT]: COLUMN_TEXT_OPERATORS_OPTIONS,
    [FrontendColumnTypeEnum.NUMBER]: COLUMN_NUMBER_OPERATORS_OPTIONS,
    [FrontendColumnTypeEnum.DATE]: COLUMN_DATE_OPERATORS_OPTIONS,
    [FrontendColumnTypeEnum.BOOL]: COLUMN_BOOL_OPERATORS_OPTIONS,
    [FrontendColumnTypeEnum.ARRAY]: COLUMN_MULTIPLE_OPERATORS_OPTIONS,
  };

  return columnType ? columnTypeOperatorsMap[columnType as FrontendColumnTypeEnum] || [] : [];
};

export const getColumnKeyTypeLabel = (keyType: KeyColumnEnum) => {
  return KEY_COLUMN_LABEL_MAP[keyType] || keyType;
};

const mediaJsonRequiredKeys = ['id', 'type', 'value'];
export const isMediaFileColumn = (type: string, val: any) => {
  // 如果是mediajson
  if (DbColumnTypeEnum.MEDIA_JSON === type) {
    return true;
  }
  if (type === DbColumnTypeEnum.JSON && _.isArray(val)) {
    // 如果有id,type,value,则默认符合MEDIA_JSON
    const firstElementKeys = _.keys(val[0]);
    // 检查requiredKeys是否都存在于firstElementKeys中
    return mediaJsonRequiredKeys.every((key) => firstElementKeys.includes(key));
  }
  return false;
};

const datetimeColumns = [
  DbColumnTypeEnum.DATETIME,
  DbColumnTypeEnum.DATE,
  DbColumnTypeEnum.TIMESTAMP,
  DbColumnTypeEnum.TIME,
  // DbColumnTypeEnum.INT,
];
export const isDatetimeColumn = (type: string) => {
  return _.includes(datetimeColumns, type);
};

// 可能后期其他字段也允许设置显示格式
const formatColumns = [
  DbColumnTypeEnum.DATE,
  DbColumnTypeEnum.DATETIME,
  DbColumnTypeEnum.TIMESTAMP,
  DbColumnTypeEnum.TIME,
];
export const isFormatColumn = (type: string) => {
  return _.includes(formatColumns, type);
};

const ID_NAME_COLUMN_TYPES = [
  DbColumnTypeEnum.USER_ID,
  DbColumnTypeEnum.ORG_ID,
  DbColumnTypeEnum.GROUP_ID,
  DbColumnTypeEnum.ROLE_ID,
  DbColumnTypeEnum.GENERAL_ROLE_ID,
];
export const isIdNameColumn = (type: string) => {
  return _.includes(ID_NAME_COLUMN_TYPES, type);
};

// 多选题对应的操作符列表
const MULTIPLE_OPERATORS = [...COMMON_OPERATORS, ColumnOperatorEnum.CONTAIN, ColumnOperatorEnum.NOT_CONTAIN];
export let COLUMN_MULTIPLE_OPERATORS_OPTIONS: ILabelValue[];

export const getMultipleSelectOptions = () => COLUMN_MULTIPLE_OPERATORS_OPTIONS;

export const initColumnLocale = () => {
  dbColumnTypeDescMap = {
    [DbColumnTypeEnum.INT]: i18n.chain.column.type.int,
    [DbColumnTypeEnum.BIGINT]: i18n.chain.column.type.bigint,
    [DbColumnTypeEnum.FLOAT]: i18n.chain.column.type.float,
    [DbColumnTypeEnum.STR]: i18n.chain.column.type.str,
    [DbColumnTypeEnum.BOOL]: i18n.chain.column.type.bool,
    [DbColumnTypeEnum.DATE]: i18n.chain.column.type.date,
    [DbColumnTypeEnum.TIME]: i18n.chain.column.type.time,
    [DbColumnTypeEnum.DATETIME]: i18n.chain.column.type.dateTime,
    [DbColumnTypeEnum.TIMESTAMP]: i18n.chain.column.type.timeStemp,
    [DbColumnTypeEnum.GEOMETRY]: i18n.chain.column.type.geometry,
    [DbColumnTypeEnum.JSON]: i18n.chain.column.type.json,
    [DbColumnTypeEnum.MEDIA_JSON]: i18n.chain.column.type.mediaJson,
    [DbColumnTypeEnum.ARRAY_STR]: i18n.chain.column.type.arrayStr,
    [DbColumnTypeEnum.ARRAY_INT]: i18n.chain.column.type.arrayInt,
    [DbColumnTypeEnum.ARRAY_FLOAT]: i18n.chain.column.type.arrayFloat,
    [DbColumnTypeEnum.USER_ID]: i18n.chain.column.type.userId,
    [DbColumnTypeEnum.ORG_ID]: i18n.chain.column.type.orgId,
    [DbColumnTypeEnum.GROUP_ID]: i18n.chain.column.type.groupId,
    [DbColumnTypeEnum.ROLE_ID]: i18n.chain.column.type.roleId,
    [DbColumnTypeEnum.GENERAL_ROLE_ID]: i18n.chain.column.type.generalRoleId,
  };

  CONSTRAINT_TYPE_LABEL_MAP = {
    [ConstraintTypeEnum.COLUMN]: i18n.chain.column.constraint.column,
    [ConstraintTypeEnum.FORMULA]: i18n.chain.column.constraint.formula,
    [ConstraintTypeEnum.SQL]: i18n.chain.column.constraint.sql,
  };

  KEY_COLUMN_LABEL_MAP = {
    [KeyColumnEnum.ID]: i18n.chain.column.enum.id,
    [KeyColumnEnum.SELECT_GEOMETRY]: i18n.chain.column.enum.selectGeometry,
    [KeyColumnEnum.DISPLAY_GEOMETRY]: i18n.chain.column.enum.displayGeometry,
    [KeyColumnEnum.LINK_GEOMETRY]: i18n.chain.column.enum.linkGeometry,
    [KeyColumnEnum.NAME]: i18n.chain.column.enum.name,
    [KeyColumnEnum.ADDRESS]: i18n.chain.column.enum.address,
    [KeyColumnEnum.UPDATE_TIME]: i18n.chain.column.enum.updateTime,
    [KeyColumnEnum.LNG]: i18n.chain.column.enum.lng,
    [KeyColumnEnum.LAT]: i18n.chain.column.enum.lat,
  };

  KEY_COLUMN_LABEL_MAP_ID = {
    [KeyColumnEnum.ID]: i18n.chain.column.enum.id,
  };

  KEY_COLUMN_LABEL_MAP_INT = {
    [KeyColumnEnum.ID]: i18n.chain.column.enum.id,
    [KeyColumnEnum.NAME]: i18n.chain.column.enum.name,
  };

  KEY_COLUMN_LABEL_MAP_STR = {
    [KeyColumnEnum.ID]: i18n.chain.column.enum.id,
    [KeyColumnEnum.NAME]: i18n.chain.column.enum.name,
    [KeyColumnEnum.ADDRESS]: i18n.chain.column.enum.address,
  };

  KEY_COLUMN_LABEL_MAP_DATE = {
    [KeyColumnEnum.UPDATE_TIME]: i18n.chain.column.enum.updateTime,
  };

  KEY_COLUMN_LABEL_MAP_FLOAT = {
    [KeyColumnEnum.LNG]: i18n.chain.column.enum.lng,
    [KeyColumnEnum.LAT]: i18n.chain.column.enum.lat,
  };

  COLUMN_OPERATOR_LABEL_MAP = {
    [ColumnOperatorEnum.EMPTY]: i18n.chain.column.operator.empty,
    [ColumnOperatorEnum.NULL]: i18n.chain.column.operator.empty,
    [ColumnOperatorEnum.NOT_EMPTY]: i18n.chain.column.operator.notEmpty,
    [ColumnOperatorEnum.NOT_NULL]: i18n.chain.column.operator.notEmpty,
    [ColumnOperatorEnum.EQ]: i18n.chain.column.operator.eq,
    [ColumnOperatorEnum.NE]: i18n.chain.column.operator.ne,
    // 文本（等于、不等于）
    [ColumnOperatorEnum.EQUAL]: i18n.chain.column.operator.equal,
    [ColumnOperatorEnum.NOT_EQUAL]: i18n.chain.column.operator.notEqual,
    [ColumnOperatorEnum.GT]: i18n.chain.column.operator.gt,
    [ColumnOperatorEnum.GE]: i18n.chain.column.operator.ge,
    [ColumnOperatorEnum.LT]: i18n.chain.column.operator.lt,
    [ColumnOperatorEnum.LE]: i18n.chain.column.operator.le,
    [ColumnOperatorEnum.CONTAIN]: i18n.chain.column.operator.contain,
    [ColumnOperatorEnum.NOT_CONTAIN]: i18n.chain.column.operator.notContain,
    [ColumnOperatorEnum.START_WITH]: i18n.chain.column.operator.startWith,
    [ColumnOperatorEnum.END_WITH]: i18n.chain.column.operator.endWith,
    [ColumnOperatorEnum.IN]: i18n.chain.column.operator.in,
    [ColumnOperatorEnum.NOT_IN]: i18n.chain.column.operator.notIn,
    [ColumnOperatorEnum.BETWEEN]: i18n.chain.column.operator.between,
    [ColumnOperatorEnum.NOT_BETWEEN]: i18n.chain.column.operator.notBetween,
  };

  COLUMN_OPREATOR_FILTER_LABEL_MAP = {
    [ColumnOperatorFilterEnum.EQ]: i18n.chain.column.operator.equal,
    [ColumnOperatorFilterEnum.NE]: i18n.chain.column.operator.notEqual,
    [ColumnOperatorFilterEnum.GT]: i18n.chain.column.operator.gt,
    [ColumnOperatorFilterEnum.GE]: i18n.chain.column.operator.ge,
    [ColumnOperatorFilterEnum.LT]: i18n.chain.column.operator.lt,
    [ColumnOperatorFilterEnum.LE]: i18n.chain.column.operator.le,
    [ColumnOperatorFilterEnum.IS]: i18n.chain.column.operator.is,
    [ColumnOperatorEnum.CONTAIN]: i18n.chain.column.operator.contain,
    [ColumnOperatorFilterEnum.START_WITH]: i18n.chain.column.operator.startWith,
    [ColumnOperatorFilterEnum.END_WITH]: i18n.chain.column.operator.endWith,
    [ColumnOperatorFilterEnum.IN]: i18n.chain.column.operator.in,
    [ColumnOperatorFilterEnum.BETWEEN]: i18n.chain.column.operator.between,
    [ColumnOperatorFilterEnum.LIKE]: i18n.chain.column.operator.like,
    [ColumnOperatorFilterEnum.ILIKE]: i18n.chain.column.operator.ilike,
    [ColumnOperatorFilterEnum.MATCH]: i18n.chain.column.operator.match,
    [ColumnOperatorFilterEnum.INTERSECT]: i18n.chain.column.operator.intersect,
    [ColumnOperatorFilterEnum.SUPERSET]: i18n.chain.column.operator.superset,
    [ColumnOperatorFilterEnum.SUBSET]: i18n.chain.column.operator.subset,
  };

  COLUMN_NUMBER_OPERATORS_OPTIONS = generateOperatorOptions(NUMBER_OPERATORS);
  COLUMN_TEXT_OPERATORS_OPTIONS = generateOperatorOptions(TEXT_OPERATORS);
  COLUMN_DATE_OPERATORS_OPTIONS = generateOperatorOptions(DATE_OPERATORS);
  COLUMN_BOOL_OPERATORS_OPTIONS = generateOperatorOptions(BOOL_OPERATORS);
  COLUMN_MULTIPLE_OPERATORS_OPTIONS = generateOperatorOptions(MULTIPLE_OPERATORS);
};

const numberCompInfo = { type: 'number', component: 'NumberPicker' };
// 后端数据类型对应的ui组件信息（流程引擎我发起的与待我审批中的高级筛选）
const dbTypeToCompInfoMap: any = {
  [DbColumnTypeEnum.STR]: { type: 'string', component: 'Input' },
  [DbColumnTypeEnum.INT]: numberCompInfo,
  [DbColumnTypeEnum.FLOAT]: numberCompInfo,
  [DbColumnTypeEnum.BOOL]: { type: 'boolean', component: 'Switch' },
};

export const getCompInfoByDbType = (dbType: string) => {
  return dbTypeToCompInfoMap[dbType] || dbTypeToCompInfoMap[DbColumnTypeEnum.STR];
};

// 在协作编辑和数据包数据查询处 equal的作用等同于in（不支持in）
export const getListOperatorOptionsForOldMode = () => {
  return [
    { label: i18n.chain.column.operator.in, value: ColumnOperatorEnum.EQUAL },
    { label: i18n.chain.column.operator.notIn, value: ColumnOperatorEnum.NOT_EQUAL },
  ];
};

export const getSystemColumnTitle = (columnTitle: string) => {
  return `${columnTitle}`;
};
