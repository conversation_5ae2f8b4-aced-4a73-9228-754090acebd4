import _ from 'lodash';
import urlParameterAppend from 'url-parameter-append';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { getDecompressValueFromUrl, getParamValueFromUrl, saveCompressValueToUrl } from '@mdtBsComm/utils/urlUtil';
import type { ICfs, IStoreToken } from '../interfaces';

interface ITaggroup {
  taggroupId: string;
  tag: string;
}

// 跨产品之间url参数传递统一使用q对象
const URL_Q_KEY = 'q';

/**
 * id 作为通信的字段标识
 * __out_url 如果存在，则优先跳转该地址
 * __resource_id 内置跳转用到的变量
 */
interface IPassThrough {
  id: string;
  __out_url?: string;
  __product?: string;
  __resource_id?: string;
  [key: string]: any;
}
interface IQKey extends ITaggroup {
  headerless: boolean;
  sidemenuless: boolean;
  menuonheader: boolean;
  rootFontSize: number;
  dmCloudFlag: boolean;
  theme: string;
  language: string;
  passThrough: IPassThrough;
  tk: string;
  itk: string;
  imperApp: number;
  detailModule: string;
  pkgId: string;
  page: string;
  activeTab: string;
  wfId: string;
  wfName: string;
  formValues: object;
  desingHeaderLess: boolean;
  enableSchemaSelect: boolean;
  platform: string;
  appId: number;
  redirectIfNoProductPermission: ICfs['redirectIfNoProductPermission'];
  assignWfId: string;
  formId: string;
  rootWfId: string;
  fromPage: string;
  wfUserTaskId: string;
  wfSpecifiedRootXmlId: string;
  showNickname: boolean;
}

let urlQ: Partial<IQKey> = {};

type IQType = typeof urlQ;

export const getQFromUrl = (): IQType => {
  const val = getDecompressValueFromUrl(URL_Q_KEY);
  return parseStrToObj(val);
};

export const saveQToUrl = (q: IQType): string => {
  const val = saveCompressValueToUrl(q);
  return `${URL_Q_KEY}=${val}`;
};

export const modifyParamsOfUrl = (url: string, ...args: any[]) => {
  return urlParameterAppend(url, ...args);
};

export const modifyUrlQ = (q: IQType = {}) => {
  urlQ = q;
};

export const getDmCloudFlagFromUrl = (): boolean => {
  return urlQ.dmCloudFlag === true;
};

export const getThemeFromUrl = (): IQType['theme'] => {
  return urlQ.theme;
};

export const getLanguageFromUrl = (): IQType['language'] => {
  return urlQ.language;
};

export const getHeaderlessFromUrl = (): IQType['headerless'] => {
  return urlQ.headerless;
};

export const getSidemenulessFromUrl = (): IQType['sidemenuless'] => {
  return urlQ.sidemenuless;
};

export const getMenuonheaderFromUrl = (): IQType['menuonheader'] => {
  return urlQ.menuonheader;
};

export const getRootFontSizeFromUrl = (): IQType['rootFontSize'] => {
  const fs = urlQ.rootFontSize ?? 16;
  return Math.max(fs, 10);
};

export const getPlatformFromUrl = (): IQType['platform'] => {
  return urlQ.platform;
};

export const getPassThroughFromUrl = () => {
  if (urlQ.passThrough && !_.isEmpty(urlQ.passThrough)) {
    return urlQ.passThrough;
  }
  return { id: '' };
};

export const getTokenFromUrl = (): IStoreToken => {
  return {
    tk: urlQ.tk,
    itk: urlQ.itk,
  };
};

export const getImperAppFromUrl = (): IQType['imperApp'] => {
  return urlQ.imperApp;
};

export const getDetailModuleFromUrl = (): IQType['detailModule'] => {
  return urlQ.detailModule;
};

export const getPkgIdFromUrl = (): IQType['pkgId'] => {
  return urlQ.pkgId;
};

export const getPageFromUrl = (): IQType['page'] => {
  return urlQ.page;
};

export const getThemeTagFromUrl = () => {
  return _.pick(urlQ, ['taggroupId', 'tag']) as ITaggroup;
};

export const getActiveTabFromUrl = (): IQType['activeTab'] => {
  return urlQ.activeTab;
};

export const getWfIdFromUrl = (): IQType['wfId'] => {
  return urlQ.wfId;
};

export const getWfNameFromUrl = (): IQType['wfName'] => {
  return urlQ.wfName;
};

export const getFormValuesFromUrl = (): IQType['formValues'] => {
  return urlQ.formValues;
};

export const getAppIdFromUrl = (): IQType['appId'] => {
  return urlQ.appId;
};

export const getRedirectIfNoProductPermissionFromUrl = (): IQType['redirectIfNoProductPermission'] => {
  return urlQ.redirectIfNoProductPermission;
};

export const getAssignWfIdFromUrl = (): IQType['assignWfId'] => {
  return urlQ.assignWfId || getParamValueFromUrl('assignWfId');
};

export const getWorkflowUserTaskIdFromUrl = (): IQType['wfUserTaskId'] => {
  return urlQ.wfUserTaskId || getParamValueFromUrl('wfUserTaskId');
};

export const getWorkflowSpecifiedRootXmlIdFromUrl = (): IQType['wfSpecifiedRootXmlId'] => {
  return urlQ.wfSpecifiedRootXmlId || getParamValueFromUrl('wfSpecifiedRootXmlId');
};

export const getFormIdFromUrl = (): IQType['formId'] => {
  return urlQ.formId;
};

export const getRootWfIdFromUrl = (): IQType['rootWfId'] => {
  return urlQ.rootWfId || getParamValueFromUrl('rootWfId');
};

export const getFromPageFromUrl = (): IQType['fromPage'] => {
  return urlQ.fromPage;
};

export const getShowNicknameFromUrl = (): IQType['showNickname'] => {
  return urlQ.showNickname;
};
