import React, { FC, ReactNode, useState } from 'react';
import { VisibilityOff1, VisibilityOn } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Typography } from '@metroDesign/typography';

// 预定义的常用正则表达式模式
export const MaskPatterns = {
  // 手机号: 保留前3位和后4位
  PHONE: '^(\\d{3})(\\d{4})(\\d{4})$',
  // 手机号: 仅保留前3位和后2位
  PHONE_STRICT: '^(\\d{3})(\\d{6})(\\d{2})$',
  // 邮箱: 保留用户名首尾字符和域名
  EMAIL: '^(\\w)([\\w.-]+)(\\w@[\\w.-]+)$',
  // 身份证: 保留前6位和后4位
  ID_CARD: '^(\\d{6})(\\d{8})(\\d{4})$',
  // 银行卡: 保留前6位和后4位
  BANK_CARD: '^(\\d{6})(\\d+)(\\d{4})$',
  // 姓名: 保留姓氏，隐藏名字
  NAME_CN: '^(.)(.+)$',
  // 地址: 保留前6个字符和后6个字符
  ADDRESS: '^(.{6})(.*)(.{6})$',
  // 信用卡: 仅显示后4位
  CREDIT_CARD: '^()(\\d+)(\\d{4})$',
};

// 预定义的掩码类型
export type MaskPatternType = keyof typeof MaskPatterns | string;

export interface ISecretPreviewProps {
  /** 需要隐藏/显示的值 */
  value: string;
  /** 初始是否可见，默认为false */
  defaultVisible?: boolean;
  /** 受控的可见状态 */
  visible?: boolean;
  /** 可见性变化时的回调 */
  onVisibleChange?: (visible: boolean) => void;
  /** 替换字符，默认为'*' */
  maskChar?: string;
  /** 最大显示的替换字符数量，默认为11 */
  maxMaskLength?: number;
  /** 最大显示的字符数量，默认为6 */
  limitLength?: number;
  /** 自定义替换规则函数，传入原始值返回替换后的值 */
  maskFormatter?: (value: string) => string;
  /** 自定义显示图标 */
  visibleIcon?: ReactNode;
  /** 自定义隐藏图标 */
  invisibleIcon?: ReactNode;
  /** 是否显示切换按钮，默认为true */
  showToggle?: boolean;
  /** 文本样式 */
  textStyle?: React.CSSProperties;
  /** 隐藏文本样式 */
  maskedTextStyle?: React.CSSProperties;
  /** 按钮属性 */
  buttonProps?: Record<string, any>;
  /** 掩码模式：使用预定义的正则或自定义正则 */
  maskPattern?: MaskPatternType;
  /** 自定义正则脱敏规则，传入正则字符串 */
  maskRegExp?: string;
}

const SecretPreview: FC<ISecretPreviewProps> = ({
  value,
  defaultVisible = false,
  visible: controlledVisible,
  onVisibleChange,
  maskChar = '*',
  maxMaskLength = 11,
  limitLength = 6,
  maskFormatter,
  visibleIcon = <VisibilityOn />,
  invisibleIcon = <VisibilityOff1 />,
  showToggle = true,
  textStyle,
  maskedTextStyle = { fontFamily: 'monospace' },
  buttonProps = { size: 'small', ghost: true, focus: false },
  maskPattern,
  maskRegExp,
}) => {
  // 支持受控和非受控模式
  const [internalVisible, setInternalVisible] = useState(defaultVisible);
  const isVisible = controlledVisible !== undefined ? controlledVisible : internalVisible;

  const handleToggleVisible = () => {
    const newVisible = !isVisible;
    setInternalVisible(newVisible);
    onVisibleChange?.(newVisible);
  };

  // 获取实际使用的正则表达式
  const getRegExpPattern = () => {
    // 优先使用maskPattern
    if (maskPattern) {
      // 如果是预定义模式，则使用对应的正则表达式
      if (MaskPatterns[maskPattern as keyof typeof MaskPatterns]) {
        return MaskPatterns[maskPattern as keyof typeof MaskPatterns];
      }
      // 否则作为自定义正则表达式使用
      return maskPattern;
    }
    // 兼容旧的maskRegExp属性
    return maskRegExp;
  };

  // 辅助函数：根据正则和 maskChar 进行脱敏（感觉有问题？）
  const maskByRegExp = (value: string, regExpStr: string, maskChar: string) => {
    try {
      // 创建正则表达式对象
      const regex = new RegExp(regExpStr);

      // 检查是否包含捕获组
      const hasGroups = /\([^?]/.test(regExpStr);

      if (hasGroups) {
        // 如果有捕获组，只对第二个捕获组内容进行脱敏(保留第一和第三组)
        return value.replace(regex, (match, ...groups) => {
          // 过滤掉最后两个参数(match索引和原始字符串)
          const actualGroups = groups.filter((_, i) => i < groups.length - 2);

          // 构建结果字符串
          let result = '';
          actualGroups.forEach((group, i) => {
            if (i === 1 && group !== undefined) {
              // 第二个捕获组(索引1)进行脱敏
              result += maskChar.repeat(group.length);
            } else if (group !== undefined) {
              // 其他捕获组保持不变
              result += group;
            }
          });

          return result;
        });
      } else {
        // 如果没有捕获组，将整个匹配部分替换为掩码
        return value.replace(regex, (match) => maskChar.repeat(match.length));
      }
    } catch (error) {
      console.error('Invalid regular expression:', error);
      return undefined;
    }
  };

  const displayContent = () => {
    if (isVisible) {
      return value;
    }
    if (maskFormatter) {
      return maskFormatter(value);
    }

    const regExpPattern = getRegExpPattern();
    if (regExpPattern) {
      const masked = maskByRegExp(value, regExpPattern, maskChar);
      if (masked !== undefined) return masked;
    }

    const maskLength = Math.min(limitLength, maxMaskLength);
    return maskChar.repeat(maskLength);
  };

  return (
    <Flex align="center" gap={4} style={{ height: '100%' }}>
      <Typography.Text style={isVisible ? textStyle : { ...maskedTextStyle, ...textStyle }}>
        {displayContent()}
      </Typography.Text>
      {showToggle && (
        <Button onlyIcon onClick={handleToggleVisible} {...buttonProps}>
          {isVisible ? visibleIcon : invisibleIcon}
        </Button>
      )}
    </Flex>
  );
};

export default SecretPreview;
