import { getDescDefaultTmpl, getNameDefaultTmpl } from '../../../utils/wfTmplUtil';

// 新增：默认流程请求配置
const DEFAULT_WORKFLOW_CONFIG = JSON.stringify(
  {
    internal_request: {
      type: 'spec_owner',
      allow_missing: true,
    },
  },
  null,
  2,
);

export const DEFAULT_EMPTY_XML = `<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:duckflow="https://duckflow.app" id="blank-diagram" targetNamespace="https://duckflow.app/schema/bpmn" exporter="duckflow (https://duckflow.app)" exporterVersion="0.13.0" duckflow:template="blank">
  <process id="Process_1" isExecutable="true" executable="true">
    <startEvent id="StartEvent_1" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="412" y="240" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
`;

/**
 *<timerEventDefinition id="TimerEventDefinition_0of5cn2">
 *  <timeCycle xsi:type="tFormalExpression" mdt:format="crontab"/>
 *</timerEventDefinition>
 */
export const getBpmnDefaultXml = () => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:duckflow="https://duckflow.app" id="blank-diagram" targetNamespace="https://duckflow.app/schema/bpmn" exporter="duckflow (https://duckflow.app)" exporterVersion="0.13.0" duckflow:template="blank">
  <process id="Process_1" isExecutable="true" executable="true">
    <extensionElements>
      <mdt:workflowName>${getNameDefaultTmpl()}</mdt:workflowName>
      <mdt:workflowDescription>${getDescDefaultTmpl()}</mdt:workflowDescription>
      <mdt:WorkflowConfig>${DEFAULT_WORKFLOW_CONFIG}</mdt:WorkflowConfig>
    </extensionElements>
    <startEvent id="StartEvent_1" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="412" y="240" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
`;
};
