/**
 * This is a sample file that should be replaced with the actual translation.
 *
 * Checkout https://github.com/bpmn-io/bpmn-js-i18n for a list of available
 * translations and labels to translate.
 */
const en: Record<string, string> = {
  'Activate the create/remove space tool': 'Activate the create/remove space tool',
  'Activate the global connect tool': 'Activate the global connect tool',
  'Activate the hand tool': 'Activate the hand tool',
  'Activate the lasso tool': 'Activate the lasso tool',
  'Ad-hoc': 'Ad-hoc',
  'Add Lane above': 'Add Lane above',
  'Add Lane below': 'Add Lane below',
  'Append ConditionIntermediateCatchEvent': 'Append ConditionIntermediateCatchEvent',
  'Append EndEvent': 'Append EndEvent',
  'Append Gateway': 'Append Gateway',
  'Append Intermediate/Boundary Event': 'Append Intermediate/Boundary Event',
  'Append MessageIntermediateCatchEvent': 'Append MessageIntermediateCatchEvent',
  'Append ReceiveTask': 'Append ReceiveTask',
  'Append SignalIntermediateCatchEvent': 'Append SignalIntermediateCatchEvent',
  'Append Task': 'Append Task',
  'Append TimerIntermediateCatchEvent': 'Append TimerIntermediateCatchEvent',
  'Append compensation activity': 'Append compensation activity',
  'Append TextAnnotation': 'Append TextAnnotation',
  'Append element': 'Append element',
  'Business Rule Task': 'Business Rule Task',
  'Boundary Event': 'Boundary Event',
  'Call Activity': 'Call Activity',
  'Cancel Boundary Event': 'Cancel Boundary Event',
  'Cancel End Event': 'Cancel End Event',
  'Change element': 'Change element',
  'Change type': 'Change type',
  Collection: 'Collection',
  'Compensation Boundary Event': 'Compensation Boundary Event',
  'Compensation End Event': 'Compensation End Event',
  'Compensation Intermediate Throw Event': 'Compensation Intermediate Throw Event',
  'Compensation Start Event': 'Compensation Start Event',
  'Complex Gateway': 'Complex Gateway',
  'Conditional Boundary Event': 'Conditional Boundary Event',
  'Conditional Boundary Event (non-interrupting)': 'Conditional Boundary Event (non-interrupting)',
  'Conditional Intermediate Catch Event': 'Conditional Intermediate Catch Event',
  'Conditional Start Event': 'Conditional Start Event',
  'Conditional Start Event (non-interrupting)': 'Conditional Start Event (non-interrupting)',
  'Connect using Association': 'Connect using Association',
  'Connect using DataInputAssociation': 'Connect using DataInputAssociation',
  'Connect using Sequence/MessageFlow or Association': 'Connect using Sequence/MessageFlow or Association',
  'Create DataObjectReference': 'Create DataObjectReference',
  'Create DataStoreReference': 'Create DataStoreReference',
  'Create element': 'Create element',
  'Create EndEvent': 'Create EndEvent',
  'Create expanded SubProcess': 'Create expanded SubProcess',
  'Create Gateway': 'Create Gateway',
  'Create Group': 'Create Group',
  'Create Intermediate/Boundary Event': 'Create Intermediate/Boundary Event',
  'Create Pool/Participant': 'Create Pool/Participant',
  'Create StartEvent': 'Create StartEvent',
  'Create Task': 'Create Task',
  Data: 'Data',
  'Data Object Reference': 'Data Object Reference',
  'Data Store Reference': 'Data Store Reference',
  'Default Flow': 'Default Flow',
  'Divide into three Lanes': 'Divide into three Lanes',
  'Divide into two Lanes': 'Divide into two Lanes',
  'Empty Pool': 'Empty Pool',
  'Empty Pool (removes content)': 'Empty Pool (removes content)',
  'End Event': 'End Event',
  'Error Boundary Event': 'Error Boundary Event',
  'Error End Event': 'Error End Event',
  'Error Start Event': 'Error Start Event',
  'Escalation Boundary Event': 'Escalation Boundary Event',
  'Escalation Boundary Event (non-interrupting)': 'Escalation Boundary Event (non-interrupting)',
  'Escalation End Event': 'Escalation End Event',
  'Escalation Intermediate Throw Event': 'Escalation Intermediate Throw Event',
  'Escalation Start Event': 'Escalation Start Event',
  'Escalation Start Event (non-interrupting)': 'Escalation Start Event (non-interrupting)',
  Events: 'Events',
  'Event Sub Process': 'Event Sub Process',
  'Event based Gateway': 'Event based Gateway',
  'Exclusive Gateway': 'Exclusive Gateway',
  'Expanded Pool': 'Expanded Pool',
  Gateways: 'Gateways',
  'Inclusive Gateway': 'Inclusive Gateway',
  'Intermediate Throw Event': 'Intermediate Throw Event',
  'Link Intermediate Catch Event': 'Link Intermediate Catch Event',
  'Link Intermediate Throw Event': 'Link Intermediate Throw Event',
  Loop: 'Loop',
  'Manual Task': 'Manual Task',
  'Message Boundary Event': 'Message Boundary Event',
  'Message Boundary Event (non-interrupting)': 'Message Boundary Event (non-interrupting)',
  'Message End Event': 'Message End Event',
  'Message Intermediate Catch Event': 'Message Intermediate Catch Event',
  'Message Intermediate Throw Event': 'Message Intermediate Throw Event',
  'Message Start Event': 'Message Start Event',
  'Message Start Event (non-interrupting)': 'Message Start Event (non-interrupting)',
  'Parallel Gateway': 'Parallel Gateway',
  'Parallel Multi Instance': 'Parallel Multi Instance',
  Participants: 'Participants',
  'Receive Task': 'Receive Task',
  Remove: 'Remove',
  'Script Task': 'Script Task',
  'Send Task': 'Send Task',
  'Sequence Flow': 'Sequence Flow',
  'Sequential Multi Instance': 'Sequential Multi Instance',
  'Service Task': 'Service Task',
  'Signal Boundary Event': 'Signal Boundary Event',
  'Signal Boundary Event (non-interrupting)': 'Signal Boundary Event (non-interrupting)',
  'Signal End Event': 'Signal End Event',
  'Signal Intermediate Catch Event': 'Signal Intermediate Catch Event',
  'Signal Intermediate Throw Event': 'Signal Intermediate Throw Event',
  'Signal Start Event': 'Signal Start Event',
  'Signal Start Event (non-interrupting)': 'Signal Start Event (non-interrupting)',
  'Start Event': 'Start Event',
  'Sub Process': 'Sub Process',
  'Sub Processes': 'Sub Processes',
  'Sub Process (collapsed)': 'Sub Process (collapsed)',
  'Sub Process (expanded)': 'Sub Process (expanded)',
  Task: 'Task',
  Tasks: 'Tasks',
  'Terminate End Event': 'Terminate End Event',
  'Timer Boundary Event': 'Timer Boundary Event',
  'Timer Boundary Event (non-interrupting)': 'Timer Boundary Event (non-interrupting)',
  'Timer Intermediate Catch Event': 'Timer Intermediate Catch Event',
  'Timer Start Event (non-interrupting)': 'Timer Start Event (non-interrupting)',
  'Timer Start Event': 'Timer Start Event',
  Transaction: 'Transaction',
  'User Task': 'User Task',
  'already rendered {element}': 'already rendered {element}',
  'diagram not part of bpmn:Definitions': 'diagram not part of bpmn:Definitions',
  'element required': 'element required',
  'element {element} referenced by {referenced}#{property} not yet drawn':
    'element {element} referenced by {referenced}#{property} not yet drawn',
  'failed to import {element}': 'failed to import {element}',
  'flow elements must be children of pools/participants': 'flow elements must be children of pools/participants',
  'more than {count} child lanes': 'more than {count} child lanes',
  'no diagram to display': 'no diagram to display',
  'no parent for {element} in {parent}': 'no parent for {element} in {parent}',
  'no process or collaboration to display': 'no process or collaboration to display',
  'no shape type specified': 'no shape type specified',
  'out of bounds release': 'out of bounds release',
  General: 'General',
  ID: 'ID',
  Name: 'Name',
  Documentation: 'Documentation',
  'Element documentation': 'Element documentation',
  'Participant Name': 'Participant Name',
  'Participant ID': 'Participant ID',
  'Process ID': 'Process ID',
  'Process name': 'Process name',
  'Process documentation': 'Process documentation',
  Executable: 'Executable',
  'Multiple elements are selected. Select a single element to edit its properties.':
    'Multiple elements are selected. Select a single element to edit its properties.',
  'Form Spec': 'Form Spec',
  'JSON Schema': 'JSON Schema',
  'Formily Schema': 'Formily1.0',
  Formily_V2: 'Formily2.0',
  Spec: 'Spec',
  'Extension properties': 'Extension properties',
  Value: 'Value',
  '<none>': '<none>',
  Script: 'Script',
  Expression: 'Expression',
  Type: 'Type',
  'Inline script': 'Inline script',
  'Dynamic Assignees': 'Dynamic Assignees',
  'Script type': 'Script type',
  'Normal end': 'Normal end',
  'Approval end': 'Approval end',
  'Approval Approved': 'Approval Approved',
  'Approval Rejected': 'Approval Rejected',
  'Event type': 'Event type',
  'Task type': 'Task type',
  'User assignment': 'User assignment',
  CandidateStarter: 'Candidate Starter',
  Condition: 'Condition',
  Service: 'Service',
  'Result variable': 'Result variable',
  auth: 'Auth',
  jingan_datamarket: 'Jingan Datamarket',
  'Service name': 'Service name',
  grant_resource_to_users: 'Grant Resource To Users',
  extract_applicant_info: 'Extract Applicant Info',
  extract_datapkg_info: 'Extract Datapkg Info',
  'Function name': 'Function Name',
  'Function params': 'Function Params',
  'Normal task': 'Normal Task',
  'Form task': 'Form Task',
  'Approval task': 'Approval Task',
  Assignment: 'Assignment',
  datamap_v2: 'Datlas',
  general: 'General',
  huangpu_patrol: 'Huangpu Patrol',
  send_notice: 'Send Notice',
  insert_record_to_datapkg: 'Insert Record To Datapkg',
  update_record_to_datapkg: 'Update Record To Datapkg',
  query_datapkg_data: 'Query Datapkg Data',
  request_api: 'Request Api',
  extract_station_master_roles: 'Extract Station Master Roles',
  apply_authorization_by_datapkg_id: 'Apply Authorization By Datapkg Id',
  send_dingtalk_webhook: 'Send dingtalk',
  send_email: 'Send email',
  send_entwx_webhook: 'Send entwx webhook',
  override_workflow_form_data: 'Override form data',
  WorkflowName: 'Title template',
  WorkflowDesc: 'Description template',
  'Workflow Detail': 'Detail page',
  'Detail Page': 'Detail page',
  template: 'Template',
  wfNameTmplPlaceholder: 'eg：The application of {{Initiator Name}}',
  wfDescTmplPlaceholder: 'eg：Holiday start time:{{startTime}}; Holiday end time:{{endTime}}',
  workflowTmpl: 'Workflow Instance',
  appUsers: 'Self App Users',
  loginUsers: 'All Datlas Users',
  linkUsers: 'All Has Link Users',
  assignUsers: 'Assign Users',
  isAnonymity: 'Is Anonymity',
  'Global Variables': 'Variables Manage',
  Setting: 'Setting',
  DataObjectRef: 'Data Object Ref',
  'Select Data Object Ref': 'Select Data Object Ref',
  Timer: 'Time',
  timerDate: 'Date',
  timerDuration: 'Duration',
  timerCycle: 'Cycle',
  crontab: 'Crontab',
  'Max cycles': 'Max cycles',
  'End time': 'End time',
  'Global Variables Setting': 'Global Variables Setting',
  'Complete Threshold': 'Complete threshold',
  normal: 'Normal',
  approval: 'Approval',
  'Gateway type': 'Gateway type',
  'Terminate If Rejected': 'Terminate if someone rejected',
  'Terminate If Rejected2': 'Terminate flow if rejected task',
  config: 'Config',
  fromSource: 'From Resource',
  multiInstance: 'Multi Instance',
  callActivity: 'Child Workflow',
  standardLoop: 'Standard Loop',
  'After Submit': 'After Submit',
  'Default values': 'Default values',
  send_entwx_notice: 'Send entwx notice',
  insert_multi_records_to_datapkg: 'Insert multi records to datapkg',
  update_multi_records_to_datapkg: 'Update multi records to datapkg',
  get_user_name: 'Get user name',
  get_user_role: 'Get user role',
  get_users_relation: 'Get users relation',
  'Workflow Config': 'Workflow Request Config',
  'Service Config': 'Service Request Config',
};
export default en;
