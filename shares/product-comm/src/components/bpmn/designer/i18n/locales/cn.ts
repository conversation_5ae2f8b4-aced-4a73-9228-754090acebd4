/**
 * This is a sample file that should be replaced with the actual translation.
 *
 * Checkout https://github.com/bpmn-io/bpmn-js-i18n for a list of available
 * translations and labels to translate.
 */
const cn: Record<string, string> = {
  'Activate the create/remove space tool': '创建/删除空间',
  'Activate the global connect tool': '连线',
  'Activate the hand tool': '拖拽模式',
  'Activate the lasso tool': '框选模式',
  'Ad-hoc': 'Ad-hoc子流程',
  'Add Lane above': '添加到通道之上',
  'Add Lane below': '添加到通道之下',
  'Append ConditionIntermediateCatchEvent': '追加中间条件捕获事件',
  'Append EndEvent': '追加结束事件',
  'Append Gateway': '追加网关',
  'Append Intermediate/Boundary Event': '追加中间/边界事件',
  'Append MessageIntermediateCatchEvent': '追加中间消息捕获事件',
  'Append ReceiveTask': '追加接受任务',
  'Append SignalIntermediateCatchEvent': '追加中间信号捕获事件',
  'Append Task': '追加任务',
  'Append TimerIntermediateCatchEvent': '追加定时中间捕获事件',
  'Append compensation activity': '追加补偿流程',
  'Append TextAnnotation': '追加文本注释',
  'Append element': '追加元素',
  'Business Rule Task': '规则任务',
  'Boundary Event': '边界事件',
  'Call Activity': '引用流程',
  'Cancel Boundary Event': '取消边界事件',
  'Cancel End Event': '结束取消事件',
  'Change element': '更换元素',
  'Change type': '更改类型',
  Collection: '集合',
  'Compensation Boundary Event': '补偿边界事件',
  'Compensation End Event': '补偿结束事件',
  'Compensation Intermediate Throw Event': '补偿中间抛出事件',
  'Compensation Start Event': '补偿启动事件',
  'Complex Gateway': '复杂网关',
  'Conditional Boundary Event': '条件边界事件',
  'Conditional Boundary Event (non-interrupting)': '条件边界事件 (非中断)',
  'Conditional Intermediate Catch Event': '条件中间捕获事件',
  'Conditional Start Event': '条件启动事件',
  'Conditional Start Event (non-interrupting)': '条件启动事件 (非中断)',
  'Connect using Association': '关联文本',
  'Connect using DataInputAssociation': '关联数据',
  'Connect using Sequence/MessageFlow or Association': '关联消息',
  'Create DataObjectReference': '创建数据对象引用',
  'Create DataStoreReference': '创建数据存储引用',
  'Create element': '创建元素',
  'Create EndEvent': '创建结束事件',
  'Create expanded SubProcess': '创建可折叠子流程',
  'Create Gateway': '创建网关',
  'Create Group': '创建组',
  'Create Intermediate/Boundary Event': '创建中间/边界事件',
  'Create Pool/Participant': '创建池/参与者',
  'Create StartEvent': '创建开始事件',
  'Create Task': '创建任务',
  Data: '数据',
  'Data Object Reference': '数据对象引用',
  'Data Store Reference': '数据存储引用',
  'Default Flow': '默认流转',
  'Divide into three Lanes': '分成三条通道',
  'Divide into two Lanes': '分成两条通道',
  'Empty Pool': '空池',
  'Empty Pool (removes content)': '空池（无内容）',
  'End Event': '结束事件',
  'Error Boundary Event': '错误边界事件',
  'Error End Event': '结束错误事件',
  'Error Start Event': '错误启动事件',
  'Escalation Boundary Event': '升级边界事件',
  'Escalation Boundary Event (non-interrupting)': '升级边界事件 (非中断)',
  'Escalation End Event': '升级结束事件',
  'Escalation Intermediate Throw Event': '升级中间抛出事件',
  'Escalation Start Event': '升级启动事件',
  'Escalation Start Event (non-interrupting)': '升级启动事件 (非中断)',
  Events: '事件',
  'Event Sub Process': '事件子流程',
  'Event based Gateway': '事件网关',
  'Exclusive Gateway': '独占网关',
  'Expanded Pool': '展开池',
  Gateways: '网关',
  'Inclusive Gateway': '包容网关',
  'Intermediate Throw Event': '中间抛出事件',
  'Link Intermediate Catch Event': '链接中间捕获事件',
  'Link Intermediate Throw Event': '链接中间抛出事件',
  Loop: '循环',
  'Manual Task': '手动任务',
  'Message Boundary Event': '消息边界事件',
  'Message Boundary Event (non-interrupting)': '消息边界事件 (非中断)',
  'Message End Event': '消息结束事件',
  'Message Intermediate Catch Event': '中间消息捕获事件',
  'Message Intermediate Throw Event': '中间消息抛出事件',
  'Message Start Event': '消息启动事件',
  'Message Start Event (non-interrupting)': '消息启动事件 (非中断)',
  'Parallel Gateway': '并行网关',
  'Parallel Multi Instance': '并行多实例',
  Participants: '参与者',
  'Receive Task': '接受任务',
  Remove: '移除',
  'Script Task': '脚本任务',
  'Send Task': '发送任务',
  'Sequence Flow': '顺序流转',
  'Sequential Multi Instance': '串行多实例',
  'Service Task': '服务任务',
  'Signal Boundary Event': '信号边界事件',
  'Signal Boundary Event (non-interrupting)': '信号边界事件 (非中断)',
  'Signal End Event': '信号结束事件',
  'Signal Intermediate Catch Event': '信号中间捕获事件',
  'Signal Intermediate Throw Event': '信号中间抛出事件',
  'Signal Start Event': '信号启动事件',
  'Signal Start Event (non-interrupting)': '信号启动事件 (非中断)',
  'Start Event': '开始事件',
  'Sub Process': '子流程',
  'Sub Processes': '子流程',
  'Sub Process (collapsed)': '可折叠子流程',
  'Sub Process (expanded)': '可展开子流程',
  Task: '任务',
  Tasks: '任务',
  'Terminate End Event': '终止结束事件',
  'Timer Boundary Event': '定时边界事件',
  'Timer Boundary Event (non-interrupting)': '定时边界事件 (非中断)',
  'Timer Intermediate Catch Event': '定时中间捕获事件',
  'Timer Start Event (non-interrupting)': '定时启动事件 (非中断)',
  'Timer Start Event': '定时启动事件',
  Transaction: '事务',
  'User Task': '用户任务',
  'already rendered {element}': '{element} 已呈现',
  'diagram not part of bpmn:Definitions': '图表不是 bpmn:Definitions 的一部分',
  'element required': '需要元素',
  'element {element} referenced by {referenced}#{property} not yet drawn':
    '元素 {element} 的引用 {referenced}#{property} 尚未绘制',
  'failed to import {element}': '{element} 导入失败',
  'flow elements must be children of pools/participants': '元素必须是池/参与者的子级',
  'more than {count} child lanes': '超过 {count} 条通道',
  'no diagram to display': '没有要显示的图表',
  'no parent for {element} in {parent}': '在 {element} 中没有父元素 {parent}',
  'no process or collaboration to display': '没有可显示的流程或协作',
  'no shape type specified': '未指定形状类型',
  'out of bounds release': '越界释放',
  // 属性面板国际化
  General: '基础信息',
  ID: '标识',
  Name: '名称',
  Documentation: '文档',
  'Element documentation': '元素的描述',
  'Participant Name': '参与者的名字',
  'Participant ID': '参与者ID',
  'Process ID': '流程ID',
  'Process name': '流程名称',
  'Process documentation': '流程文档',
  Executable: '可执行',
  'Multiple elements are selected. Select a single element to edit its properties.':
    '选择多个元素。选择单个元素以编辑其属性。',
  'Form Spec': '表单',
  'JSON Schema': 'JSON Schema',
  'Formily Schema': 'Formily1.0',
  Formily_V2: 'Formily2.0',
  Spec: '规范',
  'Extension properties': '拓展',
  Value: '值',
  '<none>': '无',
  Script: '脚本',
  Expression: '表达式',
  Type: '类型',
  'Inline script': '内联脚本',
  'Dynamic Assignees': '节点执行人动态设置',
  'Script type': '脚本类型',
  'Normal end': '默认结束',
  'Approval end': '审批结束',
  'Approval Approved': '审批通过',
  'Approval Rejected': '审批不通过',
  'Event type': '事件类型',
  'Task type': '任务类型',
  'User assignment': '用户分配',
  CandidateStarter: '谁可以填报',
  Condition: '条件',
  Service: '服务',
  'Result variable': '结果变量',
  auth: '授权',
  jingan_datamarket: '静安数据超市',
  'Service name': '服务名称',
  grant_resource_to_users: '授权资源给用户',
  extract_applicant_info: '获取申请信息',
  extract_datapkg_info: '获取数据包信息',
  'Function name': '函数名',
  'Function params': '函数参数',
  'Normal task': '默认任务',
  'Form task': '表单任务',
  'Approval task': '审批任务',
  Assignment: '人员',
  datamap_v2: 'Datlas',
  general: '通用',
  huangpu_patrol: '黄埔综合巡查',
  send_notice: '发送通知',
  insert_record_to_datapkg: '向已存在的数据包插入一条数据',
  update_record_to_datapkg: '修改数据包中的一条数据',
  query_datapkg_data: '提取数据包数据',
  request_api: '调用API',
  extract_station_master_roles: '提取站长角色ID',
  apply_authorization_by_datapkg_id: '请求对数据包关联资源的授权',
  send_dingtalk_webhook: '发送消息到钉钉',
  send_email: '发送消息到邮箱',
  send_entwx_webhook: '发送消息到企业微信',
  override_workflow_form_data: '覆盖初始表单字段值',
  WorkflowName: '标题模版',
  WorkflowDesc: '描述模版',
  'Workflow detail': '详情界面',
  'Detail page': '详情界面',
  template: '模版内容',
  wfNameTmplPlaceholder:
    '示例：{{填报人}} 发起的 为期【{{假期天数}}天】的【{{假期类型}}假期申请】<div>说明：所有流程均内置的变量有[填报人、填报人所属机构]; </div>其他局部变量(即每个流程的), 如上面示例的`假期天数`等则是表单数据中的元素。',
  wfDescTmplPlaceholder: '示例：假期开始时间:{{假期起始}}; 假期结束时间:{{假期结束}}',
  workflowTmpl: '流程实例',
  appUsers: '本机构所有人',
  loginUsers: '所有Datlas登录用户',
  linkUsers: '所有获得链接的人',
  assignUsers: '指定用户',
  isAnonymity: '是否匿名',
  'Global Variables': '变量管理',
  Setting: '设置',
  DataObjectRef: '引用对象',
  'Select Data Object Ref': '选择数据引用对象',
  Timer: '定时器',
  timerDate: '指定时间',
  timerDuration: '指定相隔时间(单次)',
  timerCycle: '指定相隔时间(多次)',
  crontab: '定时任务',
  'Max cycles': '最大循环次数',
  'End time': '终止时间',
  'Global Variables Setting': '全局变量赋值',
  'Complete Threshold': '结束阈值',
  normal: '常规',
  approval: '审批',
  'Gateway type': '网关类型',
  'Terminate If Rejected': '任一审批拒绝即终止流程',
  'Terminate If Rejected2': '审批拒绝即结束流程',
  config: '配置',
  fromSource: '从指定来源获取',
  multiInstance: '多实例',
  callActivity: '子流程',
  standardLoop: '标准循环',
  'After Submit': '提交后显示设置',
  'Default values': '默认值',
  send_entwx_notice: '发送消息到企业微信通知',
  insert_multi_records_to_datapkg: '向已存在的数据包插入多条数据',
  update_multi_records_to_datapkg: '修改数据包中的多条数据',
  get_user_name: '获取用户名',
  get_user_role: '获取用户角色',
  get_users_relation: '获取用户间关系',
  'Workflow Config': '流程请求配置',
  'Service Config': '服务请求配置',
};
export default cn;
