import _ from 'lodash';
import { BehaviorSubject, from } from 'rxjs';
import { bufferTime, skip, takeWhile } from 'rxjs/operators';
import {
  postQueryRoleIdsByUserIdsAsync,
  postUsersQueryAsync,
  queryAppNamesAsync,
  queryGroupNamesAsync,
  queryRoleNamesAsync,
  queryRoleStructureV2Async,
  queryUserNamesAsync,
} from '@mdtBsServices/auth';
import {
  IEntity,
  IEntitysQuery,
  IRequestRequestConfig,
  IRequestResult,
  IRoleStructureV2,
  IUserIdRoleId,
} from '../interfaces';
import i18n from '../languages';

const bufferTimeInMs = 1000;

export const enum NameCacheEnum {
  USER = 'user',
  USER_UUID = 'user_uuid',
  ROLE = 'roles',
  APP = 'app',
  ORGLIST = 'orgList',
  USERORG = 'userOrg',
  GROUP = 'group',
}

type ICallback = (data: IEntitysQuery, config?: IRequestRequestConfig) => IRequestResult<IEntity[]>;

class NameCacheController {
  private appId: number;
  private userNameMap: Record<number, BehaviorSubject<string>> = {};
  private appNameMap: Record<number, BehaviorSubject<string>> = {};
  private roleNameMap: Record<number, BehaviorSubject<string>> = {};
  private orgListNameMap: Record<number, BehaviorSubject<string>> = {};
  private groupNameMap: Record<number, BehaviorSubject<string>> = {};
  private userIdOrgIdMap: Record<number, BehaviorSubject<number>> = {};
  private userUuidOrgIdMap: Record<string, BehaviorSubject<string>> = {};
  private emptyName$ = new BehaviorSubject('');
  private idCollect$ = new BehaviorSubject<{ type: string; id: number | string } | null>(null);
  private nameMap: Record<string, Record<number | string, BehaviorSubject<any>>> = {
    [NameCacheEnum.APP]: this.appNameMap,
    [NameCacheEnum.USER]: this.userNameMap,
    [NameCacheEnum.ROLE]: this.roleNameMap,
    [NameCacheEnum.ORGLIST]: this.orgListNameMap,
    [NameCacheEnum.USERORG]: this.userIdOrgIdMap,
    [NameCacheEnum.GROUP]: this.groupNameMap,
    [NameCacheEnum.USER_UUID]: this.userUuidOrgIdMap,
  };
  private requestNameFuncMap: Record<string, ICallback> = {
    [NameCacheEnum.APP]: queryAppNamesAsync,
    [NameCacheEnum.USER]: queryUserNamesAsync,
    [NameCacheEnum.USER_UUID]: postUsersQueryAsync,
    [NameCacheEnum.ROLE]: queryRoleNamesAsync,
    [NameCacheEnum.GROUP]: queryGroupNamesAsync,
  };

  public constructor(appId: number) {
    this.appId = appId;
    this.appNameMap[0] = new BehaviorSubject<string>(i18n.chain.product.common);
    this.idCollect$.pipe(skip(1), bufferTime(bufferTimeInMs)).subscribe((bf) => {
      const gs = _.groupBy(bf, 'type');
      _.forEach(gs, (ids, key) => {
        this.requestName(_.map(ids, 'id').filter(Boolean), key);
      });
    });
  }

  public async getUserIdOrgIdMap(userIds: number[]) {
    const rsltMap: Record<number, number> = {};
    const eMap = this.userIdOrgIdMap;
    const notExist: number[] = [];
    _.forEach(userIds, (it) => {
      this.getNameComm(it, NameCacheEnum.USERORG, () => {
        notExist.push(it);
      });
    });
    if (notExist.length) {
      await new Promise((resolve) => {
        this.requestName(notExist, NameCacheEnum.USERORG, resolve as any);
      });
    }
    _.forEach(userIds, (it) => {
      rsltMap[it] = eMap[it].getValue();
    });
    return rsltMap;
  }

  public getName$(id: number | string, type: NameCacheEnum): BehaviorSubject<string> {
    return this.getNameComm(id, type, () => {
      this.idCollect$.next({ type, id });
    });
  }

  public getNameValue(id: number, type: NameCacheEnum): string {
    const nameMap = this.nameMap[type];
    if (!id || !nameMap) return '';
    const name$ = nameMap[id];
    return name$ ? name$.getValue() : '';
  }

  public addUserNameToCache(userId: number, userName: string) {
    this.userNameMap[userId] = new BehaviorSubject<string>(userName);
  }

  public addUserNameWithUuidToCache(userUuid: string, userName: string) {
    this.userUuidOrgIdMap[userUuid] = new BehaviorSubject<string>(userName);
  }

  public addAppNameToCache(appId: number, appName: string) {
    this.appNameMap[appId] = new BehaviorSubject<string>(appName);
  }

  public getUserName$(userId: number) {
    return this.getNameComm(userId, NameCacheEnum.USER, () => {
      this.requestName([userId], NameCacheEnum.USER);
    });
  }

  public getUserNameWithUuid$(userUuid: string) {
    return this.getNameComm(userUuid, NameCacheEnum.USER_UUID, () => {
      this.requestName([userUuid], NameCacheEnum.USER_UUID);
    });
  }

  public getAppName$(appId: number) {
    return this.getNameComm(appId, NameCacheEnum.APP, () => {
      this.requestName([appId], NameCacheEnum.APP);
    });
  }

  public getRoleName$(roleId: number) {
    return this.getNameComm(roleId, NameCacheEnum.ROLE, () => {
      this.requestName([roleId], NameCacheEnum.ROLE);
    });
  }

  public getGroupName$(groupId: number) {
    return this.getNameComm(groupId, NameCacheEnum.GROUP, () => {
      this.requestName([groupId], NameCacheEnum.GROUP);
    });
  }

  public destroy() {
    _.forEach(this.nameMap, (nameMap) => {
      _.forEach(nameMap, (it) => it.complete());
    });
    this.nameMap = {};
    this.userNameMap = {};
    this.appNameMap = {};
    this.roleNameMap = {};
    this.orgListNameMap = {};
    this.userIdOrgIdMap = {};
    this.userUuidOrgIdMap = {};
    this.requestNameFuncMap = {};
    this.groupNameMap = {};
    this.emptyName$.complete();
    this.idCollect$.complete();
  }

  private getNameComm(id: number | string, type: NameCacheEnum, fallback: any) {
    const nameMap = this.nameMap[type];
    if (!id || !nameMap) return this.emptyName$;

    let name$ = nameMap[id];
    if (!name$) {
      name$ = new BehaviorSubject<string>(`${id}`);
      nameMap[id] = name$;
      fallback();
    }
    return name$;
  }

  // 需要一一对应
  private requestName = async (ids: number[] | string[], type: string, callback?: () => void) => {
    if (type === NameCacheEnum.USERORG) {
      from(
        postQueryRoleIdsByUserIdsAsync(
          { role_types: ['organization'], users: [{ app_id: this.appId, user_id: ids as number[] }] },
          { quiet: true },
        ),
      )
        .pipe(takeWhile((v) => v.success))
        .subscribe((resp) => {
          const idNameMap: Record<number, IUserIdRoleId> = _.keyBy(resp.data, 'user_id');
          const nameMap = this.nameMap[type];
          _.forEach(ids as number[], (it) => {
            const val = idNameMap[it]?.role_id;
            const name$ = nameMap[it] as BehaviorSubject<any>;
            name$.next(val);
          });
          callback?.();
        });
      return;
    }

    if (type === NameCacheEnum.ROLE) {
      from(queryRoleStructureV2Async({ quiet: true, params: { role_ids: ids.join(',') } }))
        .pipe(takeWhile((v) => v.success))
        .subscribe((resp) => {
          const idNameMap: Record<number, IRoleStructureV2> = _.keyBy(resp.data, 'role_id');
          const nameMap = this.nameMap[type];
          _.forEach(ids as number[], (it) => {
            const val = idNameMap[it];
            const name$ = nameMap[it] as BehaviorSubject<any>;
            const ps = _.map(val?.parent_struct, (p) => this.getDisplayName(p));
            name$.next({ name: this.getDisplayName(val), fullName: _.join(ps, '/') });
          });
          callback?.();
        });
      return;
    }

    if (type === NameCacheEnum.ORGLIST) {
      from(queryRoleStructureV2Async({ quiet: true, params: { role_ids: ids.join(','), role_type: 'organization' } }))
        .pipe(takeWhile((v) => v.success))
        .subscribe((resp) => {
          const idNameMap: Record<number, IRoleStructureV2> = _.keyBy(resp.data, 'role_id');
          const nameMap = this.nameMap[type];
          _.forEach(ids as number[], (it) => {
            const val = idNameMap[it];
            const name$ = nameMap[it] as BehaviorSubject<any>;
            const ps = _.map(val?.parent_struct, (p) => this.getDisplayName(p));
            name$.next({ name: this.getDisplayName(val), fullName: _.join(ps, '/') });
          });
          callback?.();
        });
      return;
    }

    if (type === NameCacheEnum.GROUP) {
      from(queryRoleStructureV2Async({ quiet: true, params: { role_ids: ids.join(','), role_type: 'group' } }))
        .pipe(takeWhile((v) => v.success))
        .subscribe((resp) => {
          const idNameMap: Record<number, IRoleStructureV2> = _.keyBy(resp.data, 'role_id');
          const nameMap = this.nameMap[type];
          _.forEach(ids as number[], (it) => {
            const val = idNameMap[it];
            const name$ = nameMap[it] as BehaviorSubject<any>;
            const ps = _.map(val?.parent_struct, (p) => this.getDisplayName(p));
            name$.next({ name: this.getDisplayName(val), fullName: _.join(ps, '/') });
          });
          callback?.();
        });
      return;
    }

    if (type === NameCacheEnum.USER_UUID) {
      from(postUsersQueryAsync({ uuids: ids as string[], columns: ['nickname', 'name', 'uuid'] }, { quiet: true }))
        .pipe(takeWhile((v) => v.success))
        .subscribe((v) => {
          const idNameMap: Record<string, IEntity> = _.keyBy(v.data, 'uuid');
          const nameMap = this.nameMap[type];
          _.forEach(ids as string[], (it) => {
            const name$ = nameMap[it] as BehaviorSubject<any>;
            const val = this.getDisplayName(idNameMap[it]);
            val && name$.next(val);
          });
          callback?.();
        });
      return;
    }

    const func = this.requestNameFuncMap[type];
    from(func({ ids }, { quiet: true }))
      .pipe(takeWhile((v) => v.success))
      .subscribe((v) => {
        const idNameMap: Record<number, IEntity> = _.keyBy(v.data, 'id');
        const nameMap = this.nameMap[type];
        _.forEach(ids as number[], (it) => {
          const name$ = nameMap[it];
          const val = this.getDisplayName(idNameMap[it]);
          val && name$.next(val);
        });
        callback?.();
      });
  };

  private getDisplayName(item: any = {}) {
    return item.nickname || item.short_name || item.full_name || item.name;
  }
}

export { NameCacheController };
