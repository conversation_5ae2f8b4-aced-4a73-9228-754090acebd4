{"name": "@mdt/product-comm", "version": "1.28.18", "private": false, "description": "脉策产品级别通用", "keywords": ["mdt", "product", "comm", "modules"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@antv/g6": "^4.6.15", "@apollo/client": "^3.6.9", "@i18n-chain/react": "2.0.1", "@mdt/bpmn-js-properties-panel": "^1.15.12", "@mdt/business-bff-services": "^1.21.13", "@mdt/business-comm": "^1.17.7", "@mdt/business-components": "^1.19.10", "@mdt/business-services": "^1.35.14", "@videojs/http-streaming": "^2.14.2", "bpmn-js-token-simulation": "^0.31.0", "dompurify": "^3.2.0", "eventemitter2": "^6.4.4", "localforage": "^1.10.0", "react-intersection-observer": "^8.32.1", "react-markdown": "^8.0.3", "react-markdown-editor-lite": "^1.3.2", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^6.0.2", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "url-parameter-append": "^1.0.5", "video.js": "^7.20.1"}, "devDependencies": {"@types/react-syntax-highlighter": "^15.5.3", "@types/video.js": "^7.3.42"}}