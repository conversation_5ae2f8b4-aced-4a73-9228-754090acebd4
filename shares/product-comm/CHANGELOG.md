# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.28.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.17...@mdt/product-comm@1.28.18) (2025-07-24)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.16...@mdt/product-comm@1.28.17) (2025-07-15)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.15...@mdt/product-comm@1.28.16) (2025-07-08)

### Features

- ✨ 单独填报一表通的链接 ([27beb0a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27beb0ab37e01a47174d06715271440d50fe14b6))
- ✨ 增加密文展示形式, DataTable 增加密文的展示形式 ([7966081](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/796608167ef35ac8fd005a6ad778d3f8d0781d9b))

## [1.28.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.14...@mdt/product-comm@1.28.15) (2025-07-03)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.13...@mdt/product-comm@1.28.14) (2025-06-10)

### Bug Fixes

- 🐛 增加流程请求配置默认值,增加当前表单值的包裹 ([7652958](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/76529582163983f3f5dd7bcc8d4c56191f23c60e))

### Features

- ✨ 增加单独的填报链接和复制链接功能 ([4b49e6f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4b49e6f2f6ec1b2e2bb886bf9e4dc2068f0c42e1))

## [1.28.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.12...@mdt/product-comm@1.28.13) (2025-05-29)

### Features

- user task 审批新增配置 ([849dffc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/849dffc35e36a7325791bdb48f7ff47594f470ae))

## [1.28.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.11...@mdt/product-comm@1.28.12) (2025-05-19)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.10...@mdt/product-comm@1.28.11) (2025-05-14)

### Bug Fixes

- 🐛 修复周期高级设置都为分钟的问题 ([68088e2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/68088e27c1accbd04d5c39a27c747b4513ab62f8))

## [1.28.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.9...@mdt/product-comm@1.28.10) (2025-04-27)

### Bug Fixes

- 🐛 修复 condition 转换中 contain 的数据结构转换问题 ([68e94d4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/68e94d4cca58b1e7579e6fc9e9674b346a9bb952))

### Features

- ✨ 增加报表管理 h5 单独链接 ([d8c0378](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d8c0378877ead57faecf6fda75e17335643e0d41))

## [1.28.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.8...@mdt/product-comm@1.28.9) (2025-04-21)

### Bug Fixes

- 🐛 文案错误 ([3badbae](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3badbae2c4ce32ca2e77263519e530631f47ac26))

### Features

- ✨ 增加流程和服务任务的请求配置 ([dbd1791](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbd17911ff61301400592cec38d81b6d5a370258))

## [1.28.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.7...@mdt/product-comm@1.28.8) (2025-04-02)

### Features

- ✨ 报表授权 ([b8b9952](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b8b9952c41d53aabaaba9e70123d55579e50cf57))

## [1.28.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.6...@mdt/product-comm@1.28.7) (2025-03-31)

### Features

- ✨ 抽离 footer 渲染,增加统一的默认备案号 ([41db331](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41db3315a22fd1529d314e0ad0dab618af8246b3))

## [1.28.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.5...@mdt/product-comm@1.28.6) (2025-03-31)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.4...@mdt/product-comm@1.28.5) (2025-03-27)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.3...@mdt/product-comm@1.28.4) (2025-03-27)

### Features

- ✨ 增加对于 rc 的处理 ([e36db68](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e36db68165693f2205f7a3b29829b28d23cf7e26))
- ✨ 增加身份指定跳转,增加身份默认偏好,增加默认身份标识 ([6fd5f9f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fd5f9f5759256aab83e8dbe1e98e8e452f080af))

## [1.28.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.2...@mdt/product-comm@1.28.3) (2025-03-13)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.1...@mdt/product-comm@1.28.2) (2025-03-10)

**Note:** Version bump only for package @mdt/product-comm

## [1.28.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.28.0...@mdt/product-comm@1.28.1) (2025-02-27)

**Note:** Version bump only for package @mdt/product-comm

# [1.28.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.5...@mdt/product-comm@1.28.0) (2025-02-24)

### Bug Fixes

- 🐛 机构管理部门信息更新修复 ([759f2ef](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/759f2ef4a3565d2d18a32285c5e9cb728dfdbec9))

### Features

- 支持指定 wfRootForm ([d93fca7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d93fca78b5aa2331516e0062811343b542f26f61))
- ✨ 流程详情增加昵称的显示替换 ([449c9d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c9d8121ed17aeeac5515563e80b2f9a0f152f))

## [1.27.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.4...@mdt/product-comm@1.27.5) (2025-02-17)

### Features

- ✨ 个人数据权限共享 ([6bd7811](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bd78117bffb5be142533603322d6fcb1a09de91))

## [1.27.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.3...@mdt/product-comm@1.27.4) (2025-01-23)

### Features

- ✨ 选人样式优化, 修复切换部门导致右侧部门展示丢失问题, 部门增加层级展示 ([665b064](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/665b064e88857298a958268248347035b40a15a4))

## [1.27.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.2...@mdt/product-comm@1.27.3) (2025-01-16)

**Note:** Version bump only for package @mdt/product-comm

## [1.27.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.1...@mdt/product-comm@1.27.2) (2025-01-09)

### Bug Fixes

- 🐛 修复遗漏资源释放 ([e9dae82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e9dae822c19ffc8160794a4b54edaff9f3dd44a3))
- 🐛 修改 excel 表格颜色 ([9af5051](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9af50510bea0fff36f8029b0504a8e2918a60710))

### Features

- ✨ 一表通批量上传 excel 模板,丰富描述内容和支持动态解析 field ([fe94e77](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fe94e77fe890abb5f641559e775ac874c3f35921))
- ✨ 导出 excel 头根据必填和类型做颜色区分 ([3348159](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3348159ad957036eb44021c858819551c00e6db5))
- ✨ 流程引擎选择框可筛选 ([ee4b414](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee4b414ab2d1fea3d98ee13f2ae030297b87a499))

## [1.27.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.27.0...@mdt/product-comm@1.27.1) (2025-01-06)

### Bug Fixes

- 🐛 updaateTreeWithKey 的方法同时完成移位组织的功能 ([205e913](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/205e913e5e627ae3cf559991385b15db66ce2df0))

### Features

- ✨ 一表通文件上传校验数据 ([1696f17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1696f174f6b53727a2c58272f0ca8d3bd7fe608f))

# [1.27.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.7...@mdt/product-comm@1.27.0) (2024-12-23)

### Bug Fixes

- 🐛 处理 ilike 的值 ([0d7b58b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0d7b58bd36b187efe2ceb89e3c8f5d6620c757d6))

### Features

- 数据包 DDL 设置 ([543c22e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/543c22e19976e5ca690e1d581de808ac74d23a13))

## [1.26.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.6...@mdt/product-comm@1.26.7) (2024-12-16)

### Bug Fixes

- 🐛 一表通筛选器增加为空操作 ([d506e03](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d506e03e823db77a7ed4160742b0b26ca996f722))

## [1.26.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.5...@mdt/product-comm@1.26.6) (2024-12-16)

### Bug Fixes

- 🐛 引用路径 tslint 报错问题 ([113f638](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/113f638e20ebc11aec2c4a9e178e39483f5f592c))

### Features

- ✨ 显隐设置增加表达式设置,同时兼容之前 condition 模式 ([c726e8c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c726e8ccf2fb42d9b78af534158702ab9c16b542))

## [1.26.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.4...@mdt/product-comm@1.26.5) (2024-12-03)

**Note:** Version bump only for package @mdt/product-comm

## [1.26.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.3...@mdt/product-comm@1.26.4) (2024-12-02)

### Features

- ✨ 增加 datetime 筛选 ([604d558](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/604d558fea018e7a92e1e7aecd87373be1da786a))

## [1.26.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.1...@mdt/product-comm@1.26.3) (2024-12-02)

### Bug Fixes

- 🐛 细分未提交 ([5c60e6b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c60e6bfe811aa745113b8c902ba5dbdc3b2ca01))

### Features

- ✨ 增加报表的数量展示 ([52229a7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/52229a7bb80fe6d4b5ec4b22983e877d00d8ea4d))

## [1.26.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.1...@mdt/product-comm@1.26.2) (2024-12-02)

### Bug Fixes

- 🐛 细分未提交 ([7bcaec0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7bcaec062d3bcace189f371adeb5c8b1d2635ce3))

## [1.26.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.26.0...@mdt/product-comm@1.26.1) (2024-11-26)

**Note:** Version bump only for package @mdt/product-comm

# [1.26.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.25.2...@mdt/product-comm@1.26.0) (2024-11-26)

### Features

- ✨ 优化数据包定时设置 ([8b2ecff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8b2ecff7e2f06c775b4d804425527101eb7196b0))
- ✨ 时间类型存储切换 ok ([5a2e4b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a2e4b04b10212f948f488c61e8138e6cc8e71e0))
- ✨ 流程数据包创建列优化 ([bf076b8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bf076b8c47a1127ecf37f4d0790fb9f702ed37c3))
- ✨ 说明文字支持更多类型 ([86d5c94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86d5c94217edabe5444cccdaa83a4a88d62936b4))

## [1.25.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.25.1...@mdt/product-comm@1.25.2) (2024-11-14)

### Bug Fixes

- 🐛 修复周期弹窗的主题色 ([6cf0adc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cf0adcc2545c5887bb0227528ca26c448c5f778))

### Features

- ✨ 周期改版 ([901090d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/901090db719c8b5efe07849b13e8e46057ea029b))

## [1.25.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.25.0...@mdt/product-comm@1.25.1) (2024-11-07)

**Note:** Version bump only for package @mdt/product-comm

# [1.25.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.17...@mdt/product-comm@1.25.0) (2024-11-05)

### Features

- 流程引擎可配置 ([7a6eef4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7a6eef49849859e81ebad9a9c21bc897f6176241))

## [1.24.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.16...@mdt/product-comm@1.24.17) (2024-11-04)

### Bug Fixes

- 🐛 修复详情单独打开循环引用的问题 ([60f4d38](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/60f4d387fe59a652a732e7375a61f37a145f605b))
- 🐛 数据市场的反馈调整 ([732d857](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/732d8577bf8cc2f2874e142430897113e995c582))

### Features

- ✨ 导出任务中心 h5 页面 ([8422cbc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8422cbce08e7a95c25ef3a2348f3006c73a15ef8))
- ✨ 移动端 ([b20dbf7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b20dbf73e5681d398686f1f84ae049eabacbfc42))

## [1.24.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.15...@mdt/product-comm@1.24.16) (2024-10-31)

### Features

- ✨ 详情页接收明文 id,增加去掉 microheader 的子路由 ([62ee068](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/62ee0685981418512159832bedc1a5d51aa5bc3d))
- 优化 ([7ba104f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ba104f99449c27186afb7d9b00206becbb31681))

## [1.24.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.14...@mdt/product-comm@1.24.15) (2024-10-29)

**Note:** Version bump only for package @mdt/product-comm

## [1.24.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.13...@mdt/product-comm@1.24.14) (2024-10-28)

**Note:** Version bump only for package @mdt/product-comm

## [1.24.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.12...@mdt/product-comm@1.24.13) (2024-10-27)

**Note:** Version bump only for package @mdt/product-comm

## [1.24.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.11...@mdt/product-comm@1.24.12) (2024-10-25)

### Bug Fixes

- update ([c86eb0a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c86eb0acc17520fdc5da1e52f61d999e3b323c9a))
- 优化 ([3035d8a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3035d8aea28cfb43e82a08f0b38a3b8dd5c64329))

## [1.24.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.10...@mdt/product-comm@1.24.11) (2024-10-23)

**Note:** Version bump only for package @mdt/product-comm

## [1.24.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.9...@mdt/product-comm@1.24.10) (2024-10-23)

### Features

- 优化 ([f300d43](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f300d43d6a3dd3f149b338cad341c4db30527a8b))

## [1.24.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.8...@mdt/product-comm@1.24.9) (2024-10-23)

### Bug Fixes

- 🐛 优化催办的写法 ([9a90add](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a90add1c438a9d5f434e2e402f44ca267935fcb))
- 🐛 修复筛选条件和文案问题 ([3649f7b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3649f7b13ba6fe002ec004706de638d86faeb7e1))
- 🐛 筛选文案上的修改 ([b793e06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b793e06b143f73863e7d6434374f2ea0c8922fe2))

### Features

- ✨ 增加详情单独链接, 增加催办功能 ([e19c2d6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e19c2d686c03f76b77ec59ee4f2c4e8b7b468a06))

## [1.24.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.7...@mdt/product-comm@1.24.8) (2024-10-22)

### Bug Fixes

- build error ([305f99b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/305f99be8bc6f2e3b443d1e6c17681e0b5b857d9))

### Features

- onetable2.2 ([23f031b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23f031bda6e64530b89df8eff55c5317f3a656d2))
- 优化 ([7353abd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7353abd272d38aa652a25bf259d58875987adab1))

## [1.24.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.6...@mdt/product-comm@1.24.7) (2024-10-15)

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))

## [1.24.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.5...@mdt/product-comm@1.24.6) (2024-09-12)

### Bug Fixes

- 修复流程实例的标题模板，前端错误置入了题目名称而不是变量名称 ([5074379](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5074379fcbe14dd7b51f798f4f57882c885c6f9a))

### Features

- ✨ add form resource ([85bbcb9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85bbcb90bc0d2e1d8995bda981b026e391065989))
- 批量上传不做联动 ([89b1e65](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/89b1e65dd082ddada18a1b341cb0ec17f97d4536))
- 流程实例界面支持 url ([744660f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/744660f4dcaf5319690d0cf8ae03bf7f77593755))

## [1.24.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.4...@mdt/product-comm@1.24.5) (2024-08-29)

### Bug Fixes

- 🐛 修改查找 child 的写法 ([0ed94c3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0ed94c3635b3c05febd238d0fa081ae921cf405f))
- 🐛 机构管理计算子节点计算修复 ([10842f3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/10842f3698ee7f0b6c91de3dc005d65a129da648))

### Features

- ✨ 详情快速响应一个操作 ([76a0b73](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/76a0b73e34041aa277874a93f487dfd2110e4182))

## [1.24.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.3...@mdt/product-comm@1.24.4) (2024-08-27)

### Bug Fixes

- 🐛 机构管理子节点计算缺失问题 ([0df8c88](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0df8c88504cece9520bd7390c60b11d5b3c31120))
- 放宽数据包类型匹配校验 ([97296cc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/97296cc892758ce0ab84c84da70dffcd7e019997))

### Features

- ✨ 增加联想填空题型 ([ee005ea](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee005eaf25c0af597dacc2df1e166ec4eb699d5f))
- 任务合一 ([bb3b6e8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb3b6e80c16fb3204dbaf742a433cb66cf64882e))
- 时间戳类型处理 ([8742efd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8742efd3dc09a9d297f0c72c332f0511dad6f2ae))
- 补充系统变量 ([3cdcf40](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cdcf40b285ab233905cef9036848aba92862453))

## [1.24.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.2...@mdt/product-comm@1.24.3) (2024-08-19)

### Bug Fixes

- 优化 ([57b1c92](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/57b1c92afd0d3d14032a65a869f319481a800ed1))
- 🐛 name error ([8d1266c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8d1266c35e496111db4a12c3697af1cf6b784961))
- 🐛 user select display ([9370bba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9370bbab9a10eeaec4c409b142d055e93dff2ed6))

### Features

- 新增派发不允许重复派发相同的用户 ([2877b4a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2877b4aaa6463589f147e91f9b8de0f920a2f693))
- ✨ 选人 ([9d77e48](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d77e4889ae897aaa654d7a821aba4854f44719c))

## [1.24.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.1...@mdt/product-comm@1.24.2) (2024-08-13)

### Features

- ✨ 菜单布局 ([cae1522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cae1522ef2ad0d5a7919ee2fe6c848653c42c5a7))

## [1.24.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.24.0...@mdt/product-comm@1.24.1) (2024-08-12)

**Note:** Version bump only for package @mdt/product-comm

# [1.24.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.23.2...@mdt/product-comm@1.24.0) (2024-08-09)

### Bug Fixes

- 数据预览白屏 ([67ba9fc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/67ba9fc84efb5227bab63cf19ed3298fed36c572))

## [1.23.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.23.1...@mdt/product-comm@1.23.2) (2024-08-07)

### Bug Fixes

- 🐛 补全权限和缺失国际化 ([745914b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/745914b5107e06ad00ef33c40c7a9f2f9d9b16d4))

### Features

- ✨ 无匹配路由默认跳转增加配置项 ([2a55642](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2a556426323e3aaeea4bc4b2130d932ab4921cde))

## [1.23.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.23.0...@mdt/product-comm@1.23.1) (2024-08-05)

### Bug Fixes

- 🐛 excel export temp error ([318cf46](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/318cf4645081d88fea18fa7a802fc994d81efb13))

# [1.23.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.22.4...@mdt/product-comm@1.23.0) (2024-08-02)

### Features

- support url fs setting ([bd119f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bd119f83d63ab9cbfeb6aed083656338028117f1))

## [1.22.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.22.3...@mdt/product-comm@1.22.4) (2024-08-02)

### Bug Fixes

- 🐛 修复选择有其他属性时的正确展示, 新增一行和批量编辑走一套增删改 ([ff65f9e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ff65f9edd920cab22a81e837405041dce4c5b585))

### Features

- ✨ usertask 指定组织负责人 ([72472d9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72472d95d9c871233868bef78f993d8bef58b21a))
- ✨ add new question ([6fb9a82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fb9a823593847e70adba43ddc80f83650f6f88c))

### Performance Improvements

- ⚡️ 机构管理大数据渲染性能优化 ([48937f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/48937f94f922d4c6cc5a124f8c1eafb63e2c9340))

## [1.22.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.22.2...@mdt/product-comm@1.22.3) (2024-07-29)

### Features

- ✨ datapkh support id to name display ([c417beb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c417beba00c2f70b646b752080742cbdcd568933))
- ✨ orgid from frontend ([08e8b14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08e8b14d6ee450076d266bccd618841875ace939))

## [1.22.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.22.1...@mdt/product-comm@1.22.2) (2024-07-25)

### Features

- config header menu ([146d0dc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/146d0dc31e740ab2f82b940dc85d155c7629c8d4))

## [1.22.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.22.0...@mdt/product-comm@1.22.1) (2024-07-24)

### Bug Fixes

- 🐛 use config to controller ([dc5d68c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc5d68c5cda5ff38c2c0bce7dc3af679c2dc36e2))

### Features

- add url param ([69c5d0f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/69c5d0fd633aefe742e57883dd133e45ab8002a6))
- ✨ 增加一表通翻译权限,修改翻译变量名 ([c4fcfd1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4fcfd1bb2032751637133d4df13663bb00419d0))
- ✨ 增加报表管理的权限 ([d55e8bc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d55e8bc182817b016fb6a9466598adbdf38d0be6))
- one table suport more app ([528bb78](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/528bb7846983496f44acf09aabb17c78f3879dcb))

# [1.22.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.21.2...@mdt/product-comm@1.22.0) (2024-07-22)

### Bug Fixes

- change file position ([75f1bfd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/75f1bfde4d4bfbf8f8d1204adc98ca728433d2be))

### Features

- add report data page ([4669ed4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4669ed4b8f8ae17632eb8848d756026502365ad5))
- ✨ 补充填报信息,增加筛选类型 ([6cc2b45](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cc2b45ddeb161b94f02a30c9c79a17f749cf3f7))

### Performance Improvements

- ⚡️ 性能优化 ([1ad9dfe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1ad9dfeba0255f71d5f2e3a71a7cdb5b59bd6c8d))

## [1.21.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.21.1...@mdt/product-comm@1.21.2) (2024-07-17)

### Bug Fixes

- 🐛 修复上传数据转换造成的页面闪退问题 ([663b841](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/663b841499243764c7ec32cacbad75177022c42d))

### Features

- ✨ 筛选器增加 alias 映射关系 ([2cc63c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2cc63c8df3d33b183ca7c3a2d3fc86ee8f624676))
- support crontab ([1b1a750](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1b1a75004f6e1b8e1fc4ac280691fed59c7a4983))

## [1.21.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.21.0...@mdt/product-comm@1.21.1) (2024-07-10)

### Bug Fixes

- 🐛 规范模板信息 ([9b9429b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9b9429ba44a29374d3ffb0120930aa7ee3de8e14))

### Features

- ✨ 完成催办功能 ([c77be50](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c77be5032fed4c1aeea4d2496a3913e5e49917da))
- user task detail page custom ([2e5670f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2e5670f81f52ec0d3b7d4969678881ab708bf69a))

# [1.21.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.20.4...@mdt/product-comm@1.21.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))
- ✨ onetable 移动端优化 ([7f9b522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7f9b5220d5f454ead6efd1d92149d2d99e334e55))

## [1.20.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.20.3...@mdt/product-comm@1.20.4) (2024-06-24)

### Features

- ✨ 一表通移动端 详情页 ([db002b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/db002b667a7485a766e079cc4bdebea6c4f9d7d4))
- ✨ 一表通移动端 首页 ([de108da](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/de108da0b3973d88d79133f070316ca7f8505205))

## [1.20.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.20.2...@mdt/product-comm@1.20.3) (2024-06-17)

### Bug Fixes

- import error ([9711b35](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9711b35133ca7d974c1d449b66275060991722f6))

### Features

- ✨ 一表通优化 ([9b72a10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9b72a1012cd80c91ea62a6f4ae07386ef57cae65))
- ✨ 一表通概览 ([5dca300](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5dca30097fb32cd920b4211191b935c9d1d4b20e))
- ✨ 增加 onetable 的权限展示和校验 ([3442117](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/34421172ce0a438ea262eeaebd793a5b496d5325))
- ✨ 增加组件国际化的全局配置 ([7b6cfbe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7b6cfbe1badc7261a5c455549683621c532480a9))
- ✨ 市民信息组件& 子流程数据查询调整 ([ac52dd2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac52dd28fcde51da30f8e3b2db36ddde1df8a5e6))
- 追加部门过滤 ([4ad0f1a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ad0f1aaf5387c611658fd91feb70211a0b84451))

## [1.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.20.1...@mdt/product-comm@1.20.2) (2024-06-03)

### Bug Fixes

- 线上屏蔽 onetable ([a1209c0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a1209c011648f30f2c0506cb792e6cb0d16f1b19))

### Features

- ✨ callactivity 子流程支持变量 ([4a65dc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4a65dc8b3da6e6abaff7774b98e316340f5a664d))
- ✨ 报表创建 ([0efd5e5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0efd5e562304214607a5d381cf0c71ffcf6ad202))
- ✨ 报表管理列表接入接口 ([679d214](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/679d214f6da2d576df6d52af9b4444bd5be0ed50))
- ✨ 数据包行操作接口改为 bff ([cefa950](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cefa950a3b4ffc57ef0a92a04bdb5485d4bdc873))
- 修改数据完成 ([99d63b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/99d63b085bb92a7701c8c7c410b3ff1009d95d7a))
- 微信公众号获取经纬度 ([b95f82e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b95f82ef548232a881da83cddf082a76f67ecdf2))
- 通过 excel 批量更新 ([d3d02a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d3d02a1a5d383e47c43a0097a2d6df9c339a3394))

## [1.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.20.0...@mdt/product-comm@1.20.1) (2024-05-20)

### Bug Fixes

- dont open onetable ([48a3d7d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/48a3d7dc93c3c6d71d97d3436bd90e4f4cbe2bb5))

### Features

- ✨ bpmn 支持 callActivity ([0f3583d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0f3583dfd960f4ae86251b5f928a8fae9b1d9d49))
- ✨ limitSize 可配置 ([26d0e3f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/26d0e3f4293958f204dbdd3c3dc10e741b8fd21e))
- ✨ standard llop ([eb4482a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eb4482afef66710cae5148bbb0d06ae63ee7b9d9))
- ✨ 优化及 bug fix ([988c2eb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/988c2eb38ce87beb8f325558415c0b0ee5da742f))
- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))

# [1.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.13...@mdt/product-comm@1.20.0) (2024-05-13)

### Features

- ✨ bpmn 任务支持多实例 ([6dfdfa5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6dfdfa5832883b5f6dbb3ee29c17b43fbf3516ba))
- ✨ excel 模版 ([d57485d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d57485d2749ee5743d1942b68e7f0d5cd4aed178))
- ✨ 自增表格、卡片支持内部依赖 ([8f981a9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8f981a9c2bc08d4a4caaff2aaee069c8ab56ddbe))
- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [1.19.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.7...@mdt/product-comm@1.19.13) (2024-04-29)

### Features

- add amis editor ([e11f1e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e11f1e9eac9d81f2763cab3e19c694cd11fac8dd))

## [1.19.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.6...@mdt/product-comm@1.19.7) (2024-04-24)

### Bug Fixes

- 🐛 数值类型使用 numberpicker 组件 ([7e61551](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e615511d85c6e599dc3417816af4796e447ad29))

## [1.19.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.5...@mdt/product-comm@1.19.6) (2024-04-23)

**Note:** Version bump only for package @mdt/product-comm

## [1.19.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.4...@mdt/product-comm@1.19.5) (2024-04-23)

### Features

- ✨ 数据源支持当前机构部门列表 & qlang 依赖项有默认值时没有执行 sql ([c608935](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c608935ba7667ba1ac9a9eed4cff661db314199e))

## [1.19.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.3...@mdt/product-comm@1.19.4) (2024-04-15)

**Note:** Version bump only for package @mdt/product-comm

## [1.19.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.2...@mdt/product-comm@1.19.3) (2024-04-08)

**Note:** Version bump only for package @mdt/product-comm

## [1.19.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.1...@mdt/product-comm@1.19.2) (2024-04-01)

**Note:** Version bump only for package @mdt/product-comm

## [1.19.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.19.0...@mdt/product-comm@1.19.1) (2024-03-25)

### Bug Fixes

- 🐛 IframeChannelController 调整 ([51862ce](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/51862ce0c9ba572052398f18e9a7ee32e8e4df12))

### Features

- ✨ 模板增加文件夹 ([b2c0f53](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b2c0f53e91e4e77f989ee9c8f51a2818de177f7e))
- ✨ 添加 iframe 通信类, 增加 iframe 发送退出的消息给主项目 ([5e2a7d5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5e2a7d563859018c8e4a7e52af864d61a7276cff))

# [1.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.18.1...@mdt/product-comm@1.19.0) (2024-03-11)

### Features

- ✨ 其他机构数据文件夹+bff ([c815d8e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c815d8ed1de6dadd6b4419564bd6e950fdc9defc))
- ✨ 智能搜索&数据源支持权限及用户列表 ([f717ead](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f717ead66bfb87cacc9be624dcb6ce4dc0e00f34))

## [1.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.18.0...@mdt/product-comm@1.18.1) (2024-03-07)

**Note:** Version bump only for package @mdt/product-comm

# [1.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.5...@mdt/product-comm@1.18.0) (2024-03-01)

**Note:** Version bump only for package @mdt/product-comm

## [1.17.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.4...@mdt/product-comm@1.17.5) (2024-02-28)

**Note:** Version bump only for package @mdt/product-comm

## [1.17.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.3...@mdt/product-comm@1.17.4) (2024-02-23)

### Features

- ✨ bpmn 变量管理&审批调整 ([c7bf101](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c7bf101bf05471e43a1cd926094d2d80c0e35754))

## [1.17.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.2...@mdt/product-comm@1.17.3) (2024-01-29)

**Note:** Version bump only for package @mdt/product-comm

## [1.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.1...@mdt/product-comm@1.17.2) (2024-01-15)

### Features

- ✨ 模拟登录增加当前用户快速选择 ([a03388c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a03388c2185baba2d19ed0356df399fba8097d10))

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.17.0...@mdt/product-comm@1.17.1) (2024-01-08)

### Bug Fixes

- 🐛 中转逻辑修改 问题修复, 站内消息跳转 ([f4af7c2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4af7c25b441a36ce14c27c721f94179fd4ce6cb))

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.16.2...@mdt/product-comm@1.17.0) (2024-01-02)

### Bug Fixes

- 🐛 中转页逻辑修改 ([58aa238](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/58aa238064b2b3d96ee5cae10a154877b184b5f5))

### Features

- ✨ 接入移动端级联&定制表单覆盖初始表单字段值&timer 定制表单 ([e94a877](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e94a8776bbe3e63f96515202eddddc3cb8b3d632))

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.16.1...@mdt/product-comm@1.16.2) (2023-12-25)

### Bug Fixes

- 🐛 type error ([8b13f68](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8b13f68bd88ad3e1a599ee7fa32d8891e03fb2a5))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.16.0...@mdt/product-comm@1.16.1) (2023-12-25)

### Bug Fixes

- 🐛 fix ([4cf0862](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cf08628bc7891f79fe1680816e8fde4b2782d76))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.6...@mdt/product-comm@1.16.0) (2023-12-25)

### Bug Fixes

- 🐛 build error ([c79053c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c79053c51757d2b823ab3bf6d70734386588e157))

### Features

- ✨ sso 增加微信和企业微信授权分发流程, 增加分发路由中转页 ([6d341f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6d341f8b2136c94d40610edb5f7ce71a1017cc0f))
- ✨ 表单添加创建者参数& 上传数据包填报模版时支持重选数据包;文件组件添加最大最小数量限制 ([4ebb430](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ebb43037bf9d7753c44ad3f89d60b5809adb0e1))

## [1.15.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.5...@mdt/product-comm@1.15.6) (2023-12-18)

### Bug Fixes

- 🐛 memory leak ([d583292](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d583292b8d7e8b0d4fdd6b1d6df86082af1370db))

### Features

- ✨ bpmn 全局变量设置 & 数据市场审批加 loading ([d505788](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d50578812eb45b408f596b34a343142240ffefee))

## [1.15.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.4...@mdt/product-comm@1.15.5) (2023-12-11)

### Features

- ✨ 增加 parquet 上传类型 ([86d2052](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86d20528bfc5ec6e53e0e3e1385e5f6da624cd3a))

## [1.15.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.3...@mdt/product-comm@1.15.4) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))

## [1.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.2...@mdt/product-comm@1.15.3) (2023-12-04)

**Note:** Version bump only for package @mdt/product-comm

## [1.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.1...@mdt/product-comm@1.15.2) (2023-12-04)

### Bug Fixes

- 🐛 偏好标题和描述不默认赋值 ([a5cb2e8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a5cb2e8e0f1176e0bb94266b974778c08c884a40))

### Features

- ✨ 级联选择&下载数据包时支持指定列 ([8a97c2e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a97c2ea17d665b6cf3105bd455f5073d1b56e68))

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.15.0...@mdt/product-comm@1.15.1) (2023-12-01)

### Bug Fixes

- 🐛 import wrong ([50b8ff0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/50b8ff00141c6530979d18bac731e6641897cc51))

### Features

- ✨ 机构偏好 ([92a66b9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/92a66b963cdd7c2907a76802152bfbb131c51a5d))
- ✨ 自增表格&资源分享支持数据源 ([27820ab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27820abbd075bea5471885660183f851e76241b5))

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.22...@mdt/product-comm@1.15.0) (2023-11-20)

### Features

- ✨ pwa ([80985fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80985fbd6ca7f56d6b386959368e3ad44f02905b))
- ✨ 自增卡片 ([7dc9f0b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7dc9f0bec8926c123d8583b578e0c0b2bfdd041b))

## [1.14.22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.21...@mdt/product-comm@1.14.22) (2023-11-13)

### Features

- ✨ 操作日志数据包增加更新元数据的类型 ([1ed0ca8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1ed0ca8d7451ba73f08708a4090bf79d6d5743db))

## [1.14.21](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.20...@mdt/product-comm@1.14.21) (2023-11-06)

### Bug Fixes

- 🐛 数据包订阅非本机构排除数据质量监控 ([37b7cd3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/37b7cd3945ab653fa9dc9752f047beb7f4b614b5))

## [1.14.20](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.19...@mdt/product-comm@1.14.20) (2023-11-03)

### Features

- ✨ 自定义登录 ([a3d1f99](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a3d1f99c74c2e73e2e1488cd658f2522c06498d6))

## [1.14.19](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.18...@mdt/product-comm@1.14.19) (2023-11-01)

### Bug Fixes

- 🐛 修复打包报错的问题 ([2785eb5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2785eb5d713bd478d056a12bdd4ebc19e911fd85))

## [1.14.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.17...@mdt/product-comm@1.14.18) (2023-10-31)

### Bug Fixes

- 🐛 fix theme error ([4490c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4490c8398c548818fc389da96bdcc35f3b616cf9))

## [1.14.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.16...@mdt/product-comm@1.14.17) (2023-10-31)

### Performance Improvements

- ⚡️ html load faster ([e7f2d23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7f2d233d799c6081d3cae94f1ca0663ebe20e28))

## [1.14.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.15...@mdt/product-comm@1.14.16) (2023-10-30)

**Note:** Version bump only for package @mdt/product-comm

## [1.14.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.14...@mdt/product-comm@1.14.15) (2023-10-26)

### Bug Fixes

- 🐛 标题在移动端显示问题 ([39c6224](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39c62244f12bd43688273ff2e0f8e06cc3be14ce))

## [1.14.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.13...@mdt/product-comm@1.14.14) (2023-10-25)

**Note:** Version bump only for package @mdt/product-comm

## [1.14.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.12...@mdt/product-comm@1.14.13) (2023-10-23)

### Features

- ✨ 数据包订阅功能 ([f6f43ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6f43ffe94d07cbdd9d08c63304ebd1dec40030a))

## [1.14.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.11...@mdt/product-comm@1.14.12) (2023-10-16)

**Note:** Version bump only for package @mdt/product-comm

## [1.14.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.10...@mdt/product-comm@1.14.11) (2023-10-10)

### Features

- add ai assiast ([18399e0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18399e0c30afbc12e9a1ce8647e212b614423341))

## [1.14.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.9...@mdt/product-comm@1.14.10) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.14.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.8...@mdt/product-comm@1.14.9) (2023-09-04)

### Features

- ✨ operation-log ([5cbb88b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5cbb88b7eff44dcfe5f3bb6cad27927347a6ed51))

## [1.14.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.7...@mdt/product-comm@1.14.8) (2023-08-14)

**Note:** Version bump only for package @mdt/product-comm

## [1.14.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.6...@mdt/product-comm@1.14.7) (2023-07-31)

### Features

- ✨ add totalAsync ([b3bdb28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b3bdb287780d82f85c9b1149a688ddc2c4fe0d41))

## [1.14.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.5...@mdt/product-comm@1.14.6) (2023-07-24)

### Performance Improvements

- ⚡️ 性能优化(秒开) ([7142db5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7142db546bb7e1ea57651c700d2745e1f57a3a60))

## [1.14.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.4...@mdt/product-comm@1.14.5) (2023-07-03)

### Features

- ✨ 通过数据包详情页发起流程填报 ([5c2d77b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c2d77b6910e07dc9bf9a1e02dd0162c85ebb671))

## [1.14.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.3...@mdt/product-comm@1.14.4) (2023-06-20)

**Note:** Version bump only for package @mdt/product-comm

## [1.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.2...@mdt/product-comm@1.14.3) (2023-06-13)

### Bug Fixes

- 🐛 @i18n-chain/react add dependencies ([5fc7e47](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc7e47c557a03343e4885594bbcd24dbaec912c))

## [1.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.1...@mdt/product-comm@1.14.2) (2023-06-12)

### Bug Fixes

- 🐛 fix dark modal ([8c61d35](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c61d35457b10814eaf73356ff4a0d8aad854065))

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.14.0...@mdt/product-comm@1.14.1) (2023-06-12)

### Features

- ✨ bpmn add dark modal ([bbd66f3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bbd66f34e92e79d6eaf58c7b37db489d5b2fdb0a))

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.10...@mdt/product-comm@1.14.0) (2023-06-05)

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

## [1.13.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.9...@mdt/product-comm@1.13.10) (2023-05-22)

### Features

- ✨ delete v1 api, update impersonate sub perferences to v2 ([2029340](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2029340fed2368448fb723d99f8ab9f02ce2e850))

## [1.13.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.8...@mdt/product-comm@1.13.9) (2023-05-15)

**Note:** Version bump only for package @mdt/product-comm

## [1.13.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.7...@mdt/product-comm@1.13.8) (2023-04-24)

**Note:** Version bump only for package @mdt/product-comm

## [1.13.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.6...@mdt/product-comm@1.13.7) (2023-04-17)

**Note:** Version bump only for package @mdt/product-comm

## [1.13.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.5...@mdt/product-comm@1.13.6) (2023-04-11)

### Bug Fixes

- 🐛 资源分享时包含素材 ([1f0c8a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1f0c8a187699d5ce552ab96e3ec470d623fea958))

## [1.13.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.4...@mdt/product-comm@1.13.5) (2023-04-10)

### Bug Fixes

- 🐛 流程表单提升层级;资源分享支持脚本 ([6261924](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6261924f22f7f2722f98c49959b055bbf8c24b75))

## [1.13.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.3...@mdt/product-comm@1.13.4) (2023-04-07)

**Note:** Version bump only for package @mdt/product-comm

## [1.13.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.2...@mdt/product-comm@1.13.3) (2023-04-06)

### Features

- ✨ add custom bpmn property;审批流程 ([f08e20f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f08e20f156da195f860f5307f6d9a898a48bdf78))

## [1.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.1...@mdt/product-comm@1.13.2) (2023-03-24)

**Note:** Version bump only for package @mdt/product-comm

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.13.0...@mdt/product-comm@1.13.1) (2023-03-20)

### Bug Fixes

- 🐛 pro-comm build error ([439b8dd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/439b8dd86dcf8576600f413e58f220cac37cd856))

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.11.0...@mdt/product-comm@1.13.0) (2023-03-13)

### Features

- ✨ add force_update param ([6ab1342](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ab13428654fd72609420bf5ad4ca9a94e6cd3d4))
- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))
- ✨ 为抽象类和偏好类增加获取和修改属性的方法 ([6cb0ac1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cb0ac1d892afb42f1198f8e1f310113a00b66cf))

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.10.1...@mdt/product-comm@1.11.0) (2023-02-20)

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.10.0...@mdt/product-comm@1.10.1) (2023-02-13)

### Features

- ✨ app-header 同步增加配置 ([3ff609a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ff609aaa13c1f4b82fcdb4db3dfe2a9a187c27a))

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.9.4...@mdt/product-comm@1.10.0) (2023-02-06)

### Bug Fixes

- 🐛 [模拟登录]: 优化 ([cf2d205](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cf2d2051758d2e44d74526bf4799d689552570dd))

## [1.9.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.9.3...@mdt/product-comm@1.9.4) (2023-01-30)

### Bug Fixes

- 🐛 [部门与用户]: 修复删除部门没有把子部门全部删除的 bug ([a4b4153](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a4b4153aefeeffcfbab109f928fef3d47c151882))

### Features

- ✨ [顶栏]: 增加 collector 跳转 ([0202323](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0202323e075ae62dc01b5c4f48b632335b5cbed5))
- ✨ [顶栏]: 增加跳转方式的配置, 产品根据配置项决定跳转方式 ([2c3975f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c3975fba0e5ce8bff6e8f325e6731a8f3258661))

## [1.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.9.2...@mdt/product-comm@1.9.3) (2023-01-06)

### Bug Fixes

- 🐛 个人数据不做权限校验 ([93d7826](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93d7826c1e6b00884414d1001987ad966a5c7e8f))

### Features

- ✨[我的数据]双地理处理;[数据市场]排行榜新增详情入口；[资源共享]分享页面时连带 flow ([5af6057](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5af6057359309185041192f9f10b5049a47c0a06))

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.9.1...@mdt/product-comm@1.9.2) (2023-01-03)

### Features

- ✨ [部门用户]: 增加模板批量导入导出 ([ffdb07c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ffdb07cc938bd2ab3d8552f3865cb4912f64ca70))
- ✨ [首页设置]: 功能模块完成 ([690d5ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/690d5ff3a235d0cef1c47583ef9dfb9ee2cdbced))
- ✨ 新增帮助中心 ([bca156e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bca156ed842a05bd7613185a893524ed64b0c3ed))
- ✨ 资源分享支持项目、flow ([23268aa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23268aaf4467655f911d25749304059012960dff))

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.9.0...@mdt/product-comm@1.9.1) (2022-12-20)

**Note:** Version bump only for package @mdt/product-comm

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.7...@mdt/product-comm@1.9.0) (2022-12-13)

**Note:** Version bump only for package @mdt/product-comm

## [1.8.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.6...@mdt/product-comm@1.8.7) (2022-12-12)

### Bug Fixes

- 🐛 [过期时间提示]: 存储到偏好,改为用户过期时间提醒 ([4f5daec](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4f5daec9f3f6a4979455b59dccfa146a87243f54))
- 🐛 机构数据挂载图表 ([086b106](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/086b10682e34489fe995711332d0aa6c9cfef374))

### Features

- ✨ [Table 通用]: 可选列表增加全选功能 ([e3e368a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3e368ae2c5e3224e26aea0e0c47ed580ee4ee14))
- ✨ fetch -> axios ([ef4efc3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef4efc32ce30426d14708ab70ef710c827f41dac))

## [1.8.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.5...@mdt/product-comm@1.8.6) (2022-12-06)

### Bug Fixes

- 🐛 [Logo 样式]: 修复地图轻应用无法上传 logo 的问题 ([7fb2445](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7fb244571126e92b7af09bb6a61f8f061453351f))
- 🐛 修改数据包详情返回数据格式 ([6001997](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/600199700d03db7a132c8a975170a6e023b9a0e6))

## [1.8.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.4...@mdt/product-comm@1.8.5) (2022-12-05)

### Features

- ✨ dm bff ([6ac4ea6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ac4ea6bcd73077633e67745abe694a62fda6536))

## [1.8.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.3...@mdt/product-comm@1.8.4) (2022-11-30)

### Features

- ✨ [资源共享] 支持分享个人数据包、机构数据包 ([3d66dab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3d66dab5e48a864d6a6fb1f0cb7f2d700c1d6f55))

## [1.8.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.2...@mdt/product-comm@1.8.3) (2022-11-29)

### Bug Fixes

- 🐛 打开 mapEditor 支持多窗口 ([1c99a57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c99a5724eb2d875100523a9ca4b029d65ad27a1))

## [1.8.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.1...@mdt/product-comm@1.8.2) (2022-11-28)

### Features

- ✨ [偏好设置]: Logo 样式模块增加,通用顶栏适配增加 ([894b2be](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/894b2beab961f3e427e3b8c9a178845f76ba5e4d))
- ✨ [部门与用户]: 增加强制修改密码功能 ([3048ff8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3048ff8581c2662318ef57286600f47df6c32c23))

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.8.0...@mdt/product-comm@1.8.1) (2022-11-21)

### Bug Fixes

- 🐛 fix ([0b11490](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b114902369505586123747dbfbfc6e68674a8c0))
- 🐛 memory leak ([a971074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a971074e017b8ea025305ce48fdc605ab25fb550))
- 🐛[我的数据] 预览地理数据时携带 token，创建 sql 数据包 ownership 设默认值;[数据市场]跨机构加偏好 ([3cc0415](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cc0415f87ce227476c32c44b5c54c483727bcdf))

### Features

- ✨ [通用顶栏]: 加进入后台功能 ([79255f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/79255f97117626a91679c26ae4290e0c70514486))
- ✨ adapte dataapp iframe ([e413d42](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e413d42e5804dd3a6a97dabee8d0f2c56a544cf0))

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.7.3...@mdt/product-comm@1.8.0) (2022-11-14)

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ [用户管理]: 大模块重构 ([f3fa668](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f3fa668174590ea5020a9bb910b22bd37d4de067))
- ✨ 低码 ETL 总体汇总 ([16ec9b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16ec9b6e07179d2efc88db77b6893d19835f82b2))
- ✨ 数据市场、我的数据优化 ([02957c7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/02957c727b6a298f5b021f378f2adf0a7f4e0855))
- ✨ 数据申请简化 ([74a9fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74a9fc800171e9d78ca1b0cdaa9cf0a7f83b3ac7))
- ✨ 转地理数据前新增默认地理列 ([405a286](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/405a2867fdbb1c4b626f98a753b60bba39761842))
- ✨ 预览地理数据;主题库加搜索框;表格预览排序 ([77680b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77680b64fa85358356ac554d6c92460098c6a520))

## [1.7.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.7.2...@mdt/product-comm@1.7.3) (2022-10-31)

### Features

- ✨ 数据市场、我的数据 优化 ([40ac4c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40ac4c67afebdfc586da4221e2cb934c6bea51db))

## [1.7.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.7.1...@mdt/product-comm@1.7.2) (2022-10-25)

### Features

- ✨ 数据看板 ([9a85fb8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a85fb81fd7086a856c298f659795deef0f69721))

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.7.0...@mdt/product-comm@1.7.1) (2022-10-21)

**Note:** Version bump only for package @mdt/product-comm

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.6.0...@mdt/product-comm@1.7.0) (2022-09-30)

### Features

- ✨ 可见数据设置 ([c540a22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c540a22a0c8493d9c462318b8e1d9bb256d80dce))
- ✨ 数据市场 依赖偏好获取主题库、app ([4209802](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/42098024fda0f9457b6daca013b5c922e6c99ea1))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.5.3...@mdt/product-comm@1.6.0) (2022-09-26)

**Note:** Version bump only for package @mdt/product-comm

## [1.5.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.5.2...@mdt/product-comm@1.5.3) (2022-09-19)

**Note:** Version bump only for package @mdt/product-comm

## [1.5.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.5.1...@mdt/product-comm@1.5.2) (2022-09-19)

**Note:** Version bump only for package @mdt/product-comm

## [1.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.5.0...@mdt/product-comm@1.5.1) (2022-09-15)

### Bug Fixes

- 🐛 兼容深色模式+跨机构搜索无效 ([c6e05b1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c6e05b1d970bcc0af133d34ca147309924cf72d9))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.4.0...@mdt/product-comm@1.5.0) (2022-09-14)

### Features

- ✨ 放开以下功能入口：个人数据发布、审批；创建空数据包；数据更新新增追加 ([a626e0d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a626e0d726c96cdd9fab375b5129d208a6f1d197))

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.3.2...@mdt/product-comm@1.4.0) (2022-09-13)

### Features

- ✨ 数据包创建、详情+血缘图优化+个人数据发布审批 ([35a345c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35a345cc293c4f4c2adb0186ae7fac41c0ede6ad))

## [1.3.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.3.1...@mdt/product-comm@1.3.2) (2022-09-05)

**Note:** Version bump only for package @mdt/product-comm

## [1.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.3.0...@mdt/product-comm@1.3.1) (2022-08-30)

**Note:** Version bump only for package @mdt/product-comm

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.2.0...@mdt/product-comm@1.3.0) (2022-08-29)

### Bug Fixes

- 🐛 [机构管理]: 修复删除后列表不更新的问题/修复角色动态选中无效果的问题 ([81802f6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/81802f656df15a8154a60d8c771a93baa876150d))

### Performance Improvements

- ⚡️ 优化机构管理代码 ([77d25a6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77d25a613b69e8366857232d5620b95995696544))

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-comm@1.1.0...@mdt/product-comm@1.2.0) (2022-08-12)

### Bug Fixes

- 🐛 build error ([5a883f0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a883f0927c8879b976625d4ba12c6516c948558))
- 🐛 fix bug ([11e790f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/11e790f1b38b898a2475f98ccf6f790049788941))
- 🐛 markdown load error ([6e4ca84](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6e4ca8487ffaf82a2f41df331d9fcd36b556e754))
- 🐛 markdown 相关问题 ([b66e416](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b66e4162ae9d7f2a59ad5a691dca71aa5bed07e2))
- 🐛 主题库为空时接口报错 ([86b854f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86b854fe98a18c17f58d0c5550b6c385ff45077b))
- 🐛 主题库扁平化 ([66f6407](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/66f6407e62d0fe939e2345d629a6144aa80d7035))
- 🐛 修改新数据工厂的导航栏 ([53cb069](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/53cb069ac6ea490fb2df555de01f723fb207c5c1))
- 🐛 循环应用 ([8443062](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8443062465cced469833d6e45bd08e085448f33a))
- 🐛 样式污染 ([77282b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77282b20211338b0a2a5f13724b5220b6fc1ad35))

### Features

- ✨ [通用 header]: 功能扩展，样式优化 ([d843e94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d843e9438b766313f08cfbcba5734f88d852ccfb))
- ✨ 增加允许产品跳转的配置项,修改菜单栏的样式 ([c4b803f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4b803fb802e3d1dcef64a7a44ab37aa95e31992))
- ✨ 定时任务 ([704f8a3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/704f8a314e63c1bda0a7f66a09d2532f0bbc92c1))
- ✨ 数据资产跳数据市场 参数适配 ([bac37c0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bac37c0e62db902de21f46f6235c9c1994b0e981))
- ✨ 数据资产跳数据市场 参数适配 ([7dfc8d5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7dfc8d508312872306ef3bc16532b1ea22df1081))
- ✨ 机构管理 ([b79db5b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b79db5b545ba159b25a910f39b34cd851e322020))
- ✨ 机构管理初版配套修改 ([a7311bf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a7311bfeeb5c5dcc3b2509fb21ece8baefb10216))
- ✨ 模板管理 ([9da7e90](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9da7e904cacec51c3c25df6ce6602da53b46eb37))
- ✨ 血缘图 ([d9e9402](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9e94027679dc46458a0f50022805c64652c81a8))
- ✨ 血缘图 ([dbe2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbe2c308386df9286f60026925486879c7aab965))
- ✨ 跳转 datlas ([d215fa1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d215fa18ea4b2d7a8007e73fb2f1e13b0a4e47c2))

# 1.1.0 (2022-06-30)

### Bug Fixes

- 🐛 fix bug ([93b3e4e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93b3e4e8bb42c36f8567e093950c7904888b7087))
- 🐛 fix bug ([4a194e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4a194e3f09dcb4e12f5f6b637c60009feaafe32e))
- 🐛 修复微应用下，hash 路由模式资源释放错误 ([f5cf78c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f5cf78c0d8abbf00ee558307454fe07c904ed97c))
- 🐛 数据包绑定主题库；审批添加有效期 ([568d910](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/568d910c47715eff657932a4698f6893e6f3e329))

### Features

- ✨ add app header ([51825e8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/51825e8f110c33c9848d0b58d30ed2bf9f1b92ed))
- ✨ 低码 ETL 迁移数据工厂 ([3b96fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3b96fde0a1f958e105ef219a0e9fa0d58690c339))
- ✨ 支持微服务 ([41c6dac](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41c6dac6074fb9546bde5ae851d263586ae1bb21))
- ✨ 支持微服务 ([8e4c498](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8e4c498957cee15893fbbecf4956b634fec633cb))
- ✨ 添加数据包详情权限 ([9965798](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9965798c263b8d847a6f617cc6931f0fc90c1d96))
- ✨ 跨机构分享创建及审批 ([265ec15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/265ec15579d10f15b3fb0baf01acefac60453071))
- ✨ 迁移 sso + 微前端 ([b51cb10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b51cb107a0f84fb5729f16cc0118b6cf89d7c91d))
- ✨ 通用分离 ([8a90e4b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a90e4b5ee9bf1cdd6dd15d86dfa54b989f35dfc))
