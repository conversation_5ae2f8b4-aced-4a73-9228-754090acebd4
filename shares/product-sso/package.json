{"name": "@mdt/product-sso", "version": "1.8.14", "private": false, "description": "脉策产品级别sso通用", "keywords": ["mdt", "sso", "comm", "modules"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@i18n-chain/react": "2.0.1", "@mdt/business-controllers": "^1.17.8", "@mdt/login": "^1.17.6", "js-cookie": "^3.0.0-rc.0"}, "devDependencies": {"@types/json-logic-js": "^2.0.2", "@types/react-syntax-highlighter": "^13.5.2"}}