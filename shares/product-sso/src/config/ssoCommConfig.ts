import { getAbsoluteUrl, initHtmlElement } from '@mdtLogin/util/configUtil';
import { getPrefixProductName } from '../utils/productNameUtil';

export interface IDevelopValue {
  isDevelop?: boolean;
  developProxyApiUrl?: string;
  developEnvOrigin?: string;
}

export interface IRequestEncodeApi {
  api: string;
  data_keys?: string[];
  params_keys?: string[];
  keys?: string[];
}

export interface IFooter {
  visible?: boolean;
  text?: string;
  link?: string;
  icon?: string;
  psbLinkCode?: string;
}

export type IRequestEncodeApis = boolean | string | IRequestEncodeApi[];

export interface IRequestEncodeApisOptions {
  args?: string;
  algorithm?: string;
}

export type IRequestTransformApiMethod = boolean | { enable: boolean; method?: string[] };

export interface IOptions {
  isDialog?: boolean;
  redirect?: string;
}

// 是否微服务环境
export let IS_MICRO_ENV = false;
// 微服务下名称
export let MICRO_APP_NAME = '';
// 基础路由
export let BASE_NAME = '';
// 网站标题
export let WINDOW_TITLE = '';
// 网站描述
export let WINDOW_DESC = '';
// 是否启用hash路由
export let ENABLE_HASH_ROUTER = false;

//
export let DEPLOY_ORIGIN = '';
export let SITE_ORIGIN = '';

// 产品的名称，通用
export let PRODUCT_NAME = '';
// 部署的环境
export let ENV = '';
// 是否dev环境
export let IS_ENV_DEV = false;
// 是否staging环境
export let IS_ENV_STAGING = false;
// 是否prod环境
export let IS_ENV_PROD = false;

// 是否是本地开发
export let IS_DEVELOP = false;

export let PROXY_URL = '';

// api请求相关配置
export let API_URL = '';
export let REDIRECT_URI = '';
export let ENCODE_APIS: IRequestEncodeApis | undefined;
export let ENCODE_APIS_OPTIONS: IRequestEncodeApisOptions | undefined;
export let TRANSFORM_API_METHOD: IRequestTransformApiMethod | undefined;
export let API_URL_BASE_PATH = '';

export let REDIRECT = '';
export let INCLUDE_URL_PARAMS = false;
export let PLAIN_TOKEN = false;
export let PLAIN_TOKEN_KEY = 'token';
export let LANGUAGE = '';
export let TAB = '';
export let TABS = '';
export let LOGO: string | undefined;
export let PRIVACY:
  | boolean
  | { visible: boolean; check?: boolean; privacys?: { name: string; link: string }[] }
  | undefined;
export let TITLE = '';
export let DESC = '';
export let TITLE_SIZE = 24;
export let TITLE_CENTER: boolean;
export let FOOTER: IFooter | IFooter[] | undefined;
export let VERIFICATION = false;
export let BG_CONFIG = {};
export let GRID_CONFIG = {};
export let THEME = 'light';
export let SHOW_LANGUAGE = true;
export let DATLAS_URL = '';
export let LOCALE = {};
export let TABS_AUTO_HIDE = true;
export let ONE_TAB_HIDE_TABS = false;

export let SSO_OUT = '';
export let PARSE_API = '';
export let PARSE_API_METHOD = '';
export let PARSE_URL_PRARMS = [];
export let PARSE_COOKIE = '';
export let OUT_FORCE = false;

export let REGISTER_VISIBLE = false;
export let REGISTER_LINK = '';
export let REGISTER_JUMP_TARGET = '_self';

// eslint-disable-next-line sonarjs/cognitive-complexity, complexity
export const initCommConfig = (productName: string, develop: IDevelopValue, options?: IOptions, config?: any) => {
  const { isDialog = false, redirect } = options || {};
  const isMicro = window.__MICRO_APP_ENVIRONMENT__;
  // @ts-ignore
  const cfs = config || window[`__DM_${productName.replace('-', '_').toUpperCase()}_CFS`];
  const dp = cfs.deployPublicPath || (isMicro ? window.__MICRO_APP_PUBLIC_PATH__ : '/');
  cfs.deployPublicPath = dp;
  // 运行环境
  const env = cfs.deployEnv;
  // 部署域名
  const isDevelop = develop.isDevelop || false;
  // 三方sso
  const ssoOut = cfs.productSsoOut || {};
  // 注册配置
  const register = cfs.productRegister || {};

  // 通用
  PRODUCT_NAME = getPrefixProductName(productName);
  IS_MICRO_ENV = !!isMicro;
  MICRO_APP_NAME = window.__MICRO_APP_NAME__ || '';
  // 部署相关设置
  DEPLOY_ORIGIN = getAbsoluteUrl(dp);
  SITE_ORIGIN = getAbsoluteUrl(cfs.deploySitePath || dp);
  BASE_NAME = window.__MICRO_APP_BASE_ROUTE__ || cfs.deployRouterPath;
  WINDOW_TITLE = cfs.deployWindowTitle || document.title;
  WINDOW_DESC = cfs.deployWindowDesc || WINDOW_TITLE;
  ENABLE_HASH_ROUTER = cfs.deployEnableHashRouter || false;
  // 运行环境设置
  ENV = env || '';
  IS_ENV_DEV = env === 'dev';
  IS_ENV_STAGING = env === 'staging';
  IS_ENV_PROD = env === 'prod';
  IS_DEVELOP = isDevelop || IS_ENV_DEV;
  PROXY_URL = develop.developProxyApiUrl || '';
  // api请求相关配置
  API_URL = cfs.backendApiUrl ?? window.location.origin;
  DATLAS_URL = cfs.datlasUrl || API_URL;
  ENCODE_APIS = cfs.backendEncodeApis;
  ENCODE_APIS_OPTIONS = cfs.backendEncodeApisOptions;
  TRANSFORM_API_METHOD = cfs.backendTransformApiMethod;
  REDIRECT = decodeURIComponent(redirect || cfs.productRedirect || '');
  INCLUDE_URL_PARAMS = cfs.productIncludeUrlParams ?? false;
  LANGUAGE = cfs.productLanguage || '';
  TAB = cfs.productTab || '';
  TABS = cfs.productTabs;
  LOGO = !isDialog ? getAbsoluteUrl(cfs.productLogo, dp) : undefined;
  PRIVACY = cfs.productPrivacy || { visible: true, check: IS_ENV_PROD ? false : true };
  TITLE = cfs.productTitle;
  DESC = cfs.productDesc;
  FOOTER = !isDialog ? cfs.productFooter : {};
  VERIFICATION = cfs.productVerification;
  SHOW_LANGUAGE = cfs.productShowLanguage ?? true;
  LOCALE = cfs.productLocale || {};
  THEME = cfs.productTheme || 'light';
  TABS_AUTO_HIDE = cfs.productTabsAutoHide ?? true;
  ONE_TAB_HIDE_TABS = cfs.productOneTabHideTabs ?? false;
  API_URL_BASE_PATH = cfs.backendApiUrlBasePath ?? '/api';
  REDIRECT_URI = cfs.backendRedirectUri || API_URL + API_URL_BASE_PATH;
  PLAIN_TOKEN = cfs.productPlainToken ?? false;
  PLAIN_TOKEN_KEY = cfs.productPlainTokenKey ?? 'token';

  SSO_OUT = ssoOut.out ?? '';
  PARSE_API = ssoOut.api ?? '';
  PARSE_API_METHOD = ssoOut.apiMethod ?? 'get';
  PARSE_URL_PRARMS = ssoOut.apiParams || [];
  PARSE_COOKIE = ssoOut.cookie;
  OUT_FORCE = ssoOut.outForce ?? false;

  REGISTER_VISIBLE = register.visible ?? false;
  REGISTER_LINK = register.link ?? '';
  REGISTER_JUMP_TARGET = register.jumpTarget ?? '_self';

  BG_CONFIG = cfs.productBg || {};
  GRID_CONFIG = cfs.productGrid || {};
  TITLE_SIZE = cfs.productTitleSize || 24;
  TITLE_CENTER = cfs.productTitleCenter;

  // 修改一些html通用配置
  initHtmlElement(
    !!isMicro,
    WINDOW_TITLE,
    WINDOW_DESC,
    cfs.deployFavIcon || cfs.deployFavicon || `${DEPLOY_ORIGIN}static/favicon.ico`,
  );
  return cfs;
};
