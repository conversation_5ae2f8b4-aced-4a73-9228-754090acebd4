import _ from 'lodash';
import React, { CSSProperties } from 'react';
import { FOOTER } from '../../config/ssoCommConfig';
import nationalEmblem from './national_emblem.png';
// Footer interface
export interface IFooter {
  visible?: boolean;
  text?: string;
  link?: string;
  icon?: string;
  psbLinkCode?: string;
}

// Style constants
const defaultStyle = { marginLeft: 6, display: 'inline-flex', alignItems: 'center' };
const clickableStyle = { cursor: 'pointer' };
const imgStyle = { width: 14, height: 14, display: 'block', marginRight: 4 };
const pStyle: CSSProperties = {
  display: 'flex',
  flexWrap: 'wrap',
  gap: 4,
  alignItems: 'center',
  justifyContent: 'center',
};

// Link helpers
const getDefaultLink = () => 'https://beian.miit.gov.cn';
const getPsbLink = (code: string) => `https://beian.mps.gov.cn/#/query/webSearch?code=${code}`;

// Render a single link item
const LinkItem: React.FC<{
  text: string;
  link: string;
  icon?: string;
  keyId?: string;
}> = ({ text, link, icon, keyId }) => {
  const hasLink = !!link;

  return (
    <span
      key={keyId}
      style={{ ...defaultStyle, ...(hasLink ? clickableStyle : {}) }}
      onClick={hasLink ? () => window.open(link, '_blank') : undefined}
    >
      {icon ? <img src={icon} alt="" style={imgStyle} /> : <span />}
      {text}
    </span>
  );
};

// Default footer content
const DefaultFooter: React.FC<{ nationalEmblem?: string }> = ({ nationalEmblem }) => {
  const defaultLink = getDefaultLink();
  const psbLink = getPsbLink('31011002007274');

  return (
    <p style={pStyle}>
      <span>©2025 上海脉策数据科技有限公司</span>
      <LinkItem text="沪公网安备31011002007274号" link={psbLink} icon={nationalEmblem} />
      <LinkItem text="沪ICP备15037556号-8" link={defaultLink} />
    </p>
  );
};

// Array footer content
const ArrayFooter: React.FC<{ items: IFooter[] }> = ({ items }) => {
  return (
    <p style={pStyle}>
      {_.map(items, (item) => {
        const { visible = true, text = '', link = '', icon, psbLinkCode } = item;
        if (!visible) return null;
        const finalLink = psbLinkCode ? getPsbLink(psbLinkCode) : link || '';
        return <LinkItem text={text} link={finalLink} icon={icon} keyId={text} />;
      })}
    </p>
  );
};

// Object footer content
const ObjectFooter: React.FC<{ item: IFooter }> = ({ item }) => {
  const { visible = true, text = '', link = '', icon, psbLinkCode } = item;
  if (!visible) return null;
  const finalLink = psbLinkCode ? getPsbLink(psbLinkCode) : link || '';

  return (
    <p style={pStyle}>
      <LinkItem text={text} link={finalLink} icon={icon} />
    </p>
  );
};

// Main Footer component
const Footer: React.FC<{}> = () => {
  if (_.isArray(FOOTER)) {
    return <ArrayFooter items={FOOTER} />;
  }

  if (_.isBoolean(FOOTER)) {
    return FOOTER ? <DefaultFooter nationalEmblem={nationalEmblem} /> : null;
  }

  if (_.isObject(FOOTER)) {
    return <ObjectFooter item={FOOTER} />;
  }

  // Default fallback
  return <DefaultFooter nationalEmblem={nationalEmblem} />;
};

export { Footer };
