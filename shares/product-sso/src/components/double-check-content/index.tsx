import { ChangeEvent, Component } from 'react';
import { Button } from '@mdtLogin/components/button';
import { Input } from '@mdtLogin/components/input';

interface IProps {
  onClick: (val: string) => void;
  doubleCheckDesc: string;
  doubleCheckPlaceholder: string;
  doubleCheckButtonText: string;
}
interface IState {
  doubleValue: string;
  loading: boolean;
  disabled: boolean;
}

class DoubleCheckContent extends Component<IProps, IState> {
  public constructor(props: IProps) {
    super(props);
    this.state = { doubleValue: '', loading: false, disabled: true };
  }

  public _onClick = async () => {
    try {
      this.setState({ loading: true, disabled: true });
      await this.props.onClick(this.state.doubleValue);
      this.setState({ loading: false, disabled: false });
    } catch (error) {
      this.setState({ loading: false, disabled: false });
    }
  };

  public _onChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    this.setState({
      doubleValue: value,
      disabled: value.length !== 6,
    });
  };

  public render() {
    return (
      <div>
        <p>{this.props.doubleCheckDesc}</p>
        <Input placeholder={this.props.doubleCheckPlaceholder} onChange={this._onChange} />
        <Button
          style={{
            float: 'right',
            marginTop: '20px',
            width: '78px',
            height: '32px',
            fontSize: '14px',
            fontWeight: 'normal',
          }}
          onClick={this._onClick}
          disabled={this.state.disabled}
          loading={this.state.loading}
        >
          {this.props.doubleCheckButtonText}
        </Button>
      </div>
    );
  }
}

export default DoubleCheckContent;
