{"name": "workflow", "cnName": "流程引擎", "description": "流程引擎--全新的自动化流程, 带你体验高效，便捷的自动化服务", "version": "1.41.33", "private": true, "scripts": {"start": "cross-env DEVELOP_ENV=dev craco start", "start:staging": "cross-env DEVELOP_ENV=staging craco start", "start:debug": "cross-env DEVELOP_ENV=debug craco start", "release": "CI=false craco build", "release:analyze": "craco build --analyze", "test": "craco test"}, "cracoConfig": "craco.config.js", "dependencies": {"@dagrejs/dagre": "^1.1.2", "@mdt/product-micro-modules": "^1.48.33", "@mdt/product-tasks": "^1.25.18", "elkjs": "^0.9.1", "react-copy-to-clipboard": "^5.1.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}