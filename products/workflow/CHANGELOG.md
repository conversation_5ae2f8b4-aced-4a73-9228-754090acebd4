# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.41.33](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.33) (2025-07-24)

### Features

- ✨ table 支持列配置 ([dae481d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dae481d534f5a5355fdf230394f2033e5ed1842c))

## [1.41.32](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.32) (2025-07-17)

**Note:** Version bump only for package workflow

## [1.41.31](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.31) (2025-07-15)

**Note:** Version bump only for package workflow

## [1.41.30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.30) (2025-07-15)

**Note:** Version bump only for package workflow

## [1.41.29](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.29) (2025-07-08)

**Note:** Version bump only for package workflow

## [1.41.28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.28) (2025-07-03)

**Note:** Version bump only for package workflow

## [1.41.27](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.27) (2025-06-19)

**Note:** Version bump only for package workflow

## [1.41.26](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.26) (2025-06-19)

**Note:** Version bump only for package workflow

## [1.41.25](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.25) (2025-06-10)

**Note:** Version bump only for package workflow

## [1.41.24](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.24) (2025-05-29)

**Note:** Version bump only for package workflow

## [1.41.23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.23) (2025-05-19)

**Note:** Version bump only for package workflow

## [1.41.22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.22) (2025-05-14)

**Note:** Version bump only for package workflow

## [1.41.21](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.21) (2025-05-14)

**Note:** Version bump only for package workflow

## [1.41.20](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.20) (2025-04-27)

**Note:** Version bump only for package workflow

## [1.41.19](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.19) (2025-04-27)

**Note:** Version bump only for package workflow

## [1.41.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.18) (2025-04-23)

**Note:** Version bump only for package workflow

## [1.41.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.17) (2025-04-22)

**Note:** Version bump only for package workflow

## [1.41.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.16) (2025-04-21)

**Note:** Version bump only for package workflow

## [1.41.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.15) (2025-04-02)

**Note:** Version bump only for package workflow

## [1.41.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.14) (2025-04-01)

**Note:** Version bump only for package workflow

## [1.41.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.13) (2025-04-01)

**Note:** Version bump only for package workflow

## [1.41.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.12) (2025-03-31)

**Note:** Version bump only for package workflow

## [1.41.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.11) (2025-03-31)

**Note:** Version bump only for package workflow

## [1.41.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.10) (2025-03-27)

**Note:** Version bump only for package workflow

## [1.41.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.9) (2025-03-27)

**Note:** Version bump only for package workflow

## [1.41.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.8) (2025-03-27)

**Note:** Version bump only for package workflow

## [1.41.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.7) (2025-03-19)

**Note:** Version bump only for package workflow

## [1.41.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.6) (2025-03-18)

**Note:** Version bump only for package workflow

## [1.41.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.5) (2025-03-13)

**Note:** Version bump only for package workflow

## [1.41.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.4) (2025-03-10)

**Note:** Version bump only for package workflow

## [1.41.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.3) (2025-02-27)

**Note:** Version bump only for package workflow

## [1.41.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.2) (2025-02-26)

**Note:** Version bump only for package workflow

## [1.41.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.1) (2025-02-24)

**Note:** Version bump only for package workflow

# [1.41.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.41.0) (2025-02-24)

**Note:** Version bump only for package workflow

## [1.40.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.6) (2025-02-17)

**Note:** Version bump only for package workflow

## [1.40.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.5) (2025-01-23)

**Note:** Version bump only for package workflow

## [1.40.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.4) (2025-01-16)

**Note:** Version bump only for package workflow

## [1.40.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.3) (2025-01-09)

**Note:** Version bump only for package workflow

## [1.40.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.2) (2025-01-07)

**Note:** Version bump only for package workflow

## [1.40.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.1) (2025-01-06)

**Note:** Version bump only for package workflow

# [1.40.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.40.0) (2024-12-23)

**Note:** Version bump only for package workflow

## [1.39.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.10) (2024-12-16)

**Note:** Version bump only for package workflow

## [1.39.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.9) (2024-12-16)

**Note:** Version bump only for package workflow

## [1.39.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.8) (2024-12-16)

**Note:** Version bump only for package workflow

## [1.39.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.7) (2024-12-03)

**Note:** Version bump only for package workflow

## [1.39.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.6) (2024-12-02)

**Note:** Version bump only for package workflow

## [1.39.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.5) (2024-12-02)

**Note:** Version bump only for package workflow

## [1.39.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.4) (2024-12-02)

**Note:** Version bump only for package workflow

## [1.39.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.3) (2024-12-02)

**Note:** Version bump only for package workflow

## [1.39.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.2) (2024-12-02)

**Note:** Version bump only for package workflow

## [1.39.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.1) (2024-11-26)

**Note:** Version bump only for package workflow

# [1.39.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.39.0) (2024-11-26)

**Note:** Version bump only for package workflow

## [1.38.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.5) (2024-11-19)

**Note:** Version bump only for package workflow

## [1.38.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.4) (2024-11-15)

**Note:** Version bump only for package workflow

## [1.38.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.3) (2024-11-14)

**Note:** Version bump only for package workflow

## [1.38.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.2) (2024-11-14)

**Note:** Version bump only for package workflow

## [1.38.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.1) (2024-11-07)

**Note:** Version bump only for package workflow

# [1.38.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.38.0) (2024-11-05)

### Features

- 流程引擎可配置 ([7a6eef4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7a6eef49849859e81ebad9a9c21bc897f6176241))

## [1.37.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.12) (2024-11-04)

**Note:** Version bump only for package workflow

## [1.37.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.11) (2024-10-31)

**Note:** Version bump only for package workflow

## [1.37.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.10) (2024-10-29)

**Note:** Version bump only for package workflow

## [1.37.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.9) (2024-10-28)

**Note:** Version bump only for package workflow

## [1.37.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.8) (2024-10-27)

**Note:** Version bump only for package workflow

## [1.37.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.7) (2024-10-25)

**Note:** Version bump only for package workflow

## [1.37.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.6) (2024-10-25)

**Note:** Version bump only for package workflow

## [1.37.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.5) (2024-10-25)

**Note:** Version bump only for package workflow

## [1.37.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.4) (2024-10-25)

**Note:** Version bump only for package workflow

## [1.37.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.3) (2024-10-25)

**Note:** Version bump only for package workflow

## [1.37.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.2) (2024-10-23)

**Note:** Version bump only for package workflow

## [1.37.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.1) (2024-10-23)

**Note:** Version bump only for package workflow

# [1.37.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.37.0) (2024-10-23)

**Note:** Version bump only for package workflow

## [1.36.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.10) (2024-10-23)

**Note:** Version bump only for package workflow

## [1.36.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.9) (2024-10-22)

**Note:** Version bump only for package workflow

## [1.36.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.8) (2024-10-21)

**Note:** Version bump only for package workflow

## [1.36.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.7) (2024-10-15)

**Note:** Version bump only for package workflow

## [1.36.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.6) (2024-09-12)

### Features

- ✨ add form resource ([85bbcb9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85bbcb90bc0d2e1d8995bda981b026e391065989))
- ✨ add workflow ins display ([13a1b28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/13a1b280f1c40f41fbeaa90e27be38bac6f3f0d3))

## [1.36.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.5) (2024-08-29)

**Note:** Version bump only for package workflow

## [1.36.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.4) (2024-08-27)

**Note:** Version bump only for package workflow

## [1.36.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.3) (2024-08-19)

**Note:** Version bump only for package workflow

## [1.36.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.2) (2024-08-13)

**Note:** Version bump only for package workflow

## [1.36.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.1) (2024-08-12)

**Note:** Version bump only for package workflow

# [1.36.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.36.0) (2024-08-09)

**Note:** Version bump only for package workflow

## [1.35.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.35.2) (2024-08-07)

**Note:** Version bump only for package workflow

## [1.35.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.35.1) (2024-08-05)

**Note:** Version bump only for package workflow

# [1.35.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.35.0) (2024-08-02)

**Note:** Version bump only for package workflow

## [1.34.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.5) (2024-08-02)

**Note:** Version bump only for package workflow

## [1.34.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.4) (2024-07-29)

**Note:** Version bump only for package workflow

## [1.34.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.3) (2024-07-25)

**Note:** Version bump only for package workflow

## [1.34.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.2) (2024-07-25)

### Features

- ✨ add debug mode ([cb107a7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb107a775a003fb7fd0dee9231d60bfdcd5ceff5))

## [1.34.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.1) (2024-07-24)

**Note:** Version bump only for package workflow

# [1.34.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.34.0) (2024-07-22)

**Note:** Version bump only for package workflow

## [1.33.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.33.4) (2024-07-17)

**Note:** Version bump only for package workflow

## [1.33.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.33.3) (2024-07-10)

### Features

- user task detail page custom ([2e5670f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2e5670f81f52ec0d3b7d4969678881ab708bf69a))

## [1.33.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.33.2) (2024-07-03)

**Note:** Version bump only for package workflow

## [1.33.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.33.1) (2024-07-02)

**Note:** Version bump only for package workflow

# [1.33.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.33.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))

## [1.32.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.32.4) (2024-06-24)

**Note:** Version bump only for package workflow

## [1.32.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.32.3) (2024-06-17)

**Note:** Version bump only for package workflow

## [1.32.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.32.2) (2024-06-03)

**Note:** Version bump only for package workflow

## [1.32.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.32.1) (2024-05-20)

### Features

- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))
- refacto wf visualization ([5693f8e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5693f8e8279c503bfaf4fcf7e099adacef2e6b23))

# [1.32.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.32.0) (2024-05-13)

**Note:** Version bump only for package workflow

## [1.31.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.31.1) (2024-04-29)

**Note:** Version bump only for package workflow

# [1.31.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.31.0) (2024-04-24)

**Note:** Version bump only for package workflow

## [1.30.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.6) (2024-04-24)

**Note:** Version bump only for package workflow

## [1.30.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.5) (2024-04-24)

**Note:** Version bump only for package workflow

## [1.30.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.4) (2024-04-23)

**Note:** Version bump only for package workflow

## [1.30.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.3) (2024-04-23)

**Note:** Version bump only for package workflow

## [1.30.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.2) (2024-04-23)

**Note:** Version bump only for package workflow

## [1.30.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.1) (2024-04-16)

**Note:** Version bump only for package workflow

# [1.30.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.30.0) (2024-04-15)

**Note:** Version bump only for package workflow

# [1.29.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.29.0) (2024-04-08)

**Note:** Version bump only for package workflow

## [1.28.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.28.3) (2024-04-01)

**Note:** Version bump only for package workflow

## [1.28.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.28.2) (2024-04-01)

**Note:** Version bump only for package workflow

## [1.28.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.28.1) (2024-03-26)

**Note:** Version bump only for package workflow

# [1.28.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.28.0) (2024-03-25)

**Note:** Version bump only for package workflow

## [1.27.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.27.1) (2024-03-11)

**Note:** Version bump only for package workflow

# [1.27.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.27.0) (2024-03-11)

### Performance Improvements

- ⚡️ build fast and smaller ([55b555f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55b555f51ce6f1fa8c3ae7387224c52e1636ec27))

## [1.26.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.26.2) (2024-03-07)

**Note:** Version bump only for package workflow

## [1.26.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.26.1) (2024-03-01)

**Note:** Version bump only for package workflow

# [1.26.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.26.0) (2024-03-01)

**Note:** Version bump only for package workflow

## [1.25.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.25.2) (2024-02-28)

**Note:** Version bump only for package workflow

## [1.25.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.25.1) (2024-02-23)

**Note:** Version bump only for package workflow

# [1.25.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.25.0) (2024-02-23)

### Features

- ✨ bpmn 变量管理&审批调整 ([c7bf101](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c7bf101bf05471e43a1cd926094d2d80c0e35754))

# [1.24.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.24.0) (2024-01-29)

**Note:** Version bump only for package workflow

## [1.23.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.23.1) (2024-01-23)

### Bug Fixes

- 🐛 xml decode ([3c1b16a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3c1b16a55d959437a58c8df7bc49fd7873a904a5))

### Features

- ✨ add bpmn visualization ([e4b41e5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e4b41e505dd102723016580dbaa26c944bd00447))

# [1.23.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.23.0) (2024-01-15)

**Note:** Version bump only for package workflow

# [1.22.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.22.0) (2024-01-08)

**Note:** Version bump only for package workflow

## [1.21.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.21.2) (2024-01-02)

**Note:** Version bump only for package workflow

## [1.21.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.21.1) (2024-01-02)

**Note:** Version bump only for package workflow

# [1.21.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.21.0) (2024-01-02)

### Features

- ✨ 接入移动端级联&定制表单覆盖初始表单字段值&timer 定制表单 ([e94a877](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e94a8776bbe3e63f96515202eddddc3cb8b3d632))

## [1.20.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.20.3) (2023-12-25)

**Note:** Version bump only for package workflow

## [1.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.20.2) (2023-12-25)

**Note:** Version bump only for package workflow

## [1.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.20.1) (2023-12-25)

**Note:** Version bump only for package workflow

# [1.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.20.0) (2023-12-25)

### Features

- ✨ 表单添加创建者参数& 上传数据包填报模版时支持重选数据包;文件组件添加最大最小数量限制 ([4ebb430](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ebb43037bf9d7753c44ad3f89d60b5809adb0e1))

## [1.19.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.19.3) (2023-12-19)

### Features

- ✨ 数据包填报导出后支持上传 ([337dfc2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/337dfc290d87970bd590137b729aff854aed9c3d))

## [1.19.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.19.2) (2023-12-18)

### Features

- ✨ bpmn 全局变量设置 & 数据市场审批加 loading ([d505788](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d50578812eb45b408f596b34a343142240ffefee))

## [1.19.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.19.1) (2023-12-11)

**Note:** Version bump only for package workflow

# [1.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.19.0) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))

### Features

- ✨ 发送消息到钉钉 ([c4c08d0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4c08d017b73a55171da60e6679ec002a55d391d))

## [1.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.18.1) (2023-12-04)

**Note:** Version bump only for package workflow

# [1.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.18.0) (2023-12-04)

**Note:** Version bump only for package workflow

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.17.1) (2023-12-01)

**Note:** Version bump only for package workflow

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.17.0) (2023-12-01)

### Features

- ✨ api cache refactor ([859ab7f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/859ab7f71706057fb8e08b61a2e82a05d364c45e))
- ✨ 自增表格&资源分享支持数据源 ([27820ab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27820abbd075bea5471885660183f851e76241b5))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.16.0) (2023-11-20)

**Note:** Version bump only for package workflow

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.15.1) (2023-11-13)

**Note:** Version bump only for package workflow

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.15.0) (2023-11-13)

**Note:** Version bump only for package workflow

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.14.0) (2023-11-06)

**Note:** Version bump only for package workflow

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.13.1) (2023-11-03)

**Note:** Version bump only for package workflow

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.13.0) (2023-11-01)

**Note:** Version bump only for package workflow

## [1.12.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.12.2) (2023-10-31)

**Note:** Version bump only for package workflow

## [1.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.12.1) (2023-10-31)

### Performance Improvements

- ⚡️ html load faster ([e7f2d23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7f2d233d799c6081d3cae94f1ca0663ebe20e28))

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.12.0) (2023-10-30)

**Note:** Version bump only for package workflow

## [1.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.11.1) (2023-10-26)

**Note:** Version bump only for package workflow

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.11.0) (2023-10-25)

**Note:** Version bump only for package workflow

## [1.10.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.10.3) (2023-10-23)

**Note:** Version bump only for package workflow

## [1.10.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.10.2) (2023-10-16)

**Note:** Version bump only for package workflow

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.10.1) (2023-10-16)

**Note:** Version bump only for package workflow

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.10.0) (2023-10-10)

**Note:** Version bump only for package workflow

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.9.0) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.8.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.8.3) (2023-09-04)

**Note:** Version bump only for package workflow

## [1.8.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.8.2) (2023-08-22)

**Note:** Version bump only for package workflow

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.8.1) (2023-08-14)

**Note:** Version bump only for package workflow

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.8.0) (2023-07-31)

### Features

- ✨ add totalAsync ([b3bdb28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b3bdb287780d82f85c9b1149a688ddc2c4fe0d41))

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.7.1) (2023-07-26)

**Note:** Version bump only for package workflow

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.7.0) (2023-07-24)

**Note:** Version bump only for package workflow

## [1.6.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.6.2) (2023-07-03)

**Note:** Version bump only for package workflow

## [1.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.6.1) (2023-07-03)

### Features

- ✨ 通过数据包详情页发起流程填报 ([5c2d77b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c2d77b6910e07dc9bf9a1e02dd0162c85ebb671))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.6.0) (2023-06-20)

**Note:** Version bump only for package workflow

## [1.5.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.5.2) (2023-06-13)

**Note:** Version bump only for package workflow

## [1.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.5.1) (2023-06-12)

### Bug Fixes

- 🐛 fix dark modal ([8c61d35](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c61d35457b10814eaf73356ff4a0d8aad854065))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.5.0) (2023-06-12)

### Features

- ✨ bpmn add dark modal ([bbd66f3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bbd66f34e92e79d6eaf58c7b37db489d5b2fdb0a))

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.4.1) (2023-06-06)

**Note:** Version bump only for package workflow

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.4.0) (2023-06-05)

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

## [1.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.3.1) (2023-05-22)

**Note:** Version bump only for package workflow

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.3.0) (2023-05-15)

**Note:** Version bump only for package workflow

## [1.2.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.7) (2023-04-27)

**Note:** Version bump only for package workflow

## [1.2.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.6) (2023-04-24)

**Note:** Version bump only for package workflow

## [1.2.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.5) (2023-04-17)

**Note:** Version bump only for package workflow

## [1.2.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.4) (2023-04-11)

**Note:** Version bump only for package workflow

## [1.2.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.3) (2023-04-10)

### Bug Fixes

- 🐛 流程表单提升层级;资源分享支持脚本 ([6261924](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6261924f22f7f2722f98c49959b055bbf8c24b75))

## [1.2.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.2) (2023-04-07)

### Bug Fixes

- 🐛 apply user name error ([b61e09a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b61e09a0d3bcbd778a4cdcc1ea73e31830a0684f))

## [1.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.1) (2023-04-07)

### Bug Fixes

- 🐛 fix api error ([56cd6ec](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/56cd6ec6cb0717c9724fe42a0592df0b9764b943))
- 🐛 流程引擎表单样式问题 ([9a88343](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a88343fb9576d124fa005ccbcc1be889ebcfd64))

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.2.0) (2023-04-06)

### Features

- ✨ add custom bpmn property;审批流程 ([f08e20f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f08e20f156da195f860f5307f6d9a898a48bdf78))

## [1.1.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.1.3) (2023-03-24)

**Note:** Version bump only for package workflow

## [1.1.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.1.2) (2023-03-20)

### Bug Fixes

- 🐛 测试数据库连接时删除 host;流程列表加权限参数 ([b1b321f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b1b321f3ff5a74d4251bd5da34925cd3d59bf23d))

### Features

- ✨ add copy model ([051ebe0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/051ebe03864b2c261b203b7cb9c1e9a35302dd5c))

## [1.1.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.1.1) (2023-03-14)

**Note:** Version bump only for package workflow

# [1.1.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@1.1.0) (2023-03-13)

### Features

- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))

## 1.0.2 (2023-02-20)

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))
