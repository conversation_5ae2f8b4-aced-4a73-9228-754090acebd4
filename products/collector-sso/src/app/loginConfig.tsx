import _ from 'lodash';
import md5 from 'md5';
import urlParameterAppend from 'url-parameter-append';
import Dialog from '@mdtLogin/components/dialog';
import Toast from '@mdtLogin/components/toast';
import { ConfigTabEnum, IAccount, IAccountState, ILabelValue, IPhoneState } from '@mdtLogin/config';
import DialogPasswordError from '@mdtProSso/components/dialog-password-error';
import DialogPrivacy from '@mdtProSso/components/dialog-privacy';
import DoubleCheckContent from '@mdtProSso/components/double-check-content';
import { Footer } from '@mdtProSso/components/footer';
import theme from '@mdtProSso/theme/light';
import { microWillMount } from '@mdtProSso/utils/microUtil';
import { getSupportCodes } from '@mdtProSso/utils/territoryCodeUtil';
import { afterLoginSuccess, apiAuthUrl, fetchRequest, requestError } from '../_util/fetchUtil';
import {
  getLanguageFromStore,
  getPrivacyFromStore,
  getTabFromStore,
  getTokenFromStore,
  removeTokenFromStore,
  saveLanguageToStore,
  setDialogFromSessionStore,
} from '../_util/storageUtil';
import {
  getForceFromUrl,
  getLanguageFromUrl,
  getQFromUrl,
  getRedirectFromUrl,
  getTabFromUrl,
  isDialog,
} from '../_util/util';
import { Verify } from '../components/verify-page';
import {
  BG_CONFIG,
  DESC,
  DINGTALK_ID,
  DINGTALK_OAUTH_SETTING_ID,
  DINGTALK_SETTING_ID,
  DK_OAUTH_ID,
  GRID_CONFIG,
  IS_MICRO_ENV,
  JUMP_OAUTH,
  LANGUAGE,
  LOGO,
  ONE_TAB_HIDE_TABS,
  PRIVACY,
  REDIRECT,
  REDIRECT_URI,
  TAB,
  TABS,
  TEMPLATE_ID,
  TITLE,
  TITLE_CENTER,
  TITLE_SIZE,
  VERIFICATION,
  WECHAT_ID,
  WECHAT_SETTING_ID,
} from '../config';
import i18n from '../languages';

// 通用配置逻辑处理===================================================================================
let PhoneNumber: any;
let themeMode = 'light';

// 获取扫码state
const getState = (qrType: string) => {
  const stateUrl = urlParameterAppend(
    `login/${qrType}/scan/redirect`,
    'extra_uri',
    getRedirectFromUrl() || REDIRECT,
    'uri',
    `${window.location.origin}${window.location.pathname}verify?qr_type=${qrType}`,
  );
  return fetchRequest(stateUrl)
    .then((response) => {
      return receiveResponse(response, (result: any) => Promise.resolve(result.state));
    })
    .catch(() => {
      Toast.error(i18n.translate(`api.503`));
    });
};
// 空值校验
const isEmpty = (value: string, errorMsg: string) => {
  return value ? '' : errorMsg;
};
// api请求结果处理
const receiveResponse = async (response: any, callBack: Function, failCallback?: Function) => {
  const status = response.status;
  const _response = await response.json();
  const { rc, result } = _response;
  if (rc === 0) {
    return callBack(result);
  } else {
    Toast.error(requestError(_response, status, i18n));
    return failCallback?.(_response);
  }
};

// Language的相关配置================================================================================
const getLanguageConfig = () => {
  // url接收的参数
  const urlLanguage = getLanguageFromUrl();
  // store的存储的参数
  const storeLanguage = getLanguageFromStore();
  // 弹窗模式不展示语言
  const dialogType = isDialog();
  // 默认值
  const defaultLanguages = ['cn', 'en']; // 支持的语言
  let language = '';
  if (_.includes(defaultLanguages, urlLanguage)) {
    language = urlLanguage;
  }
  if (!language && _.includes(defaultLanguages, storeLanguage)) {
    language = storeLanguage;
  }
  if (!language && _.includes(defaultLanguages, LANGUAGE)) {
    language = LANGUAGE;
  }
  !language && (language = 'cn');
  // 加载语言资源
  i18n.locale(language);
  // 存储语言
  saveLanguageToStore(language);

  // 切换语言
  const changeLanguage = (value: string) => {
    saveLanguageToStore(value);
    window.location.href = urlParameterAppend(window.location.href, 'language', value);
  };

  return {
    disable: !!dialogType,
    initialState: () => ({ language }),
    supportLanguages: [
      { label: i18n.chain.cn, value: 'cn' },
      { label: i18n.chain.en, value: 'en' },
    ],
    changeLanguage,
  };
};

// 判断是否有phone
// Tabs的相关配置====================================================================================
const getTabsConfig = (privacyTitle: string) => {
  const DATLAS_KEY = 'datlas';
  // 登录项列表
  const defaultTabs = [
    ConfigTabEnum.ACCOUNT,
    ConfigTabEnum.WECHAT,
    ConfigTabEnum.DINGTALK,
    ConfigTabEnum.PHONE,
    DATLAS_KEY,
    ConfigTabEnum.DINGTALK_OAUTH,
  ];
  let tabs = defaultTabs;
  if (_.isArray(TABS)) {
    let ts: ConfigTabEnum[] = [];
    _.forEach(TABS, (it) => {
      _.includes(defaultTabs, it) && ts.push(it as ConfigTabEnum);
    });
    ts.length && (tabs = ts);
  }
  // 选中的登录项
  const urlTab = getTabFromUrl();
  const storeTab = getTabFromStore();
  let tab = '';
  if (_.includes(tabs, urlTab)) {
    tab = urlTab;
  }
  if (!tab && _.includes(tabs, storeTab)) {
    tab = storeTab;
  }
  if (!tab && _.includes(tabs, TAB)) {
    tab = TAB;
  }
  !tab && (tab = tabs[0]);
  // 标签映射
  const tabLabelMap: Record<string, ILabelValue> = {
    [ConfigTabEnum.PHONE]: {
      label: i18n.chain.loginByPhone,
      value: ConfigTabEnum.PHONE,
    },
    [ConfigTabEnum.WECHAT]: {
      label: i18n.chain.loginByWechat,
      value: ConfigTabEnum.WECHAT,
    },
    [ConfigTabEnum.DINGTALK]: {
      label: i18n.chain.loginByDingtalk,
      value: ConfigTabEnum.DINGTALK,
    },
    [ConfigTabEnum.ACCOUNT]: {
      label: i18n.chain.loginByAccount,
      value: ConfigTabEnum.ACCOUNT,
    },
    [DATLAS_KEY]: {
      label: i18n.chain.loginByDatlas,
      value: DATLAS_KEY,
      type: ConfigTabEnum.ACCOUNT,
      overrideConfig: () => ({
        ...getDatlasConfig(privacyTitle),
      }),
    },
    [ConfigTabEnum.DINGTALK_OAUTH]: {
      label: i18n.chain.loginByDingtalkOAuth,
      value: ConfigTabEnum.DINGTALK_OAUTH,
    },
  };
  const items: ILabelValue[] = _.map(tabs, (it) => tabLabelMap[it]);
  if (
    _.some(items, ({ value, type }) => _.isEqual(value, ConfigTabEnum.PHONE) || _.isEqual(type, ConfigTabEnum.PHONE))
  ) {
    import('awesome-phonenumber-fork').then((resp) => {
      PhoneNumber = resp.default;
    });
  }

  return {
    // 默认登录项
    defaultItem: tab,
    // 显示登录选项, 会按照顺序展示
    items,
    hideWhenOneTab: ONE_TAB_HIDE_TABS,
  };
};

// 手机号的相关配置==================================================================================
// 校验验证码
const verifyCaptcha = (value: string): Promise<string> => {
  return new Promise((resolve) => {
    let error = isEmpty(value, i18n.chain.captchaVerifyEmpty);
    !error && (error = new RegExp(/^\d{6}$/).test(value) ? '' : i18n.chain.captchaVerifyInvalid);
    resolve(error);
  });
};
const getPhoneConfig = (privacyTitle: string) => {
  // 校验手机号
  const verifyPhone = (value: string, iso: string): Promise<string> => {
    return new Promise((resolve) => {
      let error = isEmpty(value, i18n.chain.phoneVerifyEmpty);
      if (!error) {
        const list = PhoneNumber.getSupportedRegionCodes();
        if (list.includes(iso)) {
          const example = PhoneNumber.getExample(iso, 'mobile').getNumber('significant');
          error = new PhoneNumber(value, iso).isValid() ? '' : i18n.chain.phoneVerifyInvalid({ example });
        }
      }
      PhoneNumber.releaseMemory();
      resolve(error);
    });
  };

  // phone verifyAll
  const verifyAllPhone = (values: IPhoneState, checkPrivacy: boolean, setCheckPrivacy: Function, login: Function) => {
    return Promise.all([verifyPhone(values.phone, values.selectISO), verifyCaptcha(values.captcha)]).then(
      ([phoneError, captchaError]) => {
        const hasError = phoneError || captchaError;
        if (!hasError && !checkPrivacy) {
          Dialog.open({
            content: (close: any) => (
              <DialogPrivacy
                title={privacyTitle}
                desc={i18n.chain.privacyDesc({ str: privacyTitle })}
                buttonOkLabel={i18n.chain.privacyOk}
                buttonCancelLabel={i18n.chain.privacyCancel}
                onClick={() => {
                  setCheckPrivacy(true);
                  login();
                  close();
                }}
                onCancel={close}
              />
            ),
            width: '450px',
          });
          return Promise.resolve([true, {}]);
        }
        return [hasError, { phoneError, captchaError }];
      },
    );
  };
  // 发送验证码
  const sendCaptcha = (values: IPhoneState) => {
    fetchRequest(`login/sms/${TEMPLATE_ID}/${Number(values.code as string)}/${values.phone}`)
      .then((response) => {
        return receiveResponse(response, () => {
          Toast.success(i18n.chain.captchaSendedTip);
        });
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      });
  };
  // 点击登录
  const loginByPhone = (values: IPhoneState, setState: Function) => {
    fetchRequest(`login/sms/${TEMPLATE_ID}/${Number(values.code as string)}/${values.phone}?code=${values.captcha}`, {})
      .then(async (response) => {
        return receiveResponse(response, async (result: any) => {
          await afterLoginSuccess(result);
        });
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      })
      .finally(() => {
        setState({ ...values, buttonLoading: false, buttonDisabled: false });
      });
  };

  return {
    phoneLabel: i18n.chain.phoneLabel,
    phonePlaceholder: i18n.chain.phonePlaceholder,
    captchaLabel: i18n.chain.captchaLabel,
    captchaPlaceholder: i18n.chain.captchaPlaceholder,
    captchaInterval: 60,
    captchaButtonLabel: i18n.chain.captchaButtonLabel,
    captchaButtonSendedLabel: (gap: number) => {
      return i18n.chain.captchaButtonSendedLabel({ gap });
    },
    buttonLabel: i18n.chain.buttonLabel,
    buttonLoadingLabel: i18n.chain.buttonLoadingLabel,
    supportCodes: getSupportCodes(),
    initialState: () => ({
      phone: '',
      phoneError: '',
      captcha: '',
      captchaError: '',
      selectISO: 'CN',
      code: '86',
      buttonDisabled: false,
      buttonLoading: false,
    }),
    verifyPhone: verifyPhone,
    verifyCaptcha: verifyCaptcha,
    verifyAll: verifyAllPhone,
    sendCaptcha: sendCaptcha,
    loginIn: loginByPhone,
    verification: VERIFICATION,
  };
};

const getVerificationConfig = () => {
  return {
    verifySuccess: i18n.chain.verifySlideSuccess,
    verifyPlaceholder: i18n.chain.verifyPlaceholder,
    verifyTitle: i18n.chain.verifyTitle,
    verifyRetryText: i18n.chain.verifyRetryText,
  };
};

const getPrivacyConfig = (privacys: any[]) => {
  // store的存储隐私
  const storePrivacy = getPrivacyFromStore();
  const isStoreCheck = storePrivacy === 'true';
  let privacyVisible = true;
  let privacyCheck = false;
  if (_.isBoolean(PRIVACY)) {
    privacyVisible = PRIVACY;
  }
  if (_.isObject(PRIVACY)) {
    privacyVisible = PRIVACY.visible;
    PRIVACY.check && (privacyCheck = PRIVACY.check);
  }
  if (isStoreCheck) {
    privacyCheck = isStoreCheck;
  }

  return {
    visible: privacyVisible,
    check: privacyCheck,
    privacyText: i18n.chain.privacyText,
    privacyslotText: i18n.chain.privacySlot,
    privacys,
  };
};

// 微信扫码的相关配置==================================================================================
const getWechatConfig = (privacyTitle: string) => {
  // 定义selfRedirect
  const selfRedirect = true;
  // 获取wechat state
  const getStateWechat = () => getState('wechat');
  // 获取wechat iframe src
  const getIframeSrcWechat = (wechatState: string, cssUrl: string) => {
    return [
      `https://open.weixin.qq.com/connect/qrconnect`,
      `?appid=${WECHAT_ID}`,
      `&scope=snsapi_login`,
      `&redirect_uri=${REDIRECT_URI}/login/wx_scan/${WECHAT_SETTING_ID}`,
      `&state=${wechatState}`,
      `&style=black`,
      `&href=${cssUrl}`,
      `&login_type=jssdk`,
      `&self_redirect=${selfRedirect}`,
      `&styletype=`,
      `&sizetype=`,
      `&bgcolor=`,
      `&rst=`,
    ].join('');
  };

  // 扫码失败
  const afterFailed = (reset: Function) => {
    reset();
  };

  return {
    successContent: i18n.chain.wechatSuccessContent,
    failContent: i18n.chain.wechatFailContent,
    statusContent: i18n.chain.wechatStatusContent,
    tipColor: themeMode === 'light' ? '#202939' : '#f4f6fc',
    privacyDesc: i18n.chain.privacyDesc({ str: privacyTitle }),
    flashInterval: 1000 * 60 * 2,
    initialState: () => ({ state: '' }),
    getState: getStateWechat,
    getIframeSrc: getIframeSrcWechat,
    selfRedirect,
    afterFailed: afterFailed,
    afterSuccess: afterLoginSuccess,
  };
};

// 钉钉扫码的相关配置==================================================================================
const getDingtalkConfig = (privacyTitle: string) => {
  // 获取 dingtalk state
  const getStateDingtalk = () => getState('dingtalk');
  // dingtalk iframe src
  const splicingGotoDingtalk = (state: string, loginTmpCode?: string) => {
    const gotoArgs = [
      `appid=${DINGTALK_ID}`,
      'response_type=code',
      'scope=snsapi_login',
      `state=${state}`,
      `redirect_uri=${REDIRECT_URI}/login/dingtalk/scan/${DINGTALK_SETTING_ID}`,
    ];
    loginTmpCode && gotoArgs.push(`loginTmpCode=${loginTmpCode}`);
    return `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?${gotoArgs.join('&')}`;
  };
  // 扫码失败
  const afterFailed = (reset: Function, msg?: string) => {
    reset();
    msg && Toast.error(msg);
  };

  return {
    flashInterval: 1000 * 60 * 2,
    privacyDesc: i18n.chain.privacyDesc({ str: privacyTitle }),
    initialState: () => ({ state: '' }),
    getState: getStateDingtalk,
    splicingGoto: splicingGotoDingtalk,
    selfRedirect: true,
    afterFailed: afterFailed,
    afterSuccess: afterLoginSuccess,
  };
};

// 钉钉授权扫码的相关配置==================================================================================
const getDingtalkOAuthConfig = (privacyTitle: string) => {
  // 获取 dingtalk state
  const getStateDingtalk = () => getState('dingtalk');
  // 获取iframe src
  const getIframeSrc = (state: string) => {
    return [
      `https://login.dingtalk.com/oauth2/auth`,
      `?client_id=${DK_OAUTH_ID}`,
      `&scope=${encodeURIComponent('openid corpid')}`,
      `&response_type=code`,
      `&redirect_uri=${encodeURIComponent(REDIRECT_URI + '/login/dingtalk/scan/v2/' + DINGTALK_OAUTH_SETTING_ID)}`,
      `&state=${state}`,
      `&prompt=consent`,
      `$FEForceLogin=true`,
    ].join('');
  };

  // 扫码失败
  const afterFailed = (reset: Function, msg?: string) => {
    reset();
    msg && Toast.error(msg);
  };

  return {
    initialState: () => ({ state: '' }),
    jumpOAuth: JUMP_OAUTH,
    privacyDesc: i18n.chain.privacyDesc({ str: privacyTitle }),
    getState: getStateDingtalk,
    getIframeSrc: getIframeSrc,
    afterFailed: afterFailed,
    afterSuccess: afterLoginSuccess,
    selfRedirect: true,
  };
};

// 账号登录的相关配置==================================================================================
const getAccountConfig = (privacyTitle: string): IAccount => {
  // 校验用户名
  const verifyName = (value: string): Promise<string> => {
    return new Promise((resolve) => {
      resolve(/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(value) ? '' : i18n.chain.accountVerify);
    });
  };
  // 校验密码
  const verifyPassword = (value: string): Promise<string> => {
    return new Promise((resolve) => {
      resolve(/^\S*(?=\S{6,})(?=\S*\d)(?=\S*[a-zA-Z])\S*$/.test(value) ? '' : i18n.chain.passwordVerify);
    });
  };
  // 验证account all
  const verifyAllAccount = (
    values: IAccountState,
    checkPrivacy: boolean,
    setCheckPrivacy: Function,
    login: Function,
  ) => {
    return Promise.all([verifyName(values.name), verifyPassword(values.password)]).then(
      ([nameError, passwordError]) => {
        const hasError: string = nameError || passwordError;
        if (!hasError && !checkPrivacy) {
          Dialog.open({
            content: (close: any) => (
              <DialogPrivacy
                title={privacyTitle}
                desc={i18n.chain.privacyDesc({ str: privacyTitle })}
                buttonOkLabel={i18n.chain.privacyOk}
                buttonCancelLabel={i18n.chain.privacyCancel}
                onClick={() => {
                  setCheckPrivacy(true);
                  login();
                  close();
                }}
                onCancel={close}
              />
            ),
            width: '450px',
          });
          return Promise.resolve([true, {}]);
        }
        return [hasError, { nameError, passwordError }];
      },
    );
  };
  // 点击登录按钮
  const loginByAccount = (values: IAccountState, setState: Function) => {
    fetchRequest(`login/password`, {
      method: 'POST',
      body: JSON.stringify({ username: values.name, password: values.password }),
    })
      .then(async (response) => {
        const status = response.status;
        const _response = await response.json();
        const { result, rc } = _response;
        // eslint-disable-next-line sonarjs/no-small-switch
        switch (rc) {
          // 正常登录
          case 0:
            await afterLoginSuccess(result);
            break;
          // 非正常登录
          default:
            Toast.error(requestError(_response, status, i18n));
            break;
        }
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      })
      .finally(() => {
        setState({ ...values, buttonLoading: false, buttonDisabled: false });
      });
  };

  return {
    nameLabel: i18n.chain.nameLabel,
    namePlaceholder: i18n.chain.namePlaceholder,
    passwordLabel: i18n.chain.passwordLabel,
    passwordPlaceholder: i18n.chain.passwordPlaceholder,
    buttonLabel: i18n.chain.buttonLabel,
    buttonLoadingLabel: i18n.chain.buttonLoadingLabel,
    showPasswordForget: false,
    passwordForgetLabel: i18n.chain.passwordForget,
    initialState: () => ({
      name: '',
      nameError: '',
      password: '',
      passwordError: '',
      buttonDisabled: false,
      buttonLoading: false,
    }),
    verifyName: verifyName,
    verifyPassword: verifyPassword,
    verifyAll: verifyAllAccount,
    loginIn: loginByAccount,
    verification: VERIFICATION,
  };
};

const getDatlasConfig = (privacyTitle: string) => {
  // 校验用户名
  const verifyName = (value: string): Promise<string> => {
    return new Promise((resolve) => {
      const error = isEmpty(value, i18n.chain.nameVerifyEmpty);
      resolve(error);
    });
  };
  // 校验密码
  const verifyPassword = (value: string): Promise<string> => {
    return new Promise((resolve) => {
      const error = isEmpty(value, i18n.chain.passwordVerifyEmpty);
      resolve(error);
    });
  };
  // 验证account all
  const verifyAllAccount = (
    values: IAccountState,
    checkPrivacy: boolean,
    setCheckPrivacy: Function,
    login: Function,
  ) => {
    return Promise.all([verifyName(values.name), verifyPassword(values.password)]).then(
      ([nameError, passwordError]) => {
        const hasError: any = nameError || passwordError;
        if (!hasError && !checkPrivacy) {
          Dialog.open({
            content: (close: any) => (
              <DialogPrivacy
                title={privacyTitle}
                desc={i18n.chain.privacyDesc({ str: privacyTitle })}
                buttonOkLabel={i18n.chain.privacyOk}
                buttonCancelLabel={i18n.chain.privacyCancel}
                onClick={() => {
                  setCheckPrivacy(true);
                  login();
                  close();
                }}
                onCancel={close}
              />
            ),
            width: '450px',
          });
          return Promise.resolve([true, {}]);
        }
        return [hasError, { nameError, passwordError }];
      },
    );
  };
  // 双因子登录检查
  const loginDoubleCheck = (captcha: string, taskId: string, close: any) => {
    return fetchRequest(`${apiAuthUrl}/auth/login/double_check`, {
      method: 'POST',
      body: JSON.stringify({ code: captcha, task_id: taskId }),
    })
      .then(async (response) => {
        return receiveResponse(response, async (result: any) => {
          afterLoginSuccess({ ...result, access_token: result.auth, isDatlas: true });
          close();
        });
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      });
  };
  // 需要双因子登录
  const needDoubleCheck = (taskId: string) => {
    // 双因子登录
    Dialog.open({
      title: i18n.chain.doubleCheckTitle,
      closable: true,
      content: (close: any) => (
        <DoubleCheckContent
          onClick={(captcha: string) => loginDoubleCheck(captcha, taskId, close)}
          doubleCheckDesc={i18n.chain.doubleCheckDesc}
          doubleCheckButtonText={i18n.chain.doubleCheckOk}
          doubleCheckPlaceholder={i18n.chain.captchaPlaceholder}
        />
      ),
      width: '598px',
    });
  };
  // 点击登录按钮
  const loginByAccount = (values: IAccountState, setState: Function) => {
    fetchRequest(`${apiAuthUrl}/auth/login/byname`, {
      method: 'POST',
      body: JSON.stringify({ name: values.name, password: md5(values.password) }),
    })
      .then(async (response) => {
        const status = response.status;
        const _response = await response.json();
        const { result, rc } = _response;
        switch (rc) {
          // 正常登录
          case 0:
            afterLoginSuccess({ ...result, access_token: result.auth, isDatlas: true });
            break;
          // 密码输入次数过多提示
          case 1078:
            Dialog.open({
              content: (close: any) => (
                <DialogPasswordError
                  title={i18n.chain.accountErrorTip}
                  desc={i18n.chain.accountErrorTipDesc}
                  buttonOkLabel={i18n.chain.privacyOk}
                  onClick={() => {
                    close();
                  }}
                />
              ),
              width: '450px',
            });
            break;
          // 双因子登录
          case 2004:
            needDoubleCheck(result.task_id);
            break;
          // 非正常登录
          default:
            Toast.error(requestError(_response, status, i18n));
            break;
        }
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      })
      .finally(() => {
        setState({ ...values, buttonLoading: false, buttonDisabled: false });
      });
  };

  return {
    initialState: () => ({
      name: '',
      nameError: '',
      password: '',
      passwordError: '',
      buttonDisabled: false,
      buttonLoading: false,
    }),
    verifyName: verifyName,
    verifyPassword: verifyPassword,
    verifyAll: verifyAllAccount,
    loginIn: loginByAccount,
    verification: VERIFICATION,
  };
};

// wechat，dingtalk 扫码验证页
const renderPageVerify = () => <Verify />;

// SSO挂载前初始化====================================================================================
const willMount = async (_loading: boolean, setLoading: Function) => {
  // 如果在微服务环境下，不需要此逻辑
  if (IS_MICRO_ENV) {
    microWillMount(setLoading, removeTokenFromStore);
    return;
  }

  // 切换app后跳到sso更新token
  const q = getQFromUrl();
  if (q.freshtk) {
    await afterLoginSuccess({ auth: q.tk });
    return;
  }

  const resetState = () => {
    removeTokenFromStore();
    setLoading(false);
  };

  // 常规的逻辑处理
  // force参数
  const urlForce = getForceFromUrl();
  const force = !urlForce || urlForce === 'false' ? false : true;

  // 存储dialog
  isDialog() && setDialogFromSessionStore();

  const token = getTokenFromStore();
  if (!force && token) {
    setLoading(true);
    await fetchRequest(`account/info`, { headers: { authorization: token } })
      .then(async (response) => {
        const _response = await response.json();
        const { result, rc } = _response;
        rc === 0 ? await afterLoginSuccess({ ...result, access_token: token }) : resetState();
      })
      .catch(() => {
        resetState();
      });
  } else {
    resetState();
  }
};

export const getConfig = () => {
  // config
  const language = getLanguageConfig();

  // 隐私选项配置
  const privacys: any[] = _.isArray(_.isObject(PRIVACY) && PRIVACY.privacys)
    ? (PRIVACY as any).privacys
    : [
        { name: i18n.chain.privacy1, link: 'https://www.metrodata.cn/terms-of-service' },
        { name: i18n.chain.privacy2, link: 'https://www.metrodata.cn/privacy-policy' },
      ];
  let privacyTitle = '';
  _.forEach(privacys, ({ name }, index) => {
    privacyTitle = ` ${privacyTitle}${name} ${index !== privacys.length - 1 ? i18n.chain.privacySlot : ''} `;
  });
  let titleStyle: any = {};

  if (TITLE_CENTER ?? true) {
    titleStyle = {
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      flexDirection: 'column',
    };
  }
  return {
    // 标题设置
    title: (
      <div style={titleStyle}>
        <div className="mdt-login-title" style={TITLE_SIZE ? { fontSize: `${TITLE_SIZE}px` } : {}}>
          {TITLE || i18n.chain.loginTitle}
        </div>
        {DESC ? <div className="mdt-login-desc">{DESC}</div> : null}
      </div>
    ),
    // 主题配置
    theme,
    // 页面背景(注意顺序，需要先初始化主题配置)
    bg: {
      color: 'var(--mdt-login-dialog-content-bg-color)',
      ...BG_CONFIG,
    },
    logo: LOGO,
    gridConfig: GRID_CONFIG,
    // 支持的语言配置
    language,
    // 登录项配置
    tabs: getTabsConfig(privacyTitle),
    // 手机号登录设置(注意顺序，需要先初始化登录项配置)
    phone: getPhoneConfig(privacyTitle),
    // 微信登录设置
    wechat: getWechatConfig(privacyTitle),
    // 钉钉登录设置
    dingtalk: getDingtalkConfig(privacyTitle),
    dingtalkOAuth: getDingtalkOAuthConfig(privacyTitle),
    // 账号登录相关设置
    account: getAccountConfig(privacyTitle),
    // 隐私协议
    privacy: getPrivacyConfig(privacys),
    // 滑动认证配置
    verification: getVerificationConfig(),
    // 底部配置
    footer: { renderFooter: () => <Footer /> },
    // 扫码绑定校验
    verify: { renderPage: renderPageVerify },
    // 加载前钩子
    willMount: willMount,
  };
};
