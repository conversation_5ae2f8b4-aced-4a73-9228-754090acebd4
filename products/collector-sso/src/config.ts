import _ from 'lodash';
import { initCommConfig } from '@mdtProSso/config/ssoCommConfig';
import { initDingtalkConfig, initDkOAuthConfig, initWechatConfig } from '@mdtProSso/config/third-party-config';
import { getConfigNamesapceFromUrl, getConfigPathFromUrl, getRedirectFromUrl, isDialog } from './_util/util';

const namespaceUrl = getConfigNamesapceFromUrl();
const redirectUrl = getRedirectFromUrl();
const configPath = getConfigPathFromUrl();
const dialogType = isDialog();

const needCustomConfig = redirectUrl && configPath;
const originConfig = window.__DM_COLLECTOR_SSO_CFS || {};
let customConfig: typeof window.__DM_COLLECTOR_SSO_CFS = {};
if (needCustomConfig) {
  try {
    const path = String(new URL(`${configPath}`, redirectUrl));
    const request = new XMLHttpRequest();
    request.open('GET', path, false); // 设置同步请求
    request.setRequestHeader('Accept', 'application/javascript'); // 添加请求头
    request.send(null);
    if (request.status === 200) {
      const moduleContent = request.responseText;
      // 约定接收的是一个JSON对象
      customConfig = JSON.parse(moduleContent);
    } else {
      console.error('Failed to load module:', path);
    }
  } catch (error) {
    console.error(error);
  }
}
// 根据配置的命名空间查找
const namespaceConfig = originConfig?.productNamespaceCustomMap?.[namespaceUrl] || {};
if (namespaceUrl && !_.isEmpty(namespaceConfig)) {
  customConfig = { ...originConfig, ...customConfig, ...namespaceConfig };
}

window.__DM_COLLECTOR_SSO_CFS = { ...window.__DM_COLLECTOR_SSO_CFS, ...customConfig };
const newCfs = initCommConfig(
  'collector-sso',
  {
    isDevelop: __IS_DEVELOPMENT,
    developProxyApiUrl: __DEVELOP_PROXY_API_URL,
    developEnvOrigin: __DEVELOP_ENV_ORIGIN,
  },
  { isDialog: dialogType, redirect: redirectUrl },
);

initDingtalkConfig(newCfs.productDingtalkId || 'dingae1l0exolzihuiza');
initWechatConfig(newCfs.productWechatId || 'wx5e24b43cd1b863a9');
initDkOAuthConfig(newCfs.productDingtalkOAuthId || 'dingwlduwdterh22crii');

// 部署的环境
__webpack_public_path__ = newCfs.deployPublicPath;

export const DINGTALK_SETTING_ID = newCfs.backendDingtalkSettingId || 'eb3d6b41-c036-4de1-9faf-9893cc4d4b6e';
export const WECHAT_SETTING_ID = newCfs.backendWechatSettingId || '30801cfe-e607-4224-823d-848a91a725b5';
export const DINGTALK_OAUTH_SETTING_ID = newCfs.backendDingtalkOAuthSettingId || '72755b25-30f3-4a5d-8cfe-673cc001eb94';
export const TEMPLATE_ID = newCfs.backendTemplateId || '0c0bd4b4-f661-4c61-af3d-cc4861e45375';
export const API_AUTH_URL = newCfs.backendAuthApiUrl;
export const ENCODE_PARAM = newCfs.productEncodeParam ?? true;
export const JUMP_OAUTH = newCfs.productJumpOAuth;

export * from '@mdtProSso/config/ssoCommConfig';
export * from '@mdtProSso/config/third-party-config';
