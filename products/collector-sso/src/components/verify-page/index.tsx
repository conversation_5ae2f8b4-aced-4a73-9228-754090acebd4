import { FC } from 'react';
import { isInDingtalk, isPc } from '@mdtLogin/util/uaUtil';
import { afterLoginSuccess } from '../../_util/fetchUtil';
import { getExtraUriFromUrl } from '../../_util/util';
import { JUMP_OAUTH } from '../../config';
import i18n from '../../languages';

const Verify: FC = () => {
  const getParams = () => {
    const index = window.location.href.indexOf('?');
    const query = window.location.href.substring(index + 1);
    const vars = query.split('&');
    let params: Record<string, any> = {};
    vars.forEach((item) => {
      let temp = item.split('=');
      params[temp[0]] = decodeURIComponent(temp[1]);
    });
    return params;
  };

  const notificationWindow = window.parent!;

  const params = getParams();
  const { account_uuid, access_token, open_id, nick, nickName, type } = params;

  if (!access_token) {
    notificationWindow.postMessage(JSON.stringify({ [type]: { status: 'failed', msg: i18n.chain.verifyFailed } }), '*');
    return null;
  }

  const result = { account_uuid, access_token, open_id, nick: nick || nickName };

  if (isInDingtalk() || !isPc() || (type === 'dingtalk' && JUMP_OAUTH)) {
    afterLoginSuccess(result, getExtraUriFromUrl());
    return null;
  }

  notificationWindow.postMessage(
    JSON.stringify({
      [type]: {
        status: 'success',
        result,
        type: 'login',
      },
    }),
    '*',
  );
  return <div style={{ textAlign: 'center', marginTop: '100px' }}>{i18n.chain.loginSuccess}</div>;
};

Verify.displayName = 'Verify';
export { Verify };
