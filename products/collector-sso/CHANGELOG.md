# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.15.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.15) (2025-07-24)

**Note:** Version bump only for package collector-sso

## [1.15.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.14) (2025-07-08)

### Bug Fixes

- 🐛 双因子登录可关闭,按钮 disabled 跟随 value 验证可用性 ([cd63077](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cd6307739cc3b285b5b0afcab71fffb282bc9f6c))

## [1.15.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.13) (2025-07-03)

**Note:** Version bump only for package collector-sso

## [1.15.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.12) (2025-06-19)

**Note:** Version bump only for package collector-sso

## [1.15.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.11) (2025-06-10)

**Note:** Version bump only for package collector-sso

## [1.15.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.10) (2025-05-29)

### Bug Fixes

- url 遗漏 decode ([ce62053](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ce62053204720c398ce817b7b513beba68098a06))

## [1.15.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.9) (2025-05-19)

**Note:** Version bump only for package collector-sso

## [1.15.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.8) (2025-05-14)

**Note:** Version bump only for package collector-sso

## [1.15.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.7) (2025-04-27)

**Note:** Version bump only for package collector-sso

## [1.15.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.6) (2025-04-23)

**Note:** Version bump only for package collector-sso

## [1.15.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.5) (2025-04-23)

**Note:** Version bump only for package collector-sso

## [1.15.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.4) (2025-04-02)

**Note:** Version bump only for package collector-sso

## [1.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.3) (2025-04-01)

**Note:** Version bump only for package collector-sso

## [1.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.2) (2025-03-31)

### Features

- ✨ 抽离 footer 渲染,增加统一的默认备案号 ([41db331](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41db3315a22fd1529d314e0ad0dab618af8246b3))

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.1) (2025-03-31)

### Features

- ✨ 增加产品备案号 ([8c5ae70](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c5ae70e6a8b67faf7795b35b6bc92ff59aaeb3c))

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.15.0) (2025-02-24)

### Features

- ✨ 增加 map-api-url ([2bfb97d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2bfb97d5867b7d22d4d498a3ffa50e79189241bf))

## [1.14.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.14.4) (2025-02-17)

**Note:** Version bump only for package collector-sso

## [1.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.14.3) (2025-01-23)

**Note:** Version bump only for package collector-sso

## [1.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.14.2) (2025-01-09)

### Features

- ✨ collector-sso 增加 baseUrl 的配置 ([e3fd3bc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3fd3bc59e553d88ba49b18f6ed8ccc7f12c81ff))

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.14.1) (2025-01-06)

**Note:** Version bump only for package collector-sso

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.14.0) (2024-12-23)

**Note:** Version bump only for package collector-sso

## [1.13.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.13.4) (2024-12-16)

### Features

- ✨ sso 增加参数指定 config namesapce ([3cab764](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cab76411bb5c9b8c9158f7515b473d52f0de8f1))

## [1.13.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.13.3) (2024-12-03)

**Note:** Version bump only for package collector-sso

## [1.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.13.2) (2024-12-02)

**Note:** Version bump only for package collector-sso

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.13.1) (2024-12-02)

**Note:** Version bump only for package collector-sso

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.13.0) (2024-11-26)

**Note:** Version bump only for package collector-sso

## [1.12.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.9) (2024-11-14)

**Note:** Version bump only for package collector-sso

## [1.12.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.8) (2024-11-04)

**Note:** Version bump only for package collector-sso

## [1.12.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.7) (2024-10-28)

**Note:** Version bump only for package collector-sso

## [1.12.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.6) (2024-10-27)

**Note:** Version bump only for package collector-sso

## [1.12.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.5) (2024-10-22)

**Note:** Version bump only for package collector-sso

## [1.12.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.4) (2024-10-15)

**Note:** Version bump only for package collector-sso

## [1.12.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.3) (2024-08-27)

### Bug Fixes

- 🐛 extra url 也要判断 ([ac715b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac715b65864a986bf8a855a133f231f0ca9a81da))
- 🐛 移动端授权修复 ([f3aee1e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f3aee1e88e8f841a709716701ddf4a00d9f1b414))

## [1.12.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.2) (2024-08-13)

### Bug Fixes

- 🐛 collector-sso 修改 verify 通知层 ([0b4caf8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b4caf813d53410c236e8a8c45bca1b91029f4a4))

## [1.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.1) (2024-08-12)

### Features

- ✨ 配置只有一个登录项隐藏 tab, collectorsso 针对配置项的适配工作 ([5fc186c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc186cb5585b3386deeae25813e904fc3292749))

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.12.0) (2024-08-09)

**Note:** Version bump only for package collector-sso

## [1.11.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.11.3) (2024-08-07)

**Note:** Version bump only for package collector-sso

## [1.11.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.11.2) (2024-08-02)

### Bug Fixes

- 🐛 修复 iframe 嵌套弹窗登陆失效的问题 ([6fa5343](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fa534371210d1905a89933da22d7243318cc482))

## [1.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.11.1) (2024-07-29)

### Features

- ✨ add sso debug mode ([1aa037f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1aa037fdc62b5c284ffba97a779cda8782c8c54f))

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.11.0) (2024-07-22)

**Note:** Version bump only for package collector-sso

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.10.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))

## [1.9.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.6) (2024-06-24)

**Note:** Version bump only for package collector-sso

## [1.9.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.5) (2024-06-17)

**Note:** Version bump only for package collector-sso

## [1.9.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.4) (2024-06-03)

**Note:** Version bump only for package collector-sso

## [1.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.3) (2024-05-21)

**Note:** Version bump only for package collector-sso

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.2) (2024-05-21)

**Note:** Version bump only for package collector-sso

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.1) (2024-05-20)

### Features

- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.9.0) (2024-05-13)

**Note:** Version bump only for package collector-sso

## [1.8.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.8.2) (2024-04-29)

### Bug Fixes

- 🐛 config 头部加令牌方法重命名 ([856ca3b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/856ca3b62f7fa3a9fae290e12815c8e1072cf8e9))
- 🐛 i18n 合并成一个 json ([616d290](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/616d2902d6bf3e812f13aa077985f60c8b3da482))

### Features

- ✨ collector-sso 同步修改 ([1f14810](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1f14810012210684e9df0bd70350b56070d12ec4))
- ✨ 增加 locale 配置项,可以修改国际化 ([bd8f43a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bd8f43a4930d3ee20975f7c93f31ec3d52680764))
- ✨ 通用 request config 处理, 增加 fetchClass, 修改 sso, collector-sso ([b159192](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b159192080f27bee590ecbacfa20a6c93e14ac65))

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.8.1) (2024-04-01)

### Bug Fixes

- 🐛 修复钉钉授权 footer 影响点击的问题 ([072c427](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/072c42734dd4f242faa897bcd004bbcdc296ed83))

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.8.0) (2024-03-01)

**Note:** Version bump only for package collector-sso

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.7.1) (2024-01-15)

**Note:** Version bump only for package collector-sso

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.7.0) (2024-01-02)

### Features

- ✨ collector-sso 增加钉钉授权登录 ([ed1b170](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed1b170a417647bb0ca0c08121bb5fa7c8193459))

## [1.6.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.6.2) (2023-12-18)

**Note:** Version bump only for package collector-sso

## [1.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.6.1) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.6.0) (2023-11-20)

### Features

- ✨ pwa ([80985fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80985fbd6ca7f56d6b386959368e3ad44f02905b))

## [1.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.5.1) (2023-10-10)

### Bug Fixes

- 🐛 调整 redirect 的优先级 ([f6a1477](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6a1477fd0587789a113604a931ee9c1f431ed50))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.5.0) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.4.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.4.3) (2023-06-13)

### Bug Fixes

- 🐛 fix dialog postmessage target ([4188468](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41884682bb021d103568277bf2638c40476e3aa9))

## [1.4.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.4.2) (2023-06-12)

### Bug Fixes

- 🐛 sso verify issue handle ([e00aec1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e00aec103ad155a3e028687455fcf0f76f5e34f0))

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.4.1) (2023-06-12)

### Bug Fixes

- 🐛 fix sso verify-page possmessage target ([32af974](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/32af97467655bf3aa9dc1ff979f5b8aa8dc0f5db))

### Features

- ✨ add collector-sso wechat login ([bce6946](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bce6946ffeb67673a59211197e42e5654e965a14))

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.4.0) (2023-06-05)

### Bug Fixes

- 🐛 custom deployWindowTitle does not work ([08df192](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08df192eb886d524ef54299c120033c3affb417f))

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.3.0) (2023-05-15)

### Bug Fixes

- 🐛 handle q params to override, not replace ([4124f8a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4124f8af3d4fd81a58b09345c4dcac3e07239d4e))
- 🐛 增加 datlas token sso 直接处理 ([39e8043](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39e8043e096b893c4ece7361cd1f6c6a61c7a327))

### Features

- ✨ collector sso add dev env ([0702b2e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0702b2ec98bc151d96fe720a43ebee6c6af3f8f9))

## [1.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.2.1) (2023-04-27)

### Bug Fixes

- 🐛 修复 collector-sso 的自定义配置容错问题 ([7e03242](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e032420332a5bc1039214cae53b7ed6e4ae6474))

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.2.0) (2023-04-24)

### Bug Fixes

- 🐛 修改函数名和前置隐私协议的存储 ([e85b8a2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e85b8a236d06766b334563be1de682951d3e0e4b))
- 🐛 修改底部默认文案 ([a603693](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a603693f665bac8b3b3c23d24a21961a560f782d))
- 🐛 增加对 redirect 的解码处理,建议编码传入 ([a86e129](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a86e129f4171e03d298ab08e612142175d5b0778))

### Features

- ✨ sso 追加弹出窗的展示形式 ([c06e559](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c06e55968e8399bdcb59d36e6367c2bb3c3fa152))

# [1.1.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.1.0) (2023-04-17)

### Features

- ✨ sso collector-sso 增加自定义配置 ([65c6925](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65c6925526050e67d0a22e3c34effa26a41374cc))

## [1.0.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.9) (2023-04-06)

### Bug Fixes

- 🐛 修改 collector-sso 校验密码的规则 ([fecd89b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fecd89bb5bf7397fb00ef7ddde9ab613172a871d))

## [1.0.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.8) (2023-03-20)

**Note:** Version bump only for package collector-sso

## [1.0.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.7) (2023-03-13)

### Features

- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))

## [1.0.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.6) (2023-02-20)

### Bug Fixes

- 🐛 修复 sso 部分问题 ([762733c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/762733c23bd0fbbbcb131ac1c96a678bb7d2d11c))

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))

## [1.0.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.3) (2023-02-13)

### Features

- ✨ [sso, collector-sso]: 增加滑动认证(配置项) ([f4ce9b4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4ce9b41d652dec15e3c544f5856404767a2c183))

## [1.0.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-sso@1.0.2) (2023-02-06)

### Bug Fixes

- 🐛 跳转修改-处理加密和不加密的跳转处理 ([0e475d5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0e475d59560f9c0e73e5f449fa5255c48bfc87b0))

## 1.0.1 (2023-01-30)

### Features

- ✨ [collector-sso]: 初始化 ([658d2ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/658d2ffe4a8ced94164575fc28358b228f325502))
