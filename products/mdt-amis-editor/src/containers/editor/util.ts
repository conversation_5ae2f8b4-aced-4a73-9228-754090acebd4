import { SchemaObject } from 'amis';
import i18n from '../../languages';

export const EDITOR_DEFAULT_VALUE: SchemaObject = { type: 'page', body: [] };

export let editorLanguages: any[];

export let varsSchema: any[];

export const initEditorLocale = () => {
  varsSchema = [
    {
      type: 'object',
      properties: {
        user: {
          type: 'object',
          title: i18n.chain.userInfo,
          properties: {
            userId: {
              type: 'string',
              title: i18n.chain.userId,
            },
            uuid: {
              type: 'string',
              title: i18n.chain.uuid,
            },
            userName: {
              type: 'string',
              title: i18n.chain.userName,
            },
            email: {
              type: 'string',
              title: i18n.chain.email,
            },
            phone: {
              type: 'string',
              title: i18n.chain.phone,
            },
            expireTime: {
              type: 'string',
              title: i18n.chain.expireTime,
            },
            token: {
              type: 'string',
              title: i18n.chain.token,
            },
          },
        },
        app: {
          type: 'object',
          title: i18n.chain.appInfo,
          properties: {
            appId: {
              type: 'string',
              title: i18n.chain.appId,
            },
            appName: {
              type: 'string',
              title: i18n.chain.appName,
            },
            appExpireTime: {
              type: 'string',
              title: i18n.chain.appExpireTime,
            },
          },
        },
        'window:location': {
          type: 'object',
          title: i18n.chain.browserVar,
          properties: {
            href: {
              type: 'string',
              title: 'href',
            },
            origin: {
              type: 'string',
              title: 'origin',
            },
            protocol: {
              type: 'string',
              title: 'protocol',
            },
            host: {
              type: 'string',
              title: 'host',
            },
            hostname: {
              type: 'string',
              title: 'hostname',
            },
            port: {
              type: 'string',
              title: 'port',
            },
            pathname: {
              type: 'string',
              title: 'pathname',
            },
            search: {
              type: 'string',
              title: 'search',
            },
            hash: {
              type: 'string',
              title: 'hash',
            },
          },
        },
      },
    },
  ];

  editorLanguages = [
    {
      label: i18n.chain.cnName,
      value: 'zh-CN',
    },
    {
      label: i18n.chain.enName,
      value: 'en-US',
    },
  ];
};

export const getSchemaUrl = () => {
  let host = `${window.location.protocol}//${window.location.host}`;
  // 如果在 gh-pages 里面
  if (/^\/amis-editor-demo/.test(window.location.pathname)) {
    host += '/amis-editor';
  }
  return `${host}/schema.json`;
};
