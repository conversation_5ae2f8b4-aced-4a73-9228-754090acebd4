import _ from 'lodash';
import { ReactNode } from 'react';
import { SchemaObject } from 'amis';
import { setLocale } from 'i18n-runtime';
import { BehaviorSubject } from 'rxjs';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { EDITOR_DEFAULT_VALUE } from './util';

export interface IEditorControllerOptions {
  value?: SchemaObject;
  renderHeaderTitle?: () => ReactNode;
  renderExtraBtns?: () => ReactNode;
  showLanguage?: boolean;
}

class EditorController {
  public value$ = new BehaviorSubject<SchemaObject>(EDITOR_DEFAULT_VALUE);
  public isMobile$ = new BehaviorSubject<boolean>(false);
  public isPreview$ = new BehaviorSubject<boolean>(false);
  public language$: BehaviorSubject<string>;
  public ctx$ = new BehaviorSubject<any>({});
  public renderHeaderTitle?: () => ReactNode;
  public renderExtraBtns?: () => ReactNode;
  public showLanguage?: boolean;
  private app: AppController;

  public constructor(app: AppController, options: IEditorControllerOptions) {
    this.app = app;
    this.language$ = new BehaviorSubject<string>('');
    this.value$.next(options.value || EDITOR_DEFAULT_VALUE);
    this.renderHeaderTitle = options.renderHeaderTitle;
    this.renderExtraBtns = options.renderExtraBtns;
    this.showLanguage = options.showLanguage;
    this.initEditorLanguage();
    this.initCtx();
  }

  public destroy() {
    this.value$.complete();
    this.value$.next(EDITOR_DEFAULT_VALUE);
    this.isPreview$.complete();
    this.isMobile$.complete();
    this.language$.complete();
    this.ctx$.complete();
    this.renderHeaderTitle = null!;
    this.renderExtraBtns = null!;
  }

  public initEditorLanguage() {
    const storageI18n = localStorage.getItem('suda-i18n-locale');
    const curLanguage = this.app.getLanguage();
    i18n.locale(curLanguage);
    if (!_.isEqual(storageI18n, curLanguage)) {
      localStorage.setItem('suda-i18n-locale', curLanguage);
      this.changeLocale(curLanguage);
    }
  }

  public changeLocale = (value: string) => {
    setLocale(value);
  };

  public changeIsMobile(value: boolean) {
    this.isMobile$.next(value);
  }

  public changeIsPreview(value: boolean) {
    this.isPreview$.next(value);
  }

  public handleValueChange = (val: SchemaObject) => {
    this.value$.next(val);
  };

  private initCtx() {
    const appController = AppController.getInstance() as AppController;
    const ctx = {
      user: {
        userId: appController.getUserId(),
        userName: appController.getUserName(),
        uuid: appController.getUserUuid(),
        email: appController.getUserEmail(),
        phone: appController.getUserPhone(),
        expireTime: appController.getUserExpireTime(),
        token: appController.getUserToken(),
      },
      app: {
        appId: appController.getAppId(),
        appName: appController.getAppName(),
        appExpireTime: appController.getAppExpireTime(),
      },
    };
    this.ctx$.next(ctx);
  }
}

export { EditorController };
