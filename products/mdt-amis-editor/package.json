{"name": "mdt-amis-editor", "cnName": "JSON可视化编辑器", "description": "JSON可视化编辑器", "version": "1.12.33", "private": true, "scripts": {"start": "cross-env DEVELOP_ENV=dev craco start", "start:staging": "cross-env DEVELOP_ENV=staging craco start", "start:debug": "cross-env DEVELOP_ENV=debug craco start", "release": "CI=false craco build", "release:analyze": "craco build --analyze", "test": "craco test"}, "cracoConfig": "craco.config.js", "dependencies": {"@fortawesome/fontawesome-free": "^5.15.3", "@mdt/product-micro-modules": "^1.48.33", "amis": "6.6.0-patch.1", "amis-core": "6.6.0-patch.1", "amis-editor": "6.6.0-patch.1", "amis-editor-core": "6.6.0-patch.1", "amis-formula": "6.6.0-patch.1", "amis-theme-editor-helper": "2.0.26-patch.1", "amis-ui": "6.6.0-patch.1", "mobx": "4.15.7", "mobx-react": "6.3.1", "mobx-state-tree": "3.17.3", "office-viewer": "0.3.14-patch.1", "react-hook-form": "7.39.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}