import { FC, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { getParamValueFromUrl } from '@mdtBsComm/utils/urlUtil';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { DrawerPreviewGeometryData } from '@mdtProMicroModules/containers/drawer-preview-geometry-data';
import { FolderOperationCommon } from '@mdtProMicroModules/containers/folder-operation-common';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { CreateDatapkg } from '@mdtProMicroModules/pages/create-datapkg';
import { DrawerDatapkgDetailFactory } from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { DrawerWithinPage } from '@mdtProMicroModules/pages/drawer-within-page';
import { InstitutionDatapkgController } from './InstitutionDatapkgController';

interface IProps {
  controller: InstitutionDatapkgController;
}

const ListOpenDetail: FC<IProps> = ({ controller }) => {
  const location = useLocation<Record<string, string>>();

  useEffect(() => {
    const id = getParamValueFromUrl('openWorkItemIdentifier', location.search);
    controller.openDetailById(id);
  }, [location, controller]);

  return null;
};

const InstitutionDatapkg: FC<IProps> = ({ controller }) => {
  return (
    <div className="page-pkg-list">
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
      <DrawerDatapkgDetailFactory controller={controller.getDrawerDetailController()} />
      <CreateDatapkg controller={controller.getCreatePkgController()} />
      <ModalWithBtnsCompDialog controller={controller.getSqlEditController()} />
      <ModalWithBtnsCompDialog controller={controller.getDataEditController()} />
      <DrawerPreviewGeometryData controller={controller.getPreviewGeometryDataController()} />
      <ListOpenDetail controller={controller} />
      {controller.getFolderOperationCtrl() && (
        <FolderOperationCommon controller={controller.getFolderOperationCtrl()!} />
      )}
      <DrawerWithinPage controller={controller.getFillDesignController()} />
    </div>
  );
};

export { InstitutionDatapkg };
