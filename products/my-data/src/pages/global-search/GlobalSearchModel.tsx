import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { queryDatapkgsAsync, queryDatapkgsPaginationTotalAsync } from '@mdtBsServices/datapkgs';
import {
  IDatapkg,
  IDatapkgsQuery,
  IPromiseSettledResult,
  IRequestCancelToken,
  IServerResponse,
} from '@mdtBsServices/interfaces';
import { OwnershipEnum } from '@mdtProComm/constants';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import { DatapkgModel, IDatapkgData } from '@mdtProComm/models/DatapkgModel';
import { getOwnershipLabel } from '@mdtProComm/utils/datapkgUtil';
import { FolderModel, IFolders } from '@mdtProMicroModules/models/FolderModel';
import { getPkgOperationPermission, IPkgOperationPermission } from '@mdtProMicroModules/utils/pkgPermissionUtil';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';

export type ITableData = IDatapkgData &
  IPkgOperationPermission & {
    ownershipDisplay: string; // 属性（个人或机构）
    institutionName: string; // 机构名称
  };
interface IRespData {
  total?: number;
  list: IDatapkg[];
  userIdNameMap: Record<number, string>;
  appIdNameMap: Record<number, string>;
  folderResult?: ITableData[];
}

const COMM_QUERY_PARAMS = {
  privilege: true,
  order_by: 'update_time',
  descending: true,
  need_permissions: true,
};

export class GlobalSearchModel extends DatapkgModel {
  // eslint-disable-next-line max-params
  public static transformToTableData(pkg: IDatapkg, appMap: Record<number, string>, userMap: Record<number, string>) {
    const app = AppController.getInstance();
    const datapkgData = this.transformToDatapkgData(pkg, userMap);
    const ownershipDisplay = getOwnershipLabel(pkg.ownership);
    const tablePkg: ITableData = {
      ...datapkgData,
      ...getPkgOperationPermission({ pkg, app }),
      ownershipDisplay,
      institutionName: appMap[pkg.app_id],
    };
    return tablePkg;
  }

  public static transformFolderToTableData(item: IFolders): ITableData {
    return {
      id: item.path,
      name: item.currentPath,
      color: '',
      icon: '',
      userId: NaN,
      datasetId: '',
      userName: '',
      idColumn: '',
      packageTypeDisplay: undefined,
      updateTimeDisplay: item.createTimeDisplay,
      isPersonalPkg: false,
      ownership: OwnershipEnum.APP,
      geometryType: '',
      enableCollaborateEdit: false,
      enableDataEdit: false,
      enableDataPreview: false,
      enableDataSearch: false,
      enableDelete: false,
      enableDescriptionEdit: false,
      enableDesensitizeEdit: false,
      enableDownload: false,
      enableFieldEdit: false,
      enableFillData: false,
      enableGenealogyEdit: false,
      enableMonitorEdit: false,
      enableNameEdit: false,
      enablePreviewGeometryData: false,
      enableSqlEdit: false,
      enableThemeTagEdit: false,
      ownershipDisplay: i18n.chain.proMicroModules.folder.folder,
      institutionName: '--',
      ...item,
    };
  }

  public static queryFirstPagePkgs(data: IDatapkgsQuery, cancelToken: IRequestCancelToken, apps: string) {
    const pkgRequest = new Promise<IServerResponse<IRespData>>((resolve) => {
      const request = async () => {
        const resp = await queryDatapkgsPaginationTotalAsync(
          { ...data, ...COMM_QUERY_PARAMS },
          { cancelToken, params: { apps } },
        );
        // 如果成功，则在查询用户的名称
        let total = 0;
        let list: IDatapkg[] = [];
        let userIdNameMap: Record<number, string> = {};
        let appIdNameMap: Record<number, string> = {};
        if (resp.success) {
          const { total_count, dataResult } = resp.data!;
          list = dataResult;
          total = total_count;
          const [userResp, appResp] = (await Promise.allSettled([
            this.getUserIdNameMap(list),
            this.getAppIdNameMap(list),
          ])) as [IPromiseSettledResult<Record<number, string>>, IPromiseSettledResult<Record<number, string>>];
          userIdNameMap = userResp.value;
          appIdNameMap = appResp.value;
        }
        resolve({
          success: resp.success,
          canceled: resp.canceled,
          data: { total, list, userIdNameMap, appIdNameMap },
        });
      };
      request();
    });

    return from(pkgRequest).pipe(
      takeWhile((pkgResp) => !pkgResp.canceled),
      map((pkgResp) => {
        const { total, list, userIdNameMap, appIdNameMap } = pkgResp.data!;
        const data = _.map(list, (item) => this.transformToTableData(item, appIdNameMap, userIdNameMap));
        return [total!, data] as [number, ITableData[]];
      }),
    );
  }

  public static queryFolderFirstPagePkgs(
    data: IDatapkgsQuery,
    cancelToken: IRequestCancelToken,
    apps: string,
    folderParams: any,
  ) {
    const pkgRequest = new Promise<IServerResponse<IRespData>>((resolve) => {
      const request = async () => {
        folderParams.path ? (data.folder = folderParams.path) : (data.nofolder = true);
        const folderResp = await FolderModel.getFolder(folderParams);
        const folderResult = folderResp.success ? _.map(folderResp.data, this.transformFolderToTableData) : [];

        const resp = await queryDatapkgsPaginationTotalAsync(
          { ...data, ...COMM_QUERY_PARAMS },
          { cancelToken, params: { apps } },
        );
        // 如果成功，则在查询用户的名称
        let total = 0;
        let list: IDatapkg[] = [];
        let userIdNameMap: Record<number, string> = {};
        let appIdNameMap: Record<number, string> = {};
        if (resp.success) {
          const { total_count, dataResult } = resp.data!;
          list = dataResult;
          total = total_count;
          const [userResp, appResp] = (await Promise.allSettled([
            this.getUserIdNameMap(list),
            this.getAppIdNameMap(list),
          ])) as [IPromiseSettledResult<Record<number, string>>, IPromiseSettledResult<Record<number, string>>];
          userIdNameMap = userResp.value;
          appIdNameMap = appResp.value;
        }
        resolve({
          success: resp.success,
          canceled: resp.canceled,
          data: { total, list, userIdNameMap, appIdNameMap, folderResult },
        });
      };
      request();
    });

    return from(pkgRequest).pipe(
      takeWhile((pkgResp) => !pkgResp.canceled),
      map((pkgResp) => {
        const { total, list, userIdNameMap, appIdNameMap, folderResult = [] } = pkgResp.data!;
        const data = _.map(list, (item) => this.transformToTableData(item, appIdNameMap, userIdNameMap));
        return [total!, [...folderResult, ...data]] as [number, ITableData[]];
      }),
    );
  }

  public static queryNextPagePkgs(data: IDatapkgsQuery, cancelToken: IRequestCancelToken, apps: string) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryDatapkgsAsync({ ...data, ...COMM_QUERY_PARAMS }, { cancelToken, params: { apps } });
          // 如果成功，则在查询用户的名称
          let list: IDatapkg[] = [];
          let userIdNameMap: Record<number, string> = {};
          let appIdNameMap: Record<number, string> = {};
          // let pkgIdMetaPermissionMap: Record<string, boolean> = {};
          if (resp.success) {
            list = resp.data!;
            const [userResp, appResp] = (await Promise.allSettled([
              this.getUserIdNameMap(list),
              this.getAppIdNameMap(list),
              // this.getPkgIdMetaEditMap(list),
            ])) as [
              IPromiseSettledResult<Record<number, string>>,
              IPromiseSettledResult<Record<number, string>>,
              // IPromiseSettledResult<Record<string, boolean>>,
            ];
            userIdNameMap = userResp.value;
            appIdNameMap = appResp.value;
            // pkgIdMetaPermissionMap = metaResp.value;
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { list, userIdNameMap, appIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const { list, userIdNameMap, appIdNameMap } = resp.data!;
        return _.map(list, (item) => this.transformToTableData(item, appIdNameMap, userIdNameMap));
      }),
    );
  }

  // 请求用户名称
  private static async getUserIdNameMap(list: IDatapkg[], cancelToken?: IRequestCancelToken) {
    return CommonModel.getUserIdNameMap(_.map(list, 'user_id'), cancelToken);
  }

  // 请求机构名称
  private static async getAppIdNameMap(list: IDatapkg[], cancelToken?: IRequestCancelToken) {
    return CommonModel.getAppIdNameMap(_.map(list, 'app_id'), cancelToken);
  }
}

export type IGlobalSearchModel = typeof GlobalSearchModel;
