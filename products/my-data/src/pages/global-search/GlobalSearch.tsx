import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import Breadcrumb from '@mdtDesign/breadcrumb';
import { MyDataFilter } from '@mdtProMicroModules/components/my-data-filter';
import { DrawerPreviewGeometryData } from '@mdtProMicroModules/containers/drawer-preview-geometry-data';
import { FolderOperationCommon } from '@mdtProMicroModules/containers/folder-operation-common';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { DrawerDatapkgDetailFactory } from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { DrawerWithinPage } from '@mdtProMicroModules/pages/drawer-within-page';
import i18n from '../../languages';
import { GlobalSearchController } from './GlobalSearchController';

interface IProps {
  controller: GlobalSearchController;
}

export const Title: FC<IProps> = ({ controller }) => {
  const text = useObservableState(controller.getSearchVal$());
  const routes = [
    {
      path: '',
      breadcrumbName: `${i18n.chain.mydata.searchSuffix}"${text}"`,
    },
  ];

  return (
    <div className="table-title">
      <Breadcrumb
        prefixText={i18n.chain.mydata.back}
        className="mydata-route-home_breadcrumb"
        routes={routes}
        prevOptions={{ goPrev: () => controller.handleBack() }}
      />
      <MyDataFilter controller={controller.getFilterController()} />
    </div>
  );
};

const GlobalSearch: FC<IProps> = ({ controller }) => {
  return (
    <div className="page-pkg-list">
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
      <DrawerDatapkgDetailFactory controller={controller.getDrawerDetailController()} />
      <ModalWithBtnsCompDialog controller={controller.getSqlEditController()} />
      <ModalWithBtnsCompDialog controller={controller.getDataEditController()} />
      <DrawerPreviewGeometryData controller={controller.getPreviewGeometryDataController()} />
      <DrawerWithinPage controller={controller.getFillDesignController()} />
      {controller.getFolderOperationCtrl() && (
        <FolderOperationCommon controller={controller.getFolderOperationCtrl()!} />
      )}
    </div>
  );
};

export { GlobalSearch };
