import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { FOLDER_DATA, IDatapkgModel } from '@mdtBsBffServices/services';
import { IDatapkgsQuery, IQueryFolders, IRequestCancelToken } from '@mdtBsServices/interfaces';
import { DatapkgFolderBffService } from '@mdtProComm/bff-services';
import { OwnershipEnum } from '@mdtProComm/constants';
import { DatapkgModelBff, IDatapkgData } from '@mdtProComm/models/DatapkgModelBff';
import { FolderModel, IFolders } from '@mdtProMicroModules/models/FolderModel';
import { getPkgOperationPermission, IPkgOperationPermission } from '@mdtProMicroModules/utils/pkgPermissionUtil';
import { AppController } from '../../app/AppController';
import { DATAPKG_GLOBAL_SEARCH_DATA, DatapkgListBffService } from '../../bff-service';
import i18n from '../../languages';

export type ITableData = IDatapkgData &
  IPkgOperationPermission & {
    ownershipDisplay: string; // 属性（个人或机构）
    institutionName: string; // 机构名称
  };

const COMM_QUERY_PARAMS = {
  privilege: true,
  order_by: 'update_time',
  descending: true,
  need_permissions: true,
  ownership: undefined,
};

export class GlobalSearchModelBff extends DatapkgModelBff {
  public static transformFolderToTableData(item: IFolders): ITableData {
    return {
      id: item.path,
      name: item.currentPath,
      color: '',
      icon: '',
      userId: NaN,
      datasetId: '',
      userName: '',
      idColumn: '',
      packageTypeDisplay: undefined,
      updateTimeDisplay: item.createTimeDisplay,
      isPersonalPkg: false,
      ownership: OwnershipEnum.APP,
      geometryType: '',
      enableCollaborateEdit: false,
      enableDataEdit: false,
      enableDataPreview: false,
      enableDataSearch: false,
      enableDelete: false,
      enableDescriptionEdit: false,
      enableDesensitizeEdit: false,
      enableDownload: false,
      enableFieldEdit: false,
      enableFillData: false,
      enableGenealogyEdit: false,
      enableMonitorEdit: false,
      enableNameEdit: false,
      enablePreviewGeometryData: false,
      enableSqlEdit: false,
      enableThemeTagEdit: false,
      ownershipDisplay: i18n.chain.proMicroModules.folder.folder,
      institutionName: '--',
      ...item,
    };
  }

  public static transformToTableData(pkg: IDatapkgModel) {
    const app = AppController.getInstance();
    const datapkgData = this.transformToDatapkgData(pkg);
    const tablePkg: ITableData = {
      ...datapkgData,
      ...getPkgOperationPermission({ pkg, app }),
      ownershipDisplay: pkg.ownershipDisplay!,
      institutionName: pkg.app?.name || '',
    };
    return tablePkg;
  }

  public static queryFolderFirstPagePkgs(
    data: IDatapkgsQuery,
    cancelToken: IRequestCancelToken,
    apps: string,
    folderParams: IQueryFolders,
  ) {
    const options = {
      data: { ...data, ...COMM_QUERY_PARAMS },
      respData: DATAPKG_GLOBAL_SEARCH_DATA,
      cancelToken,
      params: { apps },
    };
    return from(
      DatapkgFolderBffService.queryFoldersDatapkgs(options, { params: folderParams, respData: FOLDER_DATA }),
    ).pipe(
      map(([datapkgResp, folderResp]) => {
        if (!datapkgResp.success) return [0, []];
        const { total_count, page_data } = datapkgResp;
        const data = _.map(page_data, (item) => this.transformToTableData(item));
        const folderData = _.map(folderResp, FolderModel.transformToFolder);
        const folderTableData = _.map(folderData, this.transformFolderToTableData);
        return [total_count, [...folderTableData, ...data]] as [number, ITableData[]];
      }),
    );
  }

  public static queryFirstPagePkgs(data: IDatapkgsQuery, cancelToken: IRequestCancelToken, apps: string) {
    const options = {
      data: { ...data, ...COMM_QUERY_PARAMS },
      respData: DATAPKG_GLOBAL_SEARCH_DATA,
      cancelToken,
      params: { apps },
    };
    return from(DatapkgListBffService.queryDatapkgsFirstPage(options)).pipe(
      takeWhile((pkgResp) => !pkgResp.canceled),
      map((resp) => {
        if (!resp.success) return [0, []];
        const { total_count, page_data } = resp;
        const data = _.map(page_data, (item) => this.transformToTableData(item));
        return [total_count, data] as [number, ITableData[]];
      }),
    );
  }

  public static queryNextPagePkgs(data: IDatapkgsQuery, cancelToken: IRequestCancelToken, apps: string) {
    const options = {
      data: { ...data, ...COMM_QUERY_PARAMS },
      respData: DATAPKG_GLOBAL_SEARCH_DATA,
      cancelToken,
      params: { apps },
    };
    return from(DatapkgListBffService.queryDatapkgsNextPage(options)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [];
        return _.map(resp.page_data, (item) => this.transformToTableData(item));
      }),
    );
  }
}

export type IGlobalSearchModel = typeof GlobalSearchModelBff;
