import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { FolderActionType } from '@mdtBsComponents/data-list-comp-table-curd';
import { IDatapkgsQuery } from '@mdtBsServices/interfaces';
import { <PERSON>Button } from '@mdtDesign/button';
import Icon from '@mdtDesign/icon';
import { MyDataMenuEnum } from '@mdtProMicroModules/components/my-data-filter';
import { FolderOperationCommonController } from '@mdtProMicroModules/containers/folder-operation-common';
import {
  IPaginationParams,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { IFolders } from '@mdtProMicroModules/models/FolderModel';
import { DatapkgController, IFilterParams } from '../../_util/DatapkgController';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { Title } from './GlobalSearch';
import { IGlobalSearchModel, ITableData } from './GlobalSearchModel';

const FOLDER_SPACE = 'datapkg';

interface ISearchFilterParams extends IFilterParams {
  name?: string;
  apps: string;
}

interface IControllerOptions {
  app: AppController;
  apps: string;
  Model: IGlobalSearchModel;
  searchVal$: BehaviorSubject<string>;
  backFunc: () => void;
}

class GlobalSearchController extends DatapkgController<ITableData> {
  private Model: IGlobalSearchModel;
  private apps: string;
  private searchVal$;
  private backFunc;
  private folderOperationCtrl?: FolderOperationCommonController<ITableData>;

  public constructor(options: IControllerOptions) {
    super({
      app: options.app,
      menu: MyDataMenuEnum.GLOBAL,
      emitter: options.app!.getEmitterController()!.getEmitterIns(),
      tableController: new TableCurdWithSimpleSearchController<ITableData>({
        dataListCompTableCurdControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: (params: ISearchFilterParams) => this.queryFirstPageTableData(params),
            loadNextPageDataListFunc: (params: IPaginationParams) => this.queryNextPageTableData(params),
            getBackendFilterParams: () => this.getBackendFilterParams(),
          },
          folderControllerOptions: {
            enableFolder: true,
          },
          curdOptions: () => {
            return {
              ...this.initCurdOptions(),
              dragCode: 'name',
              onFolderCreate: () =>
                this.folderOperationCtrl
                  ?.getCreateFolderController()
                  .openModal(this.tableController.getFolderPathValue()),
              onFolderNavClick: (path: string) =>
                this.tableController.loadDataList({ ...this.getBackendFilterParams(), path }),
            };
          },
          tableOptions: () => this.initTableOptions(),
          clickMoreBtnFunc: (menuKey: string, item?: ITableData) => {
            if (menuKey === FolderActionType.MOVE) {
              return this.folderOperationCtrl?.getTableMoveController().openModal(item);
            }
            if (menuKey === FolderActionType.RENAME) {
              return this.folderOperationCtrl?.getRenameFolderController().openModal(item);
            }
            if (menuKey === FolderActionType.DELETE) {
              return this.folderOperationCtrl?.getDeleteFolderController().openModal(item);
            }
            return this.handleMoreOperation(menuKey, item);
          },
          onDragOverFunc: (path: string, items: ITableData[]) => this.folderOperationCtrl?.moveToTarget(path, items),
          folderActionCallbackFunc: (path: string) => {
            return this.tableController.loadDataList({ ...this.getBackendFilterParams(), path });
          },
        },
        headerOptions: () => this.initHeaderOptions(),
      }),
    });
    this.Model = options.Model;
    this.apps = options.apps;
    this.searchVal$ = options.searchVal$;
    this.backFunc = options.backFunc;

    this.init();
  }

  public destroy() {
    super.destroy();
    this.Model = null!;
    this.searchVal$ = null!;
    this.backFunc = null!;
    this.folderOperationCtrl?.destroy();
  }

  public getSearchVal$() {
    return this.searchVal$;
  }

  public handleBack() {
    this.backFunc();
  }

  public getFolderOperationCtrl() {
    return this.folderOperationCtrl;
  }

  // 获取参数
  protected getBackendFilterParams = (): ISearchFilterParams => {
    const search = this.searchVal$.getValue();
    const cancelToken = this.getCancelToken();
    return { cancelToken, name: search || undefined, apps: this.apps, ...this.filterController.getFilterDataValue() };
  };

  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          code: 'name',
          name: i18n.chain.mydata.globalSearch.pkgName,
          width: 450,
          render: (val: string, record: ITableData) => (
            <LinkButton className="mydata-route-home_pkg-name" onClick={() => this.handleShowDetail(record)}>
              <span style={{ color: `${record.color}` }}>
                <Icon className="mydata-route-home_pkg-icon" icon={record.icon} />
              </span>
              {val}
            </LinkButton>
          ),
        },
        { code: 'institutionName', name: i18n.chain.mydata.globalSearch.institutionName, width: 150 },
        { code: 'ownershipDisplay', name: i18n.chain.mydata.globalSearch.ownership, width: 120 },
        { code: 'userName', name: i18n.chain.mydata.globalSearch.user, width: 120 },
        { code: 'updateTimeDisplay', name: i18n.chain.mydata.globalSearch.updateTime, width: 175 },
      ],
      type: 'page-bg',
      primaryKey: 'id',

      withVerticalBorder: false,
      // style: { width: 'calc(100vw - 260px)' },
    };
  };

  private initHeaderOptions = () => {
    return {
      createBtnLabel: '',
      hideInput: true,
      title: <Title controller={this} />,
    };
  };

  private queryFirstPageTableData = ({ name, cancelToken, apps, ...rest }: ISearchFilterParams) => {
    return this.Model.queryFirstPagePkgs({ name, ...this.Model.transformFilterParams(rest) }, cancelToken, apps);
    // const enableFolder = this.tableController.getEnableFolder();
    // if (!enableFolder) {
    //   return this.Model.queryFirstPagePkgs({ name, ...this.Model.transformFilterParams(rest) }, cancelToken, apps);
    // }
    // const folderParams: any = { space: FOLDER_SPACE };
    // const path = (rest as any).path;
    // path && (folderParams.path = path);
    // return this.Model.queryFolderFirstPagePkgs(
    //   { name, ...this.Model.transformFilterParams(rest) },
    //   cancelToken,
    //   apps,
    //   folderParams,
    // );
  };

  private queryNextPageTableData = (params: IPaginationParams) => {
    const { name, cancelToken, apps, ...rest } = this.getBackendFilterParams();
    const data: IDatapkgsQuery = { ..._.omit(params, 'path'), name, ...this.Model.transformFilterParams(rest) };
    return this.Model.queryNextPagePkgs(data, cancelToken, apps);
  };

  // 初始化
  private init() {
    const tc = this.tableController;

    this.folderOperationCtrl = tc.getEnableFolder()
      ? new FolderOperationCommonController<ITableData>({
          space: FOLDER_SPACE,
          resouceKey: 'id',
          disabledDeleteResource: true,
          getFolderPathFunc: () => tc.getFolderPathValue(),
          transformFolderToVFunc: (item: IFolders) => this.Model.transformFolderToTableData(item),
          loadDataListFunc: (params?: any) => tc.loadDataList({ ...this.getBackendFilterParams(), ...params }),
          addToDataListFunc: (data: ITableData) => tc.addDataToList(data),
          deleteDataFromListFunc: (data: ITableData, modifyTotal?: boolean) => tc.deleteDataFromList(data, modifyTotal),
        })
      : undefined;

    tc.listenBackendFilter(this.searchVal$);
  }
}

export { GlobalSearchController };
