import _ from 'lodash';
import { EventEmitter2 } from 'eventemitter2';
import { AsyncSubject, BehaviorSubject } from 'rxjs';
import { FolderActionType } from '@mdtBsComponents/data-list-comp-table-curd';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IconButton, LinkButton } from '@mdtDesign/button';
import notification from '@mdtDesign/notification';
import toastApi from '@mdtDesign/toast';
import {
  OwnershipEnum,
  PkgOperationType,
  ResourceSelectTypeEnum,
  SubscribeTypeEnum,
  TaskStatusEnum,
} from '@mdtProComm/constants';
import { DATAPKG_DELETE_KEY, DATAPKG_UPDATE_KEY, EmitterActionEnum } from '@mdtProComm/constants';
import { IEventsParams, IRequestCancelToken, ISocketTaskResp } from '@mdtProComm/interfaces';
import { IEmitterData } from '@mdtProComm/interfaces';
import { DatapkgModel as RestDatapkgModel, IDatapkgData } from '@mdtProComm/models/DatapkgModel';
import { DatapkgModelBff as BffDatapkgModel } from '@mdtProComm/models/DatapkgModelBff';
import { isPersonalPkg } from '@mdtProComm/utils/datapkgUtil';
import { IFilterForm, MyDataFilterController, MyDataMenuEnum } from '@mdtProMicroModules/components/my-data-filter';
import { MyDataFilterModel } from '@mdtProMicroModules/components/my-data-filter/MyDataFilterModel';
import { MyDataFilterModelBff } from '@mdtProMicroModules/components/my-data-filter/MyDataFilterModelBff';
import { CreateDatapkgFromSqlController } from '@mdtProMicroModules/containers/create-datapkg-from-sql';
import { DrawerPreviewGeometryDataController } from '@mdtProMicroModules/containers/drawer-preview-geometry-data';
import {
  IDownloadPkgOptions,
  PopoverPkgDownload,
  PopoverPkgDownloadController,
} from '@mdtProMicroModules/containers/popover-pkg-download';
import {
  EventSubscribeMap,
  PopoverPkgSubscribe,
  PopoverPkgSubscribeController,
  SubScribeEventMap,
} from '@mdtProMicroModules/containers/popover-pkg-subscribe';
import {
  ResourceShare,
  ResourceShareController,
  ResourceShareModel,
} from '@mdtProMicroModules/containers/resource-share';
import {
  MenuItemProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { CreateDatapkgFromSqlModel } from '@mdtProMicroModules/models/CreateDatapkgFromSqlModel';
import { CreateDatapkgFromSqlModelBff } from '@mdtProMicroModules/models/CreateDatapkgFromSqlModelBff';
import {
  DatapkgDetailFactoryController,
  DatapkgDetailFactoryModel,
  DatapkgDetailFactoryModelBff,
  DrawerDatapkgDetailFactoryController,
  IDrawerDetailOtherOptions,
} from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { DrawerWithinPageController } from '@mdtProMicroModules/pages/drawer-within-page';
import {
  IPkgBoundWfspecControllerOptions,
  PkgBoundWfspec,
  PkgBoundWfspecController,
  PkgBoundWfspecModel,
  PkgBoundWfspecModelBff,
} from '@mdtProMicroModules/pages/pkg-bound-wfspec';
import { UpdateDatapkgController, UpdateDatapkgModel } from '@mdtProMicroModules/pages/update-datapkg';
import { IPkgOperationPermission } from '@mdtProMicroModules/utils/pkgPermissionUtil';
import { addCreateDatapkgFromUploadLocalFileTask, addCreateDownloadDatapkgTask } from '@mdtProTasks/util';
import { AppController } from '../app/AppController';
import { JUMP_DATA_MAP_URL, UPLOAD_MAX_MB } from '../config';
import i18n from '../languages';
import { getModel } from './modelUtil';

const DatapkgModel = getModel(RestDatapkgModel, BffDatapkgModel);

const MAX_SIZE = 1024 * 1024 * UPLOAD_MAX_MB;

export interface IFilterParams extends IFilterForm {
  cancelToken: IRequestCancelToken;
}

interface IDatapkgControllerOptions<T> {
  menu: MyDataMenuEnum;
  showCreateBtn?: boolean; // 显示开关
  enableCreateBtn?: boolean; // 权限
  app: AppController;
  emitter: EventEmitter2;
  tableController: TableCurdWithSimpleSearchController<T>;
}

abstract class DatapkgController<T extends IDatapkgData & IPkgOperationPermission> extends RequestController {
  // 带搜索框的表格
  protected readonly tableController: TableCurdWithSimpleSearchController<T>;
  // 删除
  protected readonly deleteController: ModalWithBtnsCompEmotionController<T>;
  protected readonly filterController: MyDataFilterController;
  protected downloadController: PopoverPkgDownloadController;
  protected operationMap: Record<string, (item: T) => any> = {};
  protected activePkg?: T;
  protected emitter: EventEmitter2;
  protected app: AppController;
  // 详情
  protected drawerDetailController: DrawerDatapkgDetailFactoryController;
  private pkgDetailControllerCache: Record<string, DatapkgDetailFactoryController> = {};
  private createPkgTaskResp$?: BehaviorSubject<ISocketTaskResp>;
  private showCreateBtn: boolean;
  private enableCreateBtn: boolean;
  private sqlEditController: CreateDatapkgFromSqlController;
  private dataEditController: UpdateDatapkgController;
  private previewGeometryDataController: DrawerPreviewGeometryDataController;
  // 填报
  private fillDesignController: DrawerWithinPageController<IPkgBoundWfspecControllerOptions>;
  // 资源分享
  private resourceShareController?: ResourceShareController;
  // menu
  private menu?: MyDataMenuEnum;

  public constructor(options: IDatapkgControllerOptions<T>) {
    super();
    this.app = options.app;
    this.showCreateBtn = !!options.showCreateBtn;
    this.enableCreateBtn = !!options.enableCreateBtn;
    this.emitter = options.emitter;
    this.menu = options.menu;
    this.operationMap = {
      [PkgOperationType.DELETE]: this.handleDelete,
    };

    this.drawerDetailController = new DrawerDatapkgDetailFactoryController({
      getDatapkgDetailFactoryControllerFunc: this.getDetailController,
    });
    this.filterController = new MyDataFilterController({
      menu: options.menu,
      Model: getModel(MyDataFilterModel, MyDataFilterModelBff),
    });
    // 列表
    this.tableController = options.tableController;
    // 删除弹窗
    this.deleteController = new ModalWithBtnsCompEmotionController<T>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });

    this.downloadController = new PopoverPkgDownloadController({
      downloadPkgFunc: this.downloadDataPkg,
      loadColumnOptionsFunc: async (pkgId?: string) => {
        const resp = await DatapkgModel.queryDatapkgColumns(pkgId).toPromise();
        return resp.data || resp.page_data || [];
      },
    });

    const SqlModel = getModel(CreateDatapkgFromSqlModel, CreateDatapkgFromSqlModelBff);
    this.sqlEditController = new CreateDatapkgFromSqlController({
      Model: new SqlModel(),
    });

    this.dataEditController = new UpdateDatapkgController({
      // TODO bff model
      Model: UpdateDatapkgModel,
      appId: this.app.getAppId(),
      maxUploadMb: UPLOAD_MAX_MB,
      onFileUploadSuccessFunc: (id?: string) => {
        this.tableController.loadDataList(this.getBackendFilterParams());
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        id && this.handleShowDetail({ id } as T);
      },
      goMapEditorFunc: (pkgId: string, mode?: string) => {
        this.app!.jumpToProductMapEditor({ pkgId, mode });
      },
    });

    this.previewGeometryDataController = new DrawerPreviewGeometryDataController();

    const FillModel = getModel(PkgBoundWfspecModel, PkgBoundWfspecModelBff);
    this.fillDesignController = new DrawerWithinPageController({
      InnerView: PkgBoundWfspec,
      InnerViewController: PkgBoundWfspecController,
      Model: FillModel,
      width: '49vw',
      title: i18n.chain.mydata.dataFill,
    });

    this.tableController.listenBackendFilter(this.filterController.getFilterData$());
    this.listenDatapkgChange();
  }

  public destroy() {
    this.tableController.destroy();
    this.deleteController.destroy();
    this.filterController.destroy();
    this.downloadController.destroy();
    this.drawerDetailController.destroy();
    _.forEach(this.pkgDetailControllerCache, (it) => it.destroy());
    this.pkgDetailControllerCache = {};
    this.sqlEditController.destroy();
    this.dataEditController.destroy();
    this.previewGeometryDataController.destroy();
    this.fillDesignController.destroy();
    this.createPkgTaskResp$?.complete();
    this.createPkgTaskResp$ = undefined;
    this.operationMap = {};
    this.emitter?.off(DATAPKG_UPDATE_KEY, this.pkgUpdateListener);
    this.emitter?.off(DATAPKG_DELETE_KEY, this.pkgDeleteListener);
    this.emitter = null!;
    this.app = null!;
  }

  public openWorkItemIdentifier = async (identifier: Record<string, any>) => {
    console.log(identifier);
  };

  public openDetailById = async (id: string) => {
    if (!id) return;
    this.drawerDetailController.openModal(id);
  };

  // 列表controller
  public getTableController() {
    return this.tableController;
  }

  // 删除弹窗
  public getDeleteController() {
    return this.deleteController;
  }

  public getFilterController() {
    return this.filterController;
  }

  public getDataEditController() {
    return this.dataEditController;
  }

  // 外链数据包编辑SQL controller
  public getSqlEditController() {
    return this.sqlEditController;
  }

  // 数据详情controller
  public getDrawerDetailController() {
    return this.drawerDetailController;
  }

  public getPreviewGeometryDataController() {
    return this.previewGeometryDataController;
  }

  public getFillDesignController() {
    return this.fillDesignController;
  }

  public shareDatapkg = (originalData: T, resourceType: ResourceSelectTypeEnum, isOwner?: boolean) => {
    console.log('originalData', originalData);
    if (!originalData) return;
    if (this.resourceShareController) {
      this.resourceShareController.destroy();
    }
    this.resourceShareController = new ResourceShareController({
      app: this.app,
      Model: new ResourceShareModel({
        resourceId: originalData!.id,
        resourceType,
        isOwner,
      }),
    });
    ResourceShare.open({ controller: this.resourceShareController });
  };

  // -----------------------操作----------------

  // 立即更新
  protected handleRefresh = async (originalData: T) => {
    DatapkgModel.refreshDatapkgAsync(originalData.id).subscribe(() => {
      toastApi.success(i18n.chain.proMicroModules.datapkgDetail.dataUpdateSuccess);
    });
  };

  // 删除数据包
  protected handleDelete = (originalData?: T) => {
    return this.deleteController.openModal(originalData);
  };

  // 数据填报
  protected handleFill = (originalData?: T) => {
    return this.fillDesignController.openModal({
      app: this.app,
      pkgId: originalData!.id,
      pkgName: originalData?.name,
    });
  };

  protected handleUpload = (originalData?: T) => {
    this.activePkg = originalData;
  };

  // 查看详情
  protected handleShowDetail = (originalData: T) => {
    this.drawerDetailController.openModal(originalData.id);
  };

  // 预览地理数据
  protected handlePreviewGeometryData = (originalData: T) => {
    const { id, name, geometryType } = originalData;
    this.previewGeometryDataController.openModal({
      url: this.app!.getPreviewGeoDataHref(
        {
          packageUuid: id,
          geometryType: geometryType,
          objectType: name,
        },
        JUMP_DATA_MAP_URL,
      ),
    });
  };

  protected downloadDataPkg = async ({
    downloadType,
    exportGeoField,
    geoFormat,
    columns,
    column_mapping,
  }: IDownloadPkgOptions) => {
    const pkgName = this.activePkg!.name;
    addCreateDownloadDatapkgTask(this.activePkg!.id, pkgName, {
      file_type: downloadType,
      geo: exportGeoField,
      geometry_format: geoFormat,
      only: columns,
      column_mapping,
    });
    return true;
  };

  protected subscribeOnChange = async (item: T, keys: SubscribeTypeEnum[], status: boolean) => {
    try {
      const isCreateEvent = status;
      let subscribeResult: IEventsParams = {};
      if (isCreateEvent) {
        const resp = await Promise.all(
          _.map(keys, (key) => DatapkgModel.addEvent({ event_type: SubScribeEventMap[key], resource_ids: [item.id] })),
        );
        const allSuccess = !!_.filter(resp, ({ success }) => success).length;
        allSuccess && toastApi.success(i18n.chain.proMicroModules.datapkg.subscribe.success);
        _.forEach(resp, ({ data }) => {
          // @ts-ignore
          subscribeResult[EventSubscribeMap[data[0].event_type]] = data[0].id;
        });
      } else {
        const resp = await Promise.all(_.map(keys, (key) => DatapkgModel.deleteEvent(item[key])));
        const allSuccess = !!_.filter(resp, ({ success }) => success).length;
        _.forEach(resp, ({ data }) => {
          // @ts-ignore
          subscribeResult[EventSubscribeMap[data.event_type]] = null;
        });
        allSuccess && toastApi.success(i18n.chain.proMicroModules.datapkg.subscribe.cancelSuccess);
      }
      this.tableController.editDataInList({
        ...item,
        ...subscribeResult,
      });
      return new BehaviorSubject({ success: true });
    } catch (error) {
      return new BehaviorSubject({ success: false });
    }
  };

  protected handleMoreOperation = (menuKey: string, item?: T) => {
    let result = this.operationMap[menuKey]!(item!);
    if (result instanceof AsyncSubject) {
      return result;
    }
    result = new AsyncSubject();
    result.next({ success: true });
    result.complete();
    return result;
  };

  protected initCurdOptions = () => {
    const deleteItem: MenuItemProps = {
      title: <div style={{ color: 'var(--dmc-red-600-color)' }}>{i18n.chain.comButton.delete}</div>,
      key: PkgOperationType.DELETE,
    } as any;
    const enableFolder = this.tableController.getEnableFolder();
    return {
      enableCreate: this.showCreateBtn,
      createProps: { disabled: !this.enableCreateBtn },
      moreItems: (item: T) => {
        const menus: MenuItemProps[] = [];
        if (enableFolder) {
          menus.push({
            title: i18n.chain.comFolder.moveTo,
            key: FolderActionType.MOVE,
            icon: 'move-to',
          });
        }
        // item.enableFillData &&
        //   menus.push({
        //     title: i18n.chain.proMicroModules.datapkgDetail.fillData,
        //     key: PkgOperationType.FILL,
        //   });
        const mLen = menus.length;
        // 如果有上述菜单，最后一个做个分割
        mLen && (menus[mLen - 1].divider = true);
        item.enableDelete && menus.push(deleteItem);
        return menus;
      },
      otherBtns: (item: T) => {
        const downloadBtn = item.enableDownload ? (
          <span onClick={() => this.handleUpload(item)}>
            <PopoverPkgDownload pkgId={item.id} controller={this.downloadController} />
          </span>
        ) : (
          <IconButton type="only-icon" icon="download" disabled />
        );

        return (
          <>
            {downloadBtn}
            <IconButton
              type="only-icon"
              icon="edit"
              onClick={() => this.handleDataEdit(item)}
              disabled={!item.enableDataEdit && !item.enableSqlEdit}
            />
            <IconButton
              type="only-icon"
              icon="map-2"
              onClick={() => this.handlePreviewGeometryData(item)}
              disabled={!item.enablePreviewGeometryData || !JUMP_DATA_MAP_URL}
            />
            <PopoverPkgSubscribe
              controller={
                new PopoverPkgSubscribeController({
                  // 非本机构不需要质量监控
                  exclude: this.menu === MyDataMenuEnum.APP ? [] : [SubscribeTypeEnum.QUALITY_MONITOR],
                  datapkgUpdate: !!item.datapkgUpdate,
                  qualityMonitor: !!item.qualityMonitor,
                  onChange: (...args) => this.subscribeOnChange(item, ...args),
                })
              }
            />
            <IconButton
              type="only-icon"
              icon="form-2"
              onClick={() => this.handleFill(item)}
              disabled={!item.enableFillData}
            />
            <IconButton
              type="only-icon"
              icon="share"
              onClick={() => this.shareDatapkg(item, ResourceSelectTypeEnum.PERSONAL)}
              disabled={item.userId !== this.app.getUser()?.userId}
            />
          </>
        );
      },
      columnWidth: 200,
    };
  };

  // 获取参数
  protected getBackendFilterParams = (): IFilterParams => {
    const cancelToken = this.getCancelToken();
    return { cancelToken, ...this.filterController.getFilterDataValue() };
  };

  // 文件上传
  protected prepareUploadFile = (files: File[], datasetId: string, appId: number, ownership: OwnershipEnum) => {
    const file = files[0];
    if (file.size > MAX_SIZE) {
      return toastApi.warning(i18n.chain.proMicroModules.datapkg.limitFileSize(UPLOAD_MAX_MB));
    }
    const task = addCreateDatapkgFromUploadLocalFileTask({
      file: file,
      datasetId,
      appId,
      ownership,
    });

    this.createPkgTaskResp$ = task.getCreatePkgTaskResp$();
    this.createPkgTaskResp$!.subscribe((resp) => {
      if (TaskStatusEnum.SUCCESSFUL === resp.status) {
        const operation = resp.result.isCreate
          ? i18n.chain.proMicroModules.datapkg.create
          : i18n.chain.proMicroModules.datapkg.update;
        notification.success({
          message: i18n.chain.proMicroModules.datapkg.operateMessage(resp.result.name, operation),
          duration: 5,
          footer: (
            <LinkButton
              onClick={() => {
                this.tableController.loadDataList(this.getBackendFilterParams());
                DatapkgModel.getPkgIdByName(datasetId, resp.result.name, ownership).subscribe((id: string) => {
                  if (id) {
                    const pkg: T = { id, isPersonalPkg: isPersonalPkg(ownership) } as unknown as T;
                    this.handleShowDetail(pkg);
                  }
                });
              }}
            >
              {i18n.chain.proMicroModules.datapkg.viewPkg}
            </LinkButton>
          ),
          key: resp.task_id,
        });
      }
    });
  };

  // 确定删除
  private deleteDataToService = async (data?: T) => {
    const resp = await DatapkgModel.deletePkg(data!.id).toPromise();
    resp && toastApi.success(i18n.chain.proMicroModules.datapkgDetail.delPkg(data!.name));
    return { success: !!resp, result: data, actionType: PkgOperationType.DELETE };
  };

  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.proMicroModules.datapkgDetail.delPkgConfirm(delData?.name),
      description: i18n.chain.proMicroModules.datapkgDetail.delPkgConfirmDesc,
    };
  };

  // 监听单个数据包的变化
  private listenDatapkgChange() {
    this.emitter.on(DATAPKG_UPDATE_KEY, this.pkgUpdateListener);
    this.emitter.on(DATAPKG_DELETE_KEY, this.pkgDeleteListener);
  }

  private pkgUpdateListener = async (data: IEmitterData) => {
    const { value: val, action } = data;
    if (action === EmitterActionEnum.UPDATE_PKG) {
      const { pkgId, updatingObj } = val;
      const pkg = _.find(this.tableController.getDataList$().getValue(), { id: pkgId });
      if (updatingObj.name && pkg) {
        this.tableController.editDataInList({
          ...pkg,
          name: updatingObj.name,
        });
      }
    }
  };

  private pkgDeleteListener = async (data: IEmitterData) => {
    const { value: val, action } = data;
    if (action === EmitterActionEnum.DEL_PKG) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      this.tableController.deleteDataFromList({ id: val.pkgId } as T);
    }
  };

  // 编辑数据
  private handleDataEdit(pkg: T) {
    if (pkg.enableSqlEdit) {
      this.sqlEditController!.openModal({
        pkgId: pkg.id,
        ownership: pkg.ownership,
      }).subscribe((result) => {
        result.success && this.tableController.loadDataList(this.getBackendFilterParams());
      });
      return;
    }

    this.dataEditController?.openModal({
      pkgId: pkg.id,
      pkgName: pkg.name,
      datasetId: this.app!.getDatasetsController()!.getAppDatasetId(),
      ownership: pkg.ownership,
      geometryType: pkg.geometryType,
    });
  }

  private getDetailController = (pkgId: string, options?: IDrawerDetailOtherOptions) => {
    const { openEdit } = options || {};
    let cache = this.pkgDetailControllerCache[pkgId];
    if (!cache) {
      const Model = getModel(DatapkgDetailFactoryModel, DatapkgDetailFactoryModelBff);
      cache = new DatapkgDetailFactoryController(this.app!, {
        pkgId,
        uploadMaxMb: UPLOAD_MAX_MB,
        Model: new Model(this.app!, pkgId),
        backFunc: () => this.drawerDetailController.closeModal(),
        openEdit,
        dataAppUrl: JUMP_DATA_MAP_URL,
      });
    } else {
      openEdit && cache.handleEditData();
    }
    this.pkgDetailControllerCache[pkgId] = cache;
    return cache;
  };
}

export { DatapkgController };
