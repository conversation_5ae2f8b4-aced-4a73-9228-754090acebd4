import { saveCompressValueToUrl } from '@mdtBsComm/utils/urlUtil';
import { OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import { modifyParamsOfUrl } from '@mdtProComm/utils/urlUtil';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { AppController } from '../app/AppController';

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    const app = this.app as AppController;
    return app.getAppRouterConfig();
  }

  public getFillFormUrl(rootWfId: string) {
    const path = this.createHref({ pathname: OneTableRoutePathEnum.FILL_FORM });
    const pathWithParams = modifyParamsOfUrl(path, 'q', saveCompressValueToUrl({ rootWfId }));
    const url = new URL(pathWithParams, window.location.origin);
    return url.toString();
  }

  public getFillFormSingleUrl(rootWfId: string) {
    const path = this.createHref({ pathname: OneTableRoutePathEnum.FILL_FORM_SINGLE });
    const pathWithParams = modifyParamsOfUrl(path, 'q', saveCompressValueToUrl({ rootWfId }));
    const url = new URL(pathWithParams, window.location.origin);
    return url.toString();
  }
}

export { RouterController };
