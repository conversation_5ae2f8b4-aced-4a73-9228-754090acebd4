import { useEffect, useState } from 'react';
import { Empty } from '@metroDesign/empty';
import { Spin } from '@metroDesign/spin';
import { isPc } from '@mdtBsComm/utils';
import { getRootWfIdFromUrl } from '@mdtProComm/utils/urlUtil';
import {
  OneTableNewFillForm,
  OneTableNewFillFormController,
  OneTableNewFillFormModel,
} from '@mdtProMicroModules/pages/one-table-new-fill-form';
import i18n from '../../languages';
import './index.less';

const NewFillForm = () => {
  const rootWfId = getRootWfIdFromUrl();
  const [loading, setLoading] = useState(true);
  const [controller, setController] = useState<OneTableNewFillFormController | null>(null);

  useEffect(() => {
    let unmounted = false;
    setLoading(true);
    OneTableNewFillFormController.create({
      Model: OneTableNewFillFormModel,
      rootWfId,
      onlyForm: !isPc(),
      submitConfirmOptionsFunc: () => ({
        onlySaveCallBack: () => {},
        submitSuccessCallback: () => {},
        deleteDataCallback: () => {},
      }),
    }).then((ctrl) => {
      if (!unmounted) {
        setController(ctrl);
        setLoading(false);
      }
    });
    return () => {
      unmounted = true;
      controller?.destroy?.();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rootWfId]);

  return (
    <div className="router_new-fill-form">
      {loading ? (
        <Spin fillParent tip={i18n.chain.comDataLoading} />
      ) : controller ? (
        <OneTableNewFillForm controller={controller} />
      ) : (
        <Empty description={i18n.chain.comNoData} />
      )}
    </div>
  );
};

export default NewFillForm;
