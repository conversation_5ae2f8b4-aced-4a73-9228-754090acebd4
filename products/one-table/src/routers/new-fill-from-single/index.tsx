import { useController } from '@mdtBsComm/hooks/use-controller';
import { getParamValueFromUrl } from '@mdtBsComm/utils/urlUtil';
import { getRootWfIdFromUrl } from '@mdtProComm/utils/urlUtil';
import {
  OneTableNewFillFormSingle,
  OneTableNewFillFormSingleController,
  OneTableNewFillFormSingleModel,
} from '@mdtProMicroModules/pages/one-table-new-fill-form-single';

const NewFillFormSingle = () => {
  const rootWfId = getRootWfIdFromUrl();
  const submitSingle = getParamValueFromUrl('submitSingle', location.search);
  const [controller] = useController(() => {
    const ctrl = new OneTableNewFillFormSingleController({
      Model: OneTableNewFillFormSingleModel,
      rootWfId,
      submitSingle: submitSingle === 'true',
    });
    return [ctrl, null];
  });

  return <OneTableNewFillFormSingle controller={controller} />;
};

export default NewFillFormSingle;
