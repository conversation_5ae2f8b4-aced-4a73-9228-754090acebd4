import _ from 'lodash';
import { lazy } from 'react';
import { FileForm, LockOn1, SmallAppGridFilled, Task } from '@metro/icons';
import { PermissionEnum } from '@mdtProComm/constants';
import { OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import { ONE_TABLE_INFO } from '@mdtProMicroModules/datlas/datlasConfig';
import { type IPathMergeStrategies, mergeWithPathStrategies } from '../_util/mergeWithPathStrategiesUtil';
import i18n from '../languages';
import { flowSpec as flowEndlessSpec } from './flow-spec/_standard/endless';
import { flowSpec } from './flow-spec/_standard/normal';
import { flowSpec as flowPeriodicSpec } from './flow-spec/_standard/periodic';
import { formSpec, periodFormSpec } from './form-spec/_standard';
import { exportToMyDataSpec, selectedReinforceUsersSpec, selectedTransferUserSpec } from './task-spec/_standard';

const NewFormManagementView = lazy(() => import('../routers/new-form-management'));
const NewMissionCenterView = lazy(() => import('../routers/new-mission-center'));
const NewReportDetailView = lazy(() => import('../routers/new-report-detail'));
const NewReportDetailTaskView = lazy(() => import('../routers/new-report-detail-task'));
const NewReportDetailViewH5 = lazy(() => import('../routers/new-report-detail-h5'));
const NewReportDetailTaskViewH5 = lazy(() => import('../routers/new-report-detail-task-h5'));
const NewH5 = lazy(() => import('../routers/new-h5'));
const NewMissionCenterH5View = lazy(() => import('../routers/new-mission-center-h5'));
const NewFormManagementH5View = lazy(() => import('../routers/new-form-management-h5'));
const NewGrantedFormView = lazy(() => import('../routers/new-granted-form'));
const NewGrantedOrgFormView = lazy(() => import('../routers/new-granted-org-form'));
const NewFillFormView = lazy(() => import('../routers/new-fill-form'));
const NewFillFormSingleView = lazy(() => import('../routers/new-fill-from-single'));

// 标准可搜索的字段
export const STANDARD_ADVANCED_FILTER_KEYS = ['extra_form_field', 'extra_form_level', 'extra_form_frequency'];

const h5Menus = [
  {
    path: OneTableRoutePathEnum.REPORT_DETAIL_H5,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewReportDetailViewH5,
  },
  {
    path: OneTableRoutePathEnum.REPORT_DETAIL_TASK_H5,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewReportDetailTaskViewH5,
  },
  {
    path: OneTableRoutePathEnum.NEW_MISSION_CENTER_H5,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewMissionCenterH5View,
  },
  {
    path: OneTableRoutePathEnum.NEW_FORM_MANAGEMENT_H5,
    permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
    View: NewFormManagementH5View,
  },
  {
    path: OneTableRoutePathEnum.H5,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewH5,
  },
];

const pcMenus = [
  {
    path: OneTableRoutePathEnum.FORM_MANAGEMENT,
    permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
    View: NewFormManagementView,
  },
  {
    path: OneTableRoutePathEnum.MISSION_CENTER_ONE,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewMissionCenterView,
  },
  {
    path: OneTableRoutePathEnum.NEW_GRANTED_FORM,
    permissionKey: PermissionEnum.OT_GRANTED_FORM,
    View: NewGrantedFormView,
  },
  {
    path: OneTableRoutePathEnum.NEW_GRANTED_ORG_FORM,
    permissionKey: PermissionEnum.OT_GRANTED_FORM,
    View: NewGrantedOrgFormView,
  },
  {
    path: OneTableRoutePathEnum.REPORT_DETAIL_TASK,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewReportDetailTaskView,
  },
  {
    path: OneTableRoutePathEnum.FILL_FORM,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewFillFormView,
    headerLess: true,
    sideMenuLess: true,
  },
  {
    path: OneTableRoutePathEnum.FILL_FORM_SINGLE,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewFillFormSingleView,
    headerLess: true,
    sideMenuLess: true,
  },
  {
    path: '/test-report-detail',
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    View: NewReportDetailView,
  },
];

export const routerConfig = [...pcMenus, ...h5Menus.map((item) => ({ ...item, headerLess: true, sideMenuLess: true }))];
export const reportConfig = {
  formSpec,
  periodFormSpec,
  flowSpec,
  flowEndlessSpec,
  flowPeriodicSpec,
  selectedReinforceUsersSpec,
  selectedTransferUserSpec,
  exportToMyDataSpec,
  enableAdvanceFilter: true,
  advancedFilterSpec: generateAdvancedFilterSpec(formSpec, STANDARD_ADVANCED_FILTER_KEYS),
};
export const menuItems = [
  {
    key: OneTableRoutePathEnum.FORM_MANAGEMENT,
    title: (
      <>
        <FileForm style={{ fontSize: 18 }} key={OneTableRoutePathEnum.FORM_MANAGEMENT} />
        {i18n.chain.proMicroModules.oneTable.menu.formCreateManagement}
      </>
    ),
    className: `top-level menu-divider`,
  },
  {
    key: OneTableRoutePathEnum.MISSION_CENTER_ONE,
    title: (
      <>
        <Task style={{ fontSize: 18 }} key={OneTableRoutePathEnum.MISSION_CENTER_ONE} />
        {i18n.chain.proMicroModules.oneTable.menu.missionCenter}
      </>
    ),
    className: `top-level menu-divider`,
  },
  {
    key: OneTableRoutePathEnum.NEW_GRANTED_FORM,
    title: (
      <>
        <SmallAppGridFilled style={{ fontSize: 18 }} key={OneTableRoutePathEnum.NEW_GRANTED_FORM} />
        {i18n.chain.proMicroModules.oneTable.menu.grantedForm}
      </>
    ),
    className: `top-level menu-divider`,
  },
  {
    key: OneTableRoutePathEnum.NEW_GRANTED_ORG_FORM,
    title: (
      <>
        <LockOn1 style={{ fontSize: 18 }} key={OneTableRoutePathEnum.NEW_GRANTED_ORG_FORM} />
        {i18n.chain.proMicroModules.oneTable.menu.grantedOrgForm}
      </>
    ),
    className: `top-level menu-divider ot-granted-org-form-menu`,
  },
];

const standard = {
  appName: '一表通演示',
  appNames: [],
  menuConfig: {
    openKeys: [],
    items: menuItems,
  },
  routerConfig,
  reportConfig,
};

export default standard;
export const overrideStandardConfig = (config: any, options?: IOptions) => {
  const { mergeControls, ...rest } = options || {};
  const mergedConfig = mergeWithPathStrategies(standard, config, getMergeControls(mergeControls));
  return handleOptions(mergedConfig, rest);
};

interface IOptions {
  excludeMenus?: string[]; // 需要排除的菜单
  excludeRouterPaths?: string[]; // 需要排除的路由
  mergeControls?: IPathMergeStrategies; // 指定行的合并策略
  excludeAdvancedFilterKey?: string[]; // 需要排除的高级筛选字段
  includeAdvancedFilterKey?: string[]; // 需要包含的高级筛选字段（include可以超越filterKey，只要保证spec能取到就可以）
  includeOverrideAdvancedFilterKey?: boolean; // include追加，关闭则直接使用include， 默认为true
  extraAdvancedFilterProperties?: any | ((spec: any) => any); // 需要额外添加的高级筛选字段
}

function getMergeControls(mergeControls: IPathMergeStrategies = {}): IPathMergeStrategies {
  const defaultControls: IPathMergeStrategies = {
    'reportConfig.formSpec': 'replace',
    'reportConfig.periodFormSpec': 'replace',
    'reportConfig.flowSpec': 'replace',
    'reportConfig.flowEndlessSpec': 'replace',
    'reportConfig.flowPeriodicSpec': 'replace',
    'reportConfig.selectedReinforceUsersSpec': 'replace',
    'reportConfig.selectedTransferUserSpec': 'replace',
    'reportConfig.exportToMyDataSpec': 'replace',
  };
  return {
    ...defaultControls,
    ...mergeControls,
  };
}

function handleOptions(template: any, options?: IOptions) {
  const { excludeMenus = [], excludeRouterPaths = [] } = options || {};
  const filteredRouterConfig = _.filter(template.routerConfig, ({ path }) => !_.includes(excludeRouterPaths, path));
  const filteredMenus = _.filter(template.menuConfig.items, ({ key }) => !_.includes(excludeMenus, key));
  const templateFormSpec = template.reportConfig.formSpec;
  const filteredAdvancedFilteSpec = getFilteredAdvancedFilterSpec(templateFormSpec, options);

  return {
    ...template,
    menuConfig: {
      ...template.menuConfig,
      items: filteredMenus,
    },
    routerConfig: filteredRouterConfig,
    reportConfig: {
      ...template.reportConfig,
      advancedFilterSpec: filteredAdvancedFilteSpec,
    },
  };
}

// 构造高级筛选配置spec
function generateAdvancedFilterSpec(formSpec: any, filterKeys?: string[], properties = {}) {
  const { formilySchema, allSettingValues } = formSpec;
  const varProperties: any = {};
  _.map(filterKeys, (field) => {
    const cur = (formilySchema.schema.properties as any)[field];
    if (!cur) {
      return;
    }
    const xDecoratorProps = cur['x-decorator-props'] || {};
    varProperties[field] = {
      ...cur,
      'x-decorator-props': {
        ...xDecoratorProps,
        layout: 'vertical',
      },
    };
  });
  const girdSchema = {
    properties: {
      gridLayout: {
        type: 'void',
        'x-component': 'FormGrid',
        properties: { ...varProperties, ...properties },
      },
    },
  };
  return {
    formilySchema: {
      schema: girdSchema,
      form: {
        ...formilySchema.form,
        layout: 'horizontal',
        labelWidth: '100%',
      },
    },
    allSettingValues,
  };
}

// 根据条件筛选，获取新配置spec
function getFilteredAdvancedFilterSpec(formSpec: any, options: IOptions = {}) {
  const { excludeAdvancedFilterKey, includeAdvancedFilterKey, extraAdvancedFilterProperties } = options;
  const newFilterKeys = applyIncludeExcludeFilter(
    STANDARD_ADVANCED_FILTER_KEYS,
    ONE_TABLE_INFO.includeAdvancedFilterKey ?? includeAdvancedFilterKey,
    ONE_TABLE_INFO.excludeAdvancedFilterKey ?? excludeAdvancedFilterKey,
    ONE_TABLE_INFO.includeOverrideAdvancedFilterKey,
  );
  if (_.isFunction(extraAdvancedFilterProperties)) {
    return extraAdvancedFilterProperties(generateAdvancedFilterSpec(formSpec, newFilterKeys));
  }
  if (!_.isEmpty(extraAdvancedFilterProperties)) {
    return generateAdvancedFilterSpec(formSpec, newFilterKeys, extraAdvancedFilterProperties);
  }
  return generateAdvancedFilterSpec(formSpec, newFilterKeys);
}

function applyIncludeExcludeFilter(
  keys: string[],
  includeKeys: string[] = [],
  excludeKeys: string[] = [],
  includeOverride?: boolean,
): string[] {
  if (includeKeys.length > 0) {
    return (includeOverride ? _.uniq([...keys, ...includeKeys]) : includeKeys).filter(
      (key) => !excludeKeys.includes(key),
    );
  }

  if (excludeKeys.length > 0) {
    return keys.filter((key) => !excludeKeys.includes(key));
  }

  return keys;
}
