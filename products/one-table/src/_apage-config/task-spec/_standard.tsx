import i18n from '../../languages';

const COLUMN_WIDTH = 120;
const ARRAY_TABLE_COLUMN = 'ArrayTable.Column';

// 选择协同人员
export const selectedReinforceUsersSpec = {
  formilySchema: {
    form: { colon: false, layout: 'vertical' },
    schema: {
      properties: {
        orgUsers: {
          type: 'array',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'UserSelectInput',
          title: i18n.chain.comText.orgUser,
          'x-component-props': { withoutOrg: false },
          name: 'orgUsers',
          'x-designable-id': '0r18qzjg41s',
          'x-index': 0,
        },
      },
    },
  },
  allSettingValues: {},
};

// 选择转交
export const selectedTransferUserSpec = {
  formilySchema: {
    form: { colon: false, layout: 'vertical' },
    schema: {
      properties: {
        orgUsers: {
          type: 'array',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'UserSelectInput',
          title: i18n.chain.comText.orgUser,
          'x-component-props': { isMuti: false, withoutOrg: false },
          name: 'orgUsers',
        },
        clear_reinforce: {
          type: 'boolean',
          'x-component': 'Checkbox',
          default: false,
          'x-component-props': {
            children: i18n.chain.proMicroModules.oneTable.clearReinforce,
          },
        },
      },
    },
  },
  allSettingValues: {},
};

// 导出到我的数据
export const exportToMyDataSpec = {
  formilySchema: {
    form: { colon: false, layout: 'vertical' },
    schema: {
      properties: {
        dataType: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.exportToMyData.datapkgType,
          required: true,
          'x-decorator': 'FormItem',
          enum: [
            { label: i18n.chain.proMicroModules.oneTable.exportToMyData.personalPkg, value: 'user' },
            { label: i18n.chain.proMicroModules.oneTable.exportToMyData.appPkg, value: 'app' },
          ],
          'x-component': 'Radio.Group',
          'x-component-props': {
            spaceProps: {
              size: 0,
            },
          },
          default: 'user',
        },
        name: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.exportToMyData.pkgName,
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: i18n.chain.proMicroModules.oneTable.exportToMyData.namePlaceHolder,
          },
        },
        description: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.exportToMyData.description,
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: i18n.chain.proMicroModules.oneTable.exportToMyData.descriptionPlaceHolder,
          },
        },
        exportType: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.exportToMyData.exportData,
          required: true,
          'x-decorator': 'FormItem',
          enum: [
            { label: i18n.chain.proMicroModules.oneTable.exportToMyData.subbmitted, value: 'submitted' },
            { label: i18n.chain.proMicroModules.oneTable.exportToMyData.all, value: 'all' },
          ],
          'x-component': 'Radio.Group',
          'x-component-props': {
            direction: 'vertical',
            style: {
              width: '100%',
              borderBottom: '1px solid var(--metro-divider-0)',
            },
            spaceProps: {
              size: 0,
            },
          },
          default: 'submitted',
        },
        collapseRename: {
          type: 'void',
          'x-component': 'FormCollapse',
          'x-component-props': {
            defaultActiveKey: [],
            ghost: true,
          },
          properties: {
            collapsePanel: {
              type: 'void',
              'x-component': 'FormCollapse.CollapsePanel',
              'x-component-props': {
                header: i18n.chain.proMicroModules.oneTable.exportToMyData.mappingHeader,
              },
              properties: {
                renameTable: {
                  type: 'array',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayTable',
                  'x-component-props': {
                    bordered: false,
                    size: 'small',
                  },
                  items: {
                    type: 'object',
                    properties: {
                      columnTitle: {
                        type: 'void',
                        'x-component': ARRAY_TABLE_COLUMN,
                        'x-component-props': {
                          title: i18n.chain.proMicroModules.oneTable.exportToMyData.fieldTitle,
                          width: COLUMN_WIDTH,
                        },
                        properties: {
                          title: { type: 'string', 'x-component': 'Text' },
                        },
                      },
                      columnMapping: {
                        type: 'void',
                        'x-component': ARRAY_TABLE_COLUMN,
                        'x-component-props': {
                          title: i18n.chain.proMicroModules.oneTable.exportToMyData.map,
                          width: COLUMN_WIDTH,
                          align: 'center',
                        },
                        properties: {
                          mapping: { type: 'string', 'x-component': 'Text' },
                        },
                      },
                      columnFieldName: {
                        type: 'void',
                        'x-component': ARRAY_TABLE_COLUMN,
                        'x-component-props': {
                          title: i18n.chain.proMicroModules.oneTable.exportToMyData.fieldName,
                          width: COLUMN_WIDTH,
                        },
                        properties: {
                          fieldName: { type: 'string', 'x-component': 'Input', required: true },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  allSettingValues: {},
};
