import {
  allSettingValues,
  description,
  extraAffiliatedOrgs,
  extraFormField,
  extraFormFrequency,
  extraFormLevel,
  extraFormOwner,
  extraFormOwnerPhone,
  form,
  name,
} from './_standard';

const extraQrCode = {
  extra_qrcode: {
    type: 'array',
    'x-metro-upload-type': 'file',
    'x-decorator': 'FormItem',
    'x-component': 'Upload',
    title: '一表一码',
    'x-component-props': {
      limitSize: 500,
      carema: false,
      watermark: [],
      minCount: '',
      maxCount: '1',
    },
    name: 'uh6j0gh7cdg',
    'x-validator':
      "{{(value = [])=> {\n    if (lessThan(value.length, )) return '文件数量不能小于'; if (moreThan(value.length, 1)) return '文件数量不能大于1'; }}} ",
    'x-decorator-props': {
      layout: 'vertical',
      colon: false,
    },
  },
};

const schemaProperties = {
  ...name,
  ...extraAffiliatedOrgs,
  ...extraQrCode,
  ...extraFormField,
  ...extraFormLevel,
  ...extraFormFrequency,
  ...extraFormOwner,
  ...extraFormOwnerPhone,
  ...description,
};

export const formSpec = {
  formilySchema: {
    form,
    schema: {
      properties: schemaProperties,
    },
  },
  allSettingValues,
};
