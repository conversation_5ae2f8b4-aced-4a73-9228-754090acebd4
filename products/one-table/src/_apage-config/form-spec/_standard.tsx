import _ from 'lodash';
import { FC, useState } from 'react';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Popover } from '@metroDesign/popover';
import { Spin } from '@metroDesign/spin';
import { Typography } from '@metroDesign/typography';
import { queryPeriodFormNameAsync } from '@mdtBsServices/flowork';
import VariableInputFormily from '@mdtProMicroModules/components/variable-input-formily';
import i18n from '../../languages';

const TemplateInputWithPreview: FC<any> = (props) => {
  const { value, nameValue } = props;
  const [renderPreviewName, setRenderPreviewName] = useState('');
  const [renderLoading, setRenderLoading] = useState(false);
  const renderPreview = () => {
    return renderLoading ? <Spin /> : <div>{nameValue + renderPreviewName}</div>;
  };
  return (
    <Flex vertical gap={6}>
      <VariableInputFormily {...props} />
      {value ? (
        <Typography.Text key="desc" type="secondary">
          <Popover
            overlayStyle={{ maxWidth: 400 }}
            placement="leftBottom"
            title={i18n.chain.proMicroModules.oneTable.viewPeriodExample}
            content={renderPreview}
            onOpenChange={async (visible) => {
              if (visible) {
                setRenderLoading(true);
                const resp = await queryPeriodFormNameAsync({
                  template: value,
                  form: {
                    name: nameValue || '',
                    extra_meta: {},
                  },
                });
                if (resp.success) {
                  // @ts-ignore
                  setRenderPreviewName(resp?.data?.result);
                }

                setRenderLoading(false);
              }
            }}
            trigger="click"
          >
            <Button.Link size="small" primary>
              {i18n.chain.proMicroModules.oneTable.viewPeriodExample}
            </Button.Link>
          </Popover>
        </Typography.Text>
      ) : null}
    </Flex>
  );
};

const reportOptions: Partial<Record<string, string[]>> = {
  form_label: [
    i18n.chain.proMicroModules.oneTable.municipalProject,
    i18n.chain.proMicroModules.oneTable.districtProject,
    i18n.chain.proMicroModules.oneTable.streetProject,
    i18n.chain.proMicroModules.oneTable.dataCollection,
    i18n.chain.proMicroModules.oneTable.dataVerify,
  ],
  form_region_single: [
    i18n.chain.proMicroModules.oneTable.region.person,
    i18n.chain.proMicroModules.oneTable.region.house,
    i18n.chain.proMicroModules.oneTable.region.company,
    i18n.chain.proMicroModules.oneTable.region.matter,
    i18n.chain.proMicroModules.oneTable.region.thing,
  ],
  form_level: [
    i18n.chain.proMicroModules.oneTable.level.country,
    i18n.chain.proMicroModules.oneTable.level.province,
    i18n.chain.proMicroModules.oneTable.level.city,
    i18n.chain.proMicroModules.oneTable.level.district,
    i18n.chain.proMicroModules.oneTable.level.street,
  ],
  fill_frequency: [
    i18n.chain.proMicroModules.oneTable.frequency.year,
    i18n.chain.proMicroModules.oneTable.frequency.halfYear,
    i18n.chain.proMicroModules.oneTable.frequency.quarter,
    i18n.chain.proMicroModules.oneTable.frequency.month,
    i18n.chain.proMicroModules.oneTable.frequency.week,
    i18n.chain.proMicroModules.oneTable.frequency.day,
    i18n.chain.proMicroModules.oneTable.frequency.demand,
    i18n.chain.proMicroModules.oneTable.frequency.temporary,
  ],
};

const transformToOptions = (arr?: string[]) => _.map(arr, (it) => ({ label: it, value: it }));
const getOptionsByType = (type: string) => transformToOptions(reportOptions[type]);
const getRegionOptions = () => getOptionsByType('form_region_single');
const getLevelOptions = () => getOptionsByType('form_level');
const getFrequencyOptions = () => getOptionsByType('fill_frequency');

export const form = {
  colon: false,
  layout: 'vertical',
  labelWidth: '300px',
};

export const name = {
  name: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.infoName,
    required: true,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const namePeriodAlias = {
  next_period_form_name_template: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.periodFormNameTemplate,
    'x-decorator': 'FormItem',
    'x-component': TemplateInputWithPreview,
    'x-component-props': {
      options: [
        {
          label: i18n.chain.proMicroModules.oneTable.periodTemplate.periodSequenceNumber,
          value: 'period_sequence_number',
        },
        {
          label: i18n.chain.proMicroModules.oneTable.periodTemplate.periodSequenceNumberAn2Cn,
          value: 'period_sequence_number|an2cn',
        },
        {
          label: i18n.chain.proMicroModules.oneTable.infoName,
          value: 'form.name',
        },
        {
          label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowGroup,
          value: 'now_group',
          children: [
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowGroup,
              value: 'now',
              children: [
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowYear, value: 'now.year' },
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowMonth, value: 'now.month' },
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowDay, value: 'now.day' },
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowHour, value: 'now.hour' },
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowMinute, value: 'now.minute' },
                { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowSecond, value: 'now.second' },
              ],
            },
            { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowQuarter, value: 'now.quarter' },
            { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowWeekOfMonth, value: 'now.week_of_month' },
            { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowWeekOfYear, value: 'now.week_of_year' },
            { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowWeekday, value: 'now.isoweekday()' },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowChineseWeekday,
              value: 'now.chinese_week_day',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowChineseTenDay,
              value: 'now.chinese_ten_day',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowChineseHalfYear,
              value: 'now.chinese_half_year',
              suffix: '半年',
            },
            { label: i18n.chain.proMicroModules.oneTable.periodTemplate.nowChineseAmPm, value: 'now.chinese_am_pm' },
          ],
        },
        {
          label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodGroup,
          value: 'next_period_group',
          children: [
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodGroup,
              value: 'next_period_start_time',
              children: [
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodYear,
                  value: 'next_period_start_time.year',
                },
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodMonth,
                  value: 'next_period_start_time.month',
                },
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodDay,
                  value: 'next_period_start_time.day',
                },
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodHour,
                  value: 'next_period_start_time.hour',
                },
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodMinute,
                  value: 'next_period_start_time.minute',
                },
                {
                  label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodSecond,
                  value: 'next_period_start_time.second',
                },
              ],
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodQuarter,
              value: 'next_period_start_time.quarter',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodWeekOfMonth,
              value: 'next_period_start_time.week_of_month',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodWeekOfYear,
              value: 'next_period_start_time.week_of_year',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodWeekday,
              value: 'next_period_start_time.isoweekday()',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodChineseWeekday,
              value: 'next_period_start_time.chinese_week_day',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodChineseTenDay,
              value: 'next_period_start_time.chinese_ten_day',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodChineseHalfYear,
              value: 'next_period_start_time.chinese_half_year',
            },
            {
              label: i18n.chain.proMicroModules.oneTable.periodTemplate.nextPeriodChineseAmPm,
              value: 'next_period_start_time.chinese_am_pm',
            },
          ],
        },
      ],
    },
    'x-reactions': {
      dependencies: ['name'],
      fulfill: {
        state: {
          'componentProps.nameValue': '{{$deps[0]}}',
        },
      },
    },
    default: i18n.chain.proMicroModules.oneTable.periodFormNameDefault,
  },
};

export const extraFormField = {
  extra_form_field: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.regionTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getRegionOptions(),
    },
  },
};

export const extraFormLevel = {
  extra_form_level: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.levelTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getLevelOptions(),
    },
  },
};

export const extraFormFrequency = {
  extra_form_frequency: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.frequencyTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getFrequencyOptions(),
    },
  },
};

export const extraFormOwner = {
  extra_form_owner: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.ownerTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const extraFormOwnerPhone = {
  extra_form_owner_phone: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.ownerPhoneTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const description = {
  description: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.infoDesc,
    'x-decorator': 'FormItem',
    'x-component': 'Input.TextArea',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const extraAffiliatedOrgs = {
  affiliated_orgs: {
    title: i18n.chain.proMicroModules.oneTable.ownership,
    type: 'array',
    'x-component': 'TreeSelect',
    'x-component-props': {
      allowClear: true,
      multiple: true,
      placeholder: i18n.chain.comPlaceholder.select,
      showCheckedStrategy: 'SHOW_ALL',
      showSearch: true,
      treeCheckStrictly: true,
      treeCheckable: true,
      treeDefaultExpandAll: true,
      treeNodeFilterProp: 'label',
    },
    'x-decorator': 'FormItem',
    'x-decorator-props': {
      colon: false,
      layout: 'vertical',
    },
    'x-reactions': [
      {
        dependencies: ['affiliated_orgs'],
        fulfill: {
          state: {
            value: '{{$deps[0] ? $deps[0].map(item => item.value) : []}}',
          },
        },
      },
    ],
  },
};

export const extraAffiliatedOrgsSettingValues = {
  affiliated_orgs: {
    title: i18n.chain.proMicroModules.oneTable.ownership,
    'x-component-props': {
      showCheckedStrategy: 'SHOW_ALL',
      treeDefaultExpandAll: true,
    },
    'x-decorator-props': {
      colon: false,
      layout: 'vertical',
    },
    'x-metro-datasource': {
      config: {
        innerApi: {
          api: 'organization',
          authTypeOfApi: 'wf_spec_creator',
        },
      },
      dataRequire: {
        items: {
          properties: {
            label: {
              description: '显示值',
              type: 'string',
            },
            value: {
              description: '存储值',
              type: ['string', 'number'],
            },
          },
          required: ['label', 'value'],
          type: 'object',
        },
        type: 'array',
      },
      dataSourceType: 'innerApi',
      filters: [],
      useFilter: false,
    },
    'x-metro-option-type': 'tree_select',
  },
};

export const schemaProperties = {
  ...name,
  ...extraAffiliatedOrgs,
  ...extraFormField,
  ...extraFormLevel,
  ...extraFormFrequency,
  ...extraFormOwner,
  ...extraFormOwnerPhone,
  ...description,
};

export const periodSchemaProperties = {
  ...name,
  ...namePeriodAlias,
  ...extraAffiliatedOrgs,
  ...extraFormField,
  ...extraFormLevel,
  ...extraFormFrequency,
  ...extraFormOwner,
  ...extraFormOwnerPhone,
  ...description,
};

export const allSettingValues = {
  ...extraAffiliatedOrgsSettingValues,
};

export const formSpec = {
  formilySchema: {
    form,
    schema: {
      properties: schemaProperties,
    },
  },
  allSettingValues,
};

export const periodFormSpec = {
  formilySchema: {
    form,
    schema: {
      properties: periodSchemaProperties,
    },
  },
};
