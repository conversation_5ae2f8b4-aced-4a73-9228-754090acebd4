import _ from 'lodash';
import i18n from '../../languages';
import { allSettingValues, extraAffiliatedOrgs } from './_standard';

const reportOptions: Partial<Record<string, string[]>> = {
  form_label: [
    i18n.chain.proMicroModules.oneTable.municipalProject,
    i18n.chain.proMicroModules.oneTable.districtProject,
    i18n.chain.proMicroModules.oneTable.streetProject,
    i18n.chain.proMicroModules.oneTable.dataCollection,
    i18n.chain.proMicroModules.oneTable.dataVerify,
  ],
  form_task_type: [
    i18n.chain.proMicroModules.oneTable.dataCollection,
    i18n.chain.proMicroModules.oneTable.dataVerify,
    i18n.chain.proMicroModules.oneTable.visit,
  ],
  form_region_single: [
    i18n.chain.proMicroModules.oneTable.region.person,
    i18n.chain.proMicroModules.oneTable.region.house,
    i18n.chain.proMicroModules.oneTable.region.company,
    i18n.chain.proMicroModules.oneTable.region.matter,
    i18n.chain.proMicroModules.oneTable.region.thing,
  ],
  form_level: [
    i18n.chain.proMicroModules.oneTable.level.country,
    i18n.chain.proMicroModules.oneTable.level.province,
    i18n.chain.proMicroModules.oneTable.level.city,
    i18n.chain.proMicroModules.oneTable.level.district,
    i18n.chain.proMicroModules.oneTable.level.street,
  ],
  fill_frequency: [
    i18n.chain.proMicroModules.oneTable.frequency.year,
    i18n.chain.proMicroModules.oneTable.frequency.halfYear,
    i18n.chain.proMicroModules.oneTable.frequency.quarter,
    i18n.chain.proMicroModules.oneTable.frequency.month,
    i18n.chain.proMicroModules.oneTable.frequency.week,
    i18n.chain.proMicroModules.oneTable.frequency.day,
    i18n.chain.proMicroModules.oneTable.frequency.demand,
    i18n.chain.proMicroModules.oneTable.frequency.temporary,
  ],
  form_level_muti: [
    i18n.chain.proMicroModules.oneTable.levelMuti.district,
    i18n.chain.proMicroModules.oneTable.levelMuti.street,
    i18n.chain.proMicroModules.oneTable.levelMuti.community,
  ],
  fill_range: [
    i18n.chain.proMicroModules.oneTable.range.hangzhou,
    i18n.chain.proMicroModules.oneTable.range.shangcheng,
    i18n.chain.proMicroModules.oneTable.range.gongsu,
    i18n.chain.proMicroModules.oneTable.range.xihu,
    i18n.chain.proMicroModules.oneTable.range.binjiang,
    i18n.chain.proMicroModules.oneTable.range.xiaoshan,
    i18n.chain.proMicroModules.oneTable.range.yuhang,
    i18n.chain.proMicroModules.oneTable.range.linping,
    i18n.chain.proMicroModules.oneTable.range.qiantang,
    i18n.chain.proMicroModules.oneTable.range.fuyang,
    i18n.chain.proMicroModules.oneTable.range.linan,
    i18n.chain.proMicroModules.oneTable.range.tonglu,
    i18n.chain.proMicroModules.oneTable.range.chunan,
    i18n.chain.proMicroModules.oneTable.range.jiande,
    i18n.chain.proMicroModules.oneTable.range.xihu2,
  ],
};

const transformToOptions = (arr?: string[]) => _.map(arr, (it) => ({ label: it, value: it }));
const getOptionsByType = (type: string) => transformToOptions(reportOptions[type]);
// const getReportTagsOptions = () => getOptionsByType(ReportFormAttrEnum.TAGS);
// const getTaskTypeOptions = () => getOptionsByType(ReportFormAttrEnum.TASK_TYPE);
const getRegionOptions = () => getOptionsByType('form_region_single');
const getLevelOptions = () => getOptionsByType('form_level');
const getFrequencyOptions = () => getOptionsByType('fill_frequency');
const getLevelMutiOptions = () => getOptionsByType('form_level_muti');
const getRangeOptions = () => getOptionsByType('fill_range');

export const formSpec = {
  formilySchema: {
    form: {
      colon: false,
      layout: 'vertical',
      labelWidth: '300px',
    },
    schema: {
      properties: {
        name: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.infoName,
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.input,
          },
        },
        affiliated_orgs: { ...extraAffiliatedOrgs.affiliated_orgs, required: true },
        extra_form_field: {
          type: 'array',
          title: i18n.chain.proMicroModules.oneTable.regionTitle,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            options: getRegionOptions(),
          },
        },
        extra_form_level: {
          type: 'array',
          title: i18n.chain.proMicroModules.oneTable.levelTitle,
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            options: getLevelOptions(),
          },
        },
        extra_form_frequency: {
          type: 'array',
          title: i18n.chain.proMicroModules.oneTable.frequencyTitle,
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            options: getFrequencyOptions(),
          },
        },
        extra_form_range: {
          type: 'array',
          title: i18n.chain.proMicroModules.oneTable.rangeTitle,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            mode: 'multiple',
            options: getRangeOptions(),
          },
        },
        extra_form_rank: {
          type: 'array',
          title: i18n.chain.proMicroModules.oneTable.levelMutiTitle,
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.select,
            mode: 'multiple',
            options: getLevelMutiOptions(),
          },
        },
        extra_form_owner: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.ownerTitle,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.input,
          },
        },
        extra_form_owner_phone: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.ownerPhoneTitle,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.input,
          },
        },
        description: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.infoDesc,
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: i18n.chain.comPlaceholder.input,
          },
        },
      },
    },
  },
  allSettingValues: allSettingValues,
};
