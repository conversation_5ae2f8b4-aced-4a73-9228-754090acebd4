import { HardDrive } from '@metro/icons';
import AppGrid from '@mdtDesign/icons/app-grid';
import { PermissionEnum } from '@mdtProComm/constants';
import { formSpec } from './form-spec/hzsjczlsjzt';
import { overrideStandardConfig } from './standard';

const Key1 = '/inner-md';
const Key2 = '/app-grid';

export const hzsjczlsjzt5 = overrideStandardConfig(
  {
    appName: '杭州市基层治理数据中台',
    appNames: ['杭州市基层治理数据中台'],
    menuConfig: {
      items: [
        {
          key: Key1,
          title: (
            <>
              <HardDrive style={{ fontSize: 16 }} />
              我的数据
            </>
          ),
          className: `top-level menu-divider`,
        },
        {
          key: Key2,
          title: (
            <>
              <AppGrid size={16} />
              页面集成报表填写
            </>
          ),
          className: `top-level menu-divider`,
        },
      ],
    },
    routerConfig: [
      {
        path: Key1,
        permissionKey: PermissionEnum.MODULE_MD,
        innerurl: '/onetable/mydata',
        params: { headerless: true },
      },
      {
        path: Key2,
        permissionKey: PermissionEnum.PRODUCT_MENU_OT,
        url: '/dataapp/aside-preview/cda67a34-471e-4cdc-bdb9-135cdd819faa?feature=1&projectid=6c0326ac-f9de-4867-a36c-56d6b01042d8',
      },
    ],
    reportConfig: {
      formSpec,
    },
  },
  {
    includeAdvancedFilterKey: ['extra_form_range', 'extra_form_rank', 'affiliated_orgs'],
  },
);
