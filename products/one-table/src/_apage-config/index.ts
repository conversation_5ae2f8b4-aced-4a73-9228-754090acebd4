import _ from 'lodash';
import { ONE_TABLE_INFO } from '@mdtProMicroModules/datlas/datlasConfig';
import { configDebugUtil } from '../_util/configDebugUtil';
import { defaultConfig } from './default';
import { hzsjczlsjzt } from './hzsjczlsjzt';
import { hzsjczlsjzt2 } from './hzsjczlsjzt2';
import { hzsjczlsjzt3 } from './hzsjczlsjzt3';
import { hzsjczlsjzt4 } from './hzsjczlsjzt4';
import { hzsjczlsjzt5 } from './hzsjczlsjzt5';
import { hzsjczlsjzt6 } from './hzsjczlsjzt6';
import { lxm } from './lxm';
import { nmtljd } from './nmtljd';
import standard from './standard';
import { yst } from './yst';
// import { ys } from './ys';

const list = [hzsjczlsjzt6, lxm, nmtljd, yst];
const defaultMap: Record<string, any> = {
  default: defaultConfig,
  collect: hzsjczlsjzt,
  hzsjczlsjzt2,
  hzsjczlsjzt3,
  hzsjczlsjzt4,
  hzsjczlsjzt5,
  hzsjczlsjzt6,
  nmtljd,
  lxm,
  yst,
};

export const getOneTableConfig = (appName: string) => {
  const config = _.find(list, (it) => _.isEqual(it.appName, appName) || _.includes(it.appNames, appName));
  // 优先列表查找，然后是使用已有配置，最后是定制化
  let mergedConfig = config || defaultMap[ONE_TABLE_INFO.flowType!] || standard;
  if (ONE_TABLE_INFO.flowTypeDebug) {
    mergedConfig = configDebugUtil(mergedConfig);
  }
  return mergedConfig;
};
