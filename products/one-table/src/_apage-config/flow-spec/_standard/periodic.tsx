import _ from 'lodash';
import { FC, useEffect, useState } from 'react';
import * as cronjsMatcher from '@datasert/cronjs-matcher';
import { HelpOutlined } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Popover } from '@metroDesign/popover';
import { Select, SelectProps } from '@metroDesign/select';
import { Space } from '@metroDesign/space';
import { Tooltip } from '@metroDesign/tooltip';
import { Typography } from '@metroDesign/typography';
import { useCreation } from 'ahooks';
import cronstrue from 'cronstrue/i18n';
import { LanguageEnum } from '@mdtBsComm/constants';
import { EditorCroniterCronInput } from '@mdtProMicroModules/components/cron-input/EditCroniterCronInput';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { ONE_TABLE_INFO } from '@mdtProMicroModules/datlas/datlasConfig';
import i18n from '../../../languages';
import { getFeatureFlags } from '../comm';

const VALUE_CUSTOM = 'custom';

const getDisplayLabel = (value: string, locale: string) => {
  return (
    cronstrue.toString(value, {
      locale,
      throwExceptionOnParseError: false,
      dayOfWeekStartIndexZero: true,
      monthStartIndexZero: false,
      use24HourTimeFormat: true,
    }) || `${value}(${i18n.chain.proMicroModules.crontab.custom})`
  );
};

const FastSelect: FC<any> = (props) => {
  const { value, onChange, disabled, readOnly } = props;
  const [selectVal, setSelectVal] = useState(VALUE_CUSTOM);

  const options: SelectProps['options'] = useCreation(() => {
    const opts = ONE_TABLE_INFO.fastCronTabOptions || [
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl1, value: '0 0 * * *' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl2, value: '0 0 ? * SUN' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl3, value: '0 0 1 * *' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl4, value: '0 0 L * *' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl5, value: '0 0 1 1/3 *' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl6, value: '0 0 1 1/6 *' },
      { label: i18n.chain.proMicroModules.crontab.onetable.exampl7, value: '0 0 1 1 *' },
      ...(ONE_TABLE_INFO.fastCronTabExtraOptions || []),
    ];
    opts.unshift({ label: i18n.chain.proMicroModules.crontab.custom, value: VALUE_CUSTOM });
    return opts;
  }, []);

  const local = useCreation(() => {
    const lang = DatlasAppController.getInstance().getLanguage();
    return lang === LanguageEnum.CN ? 'zh_CN' : 'en';
  }, []);

  useEffect(() => {
    const val = !value || _.some(options, (it) => it.value === value) ? value : VALUE_CUSTOM;
    setSelectVal(val);
  }, [value, options]);

  if (disabled || readOnly) {
    const val = _.find(options, (it) => it.value === value);
    return <div>{val ? val.label : getDisplayLabel(value, local)}</div>;
  }

  const changeVal = (val: string) => {
    setSelectVal(val);
    val !== VALUE_CUSTOM && onChange(val);
  };

  const renderExecuteTip = () => {
    const futureMatches = cronjsMatcher.getFutureMatches(value, { timezone: 'UTC' });
    const items = futureMatches.map((it) => {
      return <p key={it}>{it}</p>;
    });
    return <div>{items}</div>;
  };

  const renderDescription = (label?: string) => {
    return (
      <Typography.Text key="desc" type="secondary">
        {label ? <span style={{ marginRight: 12 }}>{label}</span> : null}
        <Popover
          overlayStyle={{ maxWidth: 300 }}
          placement="leftBottom"
          title={i18n.chain.proMicroModules.crontab.viewLastTime}
          content={renderExecuteTip}
          trigger="click"
        >
          <Button.Link size="small" primary>
            {i18n.chain.proMicroModules.crontab.viewExecuteBtn}
          </Button.Link>
        </Popover>
      </Typography.Text>
    );
  };

  const csEle =
    selectVal === VALUE_CUSTOM
      ? [
          <EditorCroniterCronInput key="editor" {...props} />,
          value ? renderDescription(getDisplayLabel(value, local)) : null,
        ]
      : value
      ? renderDescription()
      : null;

  return (
    <Flex vertical gap={6}>
      <Select
        placeholder={i18n.chain.proMicroModules.crontab.hot}
        options={options}
        value={selectVal}
        onChange={changeVal}
      />
      {csEle}
    </Flex>
  );
};

export const flowSpec = {
  formilySchema: {
    form: { layout: 'vertical', colon: false },
    schema: {
      type: 'object',
      properties: {
        flowTitle: {
          type: 'void',
          'x-component': 'DescriptionText',
          'x-component-props': {
            textContent: i18n.chain.proMicroModules.oneTable.fillSetting,
            textMode: 'h3',
            style: { marginBottom: 12 },
          },
        },
        end_date: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.endFillDate,
          'x-decorator': 'FormItem',
          'x-component': 'DatePicker',
        },
        cycle_timer: {
          required: true,
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.flowCycletimer,
          'x-decorator': 'FormItem',
          'x-component': FastSelect,
        },
        flowTitle2: {
          type: 'void',
          'x-component': 'DescriptionText',
          'x-component-props': {
            textContent: i18n.chain.proMicroModules.oneTable.flowSetting,
            textMode: 'h3',
            style: { borderTop: `1px solid var(--metro-divider-0)`, paddingTop: 16 },
          },
        },
        flowConfig: {
          type: 'void',
          properties: {
            space: {
              type: 'void',
              'x-component': 'Space',
              'x-component-props': { direction: 'vertical', size: 12 },
              properties: {
                need_approval: {
                  type: 'boolean',
                  'x-component': 'Checkbox',
                  default: true,
                  'x-component-props': {
                    children: i18n.chain.proMicroModules.oneTable.needApproval,
                  },
                },
                complete_workflow_after_next_period: {
                  type: 'boolean',
                  'x-component': 'Checkbox',
                  default: false,
                  'x-component-props': {
                    children: (
                      <Space size={6}>
                        {i18n.chain.proMicroModules.oneTable.completeWorkflowAfterNextPeriod}
                        <Tooltip overlay={i18n.chain.proMicroModules.oneTable.completeWorkflowAfterNextPeriodTip}>
                          <span>
                            <HelpOutlined />
                          </span>
                        </Tooltip>
                      </Space>
                    ),
                  },
                },
                inherit_data_source: {
                  type: 'string',
                  title: i18n.chain.proMicroModules.oneTable.inheritDataSource,
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    style: {
                      marginBottom: 0,
                    },
                  },
                  'x-component': 'Radio.Group',
                  'x-component-props': {
                    direction: 'vertical',
                    spaceProps: {
                      size: 0,
                    },
                  },
                  enum: [
                    { label: i18n.chain.proMicroModules.oneTable.previousPeriod, value: 'previous_period' },
                    {
                      label: i18n.chain.proMicroModules.oneTable.noInherit,
                      value: 'no_inherit',
                    },
                    {
                      label: i18n.chain.proMicroModules.oneTable.sourceDatapkg,
                      value: 'source_datapkg',
                    },
                  ],
                  default: 'previous_period',
                },
                feature_flags: getFeatureFlags(),
              },
            },
          },
        },
      },
    },
  },
  allSettingValues: {},
};
