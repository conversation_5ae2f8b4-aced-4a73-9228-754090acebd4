import { ModuleIdEnum, PermissionEnum } from '@mdtProComm/constants';
import { OneTableH5RoutePathEnum, OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import {
  ENABLE_HASH_ROUTER,
  IS_DEVELOP,
  MICRO_DATA_FACTORY,
  MICRO_DATA_MARKET,
  MICRO_MY_DATA,
  MICRO_ONE_TABLE,
  MICRO_ORGANIZATION_MANAGEMENT,
  MICRO_RESOURCE_SHARE,
  MICRO_WORKFLOW,
} from '../config';
import DataApp from '../routers/dataapp';
import DataFactory from '../routers/datafactory';
import DataMap from '../routers/datamap';
import DataMarket from '../routers/datamarket';
import OneTable from '../routers/onetable';
import Shared from '../routers/shared';
import SSO from '../routers/sso';
import Workflow from '../routers/workflow';
import { DataMarketPath, SharedPath, WorkflowPath } from './product-path';
import { getSubAppPath } from './util';

/**
 * Tip: 需要去头的场景（eg:外链页面)指定具体的项目页面路由，配置headerLess。
 * 放在对应产品路由前面。
 */
export const allAuthRoutes: IRoute[] = [
  {
    path: getSubAppPath(ModuleIdEnum.DATA_MAP),
    View: DataMap,
    permissionKey: PermissionEnum.PRODUCT_MENU_DS,
    notExact: true,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.DATA_MARKET)}/${DataMarketPath.PKG_DETAIL}/:id`,
    View: DataMarket,
    permissionKey: PermissionEnum.PRODUCT_MENU_DM,
    notExact: true,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.DATA_MARKET)}/${ModuleIdEnum.MY_DATA}/${DataMarketPath.PKG_FACTORY_DETAIL}/:id`,
    View: DataMarket,
    permissionKey: true,
    notExact: true,
    headerLess: true,
  },
  {
    path: getSubAppPath(SharedPath.NOTIFICATION_H5),
    View: Shared,
    permissionKey: true,
    notExact: true,
    headerLess: true,
  },
  {
    path: getSubAppPath(SharedPath.FORWARDER_H5),
    View: Shared,
    permissionKey: true,
    notExact: true,
    headerLess: true,
  },
  {
    path: getSubAppPath(SharedPath.GRID_MENU_H5),
    View: Shared,
    permissionKey: true,
    notExact: true,
    headerLess: true,
  },
  {
    path: getSubAppPath(ModuleIdEnum.DATA_MARKET),
    View: DataMarket,
    permissionKey: PermissionEnum.PRODUCT_MENU_DM,
    notExact: true,
  },
  {
    path: getSubAppPath(ModuleIdEnum.DATA_FACTORY),
    View: DataFactory,
    permissionKey: PermissionEnum.PRODUCT_MENU_DF,
    notExact: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.DASHBOARD_H5_MENU}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.DASHBOARD_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.FORM_MANAGEMENT_H5_MENU}`,
    View: OneTable,
    permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.FORM_MANAGEMENT_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_FORM_H5_MENU}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_FORM_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_REVIEW_H5_MENU}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_REVIEW_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_TASK_H5_MENU}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_CENTER_TASK_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.MISSION_DETAIL}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}/${OneTableH5RoutePathEnum.SETTING}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: !IS_DEVELOP,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.REPORT_DETAIL_TASK}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.REPORT_DETAIL_TASK_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.NEW_MISSION_CENTER_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.NEW_FORM_MANAGEMENT_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.REPORT_DETAIL_H5}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.FILL_FORM}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.ONE_TABLE)}${OneTableRoutePathEnum.FILL_FORM_SINGLE}`,
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    headerLess: true,
  },
  {
    path: getSubAppPath(ModuleIdEnum.ONE_TABLE),
    View: OneTable,
    permissionKey: PermissionEnum.PRODUCT_MENU_OT,
    notExact: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.WORKFLOW_APPLY}/:id`,
    View: Workflow,
    permissionKey: true,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.WORKFLOW_DETAIL}/:wfSpecId/:wfId`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.WORKFLOW_DETAIL_DRAWER}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.APPLICATION_LIST_SINGLE}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.APPLICATION_LIST_H5_MENU}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.APPLICATION_LIST_H5}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.DATA_APPROVAL_SINGLE}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.DATA_APPROVAL_H5_MENU}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.DATA_APPROVAL_H5}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: `${getSubAppPath(ModuleIdEnum.WORKFLOW)}/${WorkflowPath.SETTING}`,
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    headerLess: true,
  },
  {
    path: getSubAppPath(ModuleIdEnum.WORKFLOW),
    View: Workflow,
    permissionKey: PermissionEnum.PRODUCT_MENU_WF,
    notExact: true,
  },
  /** route添加(脚本注释) */
  {
    path: getSubAppPath(ModuleIdEnum.DATA_APP_IFRAME),
    View: DataApp,
    permissionKey: PermissionEnum.PRODUCT_MENU_DS,
    notExact: true,
  },
];

// sso已被移除
export const unAuthApps: IRoute[] = [
  {
    path: getSubAppPath(ModuleIdEnum.SSO),
    View: SSO,
  },
];

class RouterController extends DatlasRouterController {
  public unAuthRoutes() {
    const home = unAuthApps[0].path;
    this.changeRouter({ home, routes: [...unAuthApps] });
  }

  public getAllAuthRoutes() {
    return allAuthRoutes;
  }

  /** 产品跳转(脚本注释) */
  public gotoDataFactory(sub?: string) {
    this.gotoMicro(ModuleIdEnum.DATA_FACTORY, MICRO_DATA_FACTORY.enableHashRouter, sub);
  }

  public gotoDataMarket(sub?: string) {
    this.gotoMicro(ModuleIdEnum.DATA_MARKET, MICRO_DATA_MARKET.enableHashRouter, sub);
  }

  public gotoMyData(product: string, sub?: string) {
    this.gotoMicro(`${product}/${ModuleIdEnum.MY_DATA}`, MICRO_MY_DATA.enableHashRouter, sub);
  }

  public gotoOrganizationManagement(product: string, sub?: string) {
    this.gotoMicro(
      `${product}/${ModuleIdEnum.ORGANIZATION_MANAGEMENT}`,
      MICRO_ORGANIZATION_MANAGEMENT.enableHashRouter,
      sub,
    );
  }

  public gotoResourceShare(product: string, sub?: string) {
    this.gotoMicro(`${product}/${ModuleIdEnum.RESOURCE_SHARE}`, MICRO_RESOURCE_SHARE.enableHashRouter, sub);
  }

  public gotoWorkflow(sub?: string) {
    this.gotoMicro(ModuleIdEnum.WORKFLOW, MICRO_WORKFLOW.enableHashRouter, sub);
  }

  public gotoOneTable(sub?: string) {
    this.gotoMicro(ModuleIdEnum.ONE_TABLE, MICRO_ONE_TABLE.enableHashRouter, sub);
  }

  public gotoSharedPage(sub: string) {
    this.gotoSharedMicro(sub);
  }

  public gotoLogin(logout?: boolean) {
    this.gotoPath({
      pathname: ModuleIdEnum.SSO,
      search: `${logout ? '&force=true' : ''}`,
    });
  }

  private gotoMicro(path: string, subHas?: boolean, sub?: string) {
    let pathname = sub ? `${path}/${sub}` : path;
    if (ENABLE_HASH_ROUTER && subHas && sub) {
      pathname = `${path}/#/${sub}`;
    }
    pathname = getSubAppPath(pathname);
    this.gotoPath({ pathname });
  }

  private gotoSharedMicro(sub: string) {
    let pathname = `${sub}`;
    if (ENABLE_HASH_ROUTER) {
      pathname = `#/${sub}`;
    }
    pathname = getSubAppPath(pathname);
    this.gotoPath({ pathname });
  }
}

export { RouterController };
