import { DataNode } from '@mdtBsComm/components/side-menu';
import { formateDate } from '@mdtBsComm/utils/dayUtil';
import { DatlasAppSideMenuController } from '@mdtProMicroModules/datlas/app-side-menu';
import { RoutePathEnum } from '../_util/constants';
import i18n from '../languages';
import { AppSideMenuExtra } from './AppSideMenuExtra';

export enum MenuKeyEnum {
  MANAGEMENT = 'management',
  DATA_MARKET = 'data-market',
  ONE_TABLE = 'one-table',
  DATA_MAP = 'data-map',
  DATA_SETTING = 'data-setting',
  DEFAULT_PAGE = 'default-page',
  COMMON = 'common',
  OPS_METRICS = 'ops-metrics',
  OPERATION_LOG = 'operation-log',
}

class AppSideMenuController extends DatlasAppSideMenuController {
  public getAppInfo() {
    return {
      appExpireTime: i18n.chain.orgAdmin.menu.expired(formateDate(this.app!.getAppExpireTime())),
      appName: this.app!.getAppName() || '',
    };
  }

  public getExtraRender() {
    return AppSideMenuExtra;
  }

  public getExtraClassName() {
    return 'org-manage-app-side-menu';
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  public async initMenus() {
    const ps = this.app!.getUserPermissionController().getMenuPermission();
    const menus: DataNode[] = [];

    // 用户管理
    if (ps.enableAuthManage) {
      const children: DataNode[] = [];
      ps.enableOrganization &&
        children.push({ key: RoutePathEnum.ORGANIZATION, title: i18n.chain.orgAdmin.menu.userAndOrg });
      ps.enableRoleUser && children.push({ key: RoutePathEnum.ROLE_USER, title: i18n.chain.orgAdmin.menu.roleManage });
      ps.enableAuth && children.push({ key: RoutePathEnum.AUTH_MANAGE, title: i18n.chain.orgAdmin.menu.authManage });
      menus.push({
        key: MenuKeyEnum.MANAGEMENT,
        title: i18n.chain.orgAdmin.menu.userManage,
        disabled: true,
        className: 'top-level',
        children: children,
      });
    }
    // 偏好设置
    if (ps.enablePreferenceManage) {
      const preferenceChild: DataNode[] = [];
      const children: DataNode[] = [];

      // 通用权限
      if (ps.enableLogoStyle) {
        children.push({ key: RoutePathEnum.APP_STYLE, title: i18n.chain.orgAdmin.menu.appStyle });
        // children.push({ key: RoutePathEnum.LOGO_STYLE, title: i18n.chain.orgAdmin.menu.logo });
      }
      if (ps.enableCustomLogin) {
        children.push({ key: RoutePathEnum.CUSTOM_LOGIN, title: i18n.chain.orgAdmin.menu.customLogin });
      }
      preferenceChild.push({
        key: MenuKeyEnum.COMMON,
        title: i18n.chain.orgAdmin.menu.common,
        disabled: true,
        children,
      });
      // 地图轻应用权限
      if (ps.enableDataMapManage) {
        const children: DataNode[] = [];
        ps.enableHomeSettingManage &&
          children.push({ key: RoutePathEnum.HOME_SETTING, title: i18n.chain.orgAdmin.menu.defaultPlay });
        preferenceChild.push({
          key: MenuKeyEnum.DATA_MAP,
          title: i18n.chain.product.datlas,
          disabled: true,
          children,
        });
      }
      // 如果有数据市场权限
      if (ps.enableDataMarketManage) {
        preferenceChild.push({
          key: MenuKeyEnum.DATA_MARKET,
          title: i18n.chain.product.dataMarket,
          disabled: true,
          children: [{ key: RoutePathEnum.SETTING_DATAPKG, title: i18n.chain.orgAdmin.menu.dataSetting }],
        });
      }
      // 如果有一表通
      if (ps.enableAuthManage) {
        preferenceChild.push({
          key: MenuKeyEnum.ONE_TABLE,
          title: i18n.chain.product.oneTable,
          disabled: true,
          children: [{ key: RoutePathEnum.GRANTED_OT_FORM, title: i18n.chain.orgAdmin.menu.grantedForm }],
        });
      }

      menus.push({
        key: MenuKeyEnum.DATA_SETTING,
        title: i18n.chain.orgAdmin.menu.preferenceSetting,
        className: 'top-level menu-divider',
        disabled: true,
        children: preferenceChild,
      });
    }
    // 运维统计
    if (ps.enableMaintenanceStats) {
      const opsChild: DataNode[] = [];
      // 操作日志
      if (ps.enableUserHistoryMenu) {
        opsChild.push({
          key: RoutePathEnum.OPERATION_LOG,
          title: i18n.chain.orgAdmin.menu.operationLog,
        });
      }

      menus.push({
        key: MenuKeyEnum.OPS_METRICS,
        title: i18n.chain.orgAdmin.menu.opsMetrics,
        className: 'top-level menu-divider',
        disabled: true,
        children: opsChild,
      });
    }
    this.changeMenuData(menus);
    this.changeOpenKeys([
      MenuKeyEnum.MANAGEMENT,
      MenuKeyEnum.DATA_SETTING,
      MenuKeyEnum.DATA_MAP,
      MenuKeyEnum.DATA_MARKET,
      MenuKeyEnum.ONE_TABLE,
      MenuKeyEnum.COMMON,
      MenuKeyEnum.OPS_METRICS,
    ]);
  }
}

export { AppSideMenuController };
