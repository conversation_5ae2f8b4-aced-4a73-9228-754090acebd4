.org-manage {
  &-app-info {
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 190px;
    height: 62px;
    margin: 0 12px;
    border-top: 1px solid var(--dmc-split-page-color);

    &-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-right: 10px;
      color: var(--dmc-text-6);
      font-weight: 500;
      font-size: 13px;
      background: transparent;
      border: 1px solid var(--dmc-border-1);
      border-radius: 32px;
      pointer-events: none;
    }

    &-title {
      width: 132px;
      color: var(--dmc-text-6);
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
    }

    &-expire {
      color: var(--dmc-text-4);
      font-weight: 500;
      font-size: 12px;
    }
  }
}

.metro-modal-confirm-info.modal-info-without-icon {
  .metro-modal-confirm-body > .metro-icon {
    display: none;
  }

  .metro-modal-confirm-body .metro-modal-confirm-content {
    margin-top: 20px;
  }

  .metro-modal-confirm-title {
    position: absolute;
    top: 0;
  }

  .metro-form-view {
    padding-bottom: 0;
  }

  .form-view-tool-wrap .metro-btn {
    margin-bottom: 0;
  }
}

.org-manage-app-side-menu {
  padding-bottom: 65px;
}
