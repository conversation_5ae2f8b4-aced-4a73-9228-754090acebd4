import { FC } from 'react';
import type { Dayjs } from 'dayjs';
import { BlurSearchSelect } from '@mdtBsComm/components/blur-search-select';
import { FormField } from '@mdtBsComm/components/form';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { lessThanNow } from '@mdtBsComm/utils/dayUtil';
import { FormComp } from '@mdtBsComponents/form-comp';
import Button from '@mdtDesign/button';
import DatePicker, { TimeFormat } from '@mdtDesign/date-picker';
import { Dropmenu, DropmenuProps } from '@mdtDesign/dropdown';
import Popconfirm from '@mdtDesign/popconfirm';
import Tooltip from '@mdtDesign/tooltip';
import { getOperationLabel, OPERATION_TYPE_ENUM } from '@mdtProComm/utils/operationUtil';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import i18n from '../../languages';
import { IFilters, OperationLogController } from './OperationLogController';
import './index.less';

const selectCommonStyle = {
  width: 140,
};

const RangePicker = DatePicker.RangePicker;

interface IProps {
  controller: OperationLogController;
}
const OperationLog: FC<IProps> = ({ controller }) => {
  return (
    <div className="page_operation-log">
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
    </div>
  );
};

export const Header: FC<IProps & { ColumnSetting: FC }> = ({ controller, ColumnSetting }) => {
  const userList = useObservableState(controller.getUserList$());
  const operationMenu = useObservableState(controller.getOperationMenus$());
  const formController = controller.getFormCompController();
  const formData = useObservableState(formController.getFormData$());
  const operationDisabled = !formData?.resourceType;
  const oneYearFormat = (date: Dayjs) => {
    return lessThanNow(date.add(1, 'year'));
  };
  return (
    <>
      <div className="page_operation-log-title">{i18n.chain.orgAdmin.menu.operationLog}</div>
      <div className="page_operation-log-header">
        <FormComp controller={formController} className="page_operation-log-header-left">
          <FormField name="date" label="">
            <RangePicker timeFormat={TimeFormat.hm} showTime disabledDate={oneYearFormat} />
          </FormField>
          <FormField name="user" label="">
            <BlurSearchSelect
              dropdownMatchSelectWidth={300}
              style={selectCommonStyle}
              allowClear
              placeholder={i18n.chain.orgAdmin.operationLog.user}
              options={controller.translateSelect(userList)}
            />
          </FormField>
          <FormField name="resourceType" label="">
            <BlurSearchSelect
              style={selectCommonStyle}
              allowClear
              placeholder={i18n.chain.orgAdmin.operationLog.resource}
              options={controller.getResourceTypeOptions()}
            />
          </FormField>
          <FormField name="operation" label="">
            <FormDropmenu menus={operationMenu} disabled={operationDisabled} formData={formData} />
          </FormField>
        </FormComp>
        <div className="page_operation-log-header-right">
          <ColumnSetting />
          <Button leftIcon="search" type="primary" onClick={controller.onSearch}>
            {i18n.chain.orgAdmin.operationLog.search}
          </Button>
          <Popconfirm
            placement="bottomRight"
            okText={i18n.chain.orgAdmin.operationLog.exportOk}
            cancelText={i18n.chain.orgAdmin.operationLog.exportCancel}
            message={i18n.chain.orgAdmin.operationLog.exportTitle}
            description={i18n.chain.orgAdmin.operationLog.exportDescription}
            trigger="click"
            emotion="info"
            onConfirm={controller.exportOperationLog}
          >
            <Button
              // 隐藏
              style={{ display: 'none' }}
              leftIcon="download"
            >
              {i18n.chain.orgAdmin.operationLog.export}
            </Button>
          </Popconfirm>
        </div>
      </div>
    </>
  );
};

const FormDropmenu: FC<
  DropmenuProps & {
    onChange?: (value: string) => void;
    formData?: IFilters;
  }
> = ({ onChange, formData, ...rest }) => {
  const menuDom = (
    <Dropmenu
      {...rest}
      onClickMenuItem={(item) => {
        onChange?.(item.key as string);
      }}
    >
      {getOperationLabel(formData?.operation as OPERATION_TYPE_ENUM) ?? (
        <div className="page_operation-log-placeholder">{i18n.chain.orgAdmin.operationLog.operator}</div>
      )}
    </Dropmenu>
  );
  return !formData?.resourceType ? (
    <Tooltip title={i18n.chain.orgAdmin.operationLog.operationTooltip}>{menuDom}</Tooltip>
  ) : (
    menuDom
  );
};
export { OperationLog };
