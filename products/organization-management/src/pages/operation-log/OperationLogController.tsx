import _ from 'lodash';
import { FC } from 'react';
import { BehaviorSubject } from 'rxjs';
import { Empty } from '@mdtBsComm/components/empty';
import { transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { FormCompController } from '@mdtBsComponents/form-comp';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IPaginationQuery } from '@mdtBsServices/interfaces';
import type { RangePickerProps } from '@mdtDesign/date-picker';
import Tag from '@mdtDesign/tag';
import { ResourceTypeEnum } from '@mdtProComm/constants';
import { IAuditQuery, IUsers } from '@mdtProComm/interfaces';
import {
  getOperationLabel,
  getOperationWithResourceType,
  IResourceOperationInterface,
} from '@mdtProComm/utils/operationUtil';
import { getResourceTypeLabel } from '@mdtProComm/utils/resourceUtil';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import emptyDark from './empty-dark.svg';
import emptyLight from './empty-light.svg';
import { Header } from './OperationLog';
import { IOperationLogModel } from './OperationLogModel';

export interface ILogs {
  eventTime: string;
  operationResult: string;
  operationFrom: string;
  resourceName: number;
  resourceType: string;
  operation: string;
  appId: number;
  userId: number;
  id: string;
}

export interface IFilters {
  date?: RangePickerProps['value'];
  user?: ILogs['userId'];
  resourceType?: ILogs['resourceType'];
  operation?: ILogs['operation'];
}

const RESOURCE_TYPES = [
  ResourceTypeEnum.USER,
  ResourceTypeEnum.DATAPKG,
  ResourceTypeEnum.PROJECT,
  ResourceTypeEnum.PAGE,
  ResourceTypeEnum.GRAPH,
  ResourceTypeEnum.MAP,
  ResourceTypeEnum.MEDIA,
  ResourceTypeEnum.SCRIPT,
  ResourceTypeEnum.FLOW,
  ResourceTypeEnum.WORKFOLW_SPEC,
];

class OperationLogController extends RequestController {
  private app: AppController;
  private Model: IOperationLogModel;
  private tableController: TableCurdWithSimpleSearchController<ILogs>;
  private formCompController: FormCompController<IFilters>;

  // User相关
  private userList$ = new BehaviorSubject<IUsers[]>([]);
  private userIdNameMap: Record<number, string> = {};

  // operation相关
  private operationMenus$ = new BehaviorSubject<IResourceOperationInterface[]>([]);

  public constructor(app: AppController, Model: IOperationLogModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.formCompController = new FormCompController<IFilters>();
    this.tableController = new TableCurdWithSimpleSearchController<ILogs>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: (params?: IAuditQuery) => this.queryLogsFirst(params),
          loadNextPageDataListFunc: (params: IPaginationQuery) => this.queryLogsNext(params),
        },
        tableOptions: this.initTableOptions,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.initRequiredData();
    this.listenFiltersChange();
  }

  public destroy() {
    super.destroy();
    this.tableController.destroy();
    this.userList$.complete();
    this.userList$.next([]);
    this.operationMenus$.complete();
    this.operationMenus$.next([]);
    this.formCompController.destroy();
    this.app = null!;
    this.Model = null!;
  }

  public getUserList$() {
    return this.userList$;
  }

  public getOperationMenus$() {
    return this.operationMenus$;
  }

  public getFormCompController() {
    return this.formCompController;
  }

  public getResourceTypeOptions() {
    return _.map(RESOURCE_TYPES, (key) => ({ label: getResourceTypeLabel(key, false), value: key }));
  }

  public getTableController() {
    return this.tableController;
  }

  public translateSelect = (data: any[], labelKey = 'name', valueKey = 'id') => {
    return _.map(data, (item) => ({
      label: item[labelKey],
      value: item[valueKey],
    }));
  };

  public onSearch = () => {
    const params = this.getBackendFilterParams();
    this.tableController.loadDataList(params);
  };

  public exportOperationLog = () => {
    console.log('export');
  };

  private initTableOptions = (): IVirtualizedTableProps => {
    const emptyImage = this.app.getTheme() === 'dark' ? emptyDark : emptyLight;
    return {
      columns: [
        { name: i18n.chain.orgAdmin.operationLog.date, code: 'eventTime' },
        { name: i18n.chain.orgAdmin.operationLog.user, code: 'userId', render: (userId) => this.userIdNameMap[userId] },
        {
          name: i18n.chain.orgAdmin.operationLog.resource,
          code: 'resourceType',
          render: (value) => <Tag tag={getResourceTypeLabel(value, false)} color="blue-700" />,
        },
        {
          name: i18n.chain.orgAdmin.operationLog.operator,
          code: 'operation',
          render: (value) => getOperationLabel(value),
        },
        { name: i18n.chain.orgAdmin.operationLog.object, code: 'resourceName' },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: (
        <Empty
          title={i18n.chain.orgAdmin.operationLog.emptyTitle}
          Image={<img width={180} height={160} src={emptyImage} alt="" />}
          description={i18n.chain.orgAdmin.operationLog.emptyDescription}
        />
      ),
      withVerticalBorder: false,
      headerRender: (ColumnSetting: FC) => <Header controller={this} ColumnSetting={ColumnSetting} />,
    };
  };

  private initHeaderOptions = () => {
    return {
      createBtnLabel: '',
      hideInput: true,
    };
  };

  private queryLogsFirst(params?: IAuditQuery) {
    return this.Model.queryLogsFirst(params);
  }

  private queryLogsNext(params?: IAuditQuery) {
    const filterParams = this.getBackendFilterParams();
    return this.Model.queryLogsNext({ ...filterParams, ...params });
  }

  private getBackendFilterParams(): IAuditQuery {
    const formValues = this.formCompController.getFormDataValue();
    let result: IAuditQuery = {};
    const date = _.map(formValues?.date, transformDateToUnix);
    if (!_.isEmpty(date)) {
      result.event_time_min = date[0];
      result.event_time_max = date[1];
    }
    formValues?.user && (result.user_id = formValues.user);
    formValues?.resourceType && (result.resource_type = formValues.resourceType);
    formValues?.operation && (result.operation = formValues.operation);
    return result;
  }

  private initRequiredData() {
    this.tableController.changeDataListLoading(true);
    this.Model.queryAllUsers(this.app.getAppId(), '').subscribe(({ userList, userIdNameMap }) => {
      this.tableController.loadDataList();
      this.userIdNameMap = userIdNameMap;
      this.userList$.next(userList);
    });
  }

  private listenFiltersChange() {
    this.formCompController.getChangedValues$().subscribe((value) => {
      if (_.has(value, 'resourceType')) {
        this.operationMenus$.next(getOperationWithResourceType(value.resourceType));
        setTimeout(() => {
          this.formCompController.changeFormData({
            ...this.formCompController.getFormDataValue(),
            operation: undefined,
          });
        });
      }
    });
  }
}

export { OperationLogController };
