{"name": "sso", "cnName": "脉策SSO", "description": "脉策产品统一认证中心", "version": "2.35.18", "private": true, "scripts": {"start": "cross-env DEVELOP_ENV=dev craco start", "start:staging": "cross-env DEVELOP_ENV=staging craco start", "start:debug": "cross-env DEVELOP_ENV=debug craco start", "release": "CI=false craco build", "release:analyze": "craco build --analyze", "test": "craco test"}, "cracoConfig": "craco.config.js", "dependencies": {"@mdt/product-sso": "^1.8.14", "awesome-phonenumber-fork": "1.0.4", "gdt-jsapi": "^1.9.51", "md5": "^2.3.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}