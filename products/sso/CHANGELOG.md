# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.35.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.18) (2025-07-24)

### Features

- ✨ sso 增加明文传递 token 功能 ([6703a4a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6703a4acfd590247009f9f78446d302f29df26af))

## [2.35.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.17) (2025-07-08)

### Bug Fixes

- 🐛 双因子登录可关闭,按钮 disabled 跟随 value 验证可用性 ([cd63077](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cd6307739cc3b285b5b0afcab71fffb282bc9f6c))

## [2.35.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.16) (2025-07-03)

**Note:** Version bump only for package sso

## [2.35.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.15) (2025-06-19)

**Note:** Version bump only for package sso

## [2.35.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.14) (2025-06-10)

**Note:** Version bump only for package sso

## [2.35.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.13) (2025-05-29)

### Bug Fixes

- url 遗漏 decode ([ce62053](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ce62053204720c398ce817b7b513beba68098a06))

## [2.35.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.12) (2025-05-19)

**Note:** Version bump only for package sso

## [2.35.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.11) (2025-05-14)

### Bug Fixes

- 🐛 sso config path redirect 支持 encode ([150c5f7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/150c5f776b455b62fac75d71e0db46795e8b0d1e))

## [2.35.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.10) (2025-04-27)

**Note:** Version bump only for package sso

## [2.35.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.9) (2025-04-23)

### Features

- ✨ 增加 ssoout params 再从 redirect 里面再次解析 ([6b50592](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6b5059274d58b7f49f621e9edf47fdd7c1a54f23))

## [2.35.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.8) (2025-04-23)

### Features

- ✨ sso 增加参数透传配置 ([0ada780](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0ada7806a8ba5e9bb9b25a63188dccd032706077))

## [2.35.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.7) (2025-04-21)

### Bug Fixes

- 🐛 修复免密登录无法走身份校验和切换主身份的问题 ([0f5ffd4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0f5ffd42951224b1e2198c11d6519b8ce9d3f7b4))

## [2.35.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.6) (2025-04-02)

**Note:** Version bump only for package sso

## [2.35.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.5) (2025-04-01)

**Note:** Version bump only for package sso

## [2.35.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.4) (2025-03-31)

### Features

- ✨ 抽离 footer 渲染,增加统一的默认备案号 ([41db331](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41db3315a22fd1529d314e0ad0dab618af8246b3))

## [2.35.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.3) (2025-03-31)

### Features

- ✨ 增加产品备案号 ([8c5ae70](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c5ae70e6a8b67faf7795b35b6bc92ff59aaeb3c))

## [2.35.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.2) (2025-03-27)

### Features

- ✨ 前端主导跳转默认身份 ([9883a57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9883a575446b9089511ec4a78477563062fb4689))

## [2.35.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.1) (2025-03-27)

### Features

- ✨ 增加身份指定跳转,增加身份默认偏好,增加默认身份标识 ([6fd5f9f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fd5f9f5759256aab83e8dbe1e98e8e452f080af))

# [2.35.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.35.0) (2025-02-24)

### Bug Fixes

- 🐛 sdk 需要在 ready 回调下进行 ([ac12729](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac12729939d30d889d1b5c8c3435dc7d398b9cbf))

## [2.34.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.34.4) (2025-02-17)

**Note:** Version bump only for package sso

## [2.34.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.34.3) (2025-01-23)

**Note:** Version bump only for package sso

## [2.34.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.34.2) (2025-01-09)

### Features

- ✨ collector-sso 增加 baseUrl 的配置 ([e3fd3bc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3fd3bc59e553d88ba49b18f6ed8ccc7f12c81ff))
- ✨ sso 页脚年份更新 ([6c767a9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6c767a996992d4a0fe2ca3634d52515029a871d2))

## [2.34.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.34.1) (2025-01-06)

**Note:** Version bump only for package sso

# [2.34.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.34.0) (2024-12-23)

**Note:** Version bump only for package sso

## [2.33.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.33.4) (2024-12-16)

### Features

- ✨ sso 增加参数指定 config namesapce ([3cab764](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cab76411bb5c9b8c9158f7515b473d52f0de8f1))

## [2.33.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.33.3) (2024-12-03)

**Note:** Version bump only for package sso

## [2.33.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.33.2) (2024-12-02)

**Note:** Version bump only for package sso

## [2.33.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.33.1) (2024-12-02)

**Note:** Version bump only for package sso

# [2.33.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.33.0) (2024-11-26)

### Bug Fixes

- 🐛 sso 不修改 deployEnableHashRouter ([62fd82b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/62fd82b7614b214ce21d1d63037dd1f7e6442375))

## [2.32.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.32.2) (2024-11-14)

**Note:** Version bump only for package sso

## [2.32.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.32.1) (2024-11-07)

### Features

- ✨ 国际化补充 ,sso 文案图片调整 ([d83df95](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d83df95f19b841b509eb0f9ddb55d6c0de63030a))

# [2.32.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.32.0) (2024-11-05)

### Bug Fixes

- 🐛 sso 展示栏底图展示优化 ([1fa8976](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1fa89766075f8ddb7368daf56104546f1d639005))
- 🐛 修改左边图片 ([4e4b9cb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4e4b9cb8644f2aa9a6681fae2d58bc7fbc26087b))

## [2.31.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.9) (2024-11-04)

### Features

- ✨ sso 新样式 ([fede183](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fede18371e88326ccb2e602c41261264ec8fefce))

## [2.31.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.8) (2024-10-28)

### Bug Fixes

- 🐛 带数据下发优化 ([1798427](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/179842745b86a5e3fad8472f78c1f6df2d0986a1))

## [2.31.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.7) (2024-10-28)

### Bug Fixes

- 🐛 修复动态引入浙政钉 sdk 引起的问题 ([ba94138](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ba941382f9f16eaa694ff4d980d3ed66503fde83))

## [2.31.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.6) (2024-10-27)

**Note:** Version bump only for package sso

## [2.31.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.5) (2024-10-23)

### Bug Fixes

- 🐛 身份信息修改样式和逻辑 ([d1872f1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d1872f190de93b7218405104d776e268825f1cfe))

## [2.31.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.4) (2024-10-22)

**Note:** Version bump only for package sso

## [2.31.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.3) (2024-10-15)

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))

## [2.31.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.2) (2024-08-13)

**Note:** Version bump only for package sso

## [2.31.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.1) (2024-08-12)

### Features

- ✨ 配置只有一个登录项隐藏 tab, collectorsso 针对配置项的适配工作 ([5fc186c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc186cb5585b3386deeae25813e904fc3292749))

# [2.31.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.31.0) (2024-08-09)

**Note:** Version bump only for package sso

## [2.30.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.30.3) (2024-08-07)

### Bug Fixes

- 🐛 兼容浙政钉免密参数 ([1167a0b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1167a0b489450884be0f7301da0547b81b78915f))

## [2.30.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.30.2) (2024-08-02)

### Bug Fixes

- 🐛 修复 iframe 嵌套弹窗登陆失效的问题 ([6fa5343](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fa534371210d1905a89933da22d7243318cc482))

## [2.30.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.30.1) (2024-07-29)

### Features

- ✨ add sso debug mode ([1aa037f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1aa037fdc62b5c284ffba97a779cda8782c8c54f))

# [2.30.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.30.0) (2024-07-22)

**Note:** Version bump only for package sso

## [2.29.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.29.1) (2024-07-17)

### Features

- ✨ sso 增加 api base path 配置,可以修改 api 后缀 ([fb3c4d7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fb3c4d7735249760b7cae98e0524be4ab47c1580))

# [2.29.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.29.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))
- ✨ 浙政钉增加免密授权登录 ([492a112](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/492a1128540bc3f66369a483cb5fb6c3ef27d6c3))

### Performance Improvements

- ⚡️ 浙政钉包修改为动态导入 ([1e9a9cc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1e9a9cc8589ae31b6c5423ecfca212d51de3c9e2))

## [2.28.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.6) (2024-06-24)

**Note:** Version bump only for package sso

## [2.28.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.5) (2024-06-17)

**Note:** Version bump only for package sso

## [2.28.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.4) (2024-06-03)

**Note:** Version bump only for package sso

## [2.28.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.3) (2024-05-21)

**Note:** Version bump only for package sso

## [2.28.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.2) (2024-05-21)

**Note:** Version bump only for package sso

## [2.28.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.1) (2024-05-20)

### Features

- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))

# [2.28.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.28.0) (2024-05-13)

### Features

- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [2.27.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.27.4) (2024-04-30)

### Bug Fixes

- weichat appid error ([cb07468](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb07468f8f50edde48b619f6c27d972c9ee2de77))

## [2.27.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.27.3) (2024-04-29)

### Bug Fixes

- 🐛 config 头部加令牌方法重命名 ([856ca3b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/856ca3b62f7fa3a9fae290e12815c8e1072cf8e9))
- 🐛 i18n 合并成一个 json ([616d290](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/616d2902d6bf3e812f13aa077985f60c8b3da482))

### Features

- ✨ 增加 locale 配置项,可以修改国际化 ([bd8f43a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bd8f43a4930d3ee20975f7c93f31ec3d52680764))
- ✨ 增加第三方跳转带令牌的流程 ([c961b59](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c961b59dd4ad70d73274c27f0226d3a0a305df98))
- ✨ 通用 request config 处理, 增加 fetchClass, 修改 sso, collector-sso ([b159192](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b159192080f27bee590ecbacfa20a6c93e14ac65))

## [2.27.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.27.2) (2024-03-07)

### Bug Fixes

- 🐛 sso 问题修复 ([ecb7857](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ecb7857203688217bdae0a8800632bb6072ec4ee))

## [2.27.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.27.1) (2024-03-06)

### Features

- ✨ sso 配置项可以指定域名匹配自定义登录模板 ([6a50bae](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6a50bae3f2dc83f7c8af29e61606c9f9f5c0ae12))

# [2.27.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.27.0) (2024-03-01)

**Note:** Version bump only for package sso

## [2.26.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.26.4) (2024-01-23)

### Features

- ✨ 禁用的用户在选择界面上标识 ([540d7f4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/540d7f45cd671a947aaac8da5c90731e6dbd590d))

## [2.26.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.26.3) (2024-01-15)

**Note:** Version bump only for package sso

## [2.26.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.26.2) (2024-01-08)

### Bug Fixes

- 🐛 中转逻辑修改 问题修复, 站内消息跳转 ([f4af7c2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4af7c25b441a36ce14c27c721f94179fd4ce6cb))

## [2.26.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.26.1) (2024-01-02)

### Bug Fixes

- 🐛 byauthcode 接口路径调整 ([a31efea](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a31efea7a488ba388d99d716e215606a4c2d6f44))

# [2.26.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.26.0) (2024-01-02)

### Bug Fixes

- 🐛 中转页逻辑修改 ([58aa238](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/58aa238064b2b3d96ee5cae10a154877b184b5f5))

# [2.25.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.25.0) (2023-12-25)

### Features

- ✨ sso 增加微信和企业微信授权分发流程, 增加分发路由中转页 ([6d341f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6d341f8b2136c94d40610edb5f7ce71a1017cc0f))

## [2.24.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.24.1) (2023-12-11)

### Features

- ✨ 自定义登录非当前用户登录,转到正常登录 ([7ed7681](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ed7681dedb1e9da4afe7dad5d00f53b579d8030))

# [2.24.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.24.0) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))
- 🐛 无产品权限无 redirect 给提示 ([4b6d495](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4b6d4952d63e567a5804792aa09f51cb5f5d9474))

# [2.23.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.23.0) (2023-11-20)

### Features

- ✨ pwa ([80985fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80985fbd6ca7f56d6b386959368e3ad44f02905b))

# [2.22.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.22.0) (2023-11-03)

### Bug Fixes

- 🐛 fix util error ([1238b55](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1238b55075d1ebd3da246a2cc5fb8a25ae2af77a))

### Features

- ✨ 自定义登录 ([a3d1f99](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a3d1f99c74c2e73e2e1488cd658f2522c06498d6))

## [2.21.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.21.1) (2023-10-31)

### Performance Improvements

- ⚡️ html load faster ([e7f2d23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7f2d233d799c6081d3cae94f1ca0663ebe20e28))

# [2.21.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.21.0) (2023-10-30)

### Performance Improvements

- ⚡️ build faster ([1c14c93](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c14c9346709282613bed42addcdfcf7d359575f))

## [2.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.20.2) (2023-10-23)

### Features

- add mp verify ([d80d3f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d80d3f9f683e9607ba010d788d460a98ec04fa2a))

## [2.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.20.1) (2023-10-10)

### Bug Fixes

- 🐛 调整 redirect 的优先级 ([f6a1477](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6a1477fd0587789a113604a931ee9c1f431ed50))

# [2.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.20.0) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

# [2.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.19.0) (2023-09-11)

### Features

- ✨ 对 out 的配置增加 url 参数支持 ([2de06f1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2de06f17474358ebb1527b7e3c58c2d4ec3fed8e))

# [2.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.18.0) (2023-07-31)

### Bug Fixes

- 🐛 修复 ssoout 的跳转逻辑 ([52e2b9d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/52e2b9d26b0cf48180338ab13719155e767b2337))

## [2.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.17.1) (2023-07-26)

**Note:** Version bump only for package sso

# [2.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.17.0) (2023-07-24)

### Features

- ✨ 增加 api 参数解析, 增加对外转发 ([159f874](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/159f8743b904f4afc1277dbea7b526be3b54959c))

# [2.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.16.0) (2023-07-03)

### Features

- ✨ sso add config ([a614559](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a6145597b699ee0780f89d0ef4aecae0e859f594))

## [2.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.15.3) (2023-06-13)

### Bug Fixes

- 🐛 fix dialog postmessage target ([4188468](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41884682bb021d103568277bf2638c40476e3aa9))

## [2.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.15.2) (2023-06-12)

### Bug Fixes

- 🐛 sso verify issue handle ([e00aec1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e00aec103ad155a3e028687455fcf0f76f5e34f0))

## [2.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.15.1) (2023-06-12)

### Bug Fixes

- 🐛 fix sso verify-page possmessage target ([32af974](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/32af97467655bf3aa9dc1ff979f5b8aa8dc0f5db))

### Features

- ✨ add collector-sso wechat login ([bce6946](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bce6946ffeb67673a59211197e42e5654e965a14))

# [2.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.15.0) (2023-06-05)

### Bug Fixes

- 🐛 custom deployWindowTitle does not work ([08df192](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08df192eb886d524ef54299c120033c3affb417f))

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

# [2.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.14.0) (2023-05-15)

### Bug Fixes

- 🐛 handle q params to override, not replace ([4124f8a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4124f8af3d4fd81a58b09345c4dcac3e07239d4e))
- 🐛 增加 datlas token sso 直接处理 ([39e8043](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39e8043e096b893c4ece7361cd1f6c6a61c7a327))

## [2.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.13.1) (2023-04-24)

### Bug Fixes

- 🐛 sso redirect error ([a8d0c6a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a8d0c6aa3897f53b7ecced725eedd895a0e26698))

# [2.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.13.0) (2023-04-24)

### Bug Fixes

- 🐛 修改函数名和前置隐私协议的存储 ([e85b8a2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e85b8a236d06766b334563be1de682951d3e0e4b))
- 🐛 修改底部默认文案 ([a603693](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a603693f665bac8b3b3c23d24a21961a560f782d))
- 🐛 增加对 redirect 的解码处理,建议编码传入 ([a86e129](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a86e129f4171e03d298ab08e612142175d5b0778))

### Features

- ✨ sso 追加弹出窗的展示形式 ([c06e559](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c06e55968e8399bdcb59d36e6367c2bb3c3fa152))

# [2.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.12.0) (2023-04-17)

### Bug Fixes

- 🐛 适配优化 ([fb2b5ba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fb2b5ba02adf4135909d54f5d6f665b7ba4032d1))

### Features

- ✨ login 库适配移动端 ([f472e83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f472e832286ad1e6c5d0f3929926843e5b5501a7))
- ✨ sso collector-sso 增加自定义配置 ([65c6925](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65c6925526050e67d0a22e3c34effa26a41374cc))
- ✨ 默认 app 过期时,可由用户选择其他 app ([239a93f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/239a93f73239092d0f78266cf74c6a30ef44b767))

## [2.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.11.1) (2023-03-20)

### Features

- ✨ 私有化补充配置项 ([7ec1737](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ec1737c04daa7288bace39ca28df0c2330e627f))

# [2.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.11.0) (2023-03-13)

### Features

- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))

## [2.10.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.10.4) (2023-02-20)

### Bug Fixes

- 🐛 修复 sso 部分问题 ([762733c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/762733c23bd0fbbbcb131ac1c96a678bb7d2d11c))

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))

## [2.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.10.1) (2023-02-13)

### Bug Fixes

- 🐛 sso 切换 app 退出异常问题 ([e05e8d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e05e8d823439574202e2ff9dfc1e0d0192c8ac51))

# [2.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.10.0) (2023-02-13)

### Bug Fixes

- 🐛 修复 sso 跳转至选择 app 时 在 hash 路由下异常的问题 ([91bdda8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/91bdda8178c4c93d19eb9198762a37e742a63fee))
- 🐛 修复选择 app 丢失 redirect 等参数的问题 ([941d355](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/941d35510309ee2de3ce704eaccc8bfe81fb52c6))

### Features

- ✨ [sso, collector-sso]: 增加滑动认证(配置项) ([f4ce9b4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4ce9b41d652dec15e3c544f5856404767a2c183))
- ✨ [sso]: 增加选择 app 的页面 ([9aba9e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9aba9e72a4a19dd271618415805374dd8f3c5ca4))

## [2.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.9.1) (2023-02-06)

### Bug Fixes

- 🐛 [sso]: 不把浙政钉作为默认登录项,只有在配置项配置才会出现 ([989c973](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/989c973413136ffe712baf27a96be8a858c42f9b))

# [2.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.9.0) (2023-02-06)

### Features

- ✨ [sso]: 浙政钉接入 ([f858f92](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f858f92247ecd6b70846db618266b5541f80a1b5))

## [2.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.8.1) (2023-01-06)

**Note:** Version bump only for package sso

# [2.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.8.0) (2023-01-03)

### Features

- ✨ [Login]: lib 库布局改造 ([3baf0f7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3baf0f7998d207f4d3873f2e571786ef114a6699))

# [2.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.7.0) (2022-12-13)

**Note:** Version bump only for package sso

## [2.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.6.1) (2022-12-12)

**Note:** Version bump only for package sso

# [2.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.6.0) (2022-12-12)

**Note:** Version bump only for package sso

## [2.5.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.5.2) (2022-11-28)

### Bug Fixes

- 🐛 [sso]: 修复隐私协议一直需要勾选的问题 ([0b6f55a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b6f55a6fb3eb3ecefce1e3933d7e2b660b524e8))
- 🐛 fix dev error ([e4ddba8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e4ddba885b23a168670f00c116000f23e784ae69))
- 🐛 micro local start error ([1a5db95](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1a5db9538346afbe5b9dc42687645db2bc889378))

## [2.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.5.1) (2022-11-21)

### Bug Fixes

- 🐛 [sso]: 修复配置项造成的页面异常问题 ([638be34](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/638be34335e974c29e86a3b0b83c577d3c88e287))

# [2.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.5.0) (2022-11-21)

### Bug Fixes

- 🐛 fix ([0b11490](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b114902369505586123747dbfbfc6e68674a8c0))
- 🐛 memory leak ([a971074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a971074e017b8ea025305ce48fdc605ab25fb550))

### Features

- ✨ [sso]: 新增配置项控制 ([f133fb6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f133fb651608f74b22fa7a0c35fd1aadbd6937cd))

## [2.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.4.1) (2022-11-15)

### Bug Fixes

- 🐛 fix dataapp ([24f2084](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/24f2084bb8a40f7997967e680feb0d05ac664cb9))

# [2.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.4.0) (2022-11-14)

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ 数据申请简化 ([74a9fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74a9fc800171e9d78ca1b0cdaa9cf0a7f83b3ac7))

# [2.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.3.0) (2022-09-26)

### Features

- ✨ sso 优化 ([cfa7cf2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cfa7cf2dc6ffd197d0aba91524347a245bf9a8c6))

## [2.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.2.1) (2022-09-19)

**Note:** Version bump only for package sso

# [2.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.2.0) (2022-09-13)

### Features

- ✨ [个人设置]: 手机邮箱和第三方的绑定验证 ([c489c50](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c489c50978adea72fd6bfa871fcaa5e59d6d1eda))

## [2.1.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.1.1) (2022-09-05)

### Features

- ✨ 新增文档 ([9c8a4a8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9c8a4a82251e5d4996b828b6d522d5d079542c46))

# [2.1.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.1.0) (2022-08-29)

### Features

- ✨ sso 改版+忘记密码 ([3ac016f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ac016f0d2ffb614efab0bbbea35c1348271baa9))

## [2.0.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.0.1) (2022-08-22)

### Bug Fixes

- 🐛 修改域名备案 ([8b3ba37](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8b3ba373da5290e4b2d7b92452e7bf490880b01f))

# [2.0.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>@2.0.0) (2022-08-12)

### Bug Fixes

- 🐛 markdown load error ([6e4ca84](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6e4ca8487ffaf82a2f41df331d9fcd36b556e754))
- 🐛 remove debug ([46c9b60](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46c9b604e59c40ba5f362f901459b426c4236368))
- 🐛 revert sso loading ([1d03108](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1d031089328731ffb40ef76076389cf9e5f79511))
- 🐛 sso 项目修改配合和优化交互 ([4dfb9e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4dfb9e70e71c79bfcdcdf35d8be7828f892debaf))
- 🐛 切换 app 时跳 sso 更新 token ([297c72c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/297c72c70aacd83d327824e6cc4a135dca7054e1))
- 🐛 配合发布会，先 hack 主题色 ([91a8022](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/91a802244fa6c5e2875c7932811655556f9417f5))

### Features

- ✨ add devlop product ([ef17303](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef17303bf4b5da5f04a7cf4d17486a3d3112b969))
- ✨ 模板管理 ([9da7e90](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9da7e904cacec51c3c25df6ce6602da53b46eb37))

# 1.1.0 (2022-06-30)

### Bug Fixes

- 🐛 修复分离部署加载失败 ([21ac2e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/21ac2e342500e0274517b599160cf13225868ab9))
- 🐛 修复微应用下，hash 路由模式资源释放错误 ([f5cf78c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f5cf78c0d8abbf00ee558307454fe07c904ed97c))

### Features

- ✨ 迁移 sso + 微前端 ([b51cb10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b51cb107a0f84fb5729f16cc0118b6cf89d7c91d))
