// api处理===========================================================================================
import _ from 'lodash';
import { compressToEncodedURIComponent } from 'lz-string';
import urlParameterAppend from 'url-parameter-append';
import Toast from '@mdtLogin/components/toast';
import { getAllParamsFromUrl } from '@mdtLogin/util/urlUtil';
import { microAfterLoginSuccess, microApiUrl } from '@mdtProSso/utils/microUtil';
import { AppController } from '../app/AppController';
import {
  API_URL,
  CONFIG_APP_ID,
  DATLAS_URL,
  INCLUDE_URL_PARAMS,
  IS_MICRO_ENV,
  NO_CHOOSE_APP,
  PLAIN_TOKEN,
  PLAIN_TOKEN_KEY,
  PRODUCT_AUTH_MAP,
  REDIRECT,
} from '../config';
import { IIdentities, ILoginUser } from '../interface';
import i18n from '../languages';
import { receiveResponse } from './receiveResponse';
import { getCodeFromStore, removeCodeFromStore, savePrivacyToStore, saveTokenToStore } from './storageUtil';
import {
  getForwarderUrl,
  getIdentityIdFromUrl,
  getPassThroughFromUrl,
  getQFromUrl,
  isDialog,
  isForwarder,
} from './util';

// 接入后端的api
let apiUrl = IS_MICRO_ENV ? microApiUrl() : API_URL;
apiUrl = apiUrl || API_URL;

export const fetchRequest = (url: string, config?: any) => {
  return AppController.getInstance().getFetchRequest().fetchRequest(url, config);
};

export const getTokenHeader = (tk: string, itk?: string) => {
  return AppController.getInstance().getFetchRequest().getHeaderRequestToken({}, tk, itk);
};

// api错误处理
const requestError = (response: any, status: number, i18n: any): string => {
  const { rc, msg_args } = response;
  let trc = i18n.translate(`rc.${rc}`);
  typeof trc === 'function' && (trc = trc(msg_args));
  return rc ? trc : i18n.translate(`api.${status}`);
};

const getEnvUrl = (val: string) => {
  return `${DATLAS_URL}/${val}`;
};
const getUrlFromPermissions = async (result: ILoginUser) => {
  const productArr: { id: number; envUrl: string; order: number }[] = [];
  // 用户登录返回新增mdt_product参数
  const mdtProduct = result.mdt_product || [];
  // 保证排序
  const orderMap: { [key: string]: number } = {
    dataapp: 1,
    datamarket: 2,
    datafactory: 3,
  };
  _.mapKeys(PRODUCT_AUTH_MAP, (v, k) => {
    productArr.push({
      id: _.toNumber(k),
      envUrl: getEnvUrl(v),
      order: orderMap[v],
    });
  });
  return _.find(_.orderBy(productArr, 'order'), ({ id }) => _.includes(mdtProduct, id))?.envUrl;
};

// 登录成功
const afterLoginSuccess = async (
  result: ILoginUser,
  needChooseApp = true,
  skipUrlIdentity = false,
  skipDefaultIdentity = false,
) => {
  // Step 1: 验证应用ID
  if (!validateAppId(result)) return;

  // Step 2: 处理URL中的身份导航
  if (await handleUrlIdentity(result, skipUrlIdentity)) return;

  // Step 3: 确定是否需要选择应用
  const mergedNeedChooseApp = determineNeedChooseApp(result, needChooseApp);

  // Step 4: 如需要，处理应用选择流程
  if (mergedNeedChooseApp && (await handleAppSelection(result, skipDefaultIdentity))) return;

  // Step 5: 检查主身份
  if (!mergedNeedChooseApp && !skipDefaultIdentity && (await checkDefaultIdentity(result, skipDefaultIdentity))) return;

  // Step 6: 处理第三方绑定代码
  await processBindingCode(result);

  // Step 7: 保存隐私设置并导航到目标地址
  savePrivacyToStore('true');
  await navigateToDestination(result);
};

// 验证应用ID并处理不匹配情况
const validateAppId = (result: ILoginUser): boolean => {
  const matchAppIdSuccess = _.isEqual(result.customer.app_id, CONFIG_APP_ID);

  if (CONFIG_APP_ID && !matchAppIdSuccess) {
    const expired = result.customer.expired;
    if (!expired) {
      alert(i18n.chain.matchFailed);
      const removeConfigUrl = urlParameterAppend(window.location.href, 'config', '');
      window.location.replace(removeConfigUrl);
      return false;
    }
    Toast.error(i18n.chain.matchExpired);
    return false;
  }
  return true;
};

// 处理URL中指定的身份
const handleUrlIdentity = async (result: ILoginUser, skipUrlIdentity: boolean): Promise<boolean> => {
  const isIdentity = (result.identities || []).length > 0;
  // 需要指定身份跳转
  const { appId, userId } = getIdentityIdFromUrl();

  if (appId && userId && isIdentity && !skipUrlIdentity) {
    const identityId = result.identities!.find(
      (identity) => _.toString(identity.app_id) === appId && _.toString(identity.entity_id) === userId,
    )?.id;

    if (identityId) {
      const response = await fetchRequest(`auth/v2/user/identities`, {
        method: 'POST',
        headers: { authorization: result.auth },
        body: JSON.stringify({ user_id: identityId }),
      });
      receiveResponse(response, (value: ILoginUser) => afterLoginSuccess(value, false, true));
      return true;
    }
  }
  return false;
};

// 确定是否需要选择应用
const determineNeedChooseApp = (result: ILoginUser, needChooseApp: boolean): boolean => {
  const matchAppIdSuccess = _.isEqual(result.customer.app_id, CONFIG_APP_ID);
  const configNoChooseApp = CONFIG_APP_ID && matchAppIdSuccess && needChooseApp;
  return configNoChooseApp ? false : needChooseApp;
};

// 处理应用选择流程
const handleAppSelection = async (result: ILoginUser, skipDefaultIdentity: boolean): Promise<boolean> => {
  const isIdentity = (result.identities || []).length > 0;
  // 需要选择app
  const preferenceSpec = isIdentity ? 'pref.user_choose_identity_auto' : 'pref.user_choose_app_auto';

  const response = await fetchRequest(`datamap/v2/preferences/${preferenceSpec}`, {
    headers: { authorization: result.auth },
  });

  const status = response.status;
  const _response = await response.json();
  const { result: prefValue, rc } = _response;

  if (rc !== 0) {
    Toast.error(requestError(_response, status, i18n));
    return true;
  }

  const needJump = !prefValue && !NO_CHOOSE_APP && result.apps.all.length > 1 && rc === 0;
  if (needJump) {
    AppController.gotoChoose(result, rc);
    return true;
  }

  // 不需要选择身份直接跳转默认身份
  return await checkDefaultIdentity(result, skipDefaultIdentity);
};

// 检查并处理默认身份跳转
const checkDefaultIdentity = async (result: ILoginUser, skipDefaultIdentity: boolean): Promise<boolean> => {
  const defaultIdentity = _.find(result.identities, (identity) => identity.is_main) as IIdentities | undefined;
  const currentUserId = result.customer.uuid;
  const currentIsDefaultIdentity = defaultIdentity?.id === currentUserId;

  if (!currentIsDefaultIdentity && !skipDefaultIdentity && defaultIdentity) {
    const response = await fetchRequest(`auth/v2/user/identities`, {
      method: 'POST',
      headers: { authorization: result.auth },
      body: JSON.stringify({ user_id: defaultIdentity.id }),
    });
    receiveResponse(response, (value: ILoginUser) => afterLoginSuccess(value, false, true, true));
    return true;
  }

  return false;
};

// 处理第三方绑定代码
const processBindingCode = async (result: ILoginUser): Promise<void> => {
  // 将code送到服务器，然后删除本地记录
  const code = getCodeFromStore();
  if (code) {
    const headers = getTokenHeader(result.auth);
    await fetchRequest(`auth/third_party/application/bind`, {
      method: 'POST',
      body: JSON.stringify({ bind_code: code }),
      headers,
    });
    removeCodeFromStore();
  }
};

// 导航到目标地址
const navigateToDestination = async (result: ILoginUser): Promise<void> => {
  // 微服务不需要跳转, 父子组件通信即可
  if (IS_MICRO_ENV) {
    microAfterLoginSuccess(result);
    return;
  }

  // 弹窗形式直接通知
  if (isDialog()) {
    const notificationWindow = window.parent!;
    notificationWindow.postMessage(
      {
        action: 'dialog-login',
        data: result,
      },
      '*',
    );
    return;
  }

  // 常规的逻辑处理
  saveTokenToStore(result.auth);

  // 构建重定向URL
  const redirectUrl = REDIRECT || (await getUrlFromPermissions(result));
  const isForward = isForwarder();
  const forwarderUrl = isForward ? getForwarderUrl() : '';
  const mergedUrl = forwarderUrl || redirectUrl;

  if (!mergedUrl) {
    alert(i18n.chain.noUrl);
    return;
  }

  // 处理URL参数
  let params = {};
  if (INCLUDE_URL_PARAMS) {
    params = getAllParamsFromUrl() || {};
  }

  const oldQ = getQFromUrl(mergedUrl);
  oldQ.itk && delete oldQ.itk;
  const newQ: any = { ...oldQ, tk: result.auth };
  isForward && (newQ.passThrough = getPassThroughFromUrl());
  const encodedQ = compressToEncodedURIComponent(JSON.stringify(newQ));
  const mergedParams: any = { q: encodedQ, ...params };
  if (PLAIN_TOKEN) {
    mergedParams[PLAIN_TOKEN_KEY] = result.auth;
  }
  const urlWithParams = urlParameterAppend(mergedUrl, mergedParams);
  window.location.replace(urlWithParams);
};

const autoLoginByQ = (q: Record<string, any>) => {
  const { tk } = q;
  // 微服务不需要跳转, 父子组件通信即可
  if (IS_MICRO_ENV) {
    microAfterLoginSuccess({ auth: tk });
    return;
  }

  // 常规的逻辑处理
  saveTokenToStore(tk);
  let encodedQ = JSON.stringify(q);
  encodedQ = compressToEncodedURIComponent(encodedQ);
  const urlWithParams = urlParameterAppend(REDIRECT, 'q', encodedQ);
  REDIRECT && window.location.replace(urlWithParams);
};

export { apiUrl, requestError, afterLoginSuccess, autoLoginByQ };
