import Dialog from '@mdtLogin/components/dialog';
import Toast from '@mdtLogin/components/toast';
import DialogPasswordError from '@mdtProSso/components/dialog-password-error';
import DoubleCheckContent from '@mdtProSso/components/double-check-content';
import { AppController } from '../app/AppController';
import { NO_CHOOSE_APP } from '../config';
import { LoginPathnameEnum } from '../constants/enum';
import i18n from '../languages';
import { afterLoginSuccess, fetchRequest, requestError } from './fetchUtil';
import { saveCodeToStore } from './storageUtil';

// 需要双因子登录
const needDoubleCheck = (taskId: string) => {
  // 双因子登录检查
  const loginDoubleCheck = (captcha: string, taskId: string, close: any) => {
    return fetchRequest(`${LoginPathnameEnum.DOUBLE_CHECK}`, {
      method: 'POST',
      body: JSON.stringify({ code: captcha, task_id: taskId }),
    })
      .then(async (response) => {
        return receiveResponse(response, async (result: any) => {
          await afterLoginSuccess(result);
          close();
        });
      })
      .catch(() => {
        Toast.error(i18n.translate(`api.503`));
      });
  };

  // 双因子登录
  Dialog.open({
    title: i18n.chain.doubleCheckTitle,
    closable: true,
    content: (close: any) => (
      <DoubleCheckContent
        onClick={(captcha: string) => loginDoubleCheck(captcha, taskId, close)}
        doubleCheckDesc={i18n.chain.doubleCheckDesc}
        doubleCheckButtonText={i18n.chain.doubleCheckOk}
        doubleCheckPlaceholder={i18n.chain.captchaPlaceholder}
      />
    ),
    width: '598px',
  });
};

const bindCode = (code: string, failCallback?: Function) => {
  code && saveCodeToStore(code);
  failCallback?.();
};

// api请求结果处理
const receiveResponse = async (response: any, callBack: Function, failCallback?: Function, needToast = true) => {
  const status = response.status;
  const _response = await response.json();
  const { rc, result, msg_args: args } = _response;
  switch (rc) {
    // 正常登录
    case 0:
      return callBack(result);
    case 1012:
      AppController.gotoChooseByInfo(args.token, args);
      break;
    // 用户被禁用
    case 1040:
      if (NO_CHOOSE_APP) {
        alert(requestError(_response, status, i18n));
        failCallback?.(_response);
        return;
      }
      AppController.gotoChooseByInfo(args.token, args, rc);
      break;
    // 密码输入次数过多提示
    case 1078:
      Dialog.open({
        content: (close: any) => (
          <DialogPasswordError
            title={i18n.chain.accountErrorTip}
            desc={i18n.chain.accountErrorTipDesc}
            buttonOkLabel={i18n.chain.privacyOk}
            onClick={() => {
              close();
            }}
          />
        ),
        width: '450px',
      });
      break;
    // 双因子登录
    case 2004:
      needDoubleCheck(result.task_id);
      break;
    // 平台绑定登录
    case 1130:
      bindCode(result?.bind_code || '', failCallback);
      break;
    // 非正常登录
    default:
      needToast && Toast.error(requestError(_response, status, i18n));
      failCallback?.(_response);
      break;
  }
};

export { receiveResponse };
