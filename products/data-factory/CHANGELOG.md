# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.38.33](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.33) (2025-07-24)

### Features

- ✨ table 支持列配置 ([dae481d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dae481d534f5a5355fdf230394f2033e5ed1842c))

## [2.38.32](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.32) (2025-07-17)

**Note:** Version bump only for package data-factory

## [2.38.31](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.31) (2025-07-15)

**Note:** Version bump only for package data-factory

## [2.38.30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.30) (2025-07-15)

**Note:** Version bump only for package data-factory

## [2.38.29](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.29) (2025-07-08)

**Note:** Version bump only for package data-factory

## [2.38.28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.28) (2025-07-03)

**Note:** Version bump only for package data-factory

## [2.38.27](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.27) (2025-06-19)

**Note:** Version bump only for package data-factory

## [2.38.26](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.26) (2025-06-19)

**Note:** Version bump only for package data-factory

## [2.38.25](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.25) (2025-06-10)

**Note:** Version bump only for package data-factory

## [2.38.24](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.24) (2025-05-29)

**Note:** Version bump only for package data-factory

## [2.38.23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.23) (2025-05-19)

**Note:** Version bump only for package data-factory

## [2.38.22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.22) (2025-05-14)

**Note:** Version bump only for package data-factory

## [2.38.21](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.21) (2025-05-14)

**Note:** Version bump only for package data-factory

## [2.38.20](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.20) (2025-04-27)

**Note:** Version bump only for package data-factory

## [2.38.19](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.19) (2025-04-27)

**Note:** Version bump only for package data-factory

## [2.38.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.18) (2025-04-23)

**Note:** Version bump only for package data-factory

## [2.38.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.17) (2025-04-22)

**Note:** Version bump only for package data-factory

## [2.38.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.16) (2025-04-21)

**Note:** Version bump only for package data-factory

## [2.38.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.15) (2025-04-02)

**Note:** Version bump only for package data-factory

## [2.38.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.14) (2025-04-01)

**Note:** Version bump only for package data-factory

## [2.38.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.13) (2025-04-01)

**Note:** Version bump only for package data-factory

## [2.38.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.12) (2025-03-31)

**Note:** Version bump only for package data-factory

## [2.38.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.11) (2025-03-31)

**Note:** Version bump only for package data-factory

## [2.38.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.10) (2025-03-27)

**Note:** Version bump only for package data-factory

## [2.38.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.9) (2025-03-27)

**Note:** Version bump only for package data-factory

## [2.38.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.8) (2025-03-27)

**Note:** Version bump only for package data-factory

## [2.38.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.7) (2025-03-19)

**Note:** Version bump only for package data-factory

## [2.38.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.6) (2025-03-18)

**Note:** Version bump only for package data-factory

## [2.38.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.5) (2025-03-13)

**Note:** Version bump only for package data-factory

## [2.38.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.4) (2025-03-10)

**Note:** Version bump only for package data-factory

## [2.38.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.3) (2025-02-27)

**Note:** Version bump only for package data-factory

## [2.38.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.2) (2025-02-26)

**Note:** Version bump only for package data-factory

## [2.38.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.1) (2025-02-24)

**Note:** Version bump only for package data-factory

# [2.38.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.38.0) (2025-02-24)

**Note:** Version bump only for package data-factory

## [2.37.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.6) (2025-02-17)

**Note:** Version bump only for package data-factory

## [2.37.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.5) (2025-01-23)

**Note:** Version bump only for package data-factory

## [2.37.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.4) (2025-01-16)

**Note:** Version bump only for package data-factory

## [2.37.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.3) (2025-01-09)

**Note:** Version bump only for package data-factory

## [2.37.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.2) (2025-01-07)

**Note:** Version bump only for package data-factory

## [2.37.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.1) (2025-01-06)

**Note:** Version bump only for package data-factory

# [2.37.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.37.0) (2024-12-23)

**Note:** Version bump only for package data-factory

## [2.36.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.10) (2024-12-16)

**Note:** Version bump only for package data-factory

## [2.36.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.9) (2024-12-16)

**Note:** Version bump only for package data-factory

## [2.36.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.8) (2024-12-16)

**Note:** Version bump only for package data-factory

## [2.36.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.7) (2024-12-03)

**Note:** Version bump only for package data-factory

## [2.36.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.6) (2024-12-02)

**Note:** Version bump only for package data-factory

## [2.36.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.5) (2024-12-02)

**Note:** Version bump only for package data-factory

## [2.36.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.4) (2024-12-02)

### Bug Fixes

- 🐛 修复滚动条演示 ([34ba12f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/34ba12f4c7b7e78829f69559469fb0b44ada2629))

## [2.36.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.3) (2024-12-02)

### Bug Fixes

- 🐛 修复滚动条演示 ([36cde39](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/36cde39b832a30d81b7eb43726f07e068d787554))

## [2.36.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.2) (2024-12-02)

**Note:** Version bump only for package data-factory

## [2.36.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.1) (2024-11-26)

**Note:** Version bump only for package data-factory

# [2.36.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.36.0) (2024-11-26)

### Features

- ✨ 时间类型存储切换 ok ([5a2e4b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a2e4b04b10212f948f488c61e8138e6cc8e71e0))

## [2.35.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.5) (2024-11-19)

**Note:** Version bump only for package data-factory

## [2.35.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.4) (2024-11-15)

**Note:** Version bump only for package data-factory

## [2.35.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.3) (2024-11-14)

**Note:** Version bump only for package data-factory

## [2.35.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.2) (2024-11-14)

**Note:** Version bump only for package data-factory

## [2.35.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.1) (2024-11-07)

**Note:** Version bump only for package data-factory

# [2.35.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.35.0) (2024-11-05)

**Note:** Version bump only for package data-factory

## [2.34.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.12) (2024-11-04)

**Note:** Version bump only for package data-factory

## [2.34.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.11) (2024-10-31)

**Note:** Version bump only for package data-factory

## [2.34.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.10) (2024-10-29)

**Note:** Version bump only for package data-factory

## [2.34.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.9) (2024-10-28)

**Note:** Version bump only for package data-factory

## [2.34.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.8) (2024-10-27)

**Note:** Version bump only for package data-factory

## [2.34.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.7) (2024-10-25)

**Note:** Version bump only for package data-factory

## [2.34.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.6) (2024-10-25)

**Note:** Version bump only for package data-factory

## [2.34.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.5) (2024-10-25)

**Note:** Version bump only for package data-factory

## [2.34.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.4) (2024-10-25)

**Note:** Version bump only for package data-factory

## [2.34.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.3) (2024-10-25)

**Note:** Version bump only for package data-factory

## [2.34.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.2) (2024-10-23)

**Note:** Version bump only for package data-factory

## [2.34.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.1) (2024-10-23)

**Note:** Version bump only for package data-factory

# [2.34.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.34.0) (2024-10-23)

**Note:** Version bump only for package data-factory

## [2.33.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.10) (2024-10-23)

**Note:** Version bump only for package data-factory

## [2.33.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.9) (2024-10-22)

**Note:** Version bump only for package data-factory

## [2.33.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.8) (2024-10-21)

**Note:** Version bump only for package data-factory

## [2.33.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.7) (2024-10-15)

### Features

- 数据工厂 Qe 支持手写和低码编辑器 2 种模式 ([beb878b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/beb878b414b462933999c588ef642ef7736e9ef3))

## [2.33.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.6) (2024-09-12)

**Note:** Version bump only for package data-factory

## [2.33.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.5) (2024-08-29)

**Note:** Version bump only for package data-factory

## [2.33.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.4) (2024-08-27)

**Note:** Version bump only for package data-factory

## [2.33.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.3) (2024-08-19)

**Note:** Version bump only for package data-factory

## [2.33.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.2) (2024-08-13)

**Note:** Version bump only for package data-factory

## [2.33.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.1) (2024-08-12)

**Note:** Version bump only for package data-factory

# [2.33.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.33.0) (2024-08-09)

**Note:** Version bump only for package data-factory

## [2.32.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.32.2) (2024-08-07)

**Note:** Version bump only for package data-factory

## [2.32.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.32.1) (2024-08-05)

**Note:** Version bump only for package data-factory

# [2.32.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.32.0) (2024-08-02)

**Note:** Version bump only for package data-factory

## [2.31.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.5) (2024-08-02)

**Note:** Version bump only for package data-factory

## [2.31.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.4) (2024-07-29)

**Note:** Version bump only for package data-factory

## [2.31.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.3) (2024-07-25)

**Note:** Version bump only for package data-factory

## [2.31.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.2) (2024-07-25)

**Note:** Version bump only for package data-factory

## [2.31.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.1) (2024-07-24)

**Note:** Version bump only for package data-factory

# [2.31.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.31.0) (2024-07-22)

### Features

- add lowcode qlang ([902e922](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/902e9226bbe91b164a753d21823eace5e63ff84f))

## [2.30.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.30.4) (2024-07-17)

**Note:** Version bump only for package data-factory

## [2.30.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.30.3) (2024-07-10)

**Note:** Version bump only for package data-factory

## [2.30.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.30.2) (2024-07-03)

**Note:** Version bump only for package data-factory

## [2.30.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.30.1) (2024-07-02)

**Note:** Version bump only for package data-factory

# [2.30.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.30.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))

## [2.29.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.6) (2024-06-24)

**Note:** Version bump only for package data-factory

## [2.29.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.5) (2024-06-17)

**Note:** Version bump only for package data-factory

## [2.29.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.4) (2024-06-03)

**Note:** Version bump only for package data-factory

## [2.29.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.3) (2024-05-20)

### Features

- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))

## [2.29.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.2) (2024-05-13)

**Note:** Version bump only for package data-factory

## [2.29.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.1) (2024-04-29)

**Note:** Version bump only for package data-factory

# [2.29.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.29.0) (2024-04-24)

**Note:** Version bump only for package data-factory

## [2.28.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.13) (2024-04-24)

**Note:** Version bump only for package data-factory

## [2.28.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.12) (2024-04-24)

**Note:** Version bump only for package data-factory

## [2.28.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.11) (2024-04-23)

**Note:** Version bump only for package data-factory

## [2.28.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.10) (2024-04-23)

**Note:** Version bump only for package data-factory

## [2.28.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.9) (2024-04-23)

**Note:** Version bump only for package data-factory

## [2.28.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.8) (2024-04-16)

**Note:** Version bump only for package data-factory

## [2.28.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.7) (2024-04-15)

**Note:** Version bump only for package data-factory

## [2.28.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.6) (2024-04-08)

**Note:** Version bump only for package data-factory

## [2.28.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.5) (2024-04-01)

**Note:** Version bump only for package data-factory

## [2.28.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.4) (2024-04-01)

**Note:** Version bump only for package data-factory

## [2.28.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.3) (2024-03-26)

**Note:** Version bump only for package data-factory

## [2.28.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.2) (2024-03-25)

**Note:** Version bump only for package data-factory

## [2.28.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.1) (2024-03-11)

**Note:** Version bump only for package data-factory

# [2.28.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.28.0) (2024-03-11)

### Features

- ✨ flow 对于文件夹的改造 ([8def827](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8def8278ebc2214826071c8d7f9df22cae1eeabb))
- ✨ 智能搜索&数据源支持权限及用户列表 ([f717ead](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f717ead66bfb87cacc9be624dcb6ce4dc0e00f34))

## [2.27.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.27.2) (2024-03-07)

**Note:** Version bump only for package data-factory

## [2.27.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.27.1) (2024-03-01)

**Note:** Version bump only for package data-factory

# [2.27.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.27.0) (2024-03-01)

**Note:** Version bump only for package data-factory

## [2.26.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.26.4) (2024-02-28)

**Note:** Version bump only for package data-factory

## [2.26.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.26.3) (2024-02-23)

**Note:** Version bump only for package data-factory

## [2.26.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.26.2) (2024-02-23)

**Note:** Version bump only for package data-factory

## [2.26.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.26.1) (2024-02-05)

### Features

- ✨ strip node 添加 ([0217b3c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0217b3ccae9a43f3f3428a205bc4d38005dd7607))

# [2.26.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.26.0) (2024-01-29)

**Note:** Version bump only for package data-factory

## [2.25.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.5) (2024-01-23)

### Bug Fixes

- 🐛 最近执行时间通过 flows_run 来获取 ([007b819](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/007b819ef9a3833ba349f31c329c64f39126c55b))

### Features

- ✨ 企业微信数据提取节点 ([e6882a4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e6882a47955c2850111407358ca445b5c46cf5bc))

## [2.25.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.4) (2024-01-15)

### Features

- ✨ bpmn 全局变量相关优化 ([e7b033c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7b033c3641e97300198aa6b84d87bf7bbf6c2f6))

## [2.25.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.3) (2024-01-08)

### Features

- ✨ debug 行数设置, 节点问题修复 ([2fe87d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2fe87d825b517426cac7e3d1291394296dc055b2))

## [2.25.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.2) (2024-01-02)

**Note:** Version bump only for package data-factory

## [2.25.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.1) (2024-01-02)

**Note:** Version bump only for package data-factory

# [2.25.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.25.0) (2024-01-02)

### Features

- ✨ 更新数据包增加 table_action 字段 ([81f78ab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/81f78ab16abde6c886257087a30c2101613d0746))

## [2.24.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.24.3) (2023-12-25)

**Note:** Version bump only for package data-factory

## [2.24.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.24.2) (2023-12-25)

**Note:** Version bump only for package data-factory

## [2.24.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.24.1) (2023-12-25)

**Note:** Version bump only for package data-factory

# [2.24.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.24.0) (2023-12-25)

**Note:** Version bump only for package data-factory

## [2.23.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.23.3) (2023-12-19)

**Note:** Version bump only for package data-factory

## [2.23.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.23.2) (2023-12-18)

### Bug Fixes

- 🐛 memory leak ([d583292](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d583292b8d7e8b0d4fdd6b1d6df86082af1370db))

## [2.23.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.23.1) (2023-12-11)

**Note:** Version bump only for package data-factory

# [2.23.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.23.0) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))
- 🐛 结束时间异常修复 ([a1f4b88](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a1f4b884cb46d6b93f5d3efab15e2a19561d7406))

### Features

- ✨ 钉钉电子表格和审批节点 ([bb8d625](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb8d625218ffdbecbb5c4807eaadd5e8dc4d29fe))

## [2.22.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.22.4) (2023-12-04)

**Note:** Version bump only for package data-factory

## [2.22.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.22.3) (2023-12-04)

### Bug Fixes

- df upsert junction ([3a76d46](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3a76d46aa9f18480428fac1aca4adba76bd249b8))

## [2.22.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.22.2) (2023-12-01)

**Note:** Version bump only for package data-factory

## [2.22.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.22.1) (2023-12-01)

**Note:** Version bump only for package data-factory

# [2.22.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.22.0) (2023-11-20)

**Note:** Version bump only for package data-factory

## [2.21.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.7) (2023-11-13)

**Note:** Version bump only for package data-factory

## [2.21.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.6) (2023-11-13)

**Note:** Version bump only for package data-factory

## [2.21.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.5) (2023-11-06)

**Note:** Version bump only for package data-factory

## [2.21.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.4) (2023-11-03)

**Note:** Version bump only for package data-factory

## [2.21.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.3) (2023-11-01)

**Note:** Version bump only for package data-factory

## [2.21.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.2) (2023-10-31)

**Note:** Version bump only for package data-factory

## [2.21.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.1) (2023-10-31)

**Note:** Version bump only for package data-factory

# [2.21.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.21.0) (2023-10-30)

**Note:** Version bump only for package data-factory

## [2.20.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.7) (2023-10-26)

**Note:** Version bump only for package data-factory

## [2.20.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.6) (2023-10-25)

**Note:** Version bump only for package data-factory

## [2.20.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.5) (2023-10-23)

**Note:** Version bump only for package data-factory

## [2.20.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.4) (2023-10-23)

**Note:** Version bump only for package data-factory

## [2.20.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.3) (2023-10-16)

**Note:** Version bump only for package data-factory

## [2.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.2) (2023-10-16)

**Note:** Version bump only for package data-factory

## [2.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.1) (2023-10-10)

**Note:** Version bump only for package data-factory

# [2.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.20.0) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [2.19.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.19.3) (2023-09-04)

### Bug Fixes

- 🐛 日期时间保存时没有时间 ([e35f8ad](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e35f8adff22d0cf76520e33ffdaa16f2c8263659))

## [2.19.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.19.2) (2023-08-22)

**Note:** Version bump only for package data-factory

## [2.19.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.19.1) (2023-08-14)

**Note:** Version bump only for package data-factory

# [2.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.19.0) (2023-07-31)

**Note:** Version bump only for package data-factory

## [2.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.18.1) (2023-07-26)

**Note:** Version bump only for package data-factory

# [2.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.18.0) (2023-07-24)

**Note:** Version bump only for package data-factory

## [2.17.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.7) (2023-07-03)

**Note:** Version bump only for package data-factory

## [2.17.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.6) (2023-07-03)

**Note:** Version bump only for package data-factory

## [2.17.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.5) (2023-06-20)

### Features

- ✨ modify pipe node ([5432602](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5432602cfc93fad6211157ed6342e08a55a01625))

## [2.17.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.4) (2023-06-13)

**Note:** Version bump only for package data-factory

## [2.17.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.3) (2023-06-12)

**Note:** Version bump only for package data-factory

## [2.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.2) (2023-06-12)

### Features

- ✨ add feishu node ([1780ab6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1780ab66e42cb81d49df6fe60b011920dd04e6be))

## [2.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.1) (2023-06-06)

**Note:** Version bump only for package data-factory

# [2.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.17.0) (2023-06-05)

### Bug Fixes

- 🐛 fixed i18n ([71d5bdd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/71d5bdd42b0a6a78cc4a4d8e49ebcaf47f5eb21d))
- 🐛 i18n fixed ([4bcf571](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4bcf5713b1808157ae75c18213f165e065246c06))

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

## [2.16.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.8) (2023-05-22)

**Note:** Version bump only for package data-factory

## [2.16.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.7) (2023-05-15)

**Note:** Version bump only for package data-factory

## [2.16.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.6) (2023-04-27)

**Note:** Version bump only for package data-factory

## [2.16.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.5) (2023-04-24)

**Note:** Version bump only for package data-factory

## [2.16.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.4) (2023-04-17)

**Note:** Version bump only for package data-factory

## [2.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.3) (2023-04-11)

**Note:** Version bump only for package data-factory

## [2.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.2) (2023-04-10)

**Note:** Version bump only for package data-factory

## [2.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.1) (2023-04-07)

**Note:** Version bump only for package data-factory

# [2.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.16.0) (2023-04-06)

**Note:** Version bump only for package data-factory

## [2.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.15.3) (2023-03-24)

**Note:** Version bump only for package data-factory

## [2.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.15.2) (2023-03-20)

### Features

- ✨ add flow search ([0234e71](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0234e71ee86ee57b900e534e1e3a26f32bca85d7))

## [2.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.15.1) (2023-03-14)

**Note:** Version bump only for package data-factory

# [2.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.15.0) (2023-03-13)

### Features

- ✨ add excludes param to from_datapkg node ([406f455](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/406f455e9bcd1fa753c010a2034f83235b6f57d9))
- ✨ add force_update param ([6ab1342](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ab13428654fd72609420bf5ad4ca9a94e6cd3d4))
- ✨ add more key_columns ([de4d05f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/de4d05f358d8c7107ec10e938d4331613727941a))
- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))

## [2.14.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.12) (2023-02-20)

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))

## [2.14.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.9) (2023-02-13)

**Note:** Version bump only for package data-factory

## [2.14.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.8) (2023-02-13)

### Bug Fixes

- 🐛 formily datepicker 有初始值时报错 ([779359f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/779359f04d3e3c579c412c5041cbc6cbd7aefeda))

## [2.14.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.7) (2023-02-08)

**Note:** Version bump only for package data-factory

## [2.14.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.6) (2023-02-06)

**Note:** Version bump only for package data-factory

## [2.14.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.5) (2023-01-30)

### Features

- ✨ [顶栏]: 增加 collector 跳转 ([0202323](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0202323e075ae62dc01b5c4f48b632335b5cbed5))
- ✨ [顶栏]: 增加跳转方式的配置, 产品根据配置项决定跳转方式 ([2c3975f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c3975fba0e5ce8bff6e8f325e6731a8f3258661))

## [2.14.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.4) (2023-01-06)

**Note:** Version bump only for package data-factory

## [2.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.3) (2023-01-03)

### Features

- ✨ 新增帮助中心 ([bca156e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bca156ed842a05bd7613185a893524ed64b0c3ed))
- ✨ 资源分享支持项目、flow ([23268aa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23268aaf4467655f911d25749304059012960dff))

## [2.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.2) (2022-12-20)

**Note:** Version bump only for package data-factory

## [2.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.1) (2022-12-20)

**Note:** Version bump only for package data-factory

# [2.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.14.0) (2022-12-13)

**Note:** Version bump only for package data-factory

## [2.13.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.13.4) (2022-12-12)

**Note:** Version bump only for package data-factory

## [2.13.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.13.3) (2022-12-12)

### Bug Fixes

- 🐛 [过期时间提示]: 存储到偏好,改为用户过期时间提醒 ([4f5daec](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4f5daec9f3f6a4979455b59dccfa146a87243f54))

## [2.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.13.2) (2022-12-06)

**Note:** Version bump only for package data-factory

## [2.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.13.1) (2022-12-05)

**Note:** Version bump only for package data-factory

# [2.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.13.0) (2022-12-02)

### Features

- ✨ 取消运行限制 ([77e34a7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77e34a77a6ad12c5ebdc43822909e744ccf21cd5))
- ✨ 读取数据包新增过滤参数 ([420a5c3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/420a5c3827c2a794fb97a51ecfe6861fe69771b9))

# [2.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.12.0) (2022-11-30)

### Features

- ✨ 双击节点改名 ([0957862](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/09578621492867c672598207c9109f0117d8a684))
- ✨ 通过上传创建 flow ([d325812](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d325812cf2e24dc5984a14f463ae4d7aeaf7e2a0))

## [2.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.11.1) (2022-11-29)

### Bug Fixes

- 🐛 fix geometry value dispaly error ([46da89c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46da89cb40666b2b5ea755e7a656c48233e7b5d2))
- 🐛 打开 mapEditor 支持多窗口 ([1c99a57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c99a5724eb2d875100523a9ca4b029d65ad27a1))

# [2.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.11.0) (2022-11-28)

### Bug Fixes

- 🐛 fix dev error ([e4ddba8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e4ddba885b23a168670f00c116000f23e784ae69))
- 🐛 fix flow node run error miss error msg ([88f90f7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/88f90f7d71d231fa79cb9940a9eb2db33ab69c36))
- 🐛 micro local start error ([1a5db95](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1a5db9538346afbe5b9dc42687645db2bc889378))

### Features

- ✨ add convert_geometry 节点 ([3b23481](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3b234818a869a87b9005a58247c5c5300378e92e))
- ✨ 支持远程文件节点 ([e47da76](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e47da7671926303a666455ac60710938f255046c))
- ✨ 设置试运行采样参数 ([a7fb9c4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a7fb9c4b18677de348f6b3bf2cb0eb6c12ae2ab3))

# [2.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.10.0) (2022-11-21)

### Bug Fixes

- 读取外链 error ([e00f015](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e00f0154b4376396ffe89eb6cff06c58331c9e68))
- 🐛 [数据工厂]: 修改编辑器主题和节点编辑主题 ([6be9afa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6be9afab45660720cf294d533da47c7bd7b83c3a))
- 🐛 fix ([0b11490](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b114902369505586123747dbfbfc6e68674a8c0))
- 🐛 memory leak ([a971074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a971074e017b8ea025305ce48fdc605ab25fb550))
- 🐛 修改读取数据包本机构筛选规则 ([44d418e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/44d418e867fff0b13c048d1085426ea4b481b022))
- 🐛[我的数据] 预览地理数据时携带 token，创建 sql 数据包 ownership 设默认值;[数据市场]跨机构加偏好 ([3cc0415](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cc0415f87ce227476c32c44b5c54c483727bcdf))

### Features

- ✨ flow 运行可视化 ([9d15355](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d15355e2355021d892aea58f6051167a15c32c4))
- ✨ [通用顶栏]: 加进入后台功能 ([79255f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/79255f97117626a91679c26ae4290e0c70514486))

## [2.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.9.3) (2022-11-16)

### Bug Fixes

- flow node create datapkg error ([99211d3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/99211d37d89e28146722611184ed230990b20b36))

## [2.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.9.2) (2022-11-15)

**Note:** Version bump only for package data-factory

## [2.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.9.1) (2022-11-14)

### Bug Fixes

- 🐛 [数据工厂]配置地理数据预览路径 ([14ec237](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/14ec237655c4636b3aa23e5321268b67debcfbe7))

# [2.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.9.0) (2022-11-14)

### Bug Fixes

- 🐛 sql 回显问题 ([921a683](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/921a683948f32719099b273d96e41100097d88e2))

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ 低码 ETL 总体汇总 ([16ec9b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16ec9b6e07179d2efc88db77b6893d19835f82b2))
- ✨ 数据申请简化 ([74a9fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74a9fc800171e9d78ca1b0cdaa9cf0a7f83b3ac7))

## [2.8.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.8.2) (2022-11-01)

**Note:** Version bump only for package data-factory

## [2.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.8.1) (2022-10-31)

### Features

- ✨ 数据市场、我的数据 优化 ([40ac4c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40ac4c67afebdfc586da4221e2cb934c6bea51db))

# [2.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.8.0) (2022-10-25)

### Features

- ✨ 数据看板 ([9a85fb8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a85fb81fd7086a856c298f659795deef0f69721))
- ✨ 新节点-同比/环比计算 ([0aaf000](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0aaf000a94f6bad75815d60f096a3bc839214387))
- ✨ 添加运行节点数据缓存 ([ad90cb2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ad90cb21caa396ebe4ff5c29b6dbec3745d857c4))

## [2.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.7.1) (2022-10-21)

**Note:** Version bump only for package data-factory

# [2.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.7.0) (2022-10-18)

### Bug Fixes

- 🐛 修复时间选择器 24 小时制 ([4fc9572](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4fc95720539fdcf8ad898547b765d14db642de6f))
- 🐛 解决冲突 ([e6cb24c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e6cb24c40252ccd6e745d911c9c65f692a2f7569))

### Features

- ✨ 读取已有 Flow ([93a8a0c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93a8a0cbef53fee152022918ec33d000e6212c92))
- ✨ 读取数据表节点 select 添加 showTip ([5d750e5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5d750e5f7cf0dcecf736544533c09e1baff3d53e))

## [2.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.6.1) (2022-10-13)

### Bug Fixes

- 🐛 修复鼠标拖动画布,节点看不见的问题 ([1097cf1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1097cf1e50819b0de54b3f6437a38cced655afc2))

# [2.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.6.0) (2022-10-10)

### Bug Fixes

- 🐛 过滤数据 第二次进入“操作符对应的参数” 不能添加 ([003b0c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/003b0c883c3d2cea7d21806f24818461457933f6))

### Features

- ✨ 添加画布缩放工具栏 ([932dce0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/932dce0bae0f119be9569eefa1784a1f684c530c))

# [2.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.5.0) (2022-09-30)

### Bug Fixes

- 🐛 flow 点击定时任务白屏问题 ([ee9683b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee9683b506e4d2ac9f9a667035138a49c7b22b35))
- 🐛 修复管理数据左连接和右连接 ([c435f48](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c435f482faabab6f18d9a57970a936492f6a16b8))

# [2.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.4.0) (2022-09-26)

### Bug Fixes

- 🐛 定时任务实时更新下次运行时间&起止日期字段禁用小于当前时间的选择 ([dade027](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dade027eff753d87b7d45608671bbee60f3fa17d))

### Features

- ✨ cancas 自定义缩放,按 command 滚轮滑动缩放画布 ([23e2767](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23e27679f367eff2b22f44e857f5957ceb475630))
- ✨ 创建数据表和更新数据表内容折叠 ([4f2f33e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4f2f33efc09a1ed7834084699587d44b077c3ac7))
- ✨ 定时任务增加下次运行时间 ([628e919](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/628e919be01438930f8626c4b57a8509e2d9a69a))
- ✨ 数据工厂--质量监控和 flow 支持通过点击名字进行查看详情 ([a3fa854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a3fa85495537f96157af31f00daa92bd693fe0aa))
- ✨ 新增 etl 节点文字说明 ([fe42435](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fe42435b55aa1eae117a5430c6f7793fd0e032cd))
- ✨ 新增节点过滤数据&映射空值节点参数填写优化 ([e345228](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e34522814bcd70693266ce9d2353c4492c049bc2))
- ✨ 更新数据表-更新数据表改为 select ([335ef31](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/335ef3154bac0d13ab48c704848460e6c2a7f427))
- ✨ 表单设计器 ([9a83902](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a83902ba94bd5ff3da8ddcc238b9a4c026ae141))

## [2.3.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.3.3) (2022-09-19)

**Note:** Version bump only for package data-factory

## [2.3.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.3.2) (2022-09-19)

### Features

- ✨ 读取数据包支持个人数据源 ([1696c32](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1696c3296c1360d7ae2a7d4c0daea94f6f8831c9))

## [2.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.3.1) (2022-09-15)

**Note:** Version bump only for package data-factory

# [2.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.3.0) (2022-09-14)

**Note:** Version bump only for package data-factory

## [2.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.2.1) (2022-09-13)

**Note:** Version bump only for package data-factory

# [2.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.2.0) (2022-09-13)

### Features

- ✨ python_pip 节点增加代码编辑器 ([0aa3d57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0aa3d5754f4ffaca010f61fdcb023f1c9dee7e9d))
- ✨ python 节点增加代码编辑器 ([b80b074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b80b074ca1c8d55d76b8fa43505eba8753a2a6f7))
- ✨ sql 节点增加 SQL 编辑器 ([ab24b04](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ab24b04eb260eda6f3d291f90e1334987f3f6220))
- ✨ 数据包创建、详情+血缘图优化+个人数据发布审批 ([35a345c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35a345cc293c4f4c2adb0186ae7fac41c0ede6ad))
- ✨ 数据工厂节点支持用户自定义节点名称 ([47375e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47375e9689ec168ce347a517a4ef7e5156f2c7da))
- ✨ 新增 null_if 节点 ([b5c66b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b5c66b23795ac507bc718221a620b52b6c2a3521))
- ✨ 节点 bug 修复 ([99ed381](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/99ed3814c8a3c5404bbda4196049c631e9f53482))

## [2.1.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.1.2) (2022-09-05)

**Note:** Version bump only for package data-factory

## [2.1.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.1.1) (2022-09-01)

### Features

- ✨ 经纬度节点改名 ([3817024](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3817024c8f4497eac9653ab8344b4ab36aa95089))

# [2.1.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.1.0) (2022-08-30)

### Bug Fixes

- 🐛 修复更新数据包字段名错误 ([c2b9772](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c2b97720659cf548c2fe6a03b5ad98d9e552aa83))

### Features

- ✨ 新增编辑界面保存并运行按钮 ([3d3c728](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3d3c728a303d8e8627990dfde314cd924d2e5795))
- ✨ 新版 Form 表单 ([18e9086](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18e908648d16cb954af9d5249ce5545b50195569))

## [2.0.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.0.2) (2022-08-29)

### Bug Fixes

- 🐛 修复数据编辑按钮不能跳转&修复字段过度数据预览问题&新增 Delete 删除节点 ([b225b1d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b225b1d44fafa6774030b62ae5cf5b3917cf20d0))

### Features

- ✨ 限制 Flow 数据预览数据量 ([bb2131f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb2131fd9cd98f7b83d00179e5273210bce56d47))

## [2.0.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.0.1) (2022-08-12)

**Note:** Version bump only for package data-factory

# [2.0.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/<EMAIL>-factory@2.0.0) (2022-08-12)

### Bug Fixes

- 🐛 fix bug ([11e790f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/11e790f1b38b898a2475f98ccf6f790049788941))
- 🐛 fix issues ([8789043](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8789043ceb6cf02c1355c3e01b6e0c2e5a0fbca9))
- 🐛 remove debug ([46c9b60](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46c9b604e59c40ba5f362f901459b426c4236368))
- 🐛 产品跳转 ([3b47674](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3b476749c7755f9b581ec61fa16029326cfa9a7e))
- 🐛 依赖问题修复 ([9f624b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9f624b200f143dcfd02666e75ff404ec20c737dd))
- 🐛 修复节点无法显示&完善报错提示 ([fb4059a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fb4059aa95012b7d26d77831f0c650dfb038cf74))
- 🐛 修改新数据工厂的导航栏 ([53cb069](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/53cb069ac6ea490fb2df555de01f723fb207c5c1))
- 🐛 切换 app 时跳 sso 更新 token ([297c72c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/297c72c70aacd83d327824e6cc4a135dca7054e1))
- 🐛 循环应用 ([8443062](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8443062465cced469833d6e45bd08e085448f33a))
- 🐛 样式污染 ([77282b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77282b20211338b0a2a5f13724b5220b6fc1ad35))

### Features

- ✨ [通用 header]: 功能扩展，样式优化 ([d843e94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d843e9438b766313f08cfbcba5734f88d852ccfb))
- ✨ add devlop product ([ef17303](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef17303bf4b5da5f04a7cf4d17486a3d3112b969))
- ✨ 增加允许产品跳转的配置项,修改菜单栏的样式 ([c4b803f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4b803fb802e3d1dcef64a7a44ab37aa95e31992))
- ✨ 数据工厂 ([bfd279c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bfd279c143bd334aaec4d9cb3b357cc172ae807b))
- ✨ 数据工厂 ([4db9fd7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4db9fd729014994db5be138950973d9b2ad7f77b))
- ✨ 机构管理初版配套修改 ([a7311bf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a7311bfeeb5c5dcc3b2509fb21ece8baefb10216))
- ✨ 画布 bug 修复 ([953d2e4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/953d2e488b854afa1ddc4d84ae4d6475c4230229))
- ✨ 血缘图 ([dbe2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbe2c308386df9286f60026925486879c7aab965))
- ✨ 跳转 datlas ([d215fa1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d215fa18ea4b2d7a8007e73fb2f1e13b0a4e47c2))
- ✨ 隐藏搜索与最近执行结果 ([a56ba39](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a56ba3990ff8fe5b7e6f23d2f6070f114bb5154f))

# 1.1.0 (2022-06-30)

### Bug Fixes

- 🐛 修复分离部署加载失败 ([21ac2e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/21ac2e342500e0274517b599160cf13225868ab9))
- 🐛 修复微应用下，hash 路由模式资源释放错误 ([f5cf78c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f5cf78c0d8abbf00ee558307454fe07c904ed97c))

### Features

- ✨ 低码 ETL-增加节点“读取其他机构数据包” ([73587d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/73587d83821ac6c62422f1ae37920a27524bd0a1))
- ✨ 低码 ETL 迁移数据工厂 ([3b96fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3b96fde0a1f958e105ef219a0e9fa0d58690c339))
- ✨ 支持微服务 ([41c6dac](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41c6dac6074fb9546bde5ae851d263586ae1bb21))
- ✨ 支持微服务 ([8e4c498](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8e4c498957cee15893fbbecf4956b634fec633cb))
- ✨ 迁移 sso + 微前端 ([b51cb10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b51cb107a0f84fb5729f16cc0118b6cf89d7c91d))
- ✨ 通用分离 ([8a90e4b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a90e4b5ee9bf1cdd6dd15d86dfa54b989f35dfc))
