{"name": "mdt-frontend", "license": "SIC", "private": true, "workspaces": ["docs/*", "packages/*", "products/*", "shares/*", "utils/*"], "engines": {"node": ">=16.14"}, "scripts": {"prepare": "husky install", "postinstall": "patch-package", "build": "yarn build:n", "build:l": "lerna run --stream --concurrency 1 --sort build", "build:n": "nx run-many --target=build --all", "release": "nx run-many --target=release --all", "release:data-factory": "nx release data-factory", "release:designable": "nx release designable", "release:data-market": "nx release data-market", "release:devops-frontend": "nx release devops-frontend", "release:my-data": "nx release my-data", "release:mdt-amis-editor": "nx release mdt-amis-editor", "release:one-table": "nx release one-table", "release:micro-mdt": "nx release micro-mdt", "release:organization-management": "nx release organization-management", "release:sso": "nx release sso", "release:collector-sso": "nx release collector-sso", "release:resource-share": "nx release resource-share", "release:workflow": "nx release workflow", "rv": "lerna version --conventional-commits", "rv:lib": "lerna version --no-private", "push": "nx run-many --target=push --all", "deploy": "yarn mdtfps tag", "reset": "yarn mdtfps reset-branch", "rmtag": "./.scripts/remove-tags.sh", "sync": "node .scripts/sync-config.js", "graph": "nx graph", "prettier": "prettier --write --ignore-unknown", "tsc": "rimraf .tsc && tsc && rimraf .tsc", "ct": "git add .", "lint:ts": "yarn tsc && eslint", "lint:style": "stylelint", "i18n:check": "node .scripts/i18n-lint.js", "lint:tsfix": "eslint --fix --ext .ts,.tsx ./"}, "resolutions": {"@babel/code-frame": "7.24.2", "@babel/compat-data": "7.24.4", "@babel/core": "7.24.4", "@babel/generator": "7.24.4", "@babel/helper-annotate-as-pure": "7.22.5", "@babel/helper-compilation-targets": "7.23.6", "@babel/helper-create-class-features-plugin": "7.24.4", "@babel/helper-create-regexp-features-plugin": "7.22.15", "@babel/helper-environment-visitor": "7.22.20", "@babel/helper-function-name": "7.23.0", "@babel/helper-hoist-variables": "7.22.5", "@babel/helper-member-expression-to-functions": "7.23.0", "@babel/helper-module-imports": "7.24.3", "@babel/helper-module-transforms": "7.23.3", "@babel/helper-optimise-call-expression": "7.22.5", "@babel/helper-plugin-utils": "7.24.0", "@babel/helper-remap-async-to-generator": "7.22.20", "@babel/helper-replace-supers": "7.24.1", "@babel/helper-simple-access": "7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "7.22.5", "@babel/helper-split-export-declaration": "7.22.6", "@babel/helper-string-parser": "7.24.1", "@babel/helper-validator-identifier": "7.22.20", "@babel/helper-validator-option": "7.23.5", "@babel/helper-wrap-function": "7.22.20", "@babel/helpers": "7.24.4", "@babel/highlight": "7.24.2", "@babel/parser": "7.24.4", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "7.24.4", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "7.24.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "7.24.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "7.24.1", "@babel/plugin-proposal-decorators": "7.24.1", "@babel/plugin-syntax-decorators": "7.24.1", "@babel/plugin-syntax-flow": "7.24.1", "@babel/plugin-syntax-import-assertions": "7.24.1", "@babel/plugin-syntax-import-attributes": "7.24.1", "@babel/plugin-syntax-jsx": "7.24.1", "@babel/plugin-syntax-typescript": "7.24.1", "@babel/plugin-transform-arrow-functions": "7.24.1", "@babel/plugin-transform-async-generator-functions": "7.24.3", "@babel/plugin-transform-async-to-generator": "7.24.1", "@babel/plugin-transform-block-scoped-functions": "7.24.1", "@babel/plugin-transform-block-scoping": "7.24.4", "@babel/plugin-transform-class-properties": "7.24.1", "@babel/plugin-transform-class-static-block": "7.24.4", "@babel/plugin-transform-classes": "7.24.1", "@babel/plugin-transform-computed-properties": "7.24.1", "@babel/plugin-transform-destructuring": "7.24.1", "@babel/plugin-transform-dotall-regex": "7.24.1", "@babel/plugin-transform-duplicate-keys": "7.24.1", "@babel/plugin-transform-dynamic-import": "7.24.1", "@babel/plugin-transform-exponentiation-operator": "7.24.1", "@babel/plugin-transform-export-namespace-from": "7.24.1", "@babel/plugin-transform-for-of": "7.24.1", "@babel/plugin-transform-function-name": "7.24.1", "@babel/plugin-transform-json-strings": "7.24.1", "@babel/plugin-transform-literals": "7.24.1", "@babel/plugin-transform-logical-assignment-operators": "7.24.1", "@babel/plugin-transform-member-expression-literals": "7.24.1", "@babel/plugin-transform-modules-amd": "7.24.1", "@babel/plugin-transform-modules-commonjs": "7.24.1", "@babel/plugin-transform-modules-systemjs": "7.24.1", "@babel/plugin-transform-modules-umd": "7.24.1", "@babel/plugin-transform-named-capturing-groups-regex": "7.22.5", "@babel/plugin-transform-new-target": "7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "7.24.1", "@babel/plugin-transform-numeric-separator": "7.24.1", "@babel/plugin-transform-object-assign": "7.24.1", "@babel/plugin-transform-object-rest-spread": "7.24.1", "@babel/plugin-transform-object-super": "7.24.1", "@babel/plugin-transform-optional-catch-binding": "7.24.1", "@babel/plugin-transform-optional-chaining": "7.24.1", "@babel/plugin-transform-parameters": "7.24.1", "@babel/plugin-transform-private-methods": "7.24.1", "@babel/plugin-transform-private-property-in-object": "7.24.1", "@babel/plugin-transform-property-literals": "7.24.1", "@babel/plugin-transform-react-constant-elements": "7.24.1", "@babel/plugin-transform-react-display-name": "7.24.1", "@babel/plugin-transform-react-jsx-development": "7.22.5", "@babel/plugin-transform-react-jsx": "7.23.4", "@babel/plugin-transform-react-pure-annotations": "7.24.1", "@babel/plugin-transform-regenerator": "7.24.1", "@babel/plugin-transform-reserved-words": "7.24.1", "@babel/plugin-transform-runtime": "7.24.3", "@babel/plugin-transform-shorthand-properties": "7.24.1", "@babel/plugin-transform-spread": "7.24.1", "@babel/plugin-transform-sticky-regex": "7.24.1", "@babel/plugin-transform-template-literals": "7.24.1", "@babel/plugin-transform-typeof-symbol": "7.24.1", "@babel/plugin-transform-typescript": "7.24.1", "@babel/plugin-transform-unicode-escapes": "7.24.1", "@babel/plugin-transform-unicode-property-regex": "7.24.1", "@babel/plugin-transform-unicode-regex": "7.24.1", "@babel/plugin-transform-unicode-sets-regex": "7.24.1", "@babel/preset-env": "7.24.4", "@babel/preset-flow": "7.24.1", "@babel/preset-react": "7.24.1", "@babel/preset-typescript": "7.24.1", "@babel/runtime-corejs3": "7.24.4", "@babel/runtime": "7.24.4", "@babel/template": "7.24.0", "@babel/traverse": "7.24.1", "@babel/types": "7.24.0", "@metro/components": "0.6.10", "@metro/icons": "1.52.0", "@metro/mobile-components": "0.2.0", "@metro/rule-form": "0.2.10", "amis": "6.11.0-patch.3", "amis-core": "6.11.0-patch.1", "amis-editor": "6.11.0-patch.1", "amis-editor-core": "6.11.0-patch.1", "amis-formula": "6.11.0-patch.1", "amis-theme-editor-helper": "2.0.26-patch.4", "amis-ui": "6.11.0-patch.1", "dayjs": "1.11.2", "office-viewer": "0.3.14-patch.3", "react": "17.0.2", "react-dom": "17.0.2", "react-router": "5.3.3", "react-router-dom": "5.3.3", "sql-formatter": "15.3.1", "@dnd-kit/sortable": "8.0.0"}, "dependencies": {"@carbon/icons-react": "10.49.0", "@datlas/design": "1.53.0", "@i18n-chain/react": "2.0.1", "@loadable/component": "^5.15.2", "ahooks": "^3.4.1", "axios": "^0.27.2", "clsx": "^1.2.1", "dayjs": "1.11.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "lodash": "^4.17.21", "lz-string": "^1.4.4", "process": "^0.11.10", "react": "17.0.2", "react-dom": "17.0.2", "react-router-dom": "5.3.0", "rxjs": "6.6.7", "spark-md5": "^3.0.2", "uuid": "^8.3.2", "web-vitals": "2.1.4", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0", "xlsx": "0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@craco/craco": "^6.4.5", "@docusaurus/core": "2.1.0", "@docusaurus/module-type-aliases": "2.1.0", "@docusaurus/preset-classic": "2.1.0", "@docusaurus/remark-plugin-npm2yarn": "2.1.0", "@docusaurus/theme-live-codeblock": "2.1.0", "@easyops-cn/docusaurus-search-local": "^0.31.0", "@faker-js/faker": "^7.4.0", "@mdt/scripts": "^0.4.2", "@mdx-js/react": "^1.6.22", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "12.1.5", "@testing-library/user-event": "13.5.0", "@tsconfig/docusaurus": "^1.0.5", "@types/file-saver": "^2.0.5", "@types/loadable__component": "^5.13.4", "@types/lodash": "^4.14.182", "@types/lz-string": "^1.3.34", "@types/node": "^16.11.36", "@types/react": "17.0.45", "@types/react-copy-to-clipboard": "^5.0.2", "@types/react-dom": "17.0.17", "@types/react-router-dom": "5.3.3", "@types/spark-md5": "^3.0.2", "@types/uuid": "^8.3.4", "ali-oss": "^6.17.1", "code-inspector-plugin": "^0.20.10", "copy-webpack-plugin": "^11.0.0", "craco-interpolate-html-plugin": "^0.0.6", "craco-less": "^2.0.0", "cross-env": "^7.0.3", "cssnano": "^5.1.12", "dumi": "^1.1.47", "eslint-config-alloy": "^4.6.2", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-mdt-frontend": "^1.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-sonarjs": "^0.15.0", "eslint-webpack-plugin": "^3.2.0", "filemanager-webpack-plugin": "^7.0.0", "git-cz": "^4.9.0", "gulp": "^4.0.2", "gulp-less": "^5.0.0", "gulp-postcss": "^9.0.1", "gulp-typescript": "^5.0.1", "husky": "^8.0.1", "jest-canvas-mock": "^2.4.0", "lerna": "^5.1.6", "lint-staged": "^13.0.3", "nx": "14.4.1", "patch-package": "^6.4.7", "plugin-image-zoom": "^1.1.0", "postcss": "^8.4.31", "postcss-cssnext": "^3.1.1", "postcss-less": "^6.0.0", "postcss-nested": "^5.0.6", "postcss-normalize": "^10.0.1", "postcss-pxtorem": "^6.1.0", "prettier": "^2.7.1", "prettier-eslint": "^15.0.1", "prism-react-renderer": "^1.3.5", "query-extensions": "^0.0.4", "raw-loader": "^4.0.2", "react-app-alias-ex": "^2.1.0", "react-scripts": "^5.0.1", "simple-git": "^3.16.0", "speed-measure-webpack-plugin": "^1.5.0", "style-inject": "0.3.0", "stylelint": "^14.9.1", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^26.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-declaration-strict-value": "^1.9.0", "stylelint-order": "^5.0.0", "stylelint-prettier": "^2.0.0", "thread-loader": "^3.0.4", "through2": "^4.0.2", "ts-jest": "^27.1.5", "typescript": "^4.9.5", "webpack-bundle-analyzer": "^4.5.0", "webpackbar": "^5.0.2"}}