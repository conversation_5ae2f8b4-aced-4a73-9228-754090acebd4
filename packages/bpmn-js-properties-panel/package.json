{"name": "@mdt/bpmn-js-properties-panel", "version": "1.15.12", "private": false, "description": "脉策自定义拖拽bpmn标准属性", "keywords": ["mdt", "bpmn", "properties", "custom"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@bpmn-io/properties-panel": "^1.4.0", "@mdt/business-comm": "^1.17.7", "bpmn-js": "^11.5.0", "bpmn-js-properties-panel": "^1.19.1", "camunda-bpmn-moddle": "^7.0.1"}}