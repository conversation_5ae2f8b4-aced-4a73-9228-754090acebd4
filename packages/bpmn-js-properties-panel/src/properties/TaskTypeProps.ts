import { CheckboxEntry, isCheckboxEntryEdited, isSelectEntryEdited, SelectEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { PREFIX } from '../constants';
import { getRelevantBusinessObject, isBpmnUserTask } from '../utils/ElementUtil';
import {
  addExtensionElements,
  getExtensionElement,
  removeExtensionElements,
  replaceExtensionElements,
} from '../utils/ExtensionElementsUtil';

const CUSTOM_ELEMENT_KEY = `${PREFIX}:TaskType`;

export function TaskTypeProps(props: any) {
  const { element } = props;

  if (!isBpmnUserTask(element)) {
    return [];
  }

  return [
    { id: 'taskType', component: TaskType, isEdited: isSelectEntryEdited },
    { id: 'terminateIfRejected', component: TerminateIfRejected, isEdited: isCheckboxEntryEdited },
  ];
}

function TaskType(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const businessObject = getRelevantBusinessObject(element);

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, CUSTOM_ELEMENT_KEY);
    return extensionElement?.get('body') || 'normal';
  };

  const setValue = (value: string) => {
    let extensionElement = getExtensionElement(businessObject, CUSTOM_ELEMENT_KEY);
    extensionElement && removeExtensionElements(element, businessObject, extensionElement, commandStack);
    extensionElement = bpmnFactory.create(CUSTOM_ELEMENT_KEY, { body: value });
    addExtensionElements(element, businessObject, extensionElement, bpmnFactory, commandStack);
  };

  const getOptions = () => {
    return [
      { value: 'normal', label: translate('Normal task') },
      { value: 'form', label: translate('Form task') },
      { value: 'approval', label: translate('Approval task') },
    ];
  };

  return SelectEntry({ element, id, label: translate('Type'), getValue, setValue, getOptions });
}

function TerminateIfRejected(props: any) {
  const { element, id } = props;
  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const businessObject = getRelevantBusinessObject(element);
  const componentKey = 'mdt:TerminateIfRejected';

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, componentKey);
    let value = extensionElement?.get('body');
    if (value !== false && value !== true) {
      value = true;
      requestAnimationFrame(() => setValue(value));
    }
    return value;
  };

  const setValue = (value: boolean) => {
    replaceExtensionElements(element, businessObject, bpmnFactory, commandStack, componentKey, { body: value });
  };

  if (getExtensionElement(businessObject, CUSTOM_ELEMENT_KEY)?.get('body') !== 'approval') {
    return null;
  }

  return CheckboxEntry({ element, id, label: translate('Terminate If Rejected2'), getValue, setValue });
}
