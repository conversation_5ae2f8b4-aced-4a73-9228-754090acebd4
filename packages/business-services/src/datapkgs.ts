import _ from 'lodash';
import {
  batchDeleteDatapkgColumns,
  batchModifyDatapkgDdlElements,
  batchPatchDatapkgColumns,
  batchPostDatapkgColumns,
  batchQueryDatapkgChecklogs,
  batchQueryDatapkgsQualityReport,
  checkDatapkgHasPermission,
  datapkgBindAlbums,
  datapkgBoundWfspecs,
  deleteDatapkg,
  deleteDatapkgAllConstraints,
  deleteDatapkgChecklogs,
  deleteDatapkgCollaborate,
  deleteDatapkgColumn,
  deleteDatapkgConstraint,
  deleteDatapkgDdlElement,
  deleteDatapkgGenealogys,
  deleteDatapkgIdFromAlbums,
  deleteDatapkgRow,
  deleteDatapkgRows,
  deleteDatapkgs,
  downloadDatapkg,
  fuzzyQueryDatapkgRows,
  getDatapkg,
  getDatapkgAllConstraints,
  getDatapkgCollaborate,
  getDatapkgColumns,
  getDatapkgConstraint,
  getDatapkgDdlElements,
  getDatapkgPermissions,
  getDatapkgRow,
  getDatapkgRows,
  getDatapkgsColumns,
  getDatapkgsQualityStats,
  getDatapkgsUsedTags,
  getDatapkgViews,
  patchDatapkg,
  patchDatapkgCollaborate,
  patchDatapkgColumn,
  patchDatapkgQualityReport,
  patchDatapkgRow,
  patchDatapkgRows,
  postCheckDatapkgConstraints,
  postDatapkgCollaborate,
  postDatapkgColumn,
  postDatapkgConstraint,
  postDatapkgDdlElements,
  postDatapkgGenealogy,
  postDatapkgRows,
  postDatapkgsMeta,
  postDatapkgUpdateRows,
  postDatapkgView,
  previewDatapkgRows,
  publishDatapkg,
  putDatapkgChecklog,
  putDatapkgConstraint,
  queryDatapkgChecklogs,
  queryDatapkgGenealogys,
  queryDatapkgRows,
  queryDatapkgRowsByGeometry,
  queryDatapkgs,
  queryNearestDatapkgRows,
  refreshDatapkg,
  statsDatapkgColumn,
  statsDatapkgUsage,
  updateDatapkgByUpload,
} from '@mdtApis/api/datapkgs';
import { getTotalArgs } from './_util/totalUtil';
import {
  IAlbumIds,
  IConstraintId,
  IConstraintIds,
  IDatapkg,
  IDatapkgChecklog,
  IDatapkgChecklogDelete,
  IDatapkgChecklogPut,
  IDatapkgChecklogsBatchQuery,
  IDatapkgChecklogsQuery,
  IDatapkgCollaborate,
  IDatapkgCollaboratePost,
  IDatapkgColumn,
  IDatapkgColumnBatchDelete,
  IDatapkgColumnBatchPatch,
  IDatapkgColumnDeleteQuery,
  IDatapkgColumnPatch,
  IDatapkgColumnPatchQuery,
  IDatapkgColumnPost,
  IDatapkgColumnStats,
  IDatapkgConstraint,
  IDatapkgConstraintPost,
  IDatapkgDdlElement,
  IDatapkgDdlElementPost,
  IDatapkgDdlElementQuery,
  IDatapkgDdlElementsBatchPost,
  IDatapkgDdlElementType,
  IDatapkgDownload,
  IDatapkgDownloadPost,
  IDatapkgGenealogy,
  IDatapkgGenealogyDelete,
  IDatapkgGenealogyPost,
  IDatapkgGenealogysQuery,
  IDatapkgGetQuery,
  IDatapkgId,
  IDatapkgIds,
  IDatapkgPatch,
  IDatapkgPermission,
  IDatapkgPublishPost,
  IDatapkgQualityReport,
  IDatapkgQualityReportPatch,
  IDatapkgQualityReportPatchResult,
  IDatapkgQualityStatsResult,
  IDatapkgRefreshPost,
  IDatapkgRowPatch,
  IDatapkgRows,
  IDatapkgRowsDelete,
  IDatapkgRowsFuzzyQuery,
  IDatapkgRowsPatch,
  IDatapkgRowsPatchRslt,
  IDatapkgRowsQuery,
  IDatapkgRowsQueryByGeometryPost,
  IDatapkgRowsQueryNearestPost,
  IDatapkgsAppQuery,
  IDatapkgsColumn,
  IDatapkgsMetaPost,
  IDatapkgsQualityReportQuery,
  IDatapkgsQuery,
  IDatapkgStatsUsage,
  IDatapkgUpdateByUploadPost,
  IDatapkgViewPost,
  IEmptyObj,
  IPaginationQuery,
  IPermissions,
  IPkgBoundWfspec,
  IRequestRequestConfig,
  IServerPaginationResponse,
  IServerResponse,
  ITags,
  ITaskId,
  ITotal,
} from './interfaces';

// 获取单个数据包
export const deleteDatapkgsAsync = async (data: IDatapkgIds, config?: IRequestRequestConfig) => {
  return deleteDatapkgs(data, config) as unknown as IServerResponse<IDatapkgIds>;
};

// 获取单个数据包
export const getDatapkgAsync = async (datapkgId: string, config?: IRequestRequestConfig<IDatapkgGetQuery>) => {
  return getDatapkg(datapkgId, config) as unknown as IServerResponse<IDatapkg>;
};

// 修改数据包部分属性
export const patchDatapkgAsync = async (datapkgId: string, data: IDatapkgPatch, config?: IRequestRequestConfig) => {
  return patchDatapkg(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};

// 删除数据包
export const deleteDatapkgAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return deleteDatapkg(datapkgId, config) as unknown as IServerResponse<IDatapkgId>;
};

// 数据包下载
export const downloadDatapkgAsync = async (
  datapkgId: string,
  data: IDatapkgDownloadPost,
  config?: IRequestRequestConfig,
) => {
  return downloadDatapkg(datapkgId, data, config) as unknown as IServerResponse<IDatapkgDownload>;
};

// 预览数据包数据
export const previewDatapkgRowsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return previewDatapkgRows(datapkgId, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 发布数据包
export const publishDatapkgAsync = async (
  datapkgId: string,
  data: IDatapkgPublishPost,
  config?: IRequestRequestConfig,
) => {
  return publishDatapkg(datapkgId, data, config) as unknown as IServerResponse<IDatapkgId>;
};

// 刷新数据包
export const refreshDatapkgAsync = async (
  datapkgId: string,
  data: IDatapkgRefreshPost,
  config?: IRequestRequestConfig,
) => {
  return refreshDatapkg(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};

// 上传更新数据包数据
export const updateDatapkgByUploadAsync = async (
  datapkgId: string,
  data: IDatapkgUpdateByUploadPost,
  config?: IRequestRequestConfig,
) => {
  return updateDatapkgByUpload(datapkgId, data, config) as unknown as IServerResponse<ITaskId>;
};

// 将数据包从主题库批量下架
export const deleteDatapkgIdFromAlbumsAsync = async (
  datapkgId: string,
  data: IAlbumIds,
  config?: IRequestRequestConfig,
) => {
  return deleteDatapkgIdFromAlbums(datapkgId, data, config) as unknown as IServerResponse<IDatapkgId>;
};

// 获取监测结果
export const queryDatapkgChecklogsAsync = async (
  datapkgId: string,
  config?: IRequestRequestConfig<IDatapkgChecklogsQuery>,
) => {
  return queryDatapkgChecklogs(datapkgId, config) as unknown as IServerResponse<IDatapkgChecklog[]>;
};

// 批量获取监测结果
export const batchQueryDatapkgChecklogsAsync = async (
  data: IDatapkgChecklogsBatchQuery,
  config?: IRequestRequestConfig,
) => {
  return batchQueryDatapkgChecklogs(data, config) as unknown as IServerResponse<IDatapkgChecklog[]>;
};

// 更新一条数据包的检查日志
export const putDatapkgChecklogAsync = async (
  datapkgId: string,
  data: IDatapkgChecklogPut,
  config?: IRequestRequestConfig,
) => {
  return putDatapkgChecklog(datapkgId, data, config) as unknown as IServerResponse<IDatapkgChecklog>;
};

// 删除数据包的检查日志
export const deleteDatapkgChecklogsAsync = async (
  datapkgId: string,
  data: IDatapkgChecklogDelete,
  config?: IRequestRequestConfig,
) => {
  return deleteDatapkgChecklogs(datapkgId, data, config) as unknown as IServerResponse<ITotal>;
};

// 获取协同数据包
export const getDatapkgCollaborateAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return getDatapkgCollaborate(datapkgId, config) as unknown as IServerResponse<IDatapkgCollaborate>;
};

// 新建协同数据包
export const postDatapkgCollaborateAsync = async (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return postDatapkgCollaborate(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};

// 更新协同数据包
export const patchDatapkgCollaborateAsync = async (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return patchDatapkgCollaborate(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};

// 删除协同数据包
export const deleteDatapkgCollaborateAsync = async (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return deleteDatapkgCollaborate(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};

// 获取数据包列信息
export const getDatapkgColumnsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return getDatapkgColumns(datapkgId, config) as unknown as IServerResponse<IDatapkgColumn[]>;
};

// 新增数据包列
export const postDatapkgColumnAsync = async (
  datapkgId: string,
  data: IDatapkgColumnPost,
  config?: IRequestRequestConfig,
) => {
  return postDatapkgColumn(datapkgId, data, config) as unknown as IServerResponse<IDatapkgColumn>;
};

// 批量新增数据包列
export const batchPostDatapkgColumnsAsync = async (
  datapkgId: string,
  data: IDatapkgColumnPost[],
  config?: IRequestRequestConfig,
) => {
  return batchPostDatapkgColumns(datapkgId, data, config) as unknown as IServerResponse<IDatapkgColumn[]>;
};

// 批量更新列
export const batchPatchDatapkgColumnsAsync = async (
  datapkgId: string,
  data: IDatapkgColumnBatchPatch[],
  config?: IRequestRequestConfig<IDatapkgColumnDeleteQuery>,
) => {
  return batchPatchDatapkgColumns(datapkgId, data, config) as unknown as IServerResponse<IDatapkgsColumn[]>;
};

// 删除数据包列
export const batchDeleteDatapkgColumnsAsync = async (
  datapkgId: string,
  data: IDatapkgColumnBatchDelete,
  config?: IRequestRequestConfig,
) => {
  return batchDeleteDatapkgColumns(datapkgId, data, config) as unknown as IServerResponse<boolean>;
};

// 删除数据包列
export const deleteDatapkgColumnAsync = async (
  datapkgId: string,
  column: string,
  config?: IRequestRequestConfig<IDatapkgColumnDeleteQuery>,
) => {
  return deleteDatapkgColumn(datapkgId, column, config) as unknown as IServerResponse<boolean>;
};

// 修改数据包列信息
export const patchDatapkgColumnAsync = async (
  datapkgId: string,
  column: string,
  data: IDatapkgColumnPatch,
  config?: IRequestRequestConfig<IDatapkgColumnPatchQuery>,
) => {
  return patchDatapkgColumn(datapkgId, column, data, config) as unknown as IServerResponse<IDatapkgColumn>;
};

// 获取数据包列信息
export const statsDatapkgColumnAsync = async (
  datapkgId: string,
  columnName: string,
  config?: IRequestRequestConfig,
) => {
  return statsDatapkgColumn(datapkgId, columnName, config) as unknown as IServerResponse<IDatapkgColumnStats>;
};

// 获取数据包所有约束
export const getDatapkgAllConstraintsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return getDatapkgAllConstraints(datapkgId, config) as unknown as IServerResponse<IDatapkgConstraint[]>;
};

// 新增更新约束
export const postDatapkgConstraintAsync = async (
  datapkgId: string,
  data: IDatapkgConstraintPost,
  config?: IRequestRequestConfig,
) => {
  return postDatapkgConstraint(datapkgId, data, config) as unknown as IServerResponse<IDatapkgConstraint>;
};

// 删除数据包约束
export const deleteDatapkgAllConstraintsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return deleteDatapkgAllConstraints(datapkgId, config) as unknown as IServerResponse<IConstraintIds>;
};

// 获取数据包某个具体约束
export const getDatapkgConstraintAsync = async (
  datapkgId: string,
  constraintId: string,
  config?: IRequestRequestConfig,
) => {
  return getDatapkgConstraint(datapkgId, constraintId, config) as unknown as IServerResponse<IDatapkgConstraint>;
};

// 更新约束
export const putDatapkgConstraintAsync = async (
  datapkgId: string,
  constraintId: string,
  data: IDatapkgConstraintPost,
  config?: IRequestRequestConfig,
) => {
  return putDatapkgConstraint(datapkgId, constraintId, data, config) as unknown as IServerResponse<IDatapkgConstraint>;
};

// 删除约束
export const deleteDatapkgConstraintAsync = async (
  datapkgId: string,
  constraintId: string,
  config?: IRequestRequestConfig,
) => {
  return deleteDatapkgConstraint(datapkgId, constraintId, config) as unknown as IServerResponse<IConstraintId>;
};

// 立即检查约束
export const postCheckDatapkgConstraintsAsync = async (
  datapkgId: string,
  data: IConstraintIds,
  config?: IRequestRequestConfig,
) => {
  return postCheckDatapkgConstraints(datapkgId, data, config) as unknown as IServerResponse<ITaskId>;
};

export const getDatapkgDdlElementsAsync = async (
  datapkgId: string,
  config?: IRequestRequestConfig<IDatapkgDdlElementQuery>,
) => {
  return getDatapkgDdlElements(datapkgId, config) as unknown as IServerResponse<IDatapkgDdlElement[]>;
};

export const postDatapkgDdlElementsAsync = async (
  datapkgId: string,
  data: IDatapkgDdlElementPost,
  config?: IRequestRequestConfig,
) => {
  return postDatapkgDdlElements(datapkgId, data, config) as unknown as IServerResponse<IEmptyObj>;
};

export const deleteDatapkgDdlElementAsync = async (
  datapkgId: string,
  element: string,
  config: IRequestRequestConfig<{ type: IDatapkgDdlElementType }>,
) => {
  return deleteDatapkgDdlElement(datapkgId, element, config) as unknown as IServerResponse<IEmptyObj>;
};

export const batchModifyDatapkgDdlElementsAsync = async (
  datapkgId: string,
  data: IDatapkgDdlElementsBatchPost,
  config?: IRequestRequestConfig,
) => {
  return batchModifyDatapkgDdlElements(datapkgId, data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 查询数据包族谱
export const queryDatapkgGenealogysAsync = async (
  datapkgId: string,
  config?: IRequestRequestConfig<IDatapkgGenealogysQuery>,
) => {
  return queryDatapkgGenealogys(datapkgId, config) as unknown as IServerResponse<IDatapkgGenealogy[]>;
};

// 新增数据包族谱
export const postDatapkgGenealogyAsync = async (
  datapkgId: string,
  data: IDatapkgGenealogyPost,
  config?: IRequestRequestConfig,
) => {
  return postDatapkgGenealogy(datapkgId, data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 删除数据包族谱
export const deleteDatapkgGenealogysAsync = async (
  datapkgId: string,
  config: IRequestRequestConfig<IDatapkgGenealogyDelete>,
) => {
  return deleteDatapkgGenealogys(datapkgId, config) as unknown as IServerResponse<IEmptyObj>;
};

// 获取某个数据包具体的权限
export const getDatapkgPermissionsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return getDatapkgPermissions(datapkgId, config) as unknown as IServerResponse<IPermissions<IDatapkgPermission[]>>;
};

// 获取某个数据包具体的权限
export const checkDatapkgHasPermissionAsync = async (
  datapkgId: string,
  permission: IDatapkgPermission,
  config?: IRequestRequestConfig,
) => {
  return checkDatapkgHasPermission(datapkgId, permission, config) as unknown as IServerResponse<
    IPermissions<IDatapkgPermission[]>
  >;
};

// 数据包数据查询
export const queryDatapkgRowsAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQuery,
  config: IRequestRequestConfig,
) => {
  return queryDatapkgRows(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 数据包数据分页查询
export const queryDatapkgRowsPaginationAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQuery,
  config: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryDatapkgRows(datapkgId, data, cnf) as unknown as IServerPaginationResponse<IDatapkgRows>;
};

export const queryDatapkgRowsPaginationTotalAsync: typeof queryDatapkgRowsPaginationAsync = (...args) => {
  _.update(args[2], 'params', (params = {}) => ({ ...params, fetch_total_count: true }));
  return queryDatapkgRowsPaginationAsync(...args);
};

// 数据包数据模糊查询
export const fuzzyQueryDatapkgRowsAsync = async (
  datapkgId: string,
  config: IRequestRequestConfig<IDatapkgRowsFuzzyQuery>,
) => {
  return fuzzyQueryDatapkgRows(datapkgId, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 数据包数据模糊分页查询
export const fuzzyQueryDatapkgRowsPaginationAsync = async (
  datapkgId: string,
  config: IRequestRequestConfig<IDatapkgRowsFuzzyQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return fuzzyQueryDatapkgRows(datapkgId, cnf) as unknown as IServerPaginationResponse<IDatapkgRows>;
};

// 使用围栏查询数据包数据
export const queryDatapkgRowsByGeometryAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQueryByGeometryPost,
  config?: IRequestRequestConfig,
) => {
  return queryDatapkgRowsByGeometry(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 使用围栏分页查询数据包数据
export const queryDatapkgRowsByGeometryPaginationAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQueryByGeometryPost,
  config?: IRequestRequestConfig,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryDatapkgRowsByGeometry(datapkgId, data, cnf) as unknown as IServerPaginationResponse<IDatapkgRows>;
};

// 数据包最近点查询
export const queryNearestDatapkgRowsAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQueryNearestPost,
  config?: IRequestRequestConfig,
) => {
  return queryNearestDatapkgRows(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 使用围栏分页查询数据包数据
export const queryNearestDatapkgRowsPaginationAsync = async (
  datapkgId: string,
  data: IDatapkgRowsQueryNearestPost,
  config?: IRequestRequestConfig,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryNearestDatapkgRows(datapkgId, data, cnf) as unknown as IServerPaginationResponse<IDatapkgRows>;
};

// 批量获取数据包数据
export const getDatapkgRowsAsync = async (datapkgId: string, config?: IRequestRequestConfig<IPaginationQuery>) => {
  return getDatapkgRows(datapkgId, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 分页获取数据
export const getDatapkgRowsPaginationAsync = async (
  datapkgId: string,
  config: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return getDatapkgRows(datapkgId, cnf) as unknown as IServerPaginationResponse<IDatapkgRows>;
};

// 增加数据包数据
export const postDatapkgRowsAsync = async (datapkgId: string, data: IDatapkgRows, config?: IRequestRequestConfig) => {
  return postDatapkgRows(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 更新数据包数据
export const postDatapkgUpdateRowsAsync = async (
  datapkgId: string,
  data: IDatapkgRows[],
  config?: IRequestRequestConfig,
) => {
  return postDatapkgUpdateRows(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRows[]>;
};

export const patchDatapkgRowsAsync = async (
  datapkgId: string,
  data: IDatapkgRowsPatch,
  config?: IRequestRequestConfig,
) => {
  return patchDatapkgRows(datapkgId, data, config) as unknown as IServerResponse<IDatapkgRowsPatchRslt>;
};

// 批量删除数据包数据
export const deleteDatapkgRowsAsync = async (
  datapkgId: string,
  data: IDatapkgRowsDelete,
  config?: IRequestRequestConfig,
) => {
  return deleteDatapkgRows(datapkgId, data, config) as unknown as IServerResponse<boolean>;
};

// 查询数据包单行数据
export const getDatapkgRowAsync = async (datapkgId: string, rowId: number, config?: IRequestRequestConfig) => {
  return getDatapkgRow(datapkgId, rowId, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 更改数据包单行数据
export const patchDatapkgRowAsync = async (
  datapkgId: string,
  rowId: number,
  data: IDatapkgRowPatch,
  config?: IRequestRequestConfig,
) => {
  return patchDatapkgRow(datapkgId, rowId, data, config) as unknown as IServerResponse<IDatapkgRows>;
};

// 删除数据包单行数据
export const deleteDatapkgRowAsync = async (datapkgId: string, rowId: number, config?: IRequestRequestConfig) => {
  return deleteDatapkgRow(datapkgId, rowId, config) as unknown as IServerResponse<boolean>;
};

// 获取数据包使用情况
export const statsDatapkgUsageAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return statsDatapkgUsage(datapkgId, config) as unknown as IServerResponse<IDatapkgStatsUsage>;
};

// 获取多个数据包列数据
export const getDatapkgsColumnsAsync = async (data: IDatapkgIds, config?: IRequestRequestConfig) => {
  return getDatapkgsColumns(data, config) as unknown as IServerResponse<IDatapkgsColumn[]>;
};

// 更新数据包富文本类型的description
export const postDatapkgsMetaAsync = async (data: IDatapkgsMetaPost, config?: IRequestRequestConfig) => {
  return postDatapkgsMeta(data, config) as unknown as IServerResponse<boolean>;
};

// 批量查询数据包
export const queryDatapkgsAsync = async (
  data: IDatapkgsQuery,
  config?: IRequestRequestConfig<IDatapkgsAppQuery & IPaginationQuery>,
) => {
  return queryDatapkgs(data, config) as unknown as IServerResponse<IDatapkg[]>;
};

// 分页查询数据包
export const queryDatapkgsPaginationAsync = async (
  data: IDatapkgsQuery,
  config?: IRequestRequestConfig<IDatapkgsAppQuery & IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryDatapkgs(data, cnf) as unknown as IServerPaginationResponse<IDatapkg[]>;
};

export const queryDatapkgsPaginationTotalAsync: typeof queryDatapkgsPaginationAsync = (...args) => {
  return queryDatapkgsPaginationAsync(...getTotalArgs(args, 1));
};

// 获取数据包可用的tag
export const getDatapkgsUsedTagsAsync = async (config?: IRequestRequestConfig) => {
  return getDatapkgsUsedTags(config) as unknown as IServerPaginationResponse<ITags>;
};

// 批量查询数据包数据质量情况
export const batchQueryDatapkgsQualityReportAsync = async (
  data: IDatapkgsQualityReportQuery,
  config?: IRequestRequestConfig,
) => {
  return batchQueryDatapkgsQualityReport(data, config) as unknown as IServerResponse<IDatapkgQualityReport[]>;
};

// 分页查询数据包数据质量情况
export const batchQueryDatapkgsQualityReportPaginationAsync = async (
  data: IDatapkgsQualityReportQuery,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return batchQueryDatapkgsQualityReport(data, cnf) as unknown as IServerPaginationResponse<IDatapkgQualityReport[]>;
};

// 人工标记数据包质量
export const patchDatapkgQualityReportAsync = async (
  datapkgId: string,
  data: IDatapkgQualityReportPatch,
  config?: IRequestRequestConfig,
) => {
  return patchDatapkgQualityReport(
    datapkgId,
    data,
    config,
  ) as unknown as IServerResponse<IDatapkgQualityReportPatchResult>;
};

// 查询数据包数据质量相关统计指标
export const getDatapkgsQualityStatsAsync = async (config?: IRequestRequestConfig) => {
  return getDatapkgsQualityStats(config) as unknown as IServerResponse<IDatapkgQualityStatsResult[]>;
};

// 将数据包关联多个主题库
export const datapkgBindAlbumsAsync = async (datapkgId: string, data: IAlbumIds, config?: IRequestRequestConfig) => {
  return datapkgBindAlbums(datapkgId, data, config) as unknown as IServerResponse<IDatapkgId>;
};

// 将数据包关联多个主题库
export const datapkgBoundWfspecsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return datapkgBoundWfspecs(datapkgId, config) as unknown as IServerResponse<IPkgBoundWfspec>;
};

// 查询数据包视图
export const getDatapkgViewsAsync = async (datapkgId: string, config?: IRequestRequestConfig) => {
  return getDatapkgViews(datapkgId, config) as unknown as IServerResponse<string[]>;
};

// 创建数据包视图
export const postDatapkgViewAsync = async (
  datapkgId: string,
  data: IDatapkgViewPost,
  config?: IRequestRequestConfig,
): Promise<IServerResponse<IDatapkg>> => {
  return postDatapkgView(datapkgId, data, config) as unknown as IServerResponse<IDatapkg>;
};
