import {
  checkPassword,
  deleteUsers,
  getAllUsers,
  getImpersonaterecordApps,
  getShareResourcePermissions,
  getUserRoles,
  patchShareResourcePermissions,
  postQueryRoleIdsByUserIds,
  postUser,
  postUserByExcel,
  postUsersQuery,
  putUser,
  putUserBatch,
  resetPassword,
  sendBindMsgByEmail,
  sendBindMsgByPhone,
  unbindByDingtalk,
  unbindByWechat,
  unlock,
  verifyByEmail,
  verifyByPhone,
} from '@mdtApis/api/auth';
import {
  IEmptyObj,
  IImpersonateAppListParams,
  IImpersonaterecordApp,
  IPostUsersQuery,
  IRequestRequestConfig,
  IRowValue,
  ISendBindMsgByEmail,
  ISendBindMsgByPhone,
  IServerPaginationResponse,
  IServerResponse,
  IShareResourcePermissions,
  IShareResourcePermissionsPatch,
  IShareResourcePermissionsQuery,
  IUser,
  IUserId,
  IUserIdRoleId,
  IUserIdRoleIdQuery,
  IUserPost,
  IUserPostExcel,
  IUserPut,
  IUserPutBatch,
  IUserQuery,
  IUserRoleItem,
  IUserRolesParams,
} from '../interfaces';

// 获取用户列表
export const getAllUsersAsync = async (appId: number, query?: IUserQuery, config?: IRequestRequestConfig) => {
  return getAllUsers(appId, query, config) as unknown as IServerResponse<IUser[]>;
};

// 分页查询用户列表
export const queryUsersPaginationAsync = async (appId: number, query?: IUserQuery, config?: IRequestRequestConfig) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return getAllUsers(appId, query, cnf) as unknown as IServerPaginationResponse<IUser[]>;
};

// 分页查询用户列表(带total)
export const queryUsersPaginationTotalAsync: typeof queryUsersPaginationAsync = (
  appId: number,
  query?: IUserQuery,
  config?: IRequestRequestConfig,
) => {
  return queryUsersPaginationAsync(appId, { ...(query || {}), fetch_total_count: true }, config);
};

// 新建用户
export const postUserAsync = async (data: IUserPost, config?: IRequestRequestConfig) => {
  return postUser(data, config) as unknown as IServerResponse<IUserId>;
};

// 通过excel新建用户
export const postUserByExcelAsync = async (data: IUserPostExcel, config?: IRequestRequestConfig) => {
  return postUserByExcel(data, config) as unknown as IServerResponse<IUser[]>;
};

// 修改用户
export const putUserAsync = async (data: IUserPut, config?: IRequestRequestConfig) => {
  return putUser(data, config) as unknown as IServerResponse<number>;
};

// 批量修改用户
export const putUserBatchAsync = async (data: IUserPutBatch, config?: IRequestRequestConfig) => {
  return putUserBatch(data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 删除用户
export const deleteUsersAsync = async (
  ids: string,
  app_id: number,
  pass_to: number,
  config?: IRequestRequestConfig,
) => {
  return deleteUsers(ids, app_id, pass_to, config) as unknown as IServerResponse<IEmptyObj>;
};

// 获取模拟登录app列表
export const getImpersonaterecordAppsAsync = async (
  params?: IImpersonateAppListParams,
  config?: IRequestRequestConfig,
) => {
  return getImpersonaterecordApps(params, config) as unknown as IServerResponse<IImpersonaterecordApp[]>;
};

// 检查密码
export const checkPasswordAsync = async (password: string, config?: IRequestRequestConfig) => {
  return checkPassword(password, config) as unknown as IServerResponse<IRowValue>;
};

// 发送手机号绑定短信
export const sendBindMsgByPhoneAsync = async (data: ISendBindMsgByPhone, config?: IRequestRequestConfig) => {
  return sendBindMsgByPhone(data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 手机验证
export const verifyByPhoneAsync = async (vcode: string, config?: IRequestRequestConfig) => {
  return verifyByPhone(vcode, config) as unknown as IServerResponse<IEmptyObj>;
};

// 发送邮箱号绑定短信
export const sendBindMsgByEmailAsync = async (data: ISendBindMsgByEmail, config?: IRequestRequestConfig) => {
  return sendBindMsgByEmail(data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 邮箱验证
export const verifyByEmailAsync = async (vcode: string, config?: IRequestRequestConfig) => {
  return verifyByEmail(vcode, config) as unknown as IServerResponse<IEmptyObj>;
};

// 解除绑定
export const unbindWechatAsync = async (id: number, config?: IRequestRequestConfig) => {
  return unbindByWechat(id, config) as unknown as IServerResponse<IEmptyObj>;
};

// 强制重置密码
export const resetPasswordAsync = async (userId: number, password: string, config?: IRequestRequestConfig) => {
  return resetPassword(userId, password, config) as unknown as IServerResponse<IEmptyObj>;
};

// 解除锁定用户
export const unlockAsync = async (userId: number, config?: IRequestRequestConfig) => {
  return unlock(userId, config) as unknown as IServerResponse<IEmptyObj>;
};

export const unbindDingtalkAsync = async (id: number, config?: IRequestRequestConfig) => {
  return unbindByDingtalk(id, config) as unknown as IServerResponse<IEmptyObj>;
};

// 获取用户分享的资源
export const getShareResourcePermissionsAsync = async (
  userUuid: string,
  config?: IRequestRequestConfig<IShareResourcePermissionsQuery>,
) => {
  return getShareResourcePermissions(userUuid, config) as unknown as IServerResponse<IShareResourcePermissions[]>;
};

// 修改用户资源权限
export const patchShareResourcePermissionsAsync = async (
  userUuid: string,
  data: IShareResourcePermissionsPatch,
  config?: IRequestRequestConfig,
) => {
  return patchShareResourcePermissions(userUuid, data, config) as unknown as IServerResponse<IEmptyObj>;
};

// 获取用户的角色信息
export const postQueryRoleIdsByUserIdsAsync = async (data: IUserIdRoleIdQuery, config?: IRequestRequestConfig) => {
  return postQueryRoleIdsByUserIds(data, config) as unknown as IServerResponse<IUserIdRoleId[]>;
};

// 获取用户的角色信息
export const getUserRolesAsync = async (params?: IUserRolesParams, config?: IRequestRequestConfig) => {
  return getUserRoles(params, config) as unknown as IServerResponse<IUserRoleItem[]>;
};

export const postUsersQueryAsync = async (params: IPostUsersQuery, config?: IRequestRequestConfig) => {
  return postUsersQuery(params, config) as unknown as IServerResponse<any[]>;
};
