import {
  postFormilyCheckRowsBySchema,
  postOneTableNotices,
  postOneTableUpdateAuthorizationOfGrantedForm,
  postOuterApi,
} from '@mdtApis/api';
import type {
  IFormilyCheckRowsBySchemaBody,
  IIFormilyCheckRowsBySchemaResult,
  IOneTableNoticesBody,
  IOneTableNoticesResult,
  IOneTableUpdateAuthorizationOfGrantedFormBody,
  IRequestRequestConfig,
  IServerResponse,
} from './interfaces';

export const postOneTableNoticesAsync = async (data: IOneTableNoticesBody, config?: IRequestRequestConfig) => {
  return postOneTableNotices(data, config) as unknown as IServerResponse<IOneTableNoticesResult>;
};

export const postOneTableUpdateAuthorizationOfGrantedFormAsync = async (
  data: IOneTableUpdateAuthorizationOfGrantedFormBody,
  config?: IRequestRequestConfig,
) => {
  return postOneTableUpdateAuthorizationOfGrantedForm(data, config) as unknown as IServerResponse<string>;
};

export const postFormilyCheckRowsBySchemaAsync = async (
  data: IFormilyCheckRowsBySchemaBody,
  config?: IRequestRequestConfig,
) => {
  return postFormilyCheckRowsBySchema(data, config) as unknown as IServerResponse<IIFormilyCheckRowsBySchemaResult>;
};

export const postOuterApiAsync = async (data: any, config?: IRequestRequestConfig) => {
  return postOuterApi(data, { skipResponseInterceptor: true, ...(config || {}) });
};
