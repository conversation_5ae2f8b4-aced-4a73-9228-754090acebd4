import _ from 'lodash';
import { FC } from 'react';
import { ChevronsLeft } from '@metro/icons';
import type { PaginationProps } from '@metroDesign/pagination';
import { type IControllerOptions as IDataListControllerOptions } from '@mdtBsControllers/data-list-controller';
import { FolderController, IFolderControllerOptions } from '@mdtBsControllers/folder-controller';
import i18n from '../languages';

export interface ICompFolderType {
  onFolderCreate?: (obj: any) => void;
  stopFolderContextMenu?: boolean;
  onFolderNavClick?: (path: string) => void;
}
export interface ICO extends ICompFolderType {
  [key: string]: any;
}

export type IClickItemFunc<V = any> = (item: V, index: number) => void;

export type IDynamicOptions<CO extends ICO> = ((params?: any) => CO) | CO;

export interface IControllerPaginationOptions {
  pagination?: boolean;
  paginationAtBefore?: boolean;
  paginationProps?: IPaginationProps;
  initialPageNum?: number;
}

export type IPaginationProps = Omit<PaginationProps, 'pageSize' | 'current' | 'onChange' | 'total'>;

export interface IControllerOptions<CO extends ICO, V = any> extends IControllerPaginationOptions {
  dataListControllerOptions: IDataListControllerOptions<V>;
  folderControllerOptions?: IFolderControllerOptions;
  compOptions: IDynamicOptions<CO>;
  EmptyView?: FC;
  onClickItem?: IClickItemFunc<V>;
  hideFolderHeader?: boolean;
}
class DataListCompController<CO extends ICO, V = any> extends FolderController<V> {
  private compOptions?: IDynamicOptions<CO>;
  private EmptyView?: FC;
  private onClickItem?: IClickItemFunc<V>;
  private pagination?: boolean;
  private paginationAtBefore?: boolean;
  private paginationProps?: IPaginationProps;
  private hideFolderHeader?: boolean;

  public constructor(options: IControllerOptions<CO, V>) {
    super({
      dataListControllerOptions: {
        pageSize: options.paginationProps?.defaultPageSize,
        initialPageNum: options.initialPageNum,
        ...options.dataListControllerOptions,
      },
      folderControllerOptions: options.folderControllerOptions,
    });

    this.pagination = options.pagination;
    this.paginationAtBefore = options.paginationAtBefore;
    this.paginationProps = options.paginationProps;
    this.compOptions = options.compOptions;
    this.EmptyView = options.EmptyView;
    this.onClickItem = options.onClickItem;
    this.hideFolderHeader = options.hideFolderHeader;
    !(this.pagination || this.isAllData$.getValue()) && this.computeVisibleLoadingNextTip();
  }

  public destroy() {
    this.compOptions = undefined;
    this.EmptyView = undefined;
    this.paginationProps = undefined;
    this.onClickItem = undefined;
  }

  public isPaginationMode() {
    return this.pagination;
  }

  public getHideFolderHeader() {
    return this.hideFolderHeader;
  }

  public clickPagination(pageNum: number) {
    this.changeCurrentPage(pageNum);
    this.changeLoadingNext(true);
    this.requestMoreData();
  }

  // 复写loadMoreDataList
  public loadMoreDataList(params?: any) {
    if (this.pagination) return;
    super.loadMoreDataList(params);
  }

  public getPaginationInfo() {
    return {
      paginationProps: this.paginationProps,
      paginationAtBefore: this.paginationAtBefore,
    };
  }

  public handleClickItem(item: V, index: number) {
    this.onClickItem?.(item, index);
  }

  public getCompOptions() {
    return ((_.isFunction(this.compOptions) ? this.compOptions() : this.compOptions) || {}) as CO;
  }

  public getEmptyView() {
    return this.EmptyView;
  }

  public enableFolderContextMenu() {
    return !this.getCompOptions().stopFolderContextMenu && this.getEnableFolder();
  }

  public getFolderPathBreadCrumbItems = () => {
    const currentPath = this.getCurrentFolderPath();
    const itemsTitle = (path: string, isRoot?: boolean) => (
      <a>
        {isRoot && currentPath ? <ChevronsLeft /> : null}
        {_.last(path.split('/'))}
      </a>
    );
    const itemsPath = (path: string) => (this.getBreadcrumbJumpType() === 'state' ? undefined : path);
    return [
      {
        title: itemsTitle(i18n.chain.comFolder.home, true),
        path: itemsPath(''),
        onClick: () => {
          if (!currentPath) {
            return;
          }
          this.getCompOptions().onFolderNavClick?.('');
        },
      },
      ..._.map(this.getFolderPathArr(), (path) => ({
        title: itemsTitle(path),
        path: itemsPath(path),
        key: path,
        onClick: () => {
          if (_.isEqual(currentPath, path)) {
            return;
          }
          this.getCompOptions().onFolderNavClick?.(path);
        },
      })),
    ];
  };

  protected addMoreDataToList(list: V[]) {
    if (this.pagination) {
      this.getDataList$().next([...list]);
      this.changeLoadingNext(false);
      return;
    }
    super.addMoreDataToList(list);
  }
}

export { DataListCompController };
