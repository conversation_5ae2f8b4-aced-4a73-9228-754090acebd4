import { FC, Fragment } from 'react';
import { type ItemType, Breadcrumb } from '@metroDesign/breadcrumb';
import { Pagination } from '@metroDesign/pagination';
import { RightClickFolderMenu } from '@mdtBsComm/components/right-click-folder-menu';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Spin from '@mdtDesign/spin';
import i18n from '../languages';
import { type ICO, DataListCompController } from './DataListCompController';
import './index.less';

export interface IProps<CO extends ICO, V = any> {
  controller: DataListCompController<CO, V>;
  AfterLoadingView: FC<any>;
  className?: string;
}

const PaginationLoading = ({ controller }: { controller: DataListCompController<any> }) => {
  const loading = useObservableState(() => controller.getLoadingNext$());
  return loading ? (
    <Spin className="data-list-comp_pagination_loading" spinning={true} tip={i18n.chain.comDataLoading} />
  ) : null;
};

const PaginationWrap = ({
  controller,
  paginationProps,
}: {
  controller: DataListCompController<any>;
  paginationProps: any;
}) => {
  const total = useObservableState(() => controller.getPageTotal$());
  const current = useObservableState(() => controller.getCurrentPage$());
  const pageSize = controller.getPageSize();

  return (
    <Pagination
      {...(paginationProps || {})}
      total={total}
      current={current + 1}
      pageSize={pageSize}
      showSizeChanger={false}
      onChange={(page) => controller.clickPagination(page - 1)}
    />
  );
};

const AfterLoadingViewInner = ({ controller, AfterLoadingView }: IProps<any, any>) => {
  const enableContextMenu = controller.enableFolderContextMenu();
  const { onFolderCreate } = controller.getCompOptions();
  const isPm = controller.isPaginationMode();

  const comps = [
    <RightClickFolderMenu key="view" off={!enableContextMenu} onChange={onFolderCreate}>
      <AfterLoadingView />
    </RightClickFolderMenu>,
  ];

  if (isPm) {
    const { paginationProps, paginationAtBefore } = controller.getPaginationInfo();
    const pw = <PaginationWrap key="pw" paginationProps={paginationProps} controller={controller} />;
    paginationAtBefore ? comps.unshift(pw) : comps.push(pw);
    comps.push(<PaginationLoading key="pw-loading" controller={controller} />);
  }

  return <Fragment key="1">{comps}</Fragment>;
};

export const FolderBreadView: FC<{ items: ItemType[] }> = ({ items }) => {
  return <Breadcrumb className="component_data-list-comp-breadcrumb" items={items} />;
};

// 表格==============================================================================================
export function DataListComp<CO extends ICO, V = any>({ controller, AfterLoadingView, className }: IProps<CO, V>) {
  const loading = useObservableState(() => controller.getDataListLoading$());

  const comp = loading ? (
    <div className="data-list-comp_loading">
      <Spin spinning={true} tip={i18n.chain.comDataLoading} />
    </div>
  ) : (
    <AfterLoadingViewInner controller={controller} AfterLoadingView={AfterLoadingView} />
  );

  return (
    <>
      {controller.getEnableFolder() && !controller.getHideFolderHeader() ? (
        <FolderBreadView items={controller.getFolderPathBreadCrumbItems()} />
      ) : null}
      <div className={`component_data-list-comp ${className || ''}`}>{comp}</div>
    </>
  );
}
