import _ from 'lodash';
import { useMemo, useRef, useState } from 'react';
import { Flex } from '@metroDesign/flex';
import { type SortItem, applyTransforms, commonTransforms } from 'ali-react-table-fork/biz';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { isPc } from '@mdtBsComm/utils';
import { BaseTable, VirtualizedTable } from '@mdtDesign/table';
import { ColumnSetting } from '../column-setting';
import { FolderBreadView as DataListCompFolderBreadView } from '../data-list-comp';
import { DataListComp } from '../data-list-comp';
import i18n from '../languages';
import { useDataListCompTableContext, useDataListCompTableProvider } from './dataListCompTableContext';
import { DataListCompTableController } from './DataListCompTableController';
import './index.less';

const baseStyle = {
  '--color': 'var(--metro-text-1)',
  '--bgcolor': 'var(--metro-bg-0)',
  '--header-color': 'var(--metro-text-2)',
  '--header-bgcolor': 'var(--metro-secondary-1)',
  '--border-color': 'var(--metro-divider-1)',
  '--row-hover-color': 'var(--metro-secondary-1)',
};

// 默认表头组件
export const ColumnSettingView = () => {
  const { dataListCompTableController: controller } = useDataListCompTableContext();
  const columnSettingController = controller.getColumnSettingController();
  const showColumnSetting = controller.getShowColumnSetting();
  if (!showColumnSetting || !columnSettingController) {
    return null;
  }
  return (
    <div className="component_data-list-table-header_column-setting">
      <ColumnSetting controller={columnSettingController} />
    </div>
  );
};

export const FolderBreadView = () => {
  const { dataListCompTableController: controller } = useDataListCompTableContext();
  useObservableState(() => controller.getFolderPath$());
  if (!controller.getEnableFolder()) {
    return null;
  }
  return <DataListCompFolderBreadView items={controller.getFolderPathBreadCrumbItems()} />;
};

// 子组件--虚拟表格===================================================================================
const KEY = '__SPECIAL_LOADING_ROW';
const AfterLoadingView = () => {
  const [, setFlush] = useState<number>(0);
  const columnWithsRef = useRef<number[]>([]);
  const { dataListCompTableController: controller } = useDataListCompTableContext();
  // 选中元素变化后刷新整个列表
  useObservableState(() => controller.getSelectedDataList$());
  const data = useObservableState(() => controller.getDataList$());
  const visible = useObservableState(() => controller.getVisibleLoadingNextTip$());
  const updatedColumns = useObservableState(() => controller.getColumnsUpdated$());

  const {
    className,
    emptyContent,
    style,
    useBaseTable,
    enableSort,
    sortMode,
    showColumnSetting,
    columns,
    ...resetProps
  } = controller.getCompOptions();
  const currentColumns = showColumnSetting ? updatedColumns : columns;
  const mergedUseBaseTable = useBaseTable ?? !isPc();
  const displayData = visible ? [..._.slice(data, 0, -1), { ..._.last(data), [KEY]: true }] : data;
  const cls = [visible ? 'component_data-list-table_loading' : '', className || '', 'component_data-list-table'].join(
    ' ',
  );
  const loadingRender = (v: boolean) => {
    v === true && controller.loadMoreDataList();
    return '';
  };
  // 如果用户提供的最后一列需要lock,分页需要的column也需要lock，不然用户的lock无效
  const lockLastColumn = _.get(_.last(currentColumns), 'lock');
  const displayColumns = currentColumns || [];
  const widthSize = _.size(columnWithsRef.current);
  if (widthSize !== displayColumns.length) {
    columnWithsRef.current = [];
  }
  const transforms = [
    commonTransforms.columnResize({
      sizes: widthSize ? columnWithsRef.current : _.map(displayColumns, (it: any) => it.width || 120),
      disableUserSelectWhenResizing: true,
      onChangeSizes(nextSizes: number[]) {
        columnWithsRef.current = nextSizes;
        setFlush(Math.random());
      },
    }),
  ];
  enableSort &&
    transforms.push(
      commonTransforms.sort({
        sorts: _.map(displayColumns, (it: any) => it.features?.sortable).filter(_.isObject) as SortItem[],
        mode: sortMode || 'single',
        onChangeSorts(nextSorts: SortItem[]) {
          controller.loadDataList({ sorts: nextSorts });
        },
      }),
    );
  const tableInfo = applyTransforms({ columns: displayColumns, dataSource: displayData }, ...transforms);
  // 设置最后一行自动加载
  tableInfo.columns = [
    ...tableInfo.columns,
    { name: '', code: KEY, width: 0, render: loadingRender, lock: lockLastColumn },
  ];

  const emptyContentState = useMemo(() => {
    const EmptyView = controller.getEmptyView();
    return EmptyView ? <EmptyView /> : emptyContent ? emptyContent : <div>{i18n.chain.comNoData}</div>;
  }, [controller, emptyContent]);

  const mergedStyle = {
    ...(mergedUseBaseTable ? baseStyle : {}),
    ...(style || {}),
  };

  const ViewDom = mergedUseBaseTable ? BaseTable : VirtualizedTable;
  return (
    <ViewDom
      {...resetProps}
      style={mergedStyle}
      className={cls}
      dataSource={tableInfo.dataSource}
      columns={tableInfo.columns}
      emptyContent={emptyContentState}
      getRowProps={(record: any, rowIndex: number) => {
        return {
          onClick: () => controller.handleClickItem(record, rowIndex),
        };
      }}
    />
  );
};

// 表格==============================================================================================
interface IProps<V = any> {
  controller: DataListCompTableController<V>;
  className?: string;
}
export function DataListCompTable<V = any>(props: IProps<V>) {
  const Provider = useDataListCompTableProvider();
  const value = { dataListCompTableController: props.controller };
  const { headerRender } = props.controller.getCompOptions();
  // 因为这里处理了column,所以要在controller渲染的时候 去执行一次，保证column拿的到
  props.controller.getColumnSettingController();

  const headerComponent = headerRender?.(ColumnSettingView, FolderBreadView) ?? (
    <Flex justify="space-between" align="center">
      <div>
        <FolderBreadView />
      </div>
      <ColumnSettingView />
    </Flex>
  );

  return (
    <Provider value={value}>
      {headerComponent}
      <DataListComp {...props} AfterLoadingView={AfterLoadingView} />
    </Provider>
  );
}
