import _ from 'lodash';
import { FC, ReactNode } from 'react';
import { BehaviorSubject } from 'rxjs';
import { VirtualizedTableProps } from '@mdtDesign/table/VirtualizedTable';
import { ColumnSettingController, ColumnSettingModel, IColumn } from '../column-setting';
import { DataListCompController, IControllerOptions } from '../data-list-comp';
import i18n from '../languages';

export const OPERATION_COLUMN_CODE = '__operation__';
export const SELECT_COLUMN_CODE = '__select__';

export type IVirtualizedTableProps = Omit<VirtualizedTableProps, 'dataSource'> & {
  // 为了良好的展示效果，默认pc端使用VirtualizedTable，移动端使用BaseTable
  useBaseTable?: boolean;
  sortMode?: 'single' | 'multiple';
  enableSort?: boolean;
  // 自定义头部渲染
  headerRender?: (ColumnSettingView: FC, FolderBreadView: FC) => ReactNode;
  /** 显示列设置 */
  showColumnSetting?: boolean;
  /** 列设置id，如果传入则带有记忆功能
   * 这里的id的跟随状态与你传入的值有关，比如你可以传入用户id，那么这个id的跟随状态就是用户id的跟随状态；同样的如果是针对某个人的某个列表，可以组合为userId_listId状态
   */
  columnSettingId?: string;
};

class DataListCompTableController<V = any> extends DataListCompController<IVirtualizedTableProps, V> {
  private columnSettingController?: ColumnSettingController;
  private columnsUpdated$ = new BehaviorSubject<IColumn[]>([]);
  private showColumnSetting?: boolean;

  public constructor(options: Omit<IControllerOptions<IVirtualizedTableProps, V>, 'hideFolderHeader'>) {
    super({ ...options, hideFolderHeader: true });
  }

  public destroy() {
    super.destroy();
    this.columnSettingController?.destroy();
    this.columnSettingController = undefined;
    this.columnsUpdated$.complete();
  }

  public getColumnSettingController() {
    const { columns, columnSettingId, showColumnSetting } = this.getCompOptions() || {};
    if (!this.columnSettingController) {
      this.showColumnSetting = showColumnSetting;

      this.columnSettingController = new ColumnSettingController({
        Model: ColumnSettingModel,
        columns: this.transformColumnsForSetting(columns),
        onColumnChange: (visibleColumns) => {
          this.columnsUpdated$.next(this.transformColumnsForDisplay(visibleColumns, columns));
        },
        id: columnSettingId,
        initOnChange: !showColumnSetting,
      });
      return this.columnSettingController;
    }
    return this.columnSettingController;
  }

  public getShowColumnSetting() {
    return this.showColumnSetting;
  }

  public getColumnsUpdated$() {
    return this.columnsUpdated$;
  }

  /**
   * 转换列用于设置面板
   * 1. 更新列属性
   * 2. 排除特定列
   */
  private transformColumnsForSetting(columns: any[] = []) {
    return _.flow(
      (cols) => this.transformColumn(cols, OPERATION_COLUMN_CODE, { title: i18n.chain.comOperation }),
      (cols) => this.excludeColumn(cols, SELECT_COLUMN_CODE),
    )(columns);
  }

  /**
   * 转换列用于表格显示
   * 1. 更新列属性
   * 2. 恢复排除的列
   */
  private transformColumnsForDisplay(settingColumns: any[] = [], originalColumns: any[] = []) {
    return _.flow(
      (cols) => this.transformColumn(cols, OPERATION_COLUMN_CODE, { title: '' }),
      (cols) => this.restoreColumn(cols, originalColumns, SELECT_COLUMN_CODE),
    )(settingColumns);
  }

  /**
   * 转换列属性
   * @param columns 列数组
   * @param columnCode 需要转换的列代码
   * @param props 要更新的属性
   */
  private transformColumn(columns: any[], columnCode: string, props: Record<string, any>) {
    return _.map(columns, (col) => (col.code === columnCode ? { ...col, ...props } : col));
  }

  /**
   * 排除特定列
   * @param columns 列数组
   * @param columnCode 要排除的列代码
   */
  private excludeColumn(columns: any[], columnCode: string) {
    return _.filter(columns, (col) => col.code !== columnCode);
  }

  /**
   * 恢复特定列到原始位置
   * @param columns 当前列数组
   * @param originalColumns 原始列数组
   * @param columnCode 要恢复的列代码
   */
  private restoreColumn(columns: any[], originalColumns: any[] = [], columnCode: string) {
    if (!originalColumns.length) return columns;

    const result = _.clone(columns);

    _.forEach(originalColumns, (col, index) => {
      if (col.code === columnCode) {
        result.splice(Math.min(index, result.length), 0, col);
      }
    });

    return result;
  }
}

export { DataListCompTableController };
