import _ from 'lodash';
import { CSSProperties, FC, ReactNode } from 'react';
import { AsyncSubject, BehaviorSubject } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { Draggable } from '@mdtBsComm/components/draggable';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { ButtonProps, IconButton, IconButtonProps, LinkButtonProps } from '@mdtDesign/button';
import Checkbox from '@mdtDesign/checkbox';
import { Dropmenu, MenuItemProps } from '@mdtDesign/dropdown';
import type { SelectProps } from '@mdtDesign/select';
import { ArtColumn } from '@mdtDesign/table';
import {
  IClickItemFunc,
  ICompFolderType,
  IControllerPaginationOptions,
  IFolderControllerOptions,
} from '../data-list-comp';
import {
  DataListCompTableController,
  IDataListControllerOptions,
  IDynamicOptions,
  IVirtualizedTableProps,
  OPERATION_COLUMN_CODE,
  SELECT_COLUMN_CODE,
} from '../data-list-comp-table';
import { FolderComp, FolderCompController } from '../folder-comp';
import i18n from '../languages';
import { SelectEditor } from './select-editor/SelectEditor';
import { ITextEditorProps, TextEditor } from './text-editor/TextEditor';

type IDynamicInputProps<V> = Omit<ITextEditorProps, 'value'> | ((r: V) => Omit<ITextEditorProps, 'value'>);
interface IInputOptions<V = any> {
  type: 'input';
  inputProps?: IDynamicInputProps<V>;
}
type ISelectProps<V> = Omit<SelectProps, 'onChange'> & {
  onChange?: (val: any, newRecord: V, oldRecord: V) => void;
};
type IDynamicSelectProps<V> = ISelectProps<V> | ((r: V) => ISelectProps<V>);
interface ISelectOptions<V = any> {
  type: 'select';
  selectProps: IDynamicSelectProps<V>;
}

export type ITableCurdColumn<V = any> = ArtColumn &
  ICompFolderType & {
    editOptions?: IInputOptions<V> | ISelectOptions<V>;
  };
export type IVirtualizedTableCurdProps<V = any> = Omit<IVirtualizedTableProps, 'columns'> &
  ICompFolderType & {
    columns: ITableCurdColumn<V>[];
  };

type IMenuItemProps = MenuItemProps & { icon?: string };
export type IMoreItems<V = any> = IMenuItemProps[] | ((item: V) => IMenuItemProps[]);
export type IFolderMoreItems<V = any> = IMenuItemProps[] | ((item: V, menus: IMenuItemProps[]) => IMenuItemProps[]);
export type IOtherBtns<V = any> = ReactNode | ((item: V) => ReactNode);
type IBtnProps = ButtonProps | LinkButtonProps | IconButtonProps;
// enable控制显示或隐藏
export interface ICurdOptions<V = any> extends ICompFolderType {
  enableCreate?: boolean;
  createProps?: IBtnProps;
  enableEdit?: boolean;
  editProps?: IBtnProps;
  enableDelete?: boolean;
  deleteProps?: IBtnProps;
  moreItems?: IMoreItems<V>;
  otherBtns?: IOtherBtns<V>;
  columnWidth?: number;
  enableSelect?: boolean;
  /** 当enableSelect开启后，默认开启，可以在配置项中选择关闭 */
  enableAllSelect?: boolean;
  /** 传入则开启拖拽，需要拖拽的标识 */
  dragCode?: string;
  /** 自定义拖拽渲染 */
  DragRender?: FC<{ data: V }>;
  /** 外置一个操作栏开关 */
  enableOperation?: boolean;
  /** 文件夹更多菜单 */
  folderMoreItems?: IFolderMoreItems<V>;
  /** 覆盖操作栏style */
  overrideOperationStyle?: CSSProperties;
}
export interface IMoreReset<V = any> extends IBusinessResult<V> {
  actionType?: keyof Record<MoreActionType, string>;
}
export type IClickBtnFunc<V = any> = (item?: V) => AsyncSubject<IBusinessResult<V>>;
export type IClickMoreBtnFunc<V = any> = (menuKey: string, item?: V) => AsyncSubject<IMoreReset<V>>;
export type IDragFunc<V = any> = (current: V, target: V[]) => void;
export type IDragCompFunc<V = any> = (path: string, dragItems: V[]) => void;

export interface IControllerOptions<V = any> extends IControllerPaginationOptions {
  dataListControllerOptions: IDataListControllerOptions<V>;
  folderControllerOptions?: IFolderControllerOptions;
  onClickItem?: IClickItemFunc<V>;
  tableOptions: IDynamicOptions<IVirtualizedTableCurdProps<V>>;
  curdOptions?: IDynamicOptions<ICurdOptions<V>>;
  // 新增回调
  clickCreateBtnFunc?: IClickBtnFunc<V>;
  // 更新回调
  clickEditBtnFunc?: IClickBtnFunc<V>;
  // 删除回调
  clickDeleteBtnFunc?: IClickBtnFunc<V>;
  // 点击更多回调
  clickMoreBtnFunc?: IClickMoreBtnFunc<V>;
  // 拖动开始回调
  onDragStartFunc?: IDragFunc<V>;
  // 拖动结束回调
  onDragOverFunc?: IDragCompFunc<V>;
  // 文件夹动作回调
  folderActionCallbackFunc?: (path: string, record: V) => void;
}
// 最新选中或取消选中的对象
interface ILatestSelectedItem<V> {
  selected: boolean;
  item: V;
}

export enum MoreActionType {
  ADD = 'add',
  EDIT = 'edit',
  DELETE = 'delete',
  NOTHING = 'nothing',
}

export enum FolderActionType {
  MOVE = 'folderMove',
  DELETE = 'folderDelete',
  RENAME = 'folderRename',
}
const operationStyle = {
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'space-evenly',
  width: '100%',
  height: '100%',
};

class DataListCompTableCurdController<V = any> extends DataListCompTableController<V> {
  private defaultModifyRslt = new AsyncSubject<IBusinessResult<V>>();
  private tableOptions: IDynamicOptions<IVirtualizedTableCurdProps>;
  private curdOptions?: IDynamicOptions<ICurdOptions<V>>;
  private clickCreateBtnFunc: IClickBtnFunc<V>;
  private clickEditBtnFunc: IClickBtnFunc<V>;
  private clickDeleteBtnFunc: IClickBtnFunc<V>;
  private clickMoreBtnFunc: IClickMoreBtnFunc<V>;
  private onDragStartFunc: IDragFunc<V>;
  private onDragEndFunc: IDragFunc<V>;
  private onDragOverFunc: (name: string, path: string) => void;
  private folderActionCallbackFunc: IControllerOptions['folderActionCallbackFunc'];
  private curdOptionsCache?: ICurdOptions<V>;
  private latestSelectedItem$ = new BehaviorSubject<ILatestSelectedItem<V> | undefined>(undefined);

  public constructor(options: IControllerOptions<V>) {
    super({
      dataListControllerOptions: options.dataListControllerOptions,
      folderControllerOptions: options.folderControllerOptions,
      onClickItem: options.onClickItem,
      pagination: options.pagination,
      paginationAtBefore: options.paginationAtBefore,
      paginationProps: options.paginationProps,
      compOptions: () => this.initTableOptions(),
    });
    this.tableOptions = options.tableOptions;
    this.curdOptions = options.curdOptions;
    this.clickCreateBtnFunc = options.clickCreateBtnFunc || this.defaultClickBtnFunc;
    this.clickEditBtnFunc = options.clickEditBtnFunc || this.defaultClickBtnFunc;
    this.clickDeleteBtnFunc = options.clickDeleteBtnFunc || this.defaultClickBtnFunc;
    this.clickMoreBtnFunc = options.clickMoreBtnFunc || this.defaultClickBtnFunc;
    this.onDragStartFunc = (current: V, target: V[]) => {
      this.changeDraggingDataList(target);
      return options.onDragStartFunc?.(current, target);
    };
    this.onDragEndFunc = () => {
      this.changeDraggingDataList([]);
    };
    this.onDragOverFunc = (name: string, path: string) => {
      const draggingArr: any[] = this.getDragingDataListValue();
      const isDragSelf = !!_.find(draggingArr, ({ path: draggingPath }) => _.isEqual(draggingPath, path));
      // 不允许自己拖动自己
      if (isDragSelf) {
        return;
      }
      return options.onDragOverFunc?.(path, this.getDragingDataListValue());
    };
    this.folderActionCallbackFunc = options.folderActionCallbackFunc || this.defaultFolderActionCallback;
    this.listenSelectedChange();
  }

  public destroy() {
    super.destroy();
    this.latestSelectedItem$.complete();
    this.latestSelectedItem$.next(undefined);
    this.defaultModifyRslt.complete();
    this.tableOptions = this.defaultEmptyObj;
    this.curdOptions = this.defaultEmptyObj;
    this.clickCreateBtnFunc = this.defaultClickBtnFunc;
    this.clickEditBtnFunc = this.defaultClickBtnFunc;
    this.clickDeleteBtnFunc = this.defaultClickBtnFunc;
    this.clickMoreBtnFunc = this.defaultClickBtnFunc;
    this.curdOptionsCache = undefined;
  }

  public getCurdOptions(): ICurdOptions<V> {
    return (_.isFunction(this.curdOptions) ? this.curdOptions() : this.curdOptions) || {};
  }

  public onClickCreateBtn() {
    this.clickCreateBtnFunc()
      .pipe(
        takeWhile((v) => v.success && !!v.result),
        map((v) => v.result!),
      )
      .subscribe((result) => {
        this.addDataToList(result);
      });
  }

  public onClickEditBtn(data: V) {
    this.clickEditBtnFunc(data)
      .pipe(
        takeWhile((v) => v.success),
        map((v) => v.result!),
      )
      .subscribe((result) => {
        this.editDataInList(result);
      });
  }

  public onClickDeleteBtn(data: V) {
    this.clickDeleteBtnFunc(data)
      .pipe(takeWhile((v) => v.success))
      .subscribe(() => {
        this.deleteDataFromList(data);
      });
  }

  public onClickMoreBtn(menuKey: string, data: V) {
    // TODO 优化类似的情况，应该定义好asyncsubject, 由用户返回结果即可
    this.clickMoreBtnFunc(menuKey, data)
      .pipe(takeWhile((v) => v.success))
      .subscribe(({ result, actionType }) => {
        if (actionType === MoreActionType.ADD && result) {
          return this.addDataToList(result);
        }
        if (actionType === MoreActionType.EDIT && result) {
          return this.editDataInList(result);
        }
        if (actionType === MoreActionType.DELETE) {
          return this.deleteDataFromList(data);
        }
      });
  }

  public getLatestSelectedItem$() {
    return this.latestSelectedItem$;
  }

  protected listenSelectedChange() {
    this.getLatestSelectedItem$().subscribe((selectedItem) => {
      if (!selectedItem) return;
      const { selected, item } = selectedItem;
      selected ? this.addToSelectedDataList(item) : this.removeFromSelectedDataList(item);
    });
  }

  // 渲染表头所需信息
  // eslint-disable-next-line sonarjs/cognitive-complexity
  private initTableOptions = (): IVirtualizedTableCurdProps => {
    const props = _.isFunction(this.tableOptions) ? this.tableOptions() : this.tableOptions;
    const {
      enableDelete,
      deleteProps,
      enableEdit,
      editProps,
      moreItems,
      folderMoreItems,
      otherBtns,
      columnWidth = 100,
      enableSelect,
      dragCode,
      DragRender,
      enableOperation,
      enableAllSelect = true,
      overrideOperationStyle = {},
      ...rest
    } = this.getCurdOptions();
    const isMoreItemsFunc = _.isFunction(moreItems);
    const hasMore = _.size(moreItems) > 0 || isMoreItemsFunc;
    const isFolderMoreItemsFunc = _.isFunction(folderMoreItems);
    const hasFolderMore = _.size(folderMoreItems) > 0 || isFolderMoreItemsFunc;
    const isOtherBtnsFunc = _.isFunction(otherBtns);
    const isEnableAllSelect = enableSelect && enableAllSelect;
    // 自定义编辑数据
    props.columns = _.map(props.columns, (column) => {
      const { type } = column.editOptions || {};
      if (type === 'input') {
        return {
          ...column,
          render: (value: string, r: V) => {
            const { inputProps } = (column.editOptions || {}) as IInputOptions;
            const realProps = (_.isFunction(inputProps) ? inputProps(r) : inputProps) as any;
            return (
              <TextEditor
                value={value}
                onComplete={(val) => {
                  this.editDataInList({ ...r, [column.code || '']: val });
                }}
                {...realProps}
              />
            );
          },
        };
      }
      if (type === 'select') {
        return {
          ...column,
          render: (value: string, r: V) => {
            const { selectProps } = column.editOptions as ISelectOptions;
            const realProps = _.isFunction(selectProps) ? selectProps(r) : selectProps;
            const { onChange, options, ...rest } = realProps || {};
            return (
              <SelectEditor
                value={value}
                options={options}
                onChange={(val) => {
                  const newR = { ...r, [column.code || '']: val };
                  this.editDataInList(newR);
                  onChange?.(val, newR, r);
                }}
                {...rest}
              />
            );
          },
        };
      }
      if (dragCode && column.code === dragCode) {
        return {
          ...column,
          render: (value: string, r: V, index: number) => {
            const selectItems =
              enableSelect && this.getSelectedDataListValue().length ? this.getSelectedDataListValue() : [r];
            return (
              <Draggable
                off={!!this.getSingleFilterValue()}
                dragRender={DragRender ? <DragRender data={r} /> : this.getSelectDragRender(dragCode!, r)}
                dragStart={() => this.onDragStartFunc?.(r, selectItems)}
                dragEnd={() => this.onDragEndFunc?.(r, selectItems)}
              >
                <div>
                  <FolderComp
                    controller={
                      new FolderCompController({
                        data: r,
                        onDragOver: this.onDragOverFunc,
                        onActionCallback: this.folderActionCallbackFunc,
                      })
                    }
                  >
                    {column.render?.(value, r, index) || value}
                  </FolderComp>
                </div>
              </Draggable>
            );
          },
        };
      }
      return column;
    });

    // 最后一列操作功能
    const enableRightOperation = enableEdit || enableDelete || hasMore || otherBtns || hasFolderMore || enableOperation;
    if (enableRightOperation) {
      props.columns = [
        ...props.columns,
        {
          name: '',
          code: OPERATION_COLUMN_CODE,
          width: columnWidth,
          lock: true,
          align: 'right',
          render: (value: string, r: V) => {
            const isFolder = this.getEnableFolder() && (r as any).type === 'folder';
            const edtBtn =
              enableEdit && !isFolder ? (
                <IconButton
                  type="only-icon"
                  icon="edit"
                  onClick={() => this.onClickEditBtn(r)}
                  {...(editProps as LinkButtonProps)}
                />
              ) : null;

            const delBtn =
              enableDelete && !isFolder ? (
                <IconButton
                  type="only-icon"
                  icon="delete-2"
                  onClick={() => this.onClickDeleteBtn(r)}
                  {...(deleteProps as LinkButtonProps)}
                />
              ) : null;

            const defaultFolderMenus = [
              {
                title: i18n.chain.comFolder.rename,
                key: FolderActionType.RENAME,
              },
              {
                title: i18n.chain.comFolder.moveTo,
                key: FolderActionType.MOVE,
              },
              {
                title: i18n.chain.comButton.delete,
                key: FolderActionType.DELETE,
                danger: true,
              },
            ];
            const menus = hasMore && isMoreItemsFunc ? moreItems(r) : moreItems;
            const folderMenus =
              hasFolderMore && isFolderMoreItemsFunc
                ? folderMoreItems(r, defaultFolderMenus)
                : folderMoreItems ?? defaultFolderMenus;
            const mergedMenus = isFolder ? folderMenus : menus;
            const moreBtn = mergedMenus?.length ? (
              <Dropmenu
                noSelected
                menus={mergedMenus}
                icon="more"
                iconType="only-icon"
                disabled={!_.size(mergedMenus)}
                onClickMenu={(e) => this.onClickMoreBtn(e.key as string, r)}
              />
            ) : null;

            const oBtns = !isFolder ? (otherBtns && isOtherBtnsFunc ? otherBtns(r) : otherBtns) : null;

            return (
              <div style={{ ...operationStyle, ...overrideOperationStyle }}>
                {oBtns}
                {edtBtn}
                {delBtn}
                {moreBtn}
              </div>
            );
          },
        },
      ];
    }

    // 选择功能
    if (enableSelect) {
      props.columns.unshift({
        name: '',
        title: isEnableAllSelect ? (
          <Checkbox
            checked={this.checkAllSelected()}
            indeterminate={this.checkIndeterminate()}
            onChange={(checked: boolean) => {
              checked ? this.addToAllSelectedDataList() : this.removeAllSelectedDataList();
            }}
          />
        ) : (
          ''
        ),
        code: SELECT_COLUMN_CODE,
        width: 50,
        lock: true,
        render: (value: string, r: V) => {
          return (
            <Checkbox
              checked={this.checkItemSelected(r)}
              disabled={this.checkItemDisabled(r)}
              onChange={(checked: boolean) => {
                checked ? this.addToSelectedDataList(r) : this.removeFromSelectedDataList(r);
                this.latestSelectedItem$.next({
                  selected: checked,
                  item: r,
                });
              }}
            />
          );
        },
      });
    }

    return { ...props, ...rest };
  };

  // 默认返回空对象
  private defaultEmptyObj(): any {
    return {};
  }

  // 默认编辑失败
  private defaultClickBtnFunc() {
    return this.defaultModifyRslt;
  }

  // 默认文件响应
  private defaultFolderActionCallback() {
    //
  }

  // 默认的Table拖动渲染
  private getSelectDragRender = (code: string, record?: V, limitMaxCount = 99) => {
    const selectItem = this.getSelectedDataListValue();
    const current = (record as any)?.[code];
    const showDot = !_.isEmpty(selectItem) && selectItem.length > 1;
    return (
      <div
        style={{
          position: 'relative',
          padding: '6px',
          backgroundColor: 'var(--metro-fill-2)',
          borderRadius: '6px',
          color: 'var(--metro-text-1)',
          fontSize: '0.8em',
        }}
      >
        {showDot ? (
          <div
            style={{
              position: 'absolute',
              right: -16,
              top: -16,
              width: 24,
              height: 24,
              background: 'var(--metro-primary-default)',
              color: '#fff',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {selectItem.length > limitMaxCount ? `${limitMaxCount}+` : selectItem.length}
          </div>
        ) : null}
        {selectItem.length ? (selectItem[0] as any)[code] : current}
      </div>
    );
  };
}

export { DataListCompTableCurdController };
