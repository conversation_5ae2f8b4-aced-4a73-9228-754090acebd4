import _ from 'lodash';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { IColumnSettingModel } from './ColumnSettingModel';

// 简化的列结构，只保留 name 和 title
export interface IColumn {
  name: string;
  title?: any;
}

export type ColumnChangeCallback = (visibleColumns: IColumn[]) => void;

interface IControllerOptions {
  Model: IColumnSettingModel;
  columns: IColumn[];
  id?: string; // 可选的id参数
  onColumnChange?: ColumnChangeCallback;
  keyPrefix?: string; // 允许自定义存储键前缀
  initOnChange?: boolean; // 初始化时是否触发onchange
}

class ColumnSettingController {
  protected id?: string; // 可选的id参数
  protected onColumnChange?: ColumnChangeCallback;
  protected keyPrefix?: string;

  private Model: IColumnSettingModel;
  // 原始所有列
  private readonly columns$: BehaviorSubject<IColumn[]>;
  // 记录原始列顺序的映射
  private readonly originalOrderMap: Record<string, number>;
  // 搜索文本
  private readonly searchText$: BehaviorSubject<string>;
  // 可见列
  private readonly visibleColumns$: BehaviorSubject<IColumn[]>;
  // 隐藏列
  private readonly hiddenColumns$: BehaviorSubject<IColumn[]>;
  // 过滤后的可见列
  private readonly filteredVisibleColumns$: Observable<IColumn[]>;
  // 过滤后的隐藏列
  private readonly filteredHiddenColumns$: Observable<IColumn[]>;
  // 列配置是否被修改过
  private readonly isColumnModified$: BehaviorSubject<boolean>;

  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.id = options.id;
    this.onColumnChange = options.onColumnChange;
    this.keyPrefix = options.keyPrefix;
    if (options.initOnChange) {
      options.onColumnChange?.(options.columns);
    }

    this.columns$ = new BehaviorSubject<IColumn[]>(options.columns);
    // 初始化原始顺序映射
    this.originalOrderMap = _.reduce(
      options.columns,
      (result, col, index) => {
        result[col.name] = index;
        return result;
      },
      {} as any,
    );

    this.searchText$ = new BehaviorSubject<string>('');
    this.visibleColumns$ = new BehaviorSubject<IColumn[]>([]);
    this.hiddenColumns$ = new BehaviorSubject<IColumn[]>([]);
    this.isColumnModified$ = new BehaviorSubject<boolean>(false);

    // 创建过滤后的列流
    this.filteredVisibleColumns$ = combineLatest([this.visibleColumns$, this.searchText$]).pipe(
      map(([columns, searchText]) => this.filterColumns(columns, searchText)),
      distinctUntilChanged(
        (prev, curr) => prev.length === curr.length && prev.every((col, i) => col.name === curr[i].name),
      ),
    );

    this.filteredHiddenColumns$ = combineLatest([this.hiddenColumns$, this.searchText$]).pipe(
      map(([columns, searchText]) => this.filterColumns(columns, searchText)),
      distinctUntilChanged(
        (prev, curr) => prev.length === curr.length && prev.every((col, i) => col.name === curr[i].name),
      ),
    );

    this.initColumnSettings();
  }

  /**
   * 获取所有列
   */
  public getColumns$() {
    return this.columns$;
  }

  /**
   * 获取可见列作为Observable
   */
  public getVisibleColumns$() {
    return this.visibleColumns$;
  }

  /**
   * 获取过滤后的可见列
   */
  public getFilteredVisibleColumns$() {
    return this.filteredVisibleColumns$;
  }

  /**
   * 获取隐藏列作为Observable
   */
  public getHiddenColumns$() {
    return this.hiddenColumns$;
  }

  /**
   * 获取过滤后的隐藏列
   */
  public getFilteredHiddenColumns$() {
    return this.filteredHiddenColumns$;
  }

  /**
   * 获取搜索文本作为Observable
   */
  public getSearchText$() {
    return this.searchText$;
  }

  /**
   * 获取列配置是否被修改过的状态
   */
  public getIsColumnModified$() {
    return this.isColumnModified$;
  }

  /**
   * 设置搜索文本
   */
  public setSearchText(text: string): void {
    this.searchText$.next(text);
  }

  /**
   * 更新列的可见状态和顺序
   * @param visibleColumnNames 可见列名称数组，按照期望的顺序排列
   */
  public updateColumnVisibility(visibleColumnNames: string[]) {
    const allColumns = this.columns$.getValue();

    // 按照传入的顺序排序可见列
    const visibleCols = _.sortBy(
      _.filter(allColumns, (col) => _.includes(visibleColumnNames, col.name)),
      (col) => _.indexOf(visibleColumnNames, col.name),
    );

    const hiddenCols = _.filter(allColumns, (col) => !_.includes(visibleColumnNames, col.name));

    this.visibleColumns$.next(visibleCols);
    this.hiddenColumns$.next(hiddenCols);

    // 保存配置并检查是否需要清除
    this.saveOrClearColumnSetting(visibleColumnNames);

    // 调用回调
    this.notifyColumnChange();
  }

  /**
   * 切换列的显示/隐藏状态
   * @param columnName 列名
   * @param visible 是否可见
   */
  public toggleColumnVisibility(columnName: string, visible: boolean) {
    const currentVisible = this.visibleColumns$.getValue();
    const currentHidden = this.hiddenColumns$.getValue();

    if (visible) {
      // 从隐藏列移到可见列
      const column = _.find(currentHidden, (col) => col.name === columnName);
      if (column) {
        const newHidden = _.filter(currentHidden, (col) => col.name !== columnName);

        // 使用原始顺序来确定插入位置
        const newVisible = this.insertColumnAtOriginalPosition(currentVisible, column);

        this.visibleColumns$.next(newVisible);
        this.hiddenColumns$.next(newHidden);
      }
    } else {
      // 从可见列移到隐藏列
      const column = _.find(currentVisible, (col) => col.name === columnName);
      if (column) {
        const newVisible = _.filter(currentVisible, (col) => col.name !== columnName);
        const newHidden = _.concat(currentHidden, column);

        this.visibleColumns$.next(newVisible);
        this.hiddenColumns$.next(newHidden);
      }
    }

    // 获取当前可见列名称
    const visibleColumnNames = _.map(this.visibleColumns$.getValue(), 'name');

    // 保存配置并检查是否需要清除
    this.saveOrClearColumnSetting(visibleColumnNames);

    // 调用回调
    this.notifyColumnChange();
  }

  /**
   * 重置列配置到初始状态
   */
  public resetColumnSettings() {
    const allColumns = this.columns$.getValue();

    // 按照原始顺序排序所有列
    const sortedColumns = _.sortBy(allColumns, (col) => this.originalOrderMap[col.name] || 0);

    this.visibleColumns$.next(sortedColumns);
    this.hiddenColumns$.next([]);

    // 清除localStorage中的配置(如果有id)
    if (this.id) {
      this.Model.clearColumnSetting(this.id, this.keyPrefix);
    }
    this.isColumnModified$.next(false);

    // 调用回调
    this.notifyColumnChange();
  }

  public destroy() {
    // 清理资源
    this.columns$.complete();
    this.searchText$.complete();
    this.visibleColumns$.complete();
    this.hiddenColumns$.complete();
    this.isColumnModified$.complete();
  }

  /**
   * 初始化列配置
   */
  protected initColumnSettings() {
    const allColumns = this.columns$.getValue();

    // 从localStorage获取配置(如果有id)
    const savedColumnNames = this.id ? this.Model.getColumnSetting(this.id, this.keyPrefix) : null;

    if (savedColumnNames && savedColumnNames.length > 0) {
      // 有保存的配置，使用保存的配置
      // 过滤出可见列并按照savedColumnNames的顺序排序
      const visibleCols = _.sortBy(
        _.filter(allColumns, (col) => _.includes(savedColumnNames, col.name)),
        (col) => _.indexOf(savedColumnNames, col.name),
      );

      const hiddenCols = _.filter(allColumns, (col) => !_.includes(savedColumnNames, col.name));

      this.visibleColumns$.next(visibleCols);
      this.hiddenColumns$.next(hiddenCols);
      this.isColumnModified$.next(true);
    } else {
      // 按照原始顺序排序所有列
      const sortedColumns = _.sortBy(allColumns, (col) => this.originalOrderMap[col.name] || 0);

      this.visibleColumns$.next(sortedColumns);
      this.hiddenColumns$.next([]);
      this.isColumnModified$.next(false);
    }

    // 调用回调
    this.notifyColumnChange();
  }

  /**
   * 通知列变化回调
   */
  protected notifyColumnChange() {
    if (this.onColumnChange) {
      this.onColumnChange(this.visibleColumns$.getValue());
    }
  }

  /**
   * 过滤列
   * @param columns 要过滤的列
   * @param searchText 搜索文本
   * @returns 过滤后的列
   */
  protected filterColumns(columns: IColumn[], searchText: string): IColumn[] {
    if (!searchText) return columns;

    return _.filter(columns, (col) => {
      if (typeof col.title === 'string') {
        return _.includes(_.toLower(col.title || col.name), _.toLower(searchText));
      }
      return _.includes(_.toLower(col.name), _.toLower(searchText));
    });
  }

  /**
   * 保存列配置或清除配置（如果与默认状态相同）
   * @param visibleColumnNames 可见列名称数组
   */
  protected saveOrClearColumnSetting(visibleColumnNames: string[]): void {
    // 如果没有id，则不进行持久化存储
    if (!this.id) {
      return;
    }

    // 检查是否与默认状态相同（所有列都可见且顺序一致）
    const allColumns = this.columns$.getValue();
    const defaultColumnNames = _.map(
      _.sortBy(allColumns, (col) => this.originalOrderMap[col.name] || 0),
      'name',
    );

    // 比较当前顺序与默认顺序是否一致，而不仅仅是比较集合内容
    const isDefaultState =
      visibleColumnNames.length === allColumns.length &&
      _.every(visibleColumnNames, (name, index) => name === defaultColumnNames[index]);

    if (isDefaultState) {
      this.Model.clearColumnSetting(this.id, this.keyPrefix);
      this.isColumnModified$.next(false);
    } else {
      // 无论列表中是否包含ID字段，都保存当前顺序
      this.Model.saveColumnSetting(this.id, visibleColumnNames, this.keyPrefix);
      this.isColumnModified$.next(true);
    }
  }

  /**
   * 根据原始顺序插入列
   * @param currentVisible 当前可见列
   * @param columnToInsert 要插入的列
   * @returns 插入列后的新数组
   */
  private insertColumnAtOriginalPosition(currentVisible: IColumn[], columnToInsert: IColumn): IColumn[] {
    const originalIndex = this.originalOrderMap[columnToInsert.name] || 0;

    // 复制当前可见列数组
    const result = _.clone(currentVisible);

    // 找到应该插入的位置
    const insertIndex = _.findIndex(result, (col) => (this.originalOrderMap[col.name] || 0) > originalIndex);

    // 在正确位置插入列
    if (insertIndex === -1) {
      return _.concat(result, columnToInsert);
    } else {
      return [..._.slice(result, 0, insertIndex), columnToInsert, ..._.slice(result, insertIndex)];
    }
  }
}

export { ColumnSettingController };
