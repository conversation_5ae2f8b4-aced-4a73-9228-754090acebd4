import React, { FC, useCallback, useState } from 'react';
import { Search } from '@metro/icons';
import { Close, VisibilityOff1, VisibilityOn } from '@metro/icons';
import { Badge } from '@metroDesign/badge';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Input } from '@metroDesign/input';
import { Popover } from '@metroDesign/popover';
import { Scrollbar } from '@metroDesign/scrollbar';
import { SortList } from '@metroDesign/sort-list';
import { Typography } from '@metroDesign/typography';
import classNames from 'classnames';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { LinkButton } from '@mdtDesign/button';
import i18n from '../languages';
import { ColumnSettingController, IColumn } from './ColumnSettingController';
import './index.less';

interface IProps {
  controller: ColumnSettingController;
  title?: string; // 自定义标题
  buttonText?: string; // 自定义按钮文本
}

export const ColumnSetting: FC<IProps> = ({ controller, title, buttonText }) => {
  const [visible, setVisible] = useState(false);
  const isColumnModified = useObservableState(controller.getIsColumnModified$());

  const displayTitle = title || i18n.chain.bsComm.columnSetting.title;
  const displayButtonText = buttonText || i18n.chain.bsComm.columnSetting.title;

  const onVisibleChange = () => {
    setVisible(!visible);
  };

  return (
    <Badge dot={isColumnModified}>
      <Popover
        overlayClassName="container_column-setting_popover"
        placement="bottomRight"
        title={
          <Flex justify="space-between" align="center">
            <Typography.Text strong>{displayTitle}</Typography.Text>
            <Button onlyIcon icon={<Close />} ghost focus={false} onClick={onVisibleChange} />
          </Flex>
        }
        content={<ColumnSettingContent controller={controller} />}
        trigger="click"
        arrow={false}
        open={visible}
      >
        <LinkButton
          className={classNames('container_column-setting_tool-btn', 'module_filter-list_tool-btn', {
            'container_column-setting_tool-btn-active': isColumnModified,
          })}
          leftIcon="setting"
          onClick={onVisibleChange}
        >
          {displayButtonText}
        </LinkButton>
      </Popover>
    </Badge>
  );
};

interface IColumnItemProps {
  column: IColumn;
  onToggle: (columnName: string, visible: boolean) => void;
  visible?: boolean;
}

const ColumnItem: FC<IColumnItemProps> = ({ column, onToggle, visible = true }) => {
  const handleToggle = () => {
    onToggle(column.name, !visible);
  };

  return (
    <Flex justify="space-between" align="center" style={{ width: '100%' }}>
      <Typography.Text>{column.title || column.name}</Typography.Text>
      {visible ? (
        <Button.Link icon={<VisibilityOn />} onClick={handleToggle} />
      ) : (
        <Button.Link icon={<VisibilityOff1 />} onClick={handleToggle} />
      )}
    </Flex>
  );
};

const ColumnSettingContent: FC<IProps> = ({ controller }) => {
  // 直接从控制器获取状态
  const visibleColumns = useObservableState(controller.getFilteredVisibleColumns$(), []);
  const hiddenColumns = useObservableState(controller.getFilteredHiddenColumns$(), []);
  const searchText = useObservableState(controller.getSearchText$(), '');

  // 处理列的显示/隐藏切换
  const handleToggleVisibility = useCallback(
    (columnName: string, visible: boolean) => {
      controller.toggleColumnVisibility(columnName, visible);
    },
    [controller],
  );

  // 处理排序变化
  const handleVisibleOrderChange = useCallback(
    (newList: any[]) => {
      const columnNames = newList.map((item) => item.value);
      controller.updateColumnVisibility(columnNames);
    },
    [controller],
  );

  // 处理重置
  const handleReset = useCallback(() => {
    controller.resetColumnSettings();
  }, [controller]);

  // 处理搜索变化
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      controller.setSearchText(e.target.value);
    },
    [controller],
  );

  // 准备SortList的数据
  const visibleList = visibleColumns.map((col) => ({
    label: <ColumnItem column={col} onToggle={handleToggleVisibility} visible={true} />,
    value: col.name,
  }));

  const hiddenList = hiddenColumns.map((col) => ({
    label: <ColumnItem column={col} onToggle={handleToggleVisibility} visible={false} />,
    value: col.name,
  }));

  return (
    <Flex vertical gap={12} className="container_column-setting_content">
      <Flex justify="space-between" align="center" gap={8}>
        <Input.WithoutBorder
          prefix={<Search />}
          placeholder={i18n.chain.bsComm.columnSetting.searchPlaceholder}
          value={searchText}
          onChange={handleSearchChange}
          allowClear
        />
        <Button onClick={handleReset} ghost focus={false}>
          {i18n.chain.comButton.reset}
        </Button>
      </Flex>

      <Typography.Text type="secondary" strong>
        {i18n.chain.bsComm.columnSetting.visibleFields}
      </Typography.Text>
      <Scrollbar className="container_column-setting_content-scrollbar" forceVisible style={{ maxHeight: 400 }}>
        <Flex vertical gap={12}>
          {visibleList.length ? (
            <SortList list={visibleList} onChange={handleVisibleOrderChange} disabled={!!searchText} />
          ) : (
            <Typography.Text type="secondary" className="container_column-setting_content-empty-text">
              {searchText ? i18n.chain.bsComm.columnSetting.noMatch : i18n.chain.bsComm.columnSetting.noVisibleFields}
            </Typography.Text>
          )}
          {hiddenList.length ? (
            <Flex vertical gap={12}>
              <Typography.Text type="secondary" strong>
                {i18n.chain.bsComm.columnSetting.hiddenFields}
              </Typography.Text>
              <SortList list={hiddenList} disabled />
            </Flex>
          ) : null}
        </Flex>
      </Scrollbar>
    </Flex>
  );
};

export { ColumnSettingContent };
