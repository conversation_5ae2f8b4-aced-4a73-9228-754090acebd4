import { getFromStorage, removeFromStorage, saveToStorage } from '@mdtBsComm/utils/storageUtil';

class ColumnSettingModel {
  protected static KEY_PREFIX = 'dm_common_column_setting_';

  /**
   * 保存列配置到localStorage
   * @param id 配置标识(可选)
   * @param visibleColumns 可见列
   * @param keyPrefix 可选的自定义键前缀
   */
  public static saveColumnSetting(id: string | undefined, visibleColumns: string[], keyPrefix?: string) {
    if (!id) return; // 如果没有提供id，则不进行存储

    const key = this.getStorageKey(id, keyPrefix);
    saveToStorage(key, visibleColumns);
  }

  /**
   * 从localStorage获取列配置
   * @param id 配置标识(可选)
   * @param keyPrefix 可选的自定义键前缀
   * @returns 存储的配置，如果不存在则返回null
   */
  public static getColumnSetting(id: string | undefined, keyPrefix?: string): string[] | null {
    if (!id) return null; // 如果没有提供id，则不进行获取

    const key = this.getStorageKey(id, keyPrefix);
    const setting = getFromStorage(key);
    return setting ? JSON.parse(setting) : null;
  }

  /**
   * 删除localStorage中的配置
   * @param id 配置标识(可选)
   * @param keyPrefix 可选的自定义键前缀
   */
  public static clearColumnSetting(id: string | undefined, keyPrefix?: string) {
    if (!id) return; // 如果没有提供id，则不进行清除

    const key = this.getStorageKey(id, keyPrefix);
    removeFromStorage(key);
  }

  /**
   * 获取存储键名
   * @param id 配置标识
   * @param userId 用户ID
   * @param keyPrefix 可选的自定义键前缀
   * @returns 存储键名
   */
  protected static getStorageKey(id: string, keyPrefix?: string): string {
    const prefix = keyPrefix || this.KEY_PREFIX;
    return `${prefix}${id}`;
  }
}

export type IColumnSettingModel = typeof ColumnSettingModel;
export { ColumnSettingModel };
