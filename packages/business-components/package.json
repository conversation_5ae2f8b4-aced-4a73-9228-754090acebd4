{"name": "@mdt/business-components", "version": "1.19.10", "private": false, "description": "脉策通用业务组件", "keywords": ["mdt", "business", "components"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@i18n-chain/react": "2.0.1", "@mdt/business-comm": "^1.17.7", "@mdt/business-controllers": "^1.17.8", "sql-formatter": "^12.2.4"}}