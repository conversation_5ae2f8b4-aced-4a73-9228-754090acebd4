// 列表数据结构
export const FLOWORK_TASK_SIMPLE_DATA = `
task_id
status
name
bpmn_name
task_type
form_spec
data
approval_result
assign_users
create_time
update_time
workflow_id
parent_workflow_id
`;

export const FLOWORK_TASK_DATA_WITH_EXECUTOR = `
assign_groups
assign_roles
assign_users
assign_orgs
form_spec
task_id
status
name
bpmn_name
update_time
task_type
approval_result
workflow_id
data
executor
executorObj {
  name
  nickname
}
`;
export const FLOWORK_TASK_LIST_DATA_WITH_WORKFLOW = `
form_spec
data
task_id
status
name
bpmn_name
create_time
update_time
task_type
approval_result
workflow_id
workflow {
  name
  description
  spec
  form_data
  data
  initiatorObj {
    name
    nickname
  }
}
`;

export const ONE_TABLE_ROOT_DISPATCH_WORK_FLOW_LIST_DATA_WITH_DATAPKG = `
create_time
root_dispatch_workflow_id
form_name
datapkg_id
workflow {
  data,
  workflow_id
  spec
}
`;

export const FLOWORK_TASK_LIST_DATA_WITH_WORKFLOW_DATA = `
form_spec
data
task_id
status
name
bpmn_name
create_time
update_time
task_type
approval_result
workflow_id
workflow {
  parent_workflow_id
  name
  description
  spec
  form_data
  data
}
`;

export const ONE_TABLE_FLOW_TASK_DATA_WITH_EXECUTOR = `
stage
status
workflow_id
executor
name
bpmn_name
update_time
task_info
form_spec
executorObj {
  name
}
`;
