{"name": "@mdt/business-bff-services", "version": "1.21.13", "private": false, "description": "统一的bff service", "keywords": ["mdt", "business", "bff-services"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish", "gm": "node ./.scripts/generate-module.js"}, "dependencies": {"@apollo/client": "^3.6.9", "@datlas/dm-rc": "^1.5.0", "@mdt/restful-apis": "^1.36.10", "anymatch": "^3.1.2", "crypto-js": "^4.1.1", "graphql": "^15.8.0", "socket.io-client": "^4.5.1"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/socket.io-client": "^3.0.0"}}