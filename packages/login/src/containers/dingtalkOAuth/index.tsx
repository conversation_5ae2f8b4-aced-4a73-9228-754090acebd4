import { FC, useContext, useEffect, useMemo, useState } from 'react';
import { ConfigKeyEnum, IDingtalkOAuth, useConfig } from '../../config';
import { isInDingtalk, isPc } from '../../util/uaUtil';
import { usePersistFn } from '../../util/usePersistFn';
import { LoginContext } from '../global-context/context';
import './index.less';

export interface IDingtalkOAuthProps {
  selfRedirect?: boolean;
  config?: any;
}
const DingtalkOAuth: FC<IDingtalkOAuthProps> = ({ config: propsConfig }) => {
  const { privacyState } = useContext(LoginContext);
  const [privacy] = privacyState || [];
  const originConfig = useConfig(ConfigKeyEnum.DINGTALK_OAUTH) as IDingtalkOAuth;
  const config = useMemo(() => ({ ...originConfig, ...propsConfig }), [originConfig, propsConfig]);
  const [state, setState] = useState(() => config.initialState());
  const [src, setSrc] = useState('');
  const dingtalkState = state.state;
  const defaultSrc = config.getIframeSrc(dingtalkState);
  useEffect(() => {
    if ((isInDingtalk() || !isPc() || config.jumpOAuth) && dingtalkState && defaultSrc) {
      window.location.replace(defaultSrc);
    } else {
      setSrc(defaultSrc);
    }

    return () => {
      setSrc('');
    };
  }, [defaultSrc, dingtalkState]);

  useEffect(() => {
    const request = async () => {
      const st = await config.getState();
      setState((v: any) => ({ ...v, state: st }));
    };
    request();
  }, [config]);

  const handleMessage = usePersistFn((event: MessageEvent) => {
    if (origin === window.location.origin && typeof event.data === 'string') {
      let message;
      try {
        message = JSON.parse(event.data);
      } catch (error) {
        console.error(error);
      }

      const { status, msg, result } = message.dingtalk || {};
      if (status === 'failed') {
        config.afterFailed?.(() => setSrc(defaultSrc), msg);
      }
      if (status === 'success') {
        config.afterSuccess?.(result);
      }
    }
  });

  useEffect(() => {
    window.addEventListener('message', handleMessage, false);
    return () => {
      window.removeEventListener('message', handleMessage, false);
    };
  }, [handleMessage]);

  if (!dingtalkState) return null;
  return (
    <>
      <div className="mdt-login-dingtalk-oauth-container">
        <iframe
          referrerPolicy="no-referrer"
          src={src}
          title="dingtalk-oauth"
          frameBorder="0"
          width="365px"
          height="480px"
          scrolling="no"
          sandbox={undefined}
        />
        {typeof privacy === 'boolean' && !privacy && (
          <div className="mdt-login-dingtalk-oauth-nocheck">{config.privacyDesc}</div>
        )}
      </div>
      {config.renderExtra?.(state, setState)}
    </>
  );
};

DingtalkOAuth.displayName = 'DingtalkOAuth';
export default DingtalkOAuth;
