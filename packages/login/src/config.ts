import { ReactNode } from 'react';
import { ILocale as IVerificationLocale, IVertifyProp } from './util/verification';

export interface ILabelValue {
  label: string;
  value: string;
  /** 自定义render */
  render?: (value: string) => any;
  /** 需要使用的类型，用于自定义tab的时候快速构造想要选择的类型 */
  type?: ConfigTabEnum;
  /** 对已经选择使用的type模块，属性覆盖 */
  overrideConfig?: (value: string, type?: ConfigTabEnum) => any;
}

export interface IBg {
  isVideo: boolean;
  color: string;
  url: string;
}

export interface IGridConfig {
  status?: 'on' | 'off';
  position?: 'left' | 'right';
  children?: ReactNode;
  url?: string;
  color?: string;
  isVideo?: boolean;
}

export interface ILanguageState {
  language: string;
  visible: boolean;
  [key: string]: any;
}

export interface ILanguage {
  disable?: boolean;
  supportLanguages: ILabelValue[];
  initialState: () => ILanguageState;
  changeLanguage: Function;
}

export interface IFooter {
  renderFooter: Function;
}

export interface ITabs {
  defaultItem: string;
  items: ILabelValue[];
  renderExtra?: Function;
  hideWhenOneTab?: boolean;
}

export type IVerfication =
  | boolean
  | (Omit<IVertifyProp, 'onSuccess' | 'onFail' | 'visible'> & {
      onSuccess?: (close: () => void) => void;
      onFail?: (close: () => void) => void;
    });

export interface IAccountState {
  name: string;
  nameError: string;
  password: string;
  passwordError: string;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  [key: string]: any;
}

export interface IAccount {
  nameLabel: string;
  namePlaceholder: string;
  passwordLabel: string;
  passwordPlaceholder: string;
  buttonLabel: string;
  buttonLoadingLabel: string;
  showPasswordForget?: boolean;
  passwordForgetLabel?: string;
  passwordForgetClick?: Function;
  initialState: () => IAccountState;
  verifyName: Function;
  verifyPassword: Function;
  verifyAll: Function;
  loginIn: Function;
  renderExtraFormItem?: Function;
  renderExtraButton?: Function;
  verification?: IVerfication;
}

export interface IPhoneState {
  phone: string;
  phoneError: string;
  captcha: string;
  captchaError: string;
  selectISO: string;
  code: string;
  captchaDisabled: boolean;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  [key: string]: any;
}

export interface IPasswordForgetState {
  account?: string;
  accountError?: string;
  captcha?: string;
  captchaError?: string;
  captchaDisabled?: boolean;
  password?: string;
  passwordError?: string;
  passwordConfirm?: string;
  passwordConfirmError?: string;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  buttonLabel?: string;
  buttonLoadingLabel?: string;
  [key: string]: any;
}

export interface IPhone {
  phoneLabel: string;
  phonePlaceholder: string;
  captchaLabel: string;
  captchaPlaceholder: string;
  captchaInterval: number;
  captchaButtonLabel: string;
  captchaButtonSendedLabel: Function;
  buttonLabel: string;
  buttonLoadingLabel: string;
  supportCodes: { name: string; iso: string; code: string }[];
  initialState: () => IPhoneState;
  verifyPhone: Function;
  verifyCaptcha: Function;
  verifyAll: Function;
  sendCaptcha: Function;
  loginIn: Function;
  renderExtraFormItem?: Function;
  renderExtraButton?: Function;
  verification?: IVerfication;
}

export interface IWechatState {
  state: string;
  [key: string]: any;
}

export interface IWechat {
  successContent: string;
  failContent: string;
  statusContent: string;
  tipColor: string;
  privacyDesc?: string;
  flashInterval: number;
  initialState: () => IWechatState;
  getState: Function;
  getIframeSrc: Function;
  renderExtra?: Function;
  selfRedirect?: boolean;
  afterFailed?: Function;
  afterSuccess?: Function;
}

export interface IDingtalkState {
  state: string;
  [key: string]: any;
}

export interface IDingtalk {
  flashInterval: number;
  initialState: () => IDingtalkState;
  privacyDesc?: string;
  getState: Function;
  splicingGoto: Function;
  renderExtra?: Function;
  selfRedirect?: boolean;
  afterFailed?: Function;
  afterSuccess?: Function;
}

export interface IDingtalkOAuth {
  initialState: () => IDingtalkState;
  privacyDesc?: string;
  jumpOAuth?: boolean;
  getState: Function;
  getIframeSrc: Function;
  renderExtra?: Function;
  afterFailed?: Function;
  afterSuccess?: Function;
  selfRedirect?: boolean;
}

export interface IZzding {
  id: string;
  redirect: string;
  privacyDesc?: string;
  callback?: (code: string, reset: Function) => void;
}

export interface IVerify {
  renderPage: Function;
}

export interface IPasswordForget {
  title?: any;
  steps: 'check' | 'confirm';
  accountLabel: string;
  accountPlaceholder: string;
  captchaLabel: string;
  captchaPlaceholder: string;
  captchaInterval: number;
  captchaButtonLabel: string;
  captchaButtonSendedLabel: Function;
  passwordLabel: string;
  passwordPlaceholder: string;
  passwordConfirmLabel: string;
  passwordConfirmPlaceholder: string;
  initialState: () => IPasswordForgetState;
  verifyAccount: Function;
  verifyCaptcha: Function;
  verifyPassword: Function;
  verifyPasswordConfirm: Function;
  verifyAll: Function;
  sendCaptcha: Function;
  onSubmit: Function;
  renderExtraFormItem?: Function;
  gobackClick?: Function;
}

export interface IPrivacy {
  visible: boolean;
  disable?: boolean;
  check: boolean;
  onChange?: (check: boolean) => void;
  privacyText?: string;
  privacyslotText?: string;
  privacys?: { name: string; link: string }[];
}
export interface IConfig {
  title?: any;
  logo?: string;
  bg: IBg;
  gridConfig?: IGridConfig;
  privacy: IPrivacy;
  theme: Record<string, string>;
  language: ILanguage;
  footer: IFooter;
  tabs: ITabs;
  account?: IAccount;
  phone?: IPhone;
  wechat?: IWechat;
  dingtalk?: IDingtalk;
  dingtalkOAuth?: IDingtalkOAuth;
  zzding?: IZzding;
  verify?: IVerify;
  passwordForget?: IPasswordForget;
  verification?: IVerificationLocale;
  willMount?: (loading: boolean, setLoading: Function) => Function;
}

export enum ConfigTabEnum {
  ACCOUNT = 'account',
  PHONE = 'phone',
  WECHAT = 'wechat',
  DINGTALK = 'dingtalk',
  ZZDING = 'zzding',
  DINGTALK_OAUTH = 'dingtalkOAuth',
}

export enum ConfigKeyEnum {
  LOGO = 'logo',
  TITLE = 'title',
  PRIVACY = 'privacy',
  BG = 'bg',
  GRID_CONFIG = 'gridConfig',
  THEME = 'theme',
  LANGUAGE = 'language',
  FOOTER = 'footer',
  TABS = 'tabs',
  ACCOUNT = 'account',
  PHONE = 'phone',
  WECHAT = 'wechat',
  DINGTALK = 'dingtalk',
  DINGTALK_OAUTH = 'dingtalkOAuth',
  ZZDING = 'zzding',
  VERIFY = 'verify',
  PASSWORD_FORGET = 'passwordForget',
  WILLMOUNT = 'willMount',
  VERIFICATION = 'verification',
}

let loginConfig: IConfig | null;

export const initLoginConfig = (config: any) => {
  loginConfig = config;
};

type IKey = keyof IConfig;

const getConfigByKey = (key: IKey, forceReturn?: Boolean): IConfig[IKey] => {
  if (!loginConfig || (!forceReturn && loginConfig[key] === undefined)) {
    throw new Error('Initialization of the corresponding configuration is required before use.');
  }
  return loginConfig[key];
};

export const useConfig = (key: keyof IConfig, forceReturn?: Boolean): IConfig[IKey] => {
  return getConfigByKey(key, forceReturn);
};

export const useReleaseConfig = () => {
  loginConfig = null;
};
