# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.17.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.6...@mdt/business-comm@1.17.7) (2025-07-24)

### Features

- ✨ table 支持列配置 ([dae481d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dae481d534f5a5355fdf230394f2033e5ed1842c))

## [1.17.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.5...@mdt/business-comm@1.17.6) (2025-07-08)

### Features

- ✨ 支持发起的报表新增列和删除列 ([3a25375](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3a25375acf10ee61dc6600a634c5ec131ce6cbcc))

## [1.17.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.4...@mdt/business-comm@1.17.5) (2025-07-03)

**Note:** Version bump only for package @mdt/business-comm

## [1.17.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.3...@mdt/business-comm@1.17.4) (2025-04-27)

### Features

- ✨ 增加一表通批量上传列数据超出限制的警告处理 ([da2405d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/da2405dc79a598d7f50493b6bfeefed1e2d7911c))

## [1.17.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.2...@mdt/business-comm@1.17.3) (2025-04-02)

### Features

- ✨ 报表授权 ([b8b9952](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b8b9952c41d53aabaaba9e70123d55579e50cf57))

## [1.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.1...@mdt/business-comm@1.17.2) (2025-03-31)

### Features

- ✨ 增加评论模块 ([8646ef8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8646ef89f99bb680b6627c87280820546d2ecf46))

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.17.0...@mdt/business-comm@1.17.1) (2025-01-06)

### Bug Fixes

- 🐛 联系人处理多种数据,修复组件崩溃,增加操作 ([90b6250](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/90b62500e10e5428e787905a00c6fc312b7b2b95))

### Features

- ✨ 一表通文件上传校验数据 ([1696f17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1696f174f6b53727a2c58272f0ca8d3bd7fe608f))

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.16.4...@mdt/business-comm@1.17.0) (2024-12-23)

### Features

- 数据包 DDL 设置 ([543c22e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/543c22e19976e5ca690e1d581de808ac74d23a13))

## [1.16.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.16.3...@mdt/business-comm@1.16.4) (2024-12-16)

### Features

- ✨ 显隐设置增加表达式设置,同时兼容之前 condition 模式 ([c726e8c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c726e8ccf2fb42d9b78af534158702ab9c16b542))

## [1.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.16.2...@mdt/business-comm@1.16.3) (2024-12-03)

**Note:** Version bump only for package @mdt/business-comm

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.16.0...@mdt/business-comm@1.16.2) (2024-12-02)

### Features

- ✨ 报表筛选功能 ([4e04917](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4e0491788f7dd7097acdcf8d9597c4514aa197b0))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.16.0...@mdt/business-comm@1.16.1) (2024-12-02)

### Features

- ✨ 报表筛选功能 ([8919ca7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8919ca79f49c505f1ded7211ad517a6ad8ee6025))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.15.3...@mdt/business-comm@1.16.0) (2024-11-26)

### Features

- ✨ 时间类型存储切换 ok ([5a2e4b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a2e4b04b10212f948f488c61e8138e6cc8e71e0))

## [1.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.15.2...@mdt/business-comm@1.15.3) (2024-11-14)

### Bug Fixes

- 🐛 修复逻辑 ([a99430d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a99430df1b3736d3f0c2a274b6c6548f623cc312))

### Features

- ✨ 下载可配置及周期 ([362e304](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/362e3047145f6ca4316f4d520f289fcd6aa38ace))

## [1.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.15.1...@mdt/business-comm@1.15.2) (2024-10-22)

### Features

- onetable2.2 ([23f031b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23f031bda6e64530b89df8eff55c5317f3a656d2))

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.15.0...@mdt/business-comm@1.15.1) (2024-10-15)

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.14.1...@mdt/business-comm@1.15.0) (2024-08-09)

**Note:** Version bump only for package @mdt/business-comm

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.14.0...@mdt/business-comm@1.14.1) (2024-08-07)

### Features

- ✨ 无匹配路由默认跳转增加配置项 ([2a55642](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2a556426323e3aaeea4bc4b2130d932ab4921cde))

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.13.0...@mdt/business-comm@1.14.0) (2024-07-22)

### Features

- add datapkg cron ([6bee9d7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bee9d730567ef3fae91c18d518a631c03c130ba))

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.12.3...@mdt/business-comm@1.13.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))
- ✨ onetable 移动端优化 ([7f9b522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7f9b5220d5f454ead6efd1d92149d2d99e334e55))

## [1.12.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.12.2...@mdt/business-comm@1.12.3) (2024-06-24)

### Features

- ✨ 一表通移动端 首页 ([de108da](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/de108da0b3973d88d79133f070316ca7f8505205))

## [1.12.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.12.1...@mdt/business-comm@1.12.2) (2024-06-03)

### Features

- ✨ 列表操作 ([6e2a4ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6e2a4fffc9c47dc0ccac9b3ed541661c4853239c))

## [1.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.12.0...@mdt/business-comm@1.12.1) (2024-05-20)

### Features

- ✨ 重构添加水印的方法 ([c07304f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c07304f73a408e67eab60beffe370dbdfb6c38df))

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.11.0...@mdt/business-comm@1.12.0) (2024-05-13)

### Features

- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.10...@mdt/business-comm@1.11.0) (2024-03-11)

### Features

- ✨ 文件夹功能 ([9d6680e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d6680e70da17e00d743efcf602480e9e62f0f40))
- ✨ 智能搜索&数据源支持权限及用户列表 ([f717ead](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f717ead66bfb87cacc9be624dcb6ce4dc0e00f34))

## [1.10.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.9...@mdt/business-comm@1.10.10) (2024-03-07)

### Bug Fixes

- 🐛 Select 抽离通用模糊搜索 ([22427db](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/22427db98ae72bb805f4ca87381986af14ff3177))
- 🐛 可以通过 enter 修改数据包名称 ([21cef85](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/21cef85047e2f68ae207e50ad1f319bf4a3f7588))

## [1.10.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.8...@mdt/business-comm@1.10.9) (2023-12-08)

### Features

- ✨ 钉钉电子表格和审批节点 ([bb8d625](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb8d625218ffdbecbb5c4807eaadd5e8dc4d29fe))

## [1.10.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.7...@mdt/business-comm@1.10.8) (2023-12-01)

### Features

- ✨ 机构偏好 ([92a66b9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/92a66b963cdd7c2907a76802152bfbb131c51a5d))

## [1.10.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.6...@mdt/business-comm@1.10.7) (2023-11-13)

### Features

- ✨ 数据源动态依赖题目 ([7bdfd06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7bdfd06bc786ab6dd6c3f044e23b25433e75bad4))

## [1.10.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.5...@mdt/business-comm@1.10.6) (2023-10-25)

**Note:** Version bump only for package @mdt/business-comm

## [1.10.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.4...@mdt/business-comm@1.10.5) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.10.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.3...@mdt/business-comm@1.10.4) (2023-09-04)

### Features

- ✨ operation-log ([5cbb88b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5cbb88b7eff44dcfe5f3bb6cad27927347a6ed51))

## [1.10.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.2...@mdt/business-comm@1.10.3) (2023-07-24)

### Performance Improvements

- ⚡️ 性能优化(秒开) ([7142db5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7142db546bb7e1ea57651c700d2745e1f57a3a60))

## [1.10.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.1...@mdt/business-comm@1.10.2) (2023-07-03)

### Features

- ✨ 通过数据包详情页发起流程填报 ([5c2d77b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c2d77b6910e07dc9bf9a1e02dd0162c85ebb671))

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.10.0...@mdt/business-comm@1.10.1) (2023-06-13)

### Bug Fixes

- 🐛 @i18n-chain/react add dependencies ([5fc7e47](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc7e47c557a03343e4885594bbcd24dbaec912c))

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.9.0...@mdt/business-comm@1.10.0) (2023-06-05)

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.8.1...@mdt/business-comm@1.9.0) (2023-02-13)

### Features

- ✨ 增加拼音模糊搜索方式 ([cb3fc68](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb3fc68424f2f6db83ecba9f78ea8de028687ba6))

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.8.0...@mdt/business-comm@1.8.1) (2023-01-30)

### Bug Fixes

- 🐛 [首页设置]: 首页设置如果没有 edit_user 权限则隐藏批量编辑按钮 ([b8d2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b8d2c30af87c7307c7f924a601820c5662f30bc2))
- 🐛 fix build error ([c2b124e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c2b124eeb3abc4c2b49c3c38adfe8c8faf64c9b5))

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.7.1...@mdt/business-comm@1.8.0) (2022-12-13)

**Note:** Version bump only for package @mdt/business-comm

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.7.0...@mdt/business-comm@1.7.1) (2022-12-12)

**Note:** Version bump only for package @mdt/business-comm

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.6.2...@mdt/business-comm@1.7.0) (2022-11-28)

### Features

- ✨ [偏好设置]: Logo 样式模块增加,通用顶栏适配增加 ([894b2be](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/894b2beab961f3e427e3b8c9a178845f76ba5e4d))

## [1.6.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.6.1...@mdt/business-comm@1.6.2) (2022-11-21)

### Bug Fixes

- 🐛 memory leak ([a971074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a971074e017b8ea025305ce48fdc605ab25fb550))

## [1.6.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.5.0...@mdt/business-comm@1.6.1) (2022-11-14)

### Bug Fixes

- 🐛 [通用顶栏]: 修复 app 禁止切换的逻辑和空页面样式修复 ([bb19399](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb19399cefc7bcce9b844c3fb96e0be3033aa188))

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ 可见数据设置 ([c540a22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c540a22a0c8493d9c462318b8e1d9bb256d80dce))
- ✨ 数据申请简化 ([74a9fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74a9fc800171e9d78ca1b0cdaa9cf0a7f83b3ac7))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.5.0...@mdt/business-comm@1.6.0) (2022-09-30)

### Features

- ✨ 可见数据设置 ([c540a22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c540a22a0c8493d9c462318b8e1d9bb256d80dce))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.4.1...@mdt/business-comm@1.5.0) (2022-09-26)

### Bug Fixes

- 🐛 定时任务实时更新下次运行时间&起止日期字段禁用小于当前时间的选择 ([dade027](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dade027eff753d87b7d45608671bbee60f3fa17d))

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.4.0...@mdt/business-comm@1.4.1) (2022-09-19)

**Note:** Version bump only for package @mdt/business-comm

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.3.1...@mdt/business-comm@1.4.0) (2022-09-13)

### Features

- ✨ python_pip 节点增加代码编辑器 ([0aa3d57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0aa3d5754f4ffaca010f61fdcb023f1c9dee7e9d))

## [1.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.3.0...@mdt/business-comm@1.3.1) (2022-09-05)

### Features

- ✨ 新增文档 ([9c8a4a8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9c8a4a82251e5d4996b828b6d522d5d079542c46))

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.2.0...@mdt/business-comm@1.3.0) (2022-08-30)

### Features

- ✨ 新版 Form 表单 ([18e9086](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18e908648d16cb954af9d5249ce5545b50195569))

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-comm@1.1.0...@mdt/business-comm@1.2.0) (2022-08-12)

### Bug Fixes

- 🐛 fix bug ([11e790f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/11e790f1b38b898a2475f98ccf6f790049788941))
- 🐛 样式污染 ([77282b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77282b20211338b0a2a5f13724b5220b6fc1ad35))

### Features

- ✨ 血缘图 ([dbe2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbe2c308386df9286f60026925486879c7aab965))

# 1.1.0 (2022-06-30)

### Features

- ✨ 低码 ETL 迁移数据工厂 ([3b96fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3b96fde0a1f958e105ef219a0e9fa0d58690c339))
- ✨ 迁移 sso + 微前端 ([b51cb10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b51cb107a0f84fb5729f16cc0118b6cf89d7c91d))
