{"name": "@mdt/business-comm", "version": "1.17.7", "private": false, "description": "脉策通用", "keywords": ["mdt", "business", "comm"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@i18n-chain/react": "2.0.1", "@monaco-editor/react": "^4.4.5", "rc-field-form": "^1.26.6", "rc-mentions": "^1.8.0"}}