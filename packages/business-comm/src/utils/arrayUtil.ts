/**
 * 通用的数组元素原位替换函数
 * 在数组中查找匹配的元素，并用新元素替换它们，保持在原始位置
 *
 * @template T 数组元素类型
 * @param sourceArray 源数组
 * @param oldElements 要替换的元素数组
 * @param newElements 替换后的新元素数组
 * @param matcher 匹配函数，判断元素是否匹配oldElements中的元素
 * @returns 替换后的新数组
 */
export function replaceElementsInPlace<T>(
  sourceArray: T[] = [],
  oldElements: T[] = [],
  newElements: T[] = [],
  matcher: (item: T, targetItems: T[]) => boolean = (item, targets) => targets.includes(item),
): T[] {
  if (!sourceArray?.length) return [...newElements];
  if (!oldElements?.length) return [...sourceArray, ...newElements];

  const remainingElements = sourceArray.filter((item) => !matcher(item, oldElements));

  const insertPosition = sourceArray.findIndex((item) => matcher(item, oldElements));

  if (insertPosition === -1) {
    return [...remainingElements, ...newElements];
  }

  let countBeforeInsert = 0;
  for (let i = 0; i < insertPosition; i++) {
    if (!matcher(sourceArray[i], oldElements)) {
      countBeforeInsert++;
    }
  }

  return [
    ...remainingElements.slice(0, countBeforeInsert),
    ...newElements,
    ...remainingElements.slice(countBeforeInsert),
  ];
}
