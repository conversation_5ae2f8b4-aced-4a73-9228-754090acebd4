import dayjs, { ConfigType, ManipulateType, OpUnitType, QUnitType } from 'dayjs';
import isLeapYear from 'dayjs/plugin/isLeapYear';
import isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import weekOfYear from 'dayjs/plugin/weekOfYear';

dayjs.extend(isSameOrBefore);
dayjs.extend(weekOfYear);
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeeksInYear);
dayjs.extend(isLeapYear);

export { dayjs };
export type { Dayjs, ConfigType } from 'dayjs';
export type ISupportUnit = ManipulateType | QUnitType | OpUnitType;
export const DATE_FORMATTER_0 = 'YYYY-MM-DD';
export const DATE_FORMATTER_1 = 'YYYY-MM-DD HH:mm';
export const DATE_FORMATTER_2 = 'YYYY-MM-DD HH:mm:ss';
const ISO_8601_REGEX = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(Z|[+-]\d{2}:\d{2})?$/;

const FORMATTER_MAP: Record<string, string> = {
  DATE_FORMATTER_0,
  DATE_FORMATTER_1,
  DATE_FORMATTER_2,
};

export const getCurrentDateStr = () => {
  return dayjs().format();
};

export const createDate = (date?: ConfigType) => {
  return dayjs(date);
};

export const formatDayjsValue = (date: ConfigType, formatter?: string) => {
  let result = date;
  let timestamp: number | null = null;
  if (typeof date === 'string' && /^-?\d+$/.test(date)) {
    timestamp = parseInt(date, 10);
  } else if (typeof date === 'number') {
    timestamp = Math.floor(date);
  }
  if (timestamp !== null && timestamp > 0) {
    timestamp <= 2147483647 && (timestamp *= 1000);
    result = dayjs(timestamp);
  }
  result = dayjs(result);
  return result.format(formatter);
};

export const createDateByUnix = (date: number) => {
  return dayjs.unix(date);
};

export const formateDateByUnix = (date: number, ft = 0) => {
  return dayjs.unix(date).format(FORMATTER_MAP[`DATE_FORMATTER_${ft}`] || DATE_FORMATTER_0);
};

export const formateDate = (date?: ConfigType, formatter = DATE_FORMATTER_0) => {
  return dayjs(date).format(formatter);
};

export const formateDate2 = (date?: ConfigType, ft = 0) => {
  return dayjs(date).format(FORMATTER_MAP[`DATE_FORMATTER_${ft}`] || DATE_FORMATTER_0);
};

export const formateDateWithoutMillisecond = (date: ConfigType, formatter = DATE_FORMATTER_0) => {
  const d = (date as number) * 1000;
  return dayjs(d).format(formatter);
};

export const isExpirationDateFromNow = (date: ConfigType, value: number, unit?: ManipulateType) => {
  return dayjs(date).add(value, unit).isAfter(dayjs());
};

export const transformDateToUnix = (date: ConfigType) => {
  return dayjs(date).unix();
};

export const transformDateToMilliseconds = (date: ConfigType): number => {
  return dayjs(date).valueOf();
};

export const greaterThanTime = (d1: ConfigType, d2: ConfigType) => {
  return dayjs(d1).isAfter(d2);
};

export const greaterThanNow = (d1: ConfigType) => {
  return dayjs(d1).isAfter(dayjs());
};

export const equalTime = (d1: ConfigType, d2: ConfigType) => {
  return dayjs(d1).isSame(d2);
};

export const lessThanTime = (d1: ConfigType, d2: ConfigType) => {
  return dayjs(d1).isBefore(d2);
};

export const rangeTime = (d1: ConfigType, d2: ConfigType) => {
  return dayjs() >= dayjs(d1) && dayjs() <= dayjs(d2);
};

export const rangeThanTime = (d1: ConfigType, d2: [ConfigType, ConfigType]) => {
  const date1 = dayjs(d1);
  const startDate = dayjs(d2[0]);
  const endDate = dayjs(d2[1]);

  return (date1.isAfter(startDate) && date1.isBefore(endDate)) || date1.isSame(startDate) || date1.isSame(endDate);
};

export const lessThanNow = (d1: ConfigType) => {
  return lessThanTime(d1, dayjs());
};

export const lessThanOrSameNow = (d1: ConfigType) => {
  return dayjs(d1).isSameOrBefore(dayjs());
};

export const startOf = (d: ConfigType, unit: ManipulateType | QUnitType | OpUnitType) => {
  return dayjs(d).startOf(unit);
};

export const endOf = (d: ConfigType, unit: ManipulateType | QUnitType | OpUnitType) => {
  return dayjs(d).endOf(unit);
};

export const isValidTimeString = (time: string) => {
  return ISO_8601_REGEX.test(time);
};

export const formatToBeijingISO = (d: ConfigType): string => {
  return dayjs(d).format('YYYY-MM-DDTHH:mm:ss+08:00');
};

/**
 * 解析日期，先尝试用 format 解析，如果失败，再尝试用其他标准格式解析
 */
// eslint-disable-next-line sonarjs/cognitive-complexity,complexity
export function normalizeDate(value: any, format?: string) {
  if (!value || value === '0') {
    return undefined;
  }

  let v = typeof value === 'object' || value instanceof Date ? dayjs(value) : dayjs(value, format, true);
  if (v.isValid()) {
    return v;
  }

  if (typeof value === 'string' || typeof value === 'number') {
    let formats = ['', DATE_FORMATTER_2, 'X'];
    const valStr = value.toString();
    // 特殊处理下年份
    if (/^\d{4}$/.test(valStr)) {
      const date = dayjs(valStr, 'YYYY');
      return date.isValid() ? date : undefined;
    }

    if (/^\d{4}[\-\/]\d{2}$/.test(valStr)) {
      const ft = valStr.includes('-') ? 'YYYY-MM' : 'YYYY/MM';
      const date = dayjs(valStr, ft);
      return date.isValid() ? date : undefined;
    }

    if (/^\d{4}[\-\/]\d{2}[\-\/]\d{2}\s\d{2}$/.test(valStr)) {
      const ft = valStr.includes('-') ? 'YYYY-MM-DD HH' : 'YYYY/MM/DD HH';
      const date = dayjs(valStr, ft);
      return date.isValid() ? date : undefined;
    }

    if (/^\d{4}[\-\/]\d{2}[\-\/]\d{2}\s\d{2}:\d{2}$/.test(valStr)) {
      const ft = valStr.includes('-') ? 'YYYY-MM-DD HH:mm' : 'YYYY/MM/DD HH:mm';
      const date = dayjs(valStr, ft);
      return date.isValid() ? date : undefined;
    }

    if (/^\d{10}((\.\d+)*)$/.test(valStr)) {
      formats = ['X', 'x', DATE_FORMATTER_2, ''];
    } else if (/^\d{13}((\.\d+)*)$/.test(valStr)) {
      formats = ['x', 'X', DATE_FORMATTER_2, ''];
    }

    while (formats.length) {
      const format = formats.shift()!;
      const date = dayjs(value, format);

      if (date.isValid()) {
        return date;
      }
    }
  }

  return undefined;
}
