import { Locale } from './cn';

export const en: Locale = {
  comDataLoading: 'Loading...',
  comNoData: 'No Data',
  comNoMenu: 'No Menu',
  comWelcome: 'Loading...',
  comEmpty: {
    noProduct:
      'You have not been granted any menu permissions yet. Please contact the administrator to grant you access!',
    goBack: 'Go back now',
    backDelay: 'Return to the original page in seconds',
    noCurrentProduct: 'You do not have access to the current product',
    noCurrentProductDesc: 'Please contact the administrator for access',
  },
  comLuckySheet: {
    title: 'Metro Electronic Spreadsheet',
  },
  yes: 'Yes',
  no: 'No',
  comButton: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    edit: 'Edit',
    update: 'Update',
    change: 'Change',
    detail: 'Detail',
    pass: 'Pass',
    reject: 'Reject',
    save: 'Save',
    finish: 'Finish',
    giveup: 'Cancel',
    next: 'Next',
    previous: 'Previous',
    create: 'Create',
    delete: 'Delete',
    submit: 'Submit',
    reset: 'Reset',
    publish: 'Publish',
    view: 'View',
    use: 'Use',
    close: 'Close',
    switch: 'Switch',
    send: 'Send',
    continue: 'Continue',
  },
  comPlaceholder: {
    input: 'Please Input',
    select: 'Please Select',
  },
  comFolder: {
    rename: 'Rename',
    create: 'Create Folder',
    moveTo: 'Move to',
    home: 'Home',
  },
  date: {
    every: 'Every',
    st: 'st',
    year: 'Year',
    month: 'Month',
    week: 'Week',
    day: 'Day',
    hour: 'Hour',
    minute: 'Minute',
    second: 'Second',
    mon: 'Monday',
    tue: 'Tuesday',
    wed: 'Wednesday',
    thu: 'Thursday',
    fri: 'Friday',
    sat: 'Saturday',
    sun: 'Sunday',
  },
  comOperation: 'Operation',
};
