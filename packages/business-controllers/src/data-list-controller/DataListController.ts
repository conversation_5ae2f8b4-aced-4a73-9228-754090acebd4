import _ from 'lodash';
import { BehaviorSubject, combineLatest, Observable, of, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, skip } from 'rxjs/operators';
import { search as pinyinSearch } from '@mdtBsComm/utils/pinyinUtil';
import { PaginationController } from '../pagination-controller';

export interface IPaginationParams {
  page_size: number;
  page_num: number;
}

export type ILoadDataListRslt<V = any> = Observable<[number, V[]]>;
export type ILoadDataListFunc<V = any> = (params?: any) => ILoadDataListRslt<V>;
export type ILoadNextPageDataListFunc<V = any> = (params: IPaginationParams) => Observable<V[]>;
export type IEqualItemFunc<V = any> = (item1: V, item2: V) => boolean;
export type IFilterItemFunc<V = any> = (item: V, search: string) => boolean;
export type IGetBackendFilterParams = (...params: any[]) => any;

export interface IAddDataOptions {
  modifyTotal?: boolean;
  addToFirst?: boolean;
}

export const DEBOUNCE_TIME = 300;
// 如果需要disable某行的select功能，在外部需要为该行添加disableSelect属性
export const DISABLE_SELECT_KEY = 'disableSelect';
// 如果需要readonly某行的select功能，在外部需要为该行添加readonlySelect属性
// READONLY_SELECT_KEY应用于数据有层级关系的情况下，子级才可设置，
// 和DISABLE_SELECT_KEY的区别是其选中状态依赖父级。
export const READONLY_SELECT_KEY = 'readonlySelect';

const CHINESE_REGEX = /[\u4e00-\u9fff]/;

export interface IControllerOptions<V = any> {
  // 后端分页--请求首页数据，需告诉total数
  // 如果total设置为0，则表明无需分页，可前端检索或分页
  loadDataListFunc?: (params?: any) => Observable<[number, V[]]>;
  // 后端分页--加载下一页数据，如果为undefined，则表明无需分页
  loadNextPageDataListFunc?: (params: { page_size: number; page_num: number }) => Observable<V[]>;
  // 后端检索分页时，获取检索参数
  getBackendFilterParams?: IGetBackendFilterParams;
  // 每页加载数据量
  pageSize?: number;
  // 初始化页数
  initialPageNum?: number;
  // 更新，删除需要告知如何比较新旧值，如果同时提供itemKey，以方法优先级为准
  equalItemFunc?: IEqualItemFunc<V>;
  // 更新，删除数据时使用哪个字段进行比较，默认使用id字段进行比对
  equalItemKey?: string;
  // 前端输入框检索时，需要告知如何过滤，同时提供filterItemKey, 优先使用方法
  filterItemFunc?: IFilterItemFunc<V>;
  // 前端输入框检索时，使用什么字段过滤，默认使用name字段过滤
  filterItemKey?: string;
}

// 默认加载100条数据
const DEFAULT_PAGE_SIZE = 100;
// 默认根据id比较
const DEFAULT_EQUAL_ITEM_KEY = 'id';
// 默认根据name检索
const DEFAULT_FILTER_ITEM_KEY = 'name';

/**
 * 数据集合逻辑
 *
 * ```typescript
 * // V: 表示泛型，如果是列表是字符串，使用string
 * const instance = new DataListController<string>();
 * ```
 */
class DataListController<V = any> extends PaginationController {
  // 是否是全部数据
  protected isAllData$ = new BehaviorSubject(false);
  // 数据请求中
  private readonly dataListLoading$ = new BehaviorSubject(false);
  // 展示的数据
  private readonly dataList$ = new BehaviorSubject<V[]>([]);
  // 选中的数据（可能不在当前列表中）
  private readonly selectedDataList$ = new BehaviorSubject<V[]>([]);
  // 数据是否为空
  private readonly dataListEmpty$ = new BehaviorSubject(true);
  // 是否有下一页请求提示
  private readonly visibleLoadingNextTip$ = new BehaviorSubject(false);
  // 单一字段过滤的值
  private readonly singleFilter$ = new BehaviorSubject<any>('');
  // 加载首屏数据的方法
  private loadDataListFunc: ILoadDataListFunc<V>;
  // 加载下一页数据的方法
  private loadNextPageDataListFunc: ILoadNextPageDataListFunc<V>;
  // 后端检索分页时，获取检索参数
  private getBackendFilterParams: IGetBackendFilterParams;
  // 更新，删除需要告知如何比较新旧值
  private equalItemFunc: IEqualItemFunc<V>;
  // 更新，删除时使用哪个字段进行比较
  private equalItemKey: string;
  // 前端过滤时的方法
  private filterItemFunc: IFilterItemFunc<V>;
  // 前端过滤时，用什么字段比较
  private filterItemKey: string;
  // 后端不分页时，前端临时存储，便于过滤数据
  private allData: V[] = [];
  private filterSubscription: Subscription | null = null;
  private backendFilterSubscription: Subscription | null = null;

  public constructor(options: IControllerOptions<V>) {
    super(options.pageSize || DEFAULT_PAGE_SIZE, options.initialPageNum);
    this.loadDataListFunc = options.loadDataListFunc || this.defaultLoadDataList;
    this.loadNextPageDataListFunc = options.loadNextPageDataListFunc || this.defaultLoadNextPageDataList;
    this.getBackendFilterParams = options.getBackendFilterParams || this.defaultGetBackendFilterParams;
    this.equalItemFunc = options.equalItemFunc || this.defaultEqualItemFunc;
    this.equalItemKey = options.equalItemKey || DEFAULT_EQUAL_ITEM_KEY;
    this.filterItemFunc = options.filterItemFunc || this.defaultFilterItemFunc;
    this.filterItemKey = options.filterItemKey || DEFAULT_FILTER_ITEM_KEY;
    this.isAllData$.next(!options.loadNextPageDataListFunc);
    this.computeDataListEmpty$();
    this.computePaginationController();
  }

  public destroy() {
    super.destroy();
    this.dataListLoading$.complete();
    this.dataList$.complete();
    this.dataList$.next([]);
    this.selectedDataList$.complete();
    this.selectedDataList$.next([]);
    this.dataListEmpty$.complete();
    this.visibleLoadingNextTip$.complete();
    this.stopListenFrontFilter();
    this.stopListenBackendFilter();
    this.singleFilter$.complete();
    this.loadDataListFunc = this.defaultLoadDataList;
    this.loadNextPageDataListFunc = this.defaultLoadNextPageDataList;
    this.getBackendFilterParams = this.defaultGetBackendFilterParams;
    this.equalItemFunc = this.defaultEqualItemFunc;
    this.filterItemFunc = this.defaultFilterItemFunc;
    this.allData = [];
  }

  public getEqualItemKey() {
    return this.equalItemKey;
  }

  public getDataListLoading$() {
    return this.dataListLoading$;
  }

  public getDataListLoadingValue() {
    return this.dataListLoading$.getValue();
  }

  public changeDataListLoading(v: boolean) {
    this.dataListLoading$.next(v);
  }

  public getDataList$() {
    return this.dataList$;
  }

  public getDataListValue() {
    return this.dataList$.getValue();
  }

  public changeDataList(list: V[]) {
    this.dataList$.next(list);
  }

  public getSelectedDataList$() {
    return this.selectedDataList$;
  }

  public getSelectedDataListValue() {
    return this.selectedDataList$.getValue();
  }

  public changeSelectedDataList(list: V[]) {
    this.selectedDataList$.next(list);
  }

  public getDataListEmpty$() {
    return this.dataListEmpty$;
  }

  public getDataListEmptyValue() {
    return this.dataListEmpty$.getValue();
  }

  public getVisibleLoadingNextTip$() {
    return this.visibleLoadingNextTip$;
  }

  public getVisibleLoadingNextTipValue() {
    return this.visibleLoadingNextTip$.getValue();
  }

  public getSingleFilter$() {
    return this.singleFilter$;
  }

  public getSingleFilterValue() {
    return this.singleFilter$.getValue();
  }

  // 可以被子类复写
  public changeSingleFilter(value: any) {
    this.singleFilter$.next(value);
  }

  public getAllData() {
    return this.allData;
  }

  // 直接设置数据
  public initDataDirectly(v: [number, V[]]) {
    const total = v[0] || 0;
    const list = v[1] || [];

    if (this.isAllData$.getValue()) {
      this.allData = list;
      this.changePageTotal(_.size(list));
      const search = this.getSingleFilterValue();
      // 如果是前端过滤，初始化数据时先进行过滤（针对过滤条件有初始值的情况）
      this.dataList$.next(this.handleFrontFilter(search));
    } else {
      this.changePageTotal(total);
      this.dataList$.next([...list]);
    }
  }

  // 加入一行数据
  public addDataToList(data: V, options?: IAddDataOptions) {
    const { addToFirst = true, modifyTotal = false } = options || {};
    // 如果是前端数据，需要全部数据存储
    this.isAllData$.getValue() && (this.allData = addToFirst ? [data, ...this.allData] : [...this.allData, data]);
    // 更新数据的显示
    const oldData = this.getDataListValue();
    this.changeDataList(addToFirst ? [data, ...oldData] : [...oldData, data]);
    // 修改总数，总数加1
    modifyTotal && this.changePageTotal(this.getPageTotalValue() + 1);
  }

  // 按顺序插入一行
  public addDataToListByIndex(data: V, index: number, modifyTotal?: boolean) {
    // 更新数据的显示
    const oldData = this.getDataListValue();
    const newData = [...oldData.slice(0, index), data, ...oldData.slice(index)];
    this.isAllData$.getValue() && (this.allData = [...newData]);
    this.changeDataList(newData);
    // 修改总数，总数加1
    modifyTotal && this.changePageTotal(this.getPageTotalValue() + 1);
  }

  // 更新一行数据
  public editDataInList(data: V) {
    // 全部数据存储
    this.isAllData$.getValue() &&
      (this.allData = _.map(this.allData, (it) => (this.equalItemFunc(it, data) ? data : it)));
    const newSelectedData = _.map(this.getSelectedDataListValue(), (it) => (this.equalItemFunc(it, data) ? data : it));
    this.changeSelectedDataList(newSelectedData);
    // 更新数据的显示
    const newData = _.map(this.getDataListValue(), (it) => (this.equalItemFunc(it, data) ? data : it));
    this.changeDataList(newData);
  }

  // 删除一行数据
  public deleteDataFromList(data: V, modifyTotal = false) {
    this.isAllData$.getValue() && (this.allData = _.reject(this.allData, (it) => this.equalItemFunc(it, data)));
    const newSelectedData = _.reject(this.getSelectedDataListValue(), (it) => this.equalItemFunc(it, data));
    this.changeSelectedDataList(newSelectedData);
    const newData = _.reject(this.getDataListValue(), (it) => this.equalItemFunc(it, data));
    this.changeDataList(newData);
    // 总数减一
    modifyTotal && this.changePageTotal(this.getPageTotalValue() - 1);
  }

  // 调用加载数据的方法，需要返回total
  public loadDataList(params?: any) {
    this.dataListLoading$.next(true);
    this.loadDataListFunc(params).subscribe((v) => {
      this.initDataDirectly(v);
      this.dataListLoading$.next(false);
    });
  }

  // 加载下一页
  public loadMoreDataList(params?: any) {
    const needMore = this.checkNeedMore();
    if (!needMore) return;
    this.requestMoreData(params);
  }

  public requestMoreData(params?: any) {
    const paginationParams = this.getPaginationParams();
    this.loadNextPageDataListFunc({ ...params, ...paginationParams }).subscribe((list) => {
      // 当用户手动点击时，可能pagination会不一致
      if (this.getCurrentPage$().getValue() !== paginationParams.page_num) return;
      this.addMoreDataToList(list);
    });
  }

  // 前端单一字段过滤，通用方法
  public listenFrontFilter() {
    this.isAllData$.next(true);
    this.filterSubscription = this.singleFilter$
      .pipe(
        skip(1),
        debounceTime(DEBOUNCE_TIME),
        distinctUntilChanged(),
        map((v) => {
          return this.handleFrontFilter(v);
        }),
      )
      .subscribe((v) => {
        this.dataList$.next(v);
      });
  }

  public stopListenFrontFilter() {
    this.filterSubscription?.unsubscribe();
    this.filterSubscription = null;
  }

  // 后端过滤， 可以多个字段或单一字段
  public listenBackendFilter(...desp: BehaviorSubject<any>[]): Subscription {
    this.isAllData$.next(false);
    // 添加return是为了方便外部取消监听
    this.backendFilterSubscription = combineLatest(desp)
      .pipe(
        skip(1),
        debounceTime(DEBOUNCE_TIME),
        distinctUntilChanged((prev, curr) => {
          return JSON.stringify(prev) === JSON.stringify(curr);
        }),
        map((v) => this.getBackendFilterParams(v)),
      )
      .subscribe((params) => {
        this.loadDataList(params);
      });

    return this.backendFilterSubscription;
  }

  public stopListenBackendFilter() {
    this.backendFilterSubscription?.unsubscribe();
    this.backendFilterSubscription = null;
  }

  // 添加选中数据
  public addToSelectedDataList(data: V | V[]) {
    const selectedList = this.getSelectedDataListValue();
    this.selectedDataList$.next(_.uniqBy(_.concat(selectedList, data), this.equalItemKey));
  }

  // 添加全部选中数据
  public addToAllSelectedDataList() {
    const canSelectList = _.filter(this.getDataListValue(), (v) => !this.checkItemDisabled(v)) as V[];
    this.addToSelectedDataList(canSelectList);
  }

  // 取消全部选中数据
  public removeAllSelectedDataList() {
    const canSelectList = _.filter(this.getDataListValue(), (v) => !this.checkItemDisabled(v)) as V[];
    this.removeFromSelectedDataList(canSelectList);
  }

  // 取消部分选中数据
  public removeFromSelectedDataList(data: V | V[]) {
    const list = _.isArray(data) ? data : [data];
    const selectedList = this.getSelectedDataListValue();
    this.selectedDataList$.next(_.differenceWith(selectedList, list, this.equalItemFunc.bind(this)));
  }

  // 判断item是否选中
  public checkItemSelected(item: V) {
    const selectedList = this.getSelectedDataListValue();
    return _.some(selectedList, (selected) => this.equalItemFunc(selected, item));
  }

  // 判断item是否禁用
  public checkItemDisabled(item: V) {
    return _.get(item, DISABLE_SELECT_KEY) || _.get(item, READONLY_SELECT_KEY);
  }

  // 判断是否全部选中
  public checkAllSelected() {
    const dataList = this.getDataListValue();
    const canSelectedAllList = _.filter(dataList, (v) => !this.checkItemDisabled(v));
    const canSelectedList = _.filter(
      this.getSelectedDataListValue(),
      (v) => !this.checkItemDisabled(v) && _.find(dataList, (item) => this.equalItemFunc(item, v)),
    );
    const [allCount, selectedCount] = [canSelectedAllList.length, canSelectedList.length];
    return _.isEmpty(canSelectedList) ? false : _.isEqual(allCount, selectedCount);
  }

  // 判断半选状态
  public checkIndeterminate() {
    const dataList = this.getDataListValue();
    const canSelectedAllList = _.filter(dataList, (v) => !this.checkItemDisabled(v));
    const canSelectedList = _.filter(
      this.getSelectedDataListValue(),
      (v) => !this.checkItemDisabled(v) && _.find(dataList, (item) => this.equalItemFunc(item, v)),
    );
    const [allCount, selectedCount] = [canSelectedAllList.length, canSelectedList.length];
    return !(_.isEmpty(canSelectedList) || _.isEqual(allCount, selectedCount));
  }

  // 获取下一页参数
  public getPaginationParams() {
    const pageSize = this.getPageSize();
    const pageNum = this.getCurrentPageValue();
    return { page_size: pageSize, page_num: pageNum };
  }

  protected addMoreDataToList(list: V[]) {
    const pre = this.getDataListValue();
    this.dataList$.next([...pre, ...list]);
    this.changeLoadingNext(false);
  }

  // 计算空状态
  protected computeDataListEmpty$() {
    this.dataList$.pipe(map((v) => _.size(v) === 0)).subscribe((v) => {
      this.dataListEmpty$.next(v);
    });
  }

  // 如果加载需要重置分页相关的参数
  protected computePaginationController() {
    this.dataListLoading$
      .pipe(
        skip(3),
        filter((v) => v),
      )
      .subscribe(() => {
        this.resetPagination();
      });
  }

  // 计算是否可以继续请求下一页数据
  protected computeVisibleLoadingNextTip() {
    combineLatest(this.getLoadingNext$(), this.getHasNextPage$())
      .pipe(
        skip(1),
        map(([l, h]) => l || h),
        distinctUntilChanged(),
      )
      .subscribe((v) => {
        this.visibleLoadingNextTip$.next(v);
      });
  }

  // 前端单一字段过滤，数据过滤方法
  private handleFrontFilter(search: any) {
    return search === 0 || search ? _.filter(this.allData, (it) => this.filterItemFunc(it, search)) : [...this.allData];
  }

  // 默认加载数据
  private defaultLoadDataList() {
    return of([0, []]) as ILoadDataListRslt<V>;
  }

  // 默认加载下一页数据
  private defaultLoadNextPageDataList() {
    return of([]);
  }

  // 默认使用id进行比较
  private defaultEqualItemFunc(item: V, newItem: V) {
    return _.get(item, this.equalItemKey) === _.get(newItem, this.equalItemKey);
  }

  // 默认使用name进行过滤
  private defaultFilterItemFunc(item: V, search: string): boolean {
    const source = _.get(item, this.filterItemKey);
    if (CHINESE_REGEX.test(search)) {
      return _.includes(source, search);
    }
    return pinyinSearch(source, search);
  }

  // 默认获取参数
  private defaultGetBackendFilterParams() {
    return undefined;
  }
}

export { DataListController };
