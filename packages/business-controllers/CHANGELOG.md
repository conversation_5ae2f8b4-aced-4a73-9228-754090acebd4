# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.17.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.7...@mdt/business-controllers@1.17.8) (2025-07-24)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.6...@mdt/business-controllers@1.17.7) (2025-07-08)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.5...@mdt/business-controllers@1.17.6) (2025-07-03)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.4...@mdt/business-controllers@1.17.5) (2025-05-19)

### Bug Fixes

- 🐛 调整 isAllData 为响应变量,根据监听方式进一步判断 isAllData 的值 ([3e0c3d6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3e0c3d609ec2932bfcd94e5f30e1270c7679d599))

## [1.17.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.3...@mdt/business-controllers@1.17.4) (2025-05-14)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.2...@mdt/business-controllers@1.17.3) (2025-04-27)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.1...@mdt/business-controllers@1.17.2) (2025-04-02)

**Note:** Version bump only for package @mdt/business-controllers

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.17.0...@mdt/business-controllers@1.17.1) (2025-03-31)

**Note:** Version bump only for package @mdt/business-controllers

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.16.3...@mdt/business-controllers@1.17.0) (2025-02-24)

### Features

- bff suport params encode ([d9648b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9648b2cf9aa44b7c4f1a49c82bb5fe772cfc354))

## [1.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.16.2...@mdt/business-controllers@1.16.3) (2025-02-17)

### Bug Fixes

- 修复 body 是 array 的情况 ([862dc75](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/862dc753852afda8e4cd40cf072da17c4ed17241))

### Features

- ✨ 增加指定分页加载功能 ([4cbbfcb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cbbfcbb13e1e3170dc9d4c143ca5ba6fa4fd6d2))
- support exclude unendocde keys ([4080dc3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4080dc3a70df5dd34f7c7dab2d71605b2c1709df))

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.16.1...@mdt/business-controllers@1.16.2) (2025-01-23)

### Features

- ✨ 增加外部请求设置服务器代理请求和 cookie 携带 ([46bfc98](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46bfc98a344086c015ff163d3d3a6d43c6a5ec2a))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.16.0...@mdt/business-controllers@1.16.1) (2025-01-06)

**Note:** Version bump only for package @mdt/business-controllers

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.15.4...@mdt/business-controllers@1.16.0) (2024-12-23)

**Note:** Version bump only for package @mdt/business-controllers

## [1.15.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.15.3...@mdt/business-controllers@1.15.4) (2024-12-16)

**Note:** Version bump only for package @mdt/business-controllers

## [1.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.15.2...@mdt/business-controllers@1.15.3) (2024-12-03)

**Note:** Version bump only for package @mdt/business-controllers

## [1.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.15.0...@mdt/business-controllers@1.15.2) (2024-12-02)

**Note:** Version bump only for package @mdt/business-controllers

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.15.0...@mdt/business-controllers@1.15.1) (2024-12-02)

**Note:** Version bump only for package @mdt/business-controllers

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.5...@mdt/business-controllers@1.15.0) (2024-11-26)

**Note:** Version bump only for package @mdt/business-controllers

## [1.14.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.4...@mdt/business-controllers@1.14.5) (2024-11-14)

### Features

- ✨ 周期改版 ([901090d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/901090db719c8b5efe07849b13e8e46057ea029b))

## [1.14.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.3...@mdt/business-controllers@1.14.4) (2024-10-28)

### Bug Fixes

- 🐛 带数据下发优化 ([1798427](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/179842745b86a5e3fad8472f78c1f6df2d0986a1))

## [1.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.2...@mdt/business-controllers@1.14.3) (2024-10-27)

### Bug Fixes

- 优化 ([a483603](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a4836035d5284c6819d7404e70418afdc085661d))

## [1.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.1...@mdt/business-controllers@1.14.2) (2024-10-22)

### Features

- onetable2.2 ([23f031b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23f031bda6e64530b89df8eff55c5317f3a656d2))

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.14.0...@mdt/business-controllers@1.14.1) (2024-10-15)

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.13.1...@mdt/business-controllers@1.14.0) (2024-08-09)

**Note:** Version bump only for package @mdt/business-controllers

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.13.0...@mdt/business-controllers@1.13.1) (2024-08-07)

### Features

- ✨ 追加手动分页 ([38f7c2f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/38f7c2f6740e3b5abf99f92c5d80b78b06a4866b))

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.12.0...@mdt/business-controllers@1.13.0) (2024-07-22)

**Note:** Version bump only for package @mdt/business-controllers

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.11.3...@mdt/business-controllers@1.12.0) (2024-07-02)

**Note:** Version bump only for package @mdt/business-controllers

## [1.11.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.11.2...@mdt/business-controllers@1.11.3) (2024-06-24)

**Note:** Version bump only for package @mdt/business-controllers

## [1.11.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.11.1...@mdt/business-controllers@1.11.2) (2024-06-03)

**Note:** Version bump only for package @mdt/business-controllers

## [1.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.11.0...@mdt/business-controllers@1.11.1) (2024-05-20)

**Note:** Version bump only for package @mdt/business-controllers

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.10.1...@mdt/business-controllers@1.11.0) (2024-05-13)

### Features

- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.10.0...@mdt/business-controllers@1.10.1) (2024-04-29)

### Bug Fixes

- 🐛 config 头部加令牌方法重命名 ([856ca3b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/856ca3b62f7fa3a9fae290e12815c8e1072cf8e9))

### Features

- ✨ 通用 request config 处理, 增加 fetchClass, 修改 sso, collector-sso ([b159192](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b159192080f27bee590ecbacfa20a6c93e14ac65))

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.10...@mdt/business-controllers@1.10.0) (2024-03-11)

### Features

- ✨ 文件夹功能 ([9d6680e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d6680e70da17e00d743efcf602480e9e62f0f40))

## [1.9.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.9...@mdt/business-controllers@1.9.10) (2024-03-07)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.8...@mdt/business-controllers@1.9.9) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))

## [1.9.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.7...@mdt/business-controllers@1.9.8) (2023-12-01)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.6...@mdt/business-controllers@1.9.7) (2023-11-13)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.5...@mdt/business-controllers@1.9.6) (2023-10-25)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.4...@mdt/business-controllers@1.9.5) (2023-09-18)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.3...@mdt/business-controllers@1.9.4) (2023-09-04)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.2...@mdt/business-controllers@1.9.3) (2023-07-24)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.1...@mdt/business-controllers@1.9.2) (2023-07-03)

**Note:** Version bump only for package @mdt/business-controllers

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.9.0...@mdt/business-controllers@1.9.1) (2023-06-13)

**Note:** Version bump only for package @mdt/business-controllers

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.8.0...@mdt/business-controllers@1.9.0) (2023-06-05)

**Note:** Version bump only for package @mdt/business-controllers

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.7.0...@mdt/business-controllers@1.8.0) (2023-03-13)

**Note:** Version bump only for package @mdt/business-controllers

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.6.0...@mdt/business-controllers@1.7.0) (2023-02-13)

### Features

- ✨ 增加拼音模糊搜索方式 ([cb3fc68](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb3fc68424f2f6db83ecba9f78ea8de028687ba6))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.5.0...@mdt/business-controllers@1.6.0) (2022-12-13)

**Note:** Version bump only for package @mdt/business-controllers

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.4.0...@mdt/business-controllers@1.5.0) (2022-12-12)

### Features

- ✨ [Table 通用]: 可选列表增加全选功能 ([e3e368a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3e368ae2c5e3224e26aea0e0c47ed580ee4ee14))

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.3.0...@mdt/business-controllers@1.4.0) (2022-10-31)

### Features

- ✨ 数据市场、我的数据 优化 ([40ac4c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40ac4c67afebdfc586da4221e2cb934c6bea51db))

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.2.1...@mdt/business-controllers@1.3.0) (2022-09-26)

**Note:** Version bump only for package @mdt/business-controllers

## [1.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.2.0...@mdt/business-controllers@1.2.1) (2022-09-19)

**Note:** Version bump only for package @mdt/business-controllers

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-controllers@1.1.0...@mdt/business-controllers@1.2.0) (2022-08-12)

### Bug Fixes

- 🐛 fix bug ([11e790f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/11e790f1b38b898a2475f98ccf6f790049788941))
- 🐛 前端分页时不再监听加载下一页 ([6b51e09](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6b51e097e25e3ac6e9deec592fe53b926e86d85d))

# 1.1.0 (2022-06-30)

### Features

- ✨ 跨机构分享创建及审批 ([265ec15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/265ec15579d10f15b3fb0baf01acefac60453071))
