export interface IPaginationQuery {
  page_size?: number;
  page_num?: number;
  fetch_total_count?: boolean;
}

export interface IRefreshTimerPost {
  refresh_timer?: string | null;
  next_refresh_time?: number | null;
}

export interface IOrderQuery {
  order_by?: string;
  descending?: boolean;
}

export interface IDatapkgId {
  datapkg_id: string;
}

export interface IPkgBoundWfspec {
  workflow_spec_ids: string[];
}

export interface IDatapkgIds {
  datapkg_ids: string[];
}

export interface ITaggroupId {
  taggroup_id: string;
}

export interface ITaggroupIds {
  taggroup_ids: string[];
}

export interface ITaggroupTags {
  taggroup_tags: string[];
  taggroup_id: string;
}

export interface ITags {
  tags: ITaggroupTags[];
}

export interface IAlbumId {
  album_id: string[];
}

export interface IAlbumIds {
  album_ids: string[];
}

export interface IId {
  id: string;
}

export interface IIds {
  ids: string;
}

export interface IConstraintId {
  constraint_id: string;
}

export interface IConstraintIds {
  constraint_ids: string[];
}

export interface IPermissions<T> {
  permissions: T;
}

export interface ITotal {
  total: number;
}

export interface IDatasetId {
  dataset_id: string;
}

export interface IDatasetIds {
  datapkg_ids: string;
}

export interface ITicketId {
  ticket_id: string;
}

export interface ITaskId {
  task_id: string;
}

export interface IUserId {
  user_id: number;
  user_uuid: string;
}

export type IEmptyObj = object;

export type IRowValue = string | number | boolean | object | null | undefined;

// 资源所有权  app: app级别   user: 用户级别
export type IOwnership = 'app' | 'user';
