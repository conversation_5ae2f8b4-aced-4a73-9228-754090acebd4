import type { IResourceAuthorizationPut } from './auth';
import type { IFloworkFormSpec } from './flowork';
import type { IRequestOwner } from './request';

export interface IOneTableNoticesResult {
  rc: number;
  result?: any;
  message?: string;
  error?: {
    message: string;
    requestId: string;
  };
}

export interface IOneTableNotices {
  url?: string;
  title?: string;
  _extra?: any;
  recipients?: { user_id: number[]; app_id: number }[];
  recipient_roles?: { user_id: number[]; app_id: number }[];
  recipient_roles_leader_only?: boolean;
  category?: 1 | 2 | 3 | 4 | 5;
  msg_type?: 'text' | 'markdown' | 'textcard';
  message: string;
}

export interface IOneTableNoticesBody {
  notices: IOneTableNotices[];
  app: number;
}

export type IOneTableUpdateAuthorizationOfGrantedFormBody = IResourceAuthorizationPut;

export interface IFormilyCheckRowResultError {
  instancePath: string;
  schemaPath: string;
  keyword: string;
  message: string;
  params: Record<string, any>;
}

export interface IFormilyCheckRowResult {
  validate: boolean;
  msg?: string;
  errors: IFormilyCheckRowResultError[];
}

export interface IIFormilyCheckRowsBySchemaResult {
  success: boolean;
  page_data?: IFormilyCheckRowResult[];
}

export interface IFormilyCheckRowsBySchemaBody {
  rows: Record<string, any>[];
  schema: IFloworkFormSpec;
  pkgId?: string;
  schemaOwner?: IRequestOwner;
}
