import type { IOrderQuery, IOwnership, IPaginationQuery, IRefreshTimerPost, IRowValue, ITaggroupTags } from './comm';

export type IDatapkgAggType = 'detail' | 'aggregatation' | 'index';

export interface IDatapkgUpdateFrequency {
  unit: string;
  value: number;
}

export type IDatapkgPackageType =
  | 'table'
  | 'view'
  | 'sql'
  | 'extable'
  | 'collaboration'
  | 'collaboration_child'
  | 'customer'
  | 'mview'
  | 'collaboration_v2';

export type IDatapkgConnRole = 1 | 2 | 3 | 4 | 5;

export type IDatapkgUpdateMethod = 'user' | 'backend';

export type IDatapkgGeometryType =
  | 'point'
  | 'line'
  | 'polygon'
  | 'plain'
  | 'point_to_point'
  | 'point_to_line'
  | 'point_to_polygon'
  | 'line_to_point'
  | 'line_to_line'
  | 'line_to_polygon'
  | 'polygon_to_point'
  | 'polygon_to_line'
  | 'polygon_to_polygon';

export interface IDatapkgRichDescription {
  article_id?: string;
  content?: string;
  content_type?: string;
  version?: string;
}

export type IDatapkgColumnType =
  | 'bool'
  | 'datetime'
  | 'date'
  | 'float'
  | 'geometry'
  | 'int'
  | 'json'
  | 'str'
  | 'time'
  | 'bytea'
  | 'bit'
  | 'bigint'
  | 'array'
  | 'mediajson'
  | 'timestamp';

export interface IDatapkgColumnProjections {
  type: IDatapkgColumnType;
  target: string;
  keypath: string;
  formula: string;
  source: string;
  is_extra: boolean;
  name?: string;
}

export type IDatapkgPermission =
  | 'read'
  | 'update'
  | 'insert'
  | 'delete'
  | 'download'
  | 'view_detail'
  | 'view_full_description';

export type IDatapkgCollaboratePermission = 'read' | 'update' | 'delete' | 'insert' | 'view_detail';

export type IDatapkgStatus = 'need_approval' | 'approved' | 'rejected';

export interface IDatapkgFeatureFlags {
  disable_preview: boolean;
  disable_view_detail: boolean;
  inconsistent_cnt: boolean;
  mask_data: boolean;
  row_level_permission: boolean; // 是否协同数据包
}

export type IDatapkgProjection = IDatapkgColumnProjections;

export interface IDatapkgExternalDb {
  sql: string;
}

export interface IDatapkg extends IRefreshTimerPost {
  agg_type?: IDatapkgAggType;
  update_frequency?: IDatapkgUpdateFrequency;
  id: string;
  dataset_id: string;
  view_detail?: boolean;
  available_columns?: string[];
  write_options?: object;
  table_name: string;
  table_cond?: string;
  user_id: number;
  count?: number | string;
  package_type: IDatapkgPackageType;
  table_schema: string;
  conn_role: IDatapkgConnRole;
  update_method?: IDatapkgUpdateMethod;
  geometry_type: IDatapkgGeometryType;
  rich_description?: IDatapkgRichDescription;
  full_description?: IDatapkgRichDescription;
  update_time: number;
  data_schema?: object;
  approver?: number;
  column_projections?: IDatapkgColumnProjections[];
  tags?: ITaggroupTags[];
  permissions?: IDatapkgPermission[];
  create_time: number;
  name: string;
  description?: string;
  status: IDatapkgStatus;
  feature_flags: IDatapkgFeatureFlags;
  app_id: number;
  projection?: IDatapkgProjection;
  key_columns?: Record<string, string>;
  external_db?: IDatapkgExternalDb;
  ownership?: IOwnership;
  last_refresh_time?: number;
  row_permission_strategy?: IDatapkgRowPermissionStrategy;
}

export interface IDatapkgQualityReport {
  datapkg_time: number;
  manual_result: boolean;
  app_id: number;
  datapkg_id: string;
  auto_result: boolean;
  manual_time: number;
  name: string;
  auto_time: number;
}

export interface IDatapkgGetQuery {
  need_permissions: boolean;
}

export interface IDatapkgPatch extends IRefreshTimerPost {
  agg_type?: IDatapkgAggType;
  update_frequency?: IDatapkgUpdateFrequency;
  dataset_id?: string;
  available_columns?: string[];
  write_options?: object;
  table_name?: string;
  table_cond?: string;
  count?: number;
  package_type?: IDatapkgPackageType;
  table_schema?: string;
  update_method?: IDatapkgUpdateMethod;
  geometry_type?: IDatapkgGeometryType;
  rich_description?: IDatapkgRichDescription;
  full_description?: IDatapkgRichDescription;
  data_schema?: object;
  sql?: string;
  column_projections?: IDatapkgColumnProjections;
  tags?: ITaggroupTags[];
  name?: string;
  description?: string;
  feature_flags?: IDatapkgFeatureFlags;
  remove_tags?: ITaggroupTags[];
  key_columns?: Record<string, string>;
  force_refresh?: boolean;
}

export type IDatapkgDownloadFileType = 'csv' | 'xlsx' | 'xls';

export interface IDatapkgDownloadPost {
  only?: string[];
  file_type?: IDatapkgDownloadFileType;
  geo?: boolean;
  condition?: IDatapkgRowsQueryCondition[];
  operator_filter?: IOperatorFilter;
  excludes?: string[];
  q?: string;
  geometry_format?: string;
  column_mapping?: Record<string, string>;
}

export interface IDatapkgDownload {
  task_id: string;
  file_id: string;
}

export interface IDatapkgPublishPost {
  copy_data?: boolean;
  description?: string;
  name?: string;
}

export interface IDatapkgRefreshPost {
  force_refresh?: boolean;
}

export interface IDatapkgRows {
  columns: string[];
  values: IRowValue[][];
}

export interface IDatapkgRowsPatch {
  values: Record<string, any>;
  condition?: Record<string, any>;
  legacy_condition?: Record<string, any>;
  update_all?: boolean;
  srs?: string;
  row_permission_params?: {
    check_data_submitted?: boolean;
    permission_scope?: 'all' | 'normal';
  };
}

export interface IDatapkgRowsPatchRslt {
  updated: number;
}

export interface IDatapkgUpdateByUploadPost {
  description?: string;
  region: number;
  tags?: ITaggroupTags[];
  no_geo_validate?: boolean;
  force_replace?: boolean;
  append?: boolean;
  file?: string;
  file_id?: string;
  column_type?: Record<string, IDatapkgColumnType>;
  key_columns?: Record<string, string>;
  geometry_format?: string;
  srs?: string;
}

export interface IDatapkgChecklogsQuery extends IPaginationQuery {
  all_version?: boolean;
  constraint_id?: string;
  datapkg_version?: number;
}

export interface IDatapkgChecklogsBatchQuery extends IPaginationQuery {
  datapkg_ids: string[];
  all_version?: boolean;
  constraint_ids?: string[];
  outdate?: boolean;
}

export interface IDatapkgChecklog {
  datapkg_id: string;
  constraint_id: string;
  result: boolean;
  datapkg_version: number;
  message: string;
  update_time: number;
}

export interface IDatapkgChecklogPut {
  constraint_id: string;
  result?: boolean;
  message?: string;
}

export interface IDatapkgChecklogDelete {
  constraint_id?: string;
  all_version?: boolean;
  datapkg_version?: number;
}

export interface IDatapkgCollaborateInfo {
  role_ids?: number[];
  permission: IDatapkgCollaboratePermission;
  conditions?: IDatapkgRowsQueryCondition[];
  user_ids?: number[];
}

export interface IDatapkgRowPermissionStrategy {
  strategy: string; // onetable | classic | operator_filter
  config?: Record<string, any>;
}

export interface IDatapkgCollaboratePost {
  row_permission_strategy?: IDatapkgRowPermissionStrategy;
  collaborate_info?: IDatapkgCollaborateInfo[];
}

export type IDatapkgCollaborate = IDatapkgCollaboratePost;

export interface IDatapkgColumn {
  comment: string;
  id: string;
  name: string;
  type: IDatapkgColumnType;
  view_format?: Record<string, string>;
  title?: string;
  nullable?: boolean;
}

export interface IDatapkgColumnViewFormatMaskRegex {
  mask_type: 'regexp';
  pattern: string;
  replacement: string;
}

export interface IDatapkgColumnViewFormatMaskMd5 {
  mask_type: 'md5';
}

export interface IDatapkgColumnViewFormatMaskOverlay {
  mask_type: 'overlay';
  replacement: string;
  from_pos: number;
  for_pos: number;
}

export type IDatapkgColumnViewFormatMask =
  | IDatapkgColumnViewFormatMaskRegex
  | IDatapkgColumnViewFormatMaskMd5
  | IDatapkgColumnViewFormatMaskOverlay
  | null;

export interface IDatapkgColumnViewFormat {
  mask?: IDatapkgColumnViewFormatMask;
  format?: string;
}

export interface IDatapkgColumnPost {
  autoincrement?: boolean;
  comment?: string;
  name?: string;
  nullable?: boolean; // 是否允许为空值
  unique?: boolean; // 该列是否唯一
  index?: boolean; // 该列是否新建普通索引
  primary_key?: boolean; // 该列是否为主键
  type?: IDatapkgColumnType;
  view_format?: IDatapkgColumnViewFormat;
  force_refresh?: boolean; // 是否强制刷新meta信息
}

export type IDatapkgColumnPatch = IDatapkgColumnPost;

export interface IDatapkgColumnPatchQuery {
  column_attr?: string; // 'id' | 'name'
  change_ddl?: boolean;
}

export interface IDatapkgColumnBatchPatch extends Omit<Partial<IDatapkgColumn>, 'id'>, IDatapkgColumnPatchQuery {
  column: string; // id或name
}

export interface IDatapkgColumnBatchDelete extends IDatapkgColumnPatchQuery {
  columns: string[];
}

export type IDatapkgColumnDeleteQuery = IDatapkgColumnPatchQuery;

export interface IDatapkgColumnsGenealogyPost {
  reverse: boolean;
  source: string;
  source_datapkg_id: string;
  target: string;
  target_datapkg_id: string;
}

export interface IDatapkgColumnsGenealogy extends Omit<IDatapkgColumnsGenealogyPost, 'reverse'> {
  level: number;
}

export interface IDatapkgColumnsGenealogyDelete {
  reverse: boolean;
  source: string;
  target: string;
}

export interface IDatapkgColumnStats {
  column_name: string;
  column_type: IDatapkgColumnType;
  count?: number;
  max?: number | string;
  mean?: number;
  median?: number;
  min?: number | string;
  values?: (string | boolean)[];
}

export type IDatapkgConstraintOperator =
  | 'eq'
  | 'ne'
  | 'gt'
  | 'ge'
  | 'lt'
  | 'le'
  | 'between'
  | 'in'
  | 'not_in'
  | 'not'
  | 'true'
  | 'false'
  | 'start_with'
  | 'end_with'
  | 'like'
  | 'not_like'
  | 'match'
  | 'not_match'
  | 'contain'
  | 'not_contain'
  | 'null'
  | 'not_null';

export type IDatapkgConstraintType = 'column' | 'sql' | 'formula' | 'rawsql' | 'operator';

export interface IDatapkgConstraintPost {
  expression: string;
  name: string;
  operator?: IDatapkgConstraintOperator;
  description?: string;
  constraint_type: IDatapkgConstraintType;
  threshold?: any;
}

export interface IDatapkgConstraint extends IDatapkgConstraintPost {
  constraint_id: string;
  create_time: number;
  update_time: number;
  datapkg_id: string;
}

export type IDatapkgDdlElementType = 'index' | 'unique_constraint' | 'primary_key';

export interface IDatapkgDdlElementQuery {
  show_hidden?: boolean;
  type?: IDatapkgDdlElementType;
}

export interface IDatapkgDdlElementComm {
  type: IDatapkgDdlElementType;
  name: string;
}

export interface IDatapkgDdlElement extends IDatapkgDdlElementComm {
  columns: string[];
  has_hidden: boolean;
  unique: boolean;
}

export interface IDatapkgDdlElementPost {
  type: IDatapkgDdlElementType;
  columns: string[];
  unique?: boolean;
}

export interface IDatapkgDdlElementsBatchPost {
  to_create?: IDatapkgDdlElementPost[];
  to_drop?: IDatapkgDdlElementComm[];
}

export interface IDatapkgColumnsBatchUpdatePost {
  pkgId: string;
  keyColumns?: Record<string, any>;
  delColumns?: string[];
  updateColumns?: IDatapkgColumnBatchPatch[];
  addDllElements?: IDatapkgDdlElementPost[];
  delDllElements?: IDatapkgDdlElementComm[];
}

export interface IDatapkgGenealogysQuery {
  offspring_level?: number;
  ancestor_level?: number;
}

export interface IDatapkgGenealogy {
  level: number;
  source: string;
  targets: string[];
}

export type IDatapkgGenealogyScope = 'all' | 'app' | 'user';

export interface IDatapkgGenealogyPost {
  children?: string[];
  parent?: string[];
  replace?: boolean;
  scope?: IDatapkgGenealogyScope;
}

export interface IDatapkgGenealogyDelete {
  children?: string;
  parent?: string;
  scope?: IDatapkgGenealogyScope;
}

export interface IDatapkgRowsQueryCondition {
  column: string;
  // TODO 等待统一IDatapkgConstraintOperator
  operator: string;
  values?: any[];
}

export interface ICondition {
  column?: string;
  operator?: string;
  param?: any;
  $not?: ICondition;
}

export interface IOperatorFilter {
  $and?: Array<ICondition | IOperatorFilter>;
  $or?: Array<ICondition | IOperatorFilter>;
  $not?: ICondition;
}

export interface IDatapkgRowsQueryOrderBy {
  asc?: boolean;
  nulls_position?: 'default' | 'first' | 'last';
  field: string;
}

export interface IDatapkgRowsQuery extends IPaginationQuery {
  excludes?: string[];
  with_geometry?: boolean;
  condition?: IDatapkgRowsQueryCondition[];
  only?: string[];
  orderby?: IDatapkgRowsQueryOrderBy[];
  operator_filter?: Record<string, any>;
}

export interface IDatapkgRowsFuzzyQuery extends IPaginationQuery {
  q: string;
}

export type IDatapkgRowsQuerySpatialRelation =
  | 'within'
  | 'overlaps'
  | 'intersects'
  | 'intersection'
  | 'intersects_no_touches'
  | 'intersects_circle_fast';

export interface IDatapkgRowsQueryByGeometryPost {
  geometry?: string;
  datapkg_ids?: string[];
  spatial_relation?: IDatapkgRowsQuerySpatialRelation;
  circles?: number[][];
  buffer?: number;
  filters?: object[];
}

export interface IDatapkgRowsQueryNearestPost {
  top?: number;
  lng: number;
  max_distance: number;
  lat: number;
  filters?: object[];
  geo_filters?: object[];
}

export interface IDatapkgRowPatch {
  columns: string[];
  values: IRowValue[];
}

export interface IDatapkgRowsDelete {
  row_ids: number[];
}

export interface IDatapkgsAppQuery {
  target_app?: number;
  // 后端文档类型定义错误
  apps?: string;
}

export interface IDatapkgsQuery extends IOrderQuery, IPaginationQuery {
  name?: string;
  id?: string[];
  dataset_id?: string[];
  in_dataset?: boolean;
  tags?: ITaggroupTags[];
  need_tags?: boolean;
  feature_flags?: IDatapkgFeatureFlags;
  geometry_type?: IDatapkgGeometryType | IDatapkgGeometryType[];
  need_permissions?: boolean;
  privilege?: boolean | IDatapkgPermission | IDatapkgPermission[];
  fuzzy_tag?: string;
  status?: string;
  ownership?: IOwnership;
  descending?: boolean;
  exclude_customer?: boolean;
  folder?: string;
  nofolder?: boolean;
  product_type?: string;
  album_id?: string[];
  user_id?: number;
  column?: string;
  create_time_max?: number;
  create_time_min?: number;
  update_time_max?: number;
  update_time_min?: number;
}

export interface IDatapkgsQualityReportQuery {
  datapkg_ids?: string[]; // 数据包批量ID
  manual_result?: boolean | null; // 过滤合格或不合格的人工检查结果
  auto_result?: boolean | null; // 过滤合格或不合格的自动检查结果
  datapkg_name?: string;
}

export interface IDatapkgMeta {
  rich_description?: IDatapkgRichDescription;
  full_description?: IDatapkgRichDescription;
}

export interface IDatapkgsMetaPost extends IDatapkgMeta {
  datapkg_ids: string[];
}

export interface IDatapkgStatsUsage {
  total_using_app: number;
  total_using_user: number;
}

export interface IDatapkgsColumn {
  datapkg_id: string;
  columns: IDatapkgColumn[];
}

export interface IDatapkgQualityReportPatch {
  manual_result: boolean;
}

export interface IDatapkgQualityReportPatchResult extends IDatapkgQualityReportPatch {
  manual_time: number;
}

export interface IDatapkgQualityStatsResult {
  auto_result: boolean;
  count: number;
  manual_result: boolean;
}

export interface IDatapkgViewPost extends Partial<IDatapkg> {
  name: string;
}
