import { IPaginationQuery } from './comm';
import { IOperatorFilter } from './datapkgs';

export type IIFloworkTaskStatus = 'idle' | 'ready' | 'completed' | 'cancelled' | 'waiting';
export type IIFloworkTaskType = 'normal' | 'form' | 'approval';
export type IIFloworkApprovalResult = 'approved' | 'rejected' | 'pending' | 'cancelled' | 'request_more_info';
export type IWorkflowSpecStatus = 'default' | 'outdate' | 'deleted';
export type IWorkflowStatus = 'started' | 'ready' | 'completed' | 'ongoing' | 'cancelled';
export interface ICommQuery extends IPaginationQuery {
  create_time_max?: number;
  create_time_min?: number;
  update_time_max?: number;
  update_time_min?: number;
  // "+name,-address,price": 按name升序,address降序,price升序排列
  // 所有允许的排序字段为: workflow_id,create_time,update_time
  orderby?: string;
  approval_result?: IIFloworkApprovalResult;
}

export type IFloworkFormsQuery = IPaginationQuery;

export interface IFloworkFormsPostQuery {
  tag?: string;
  name?: string;
  fuzzy_tag?: string;
  fuzzy_name?: string;
  form_type?: string;
  process_type?: string;
  meta_query?: Record<string, any>;
  orderby?: string;
  meta_column_types?: Record<string, any>;
  create_time_max?: number;
  create_time_min?: number;
  update_time_max?: number;
  update_time_min?: number;
}

export interface IFloworkFormSpec {
  formilySchema: Record<string, any>;
  allSettingValues: Record<string, any>;
}

export interface IFloworkFormPost {
  name: string;
  form_spec: IFloworkFormSpec;
  tags?: string[];
  description?: string;
  extra_meta?: Record<string, any>;
  form_type?: string;
  process_type?: string;
}

export type IFloworkFormPatch = Partial<IFloworkFormPost>;

export interface IFloworkForm extends IFloworkFormPost {
  id: string;
  create_time: number;
  update_time: number;
  user_id: number;
  app_id: number;
}

export interface IFloworkServiceFunction {
  function_name: string;
  title: string;
  description: string;
  schema?: any;
}

export interface IFloworkService {
  service_name: string;
  description: string;
  title: string;
  functions: IFloworkServiceFunction[];
}

export interface IFloworkTasksQuery extends ICommQuery {
  with_data?: boolean;
  assign_me?: boolean;
  task_type?: string;
  executor?: number;
  workflow_id?: string;
  parent_workflow_id?: string;
  status?: string | string[];
  workflow_spec_id?: string | string[];
  executor_is_me?: boolean;
  workflow_data_query?: Record<string, any>;
  fuzzy_bpmn_name?: string;
  bpmn_name?: string | string[];
  fuzzy_workflow_name?: string;
  with_parent_workflow_id?: boolean;
  name?: string;
  fuzzy_name?: string;
  process_type?: string[];
}

export interface IFloworkTaskGet {
  with_data?: boolean;
}

export interface IOneTableRootDispatchWorkflow {
  create_time: number;
  root_dispatch_workflow_id: string;
  datapkg_id: string;
  form_name: string;
}

export interface IFloworkTask {
  executor?: number;
  assign_groups?: number[];
  assign_roles?: number[];
  assign_orgs?: number[];
  // 数据表单
  form_spec?: Record<string, any>;
  task_id: string;
  status: IIFloworkTaskStatus;
  name: string;
  bpmn_name: string;
  create_time: number;
  task_type: IIFloworkTaskType;
  update_time: number;
  approval_result: IIFloworkApprovalResult;
  complete_comment?: string;
  assign_users?: number[];
  workflow_id: string;
  data?: Record<string, any>;
  parent_workflow_id?: string;
}

export interface IFloworkTaskCompletePost {
  form_data?: Record<string, any>;
  complete_comment?: string;
  approval_result: IIFloworkApprovalResult;
}

export interface IWorkflowsQuery extends ICommQuery {
  initiator_is_me?: boolean;
  with_form_data?: boolean;
  with_data?: boolean;
  initiator_app?: number;
  status?: IWorkflowStatus;
  workflow_spec_id?: string;
  with_spec?: 'simple' | 'detail';
  id?: string;
  initiator?: number;
  name?: string;
  fuzzy_name?: string;
  process_type?: string[];
}

export interface IWorkflowsQueryPost extends Omit<IWorkflowsQuery, 'page_num' | 'page_size' | 'id'> {
  ids?: string[];
  workflow_data_query?: Record<string, any>;
}

export interface IWorkflow {
  // 精确查找
  initiator: number;
  workflow_id: string;
  status: IWorkflowStatus;
  create_time: number;
  update_time: number;
  approval_result: IIFloworkApprovalResult;
  spec?: Record<string, any>;
  spiff?: Record<string, any>;
  form_data?: Record<string, any>;
  initiator_app: number;
  engine_context?: Record<string, any>;
  name: string;
  description?: string;
  data?: Record<string, any>;
  parent_workflow_id?: string;
}

export interface IWorkflowGet {
  with_data?: boolean;
  with_form_data?: boolean;
  with_spec?: string;
  with_engine?: boolean;
}

export interface IWorkflowDelete {
  delete_linked_workflows: 'descendants' | 'children';
}

export type IWorkflowCompletePost = IFloworkTaskCompletePost;

export interface IWorkflowNodesQuery {
  with_data?: boolean;
}

export interface IWorkflowCancelPost {
  cancel_linked_workflows?: null | 'children' | 'descendants';
  success?: boolean;
  cancel_comment?: string;
}

export interface IWorkflowNode {
  approval_result?: IIFloworkApprovalResult;
  status: IIFloworkTaskStatus;
  id: string;
  data: Record<string, any>;
  spec: {
    id: string;
    name: string;
    node_class: string;
    task_type: IIFloworkTaskType;
  };
  children_ids: string[];
  parent_id: string;
  is_root?: boolean;
}

export interface IWorkflowTasksQuery extends ICommQuery {
  with_data?: boolean;
  assign_me?: boolean;
  executor_is_me?: boolean;
  task_type?: string;
  status?: string;
}

export interface IWorkflowSpecsQuery extends Omit<ICommQuery, 'approval_result'> {
  fuzzy_name?: string;
  // 精确查找
  name?: string;
  create_by_me?: boolean;
  path?: string;
  recursive?: boolean;
  id?: string;
  active?: boolean;
  process_type?: string;
}

export interface IWorkflowSpecsQueryPost extends Omit<IWorkflowSpecsQuery, 'page_size' | 'page_num' | 'id'> {
  ids?: string[];
}

export interface IWorkflowSpec extends IWorkflowSpecPost {
  id: string;
  status: IWorkflowSpecStatus;
  user_id: number;
  app_id: number;
  spiff?: any;
  create_time: number;
  update_time: number;
  form_spec?: Record<string, any>;
  version_id?: string;
  engine_context?: Record<string, any>;
  process_type: any;
  active: boolean;
  initiate_user_type?: number;
  initiate_anonymous?: boolean;
  candidate_starters: Record<string, any>;
  all_variables: Record<string, any>;
}

export interface IWorkflowSpecPost {
  description?: string;
  path?: string;
  name: string;
  bind_datapkg?: string;
  bpmn_xml: string;
  process_type?: 'normal' | 'approval' | 'datapkg_form' | 'one_table';
  active?: boolean;
}

export type IWorkflowSpecPut = Partial<IWorkflowSpecPost>;

export interface IWorkflowSpecGet {
  with_xml?: boolean;
  with_engine?: boolean;
}

export interface IWorkflowGenealogyQuery {
  down?: boolean;
  max_level?: number;
}

export interface IWorkflowGenealogyBatchPost extends IWorkflowGenealogyQuery {
  ids: string[];
  ignore_canceled?: boolean;
}

export interface IWorkflowGenealogy {
  source: string;
  targets: string[];
  level: number;
}

export interface IWorkflowGenealogyBatchItem {
  workflow_id: string;
  genealogy: IWorkflowGenealogy;
}

export type IWorkflowSpecFlowsQuery = Omit<IWorkflowsQuery, 'workflow_spec_id'>;

export interface IWorkflowSpecFlowPost {
  form_data: Record<string, any>;
  name?: string;
}

export type IWorkflowSpecsRefQuery = IPaginationQuery;

export interface IWorkflowSpecRef {
  id: string;
  name: string;
  global_variables: Record<string, any>[];
}

export interface IOnetableManagedFormsQueryPost {
  root_workflow_spec_id?: string;
  workflow_status?: string[];
  root_workflow_id?: string;
  bind_form_id?: string;
  bind_form_ids?: string[];
  orderby?: string;
  with_root_workflow_info?: boolean;
  with_form_info?: boolean;
  with_assign_workflow_info?: boolean;
  version_group?: number;
  bind_form_query?: IFloworkFormsPostQuery;
}

export interface IOnetableManagedForm {
  version_group: number;
  current_version: number;
  last_version?: number;
  version_history?: IOnetableFormVersionHistoryItem[];
  root_workflow_id: string;
  root_workflow?: Record<string, any>;
  bind_form_id: string;
  bind_form?: Record<string, any>;
  onetable_form_type: IOnetableFormType;
  assign_workflow_id?: string;
  assign_workflow?: Record<string, any>;
  unfold_version_history?: boolean;
}

export interface IOnetableGrantedFormQueryPost {
  scope?: 'app' | 'org';
  level?: 'read' | 'write';
  org_id?: number;
  workflow_status?: string[];
  is_started?: boolean;
  root_workflow_ids?: string[];
  bind_form_ids?: string[];
  bind_form_query?: IFloworkFormsPostQuery;
  orderby?: string;
  with_assign_workflow_info?: boolean;
  version_group?: number;
  unfold_version_history?: boolean;
  with_version_history?: boolean;
  create_time_min?: number;
  create_time_max?: number;
  update_time_min?: number;
  update_time_max?: number;
  initiate_time_min?: number;
  initiate_time_max?: number;
}

export type IOnetableGrantedForm = Omit<IOnetableManagedForm, 'unfold_version_history'>;

export type IOnetablePersonalStatus = 'unhandled' | 'handled';
export type IOnetableFormType = 'normal' | 'periodic' | 'endless';

export interface IOnetableInvolvedFormsQueryPost {
  root_workflow_spec_id?: string;
  assign_workflow_spec_id?: string;
  onetable_personal_status?: IOnetablePersonalStatus[];
  workflow_status?: string[];
  root_workflow_id?: string;
  assign_workflow_id?: string;
  bind_form_id?: string;
  bind_form_ids?: string[];
  form_admin_is_me?: boolean;
  orderby?: string;
  with_root_workflow_info?: boolean;
  with_assign_workflow_info?: boolean;
  with_assign_task_info?: boolean;
  with_form_info?: boolean;
  version_group?: number;
  unfold_version_history?: boolean;
  unhandled_reason?: string[];
  bind_form_query?: IFloworkFormsPostQuery;
}

export interface IOnetableInvolvedUsersQueryPost {
  max_depth?: number;
  root_workflow_id: string;
  only_downstream?: boolean;
  assign_workflow_id?: string; // 指定要查询的子流程
}

export interface IOnetableInvolvedUsers {
  users: number[];
}

export interface IOnetableFormVersionHistoryItem {
  version_group: number;
  current_version: number;
  last_version: number;
  root_workflow_id: string;
  bind_form_id: string;
  assign_workflow_id: string;
}

export interface IOnetableInvolvedForm {
  version_group: number;
  current_version: number;
  last_version?: number;
  version_history?: IOnetableFormVersionHistoryItem[];
  root_workflow_id: string;
  root_workflow: Record<string, any>;
  bind_form_id: string;
  bind_form?: Record<string, any>;
  onetable_form_type: IOnetableFormType;
  assign_workflow_id: string;
  assign_workflow?: Record<string, any>;
  assign_task_id?: string;
  assign_task?: Record<string, any>;
  onetable_personal_status?: IOnetablePersonalStatus;
  unhandled_reason?: string;
  handled_reason?: string;
}

export interface IOnetableDownstreamUsersPost {
  max_depth?: number;
  root_dispatch_workflow_id: string;
  fillform_workflow_spec_id?: string;
  check_user_in_org?: boolean;
  check_submitted?: boolean;
  check_data_submitted?: boolean;
  assign_workflow_id?: string;
  for_superior?: boolean;
}

export interface IOnetableDownstreamStatPost {
  assign_workflow_id?: string;
  max_depth?: number;
  data_filter?: IOperatorFilter;
  row_permission_params?: {
    check_data_submitted?: boolean;
    permission_scope?: 'all' | 'normal';
    check_granted?: boolean;
  };
}

interface ITasksQueryPost {
  workflow_spec_id?: string;
  workflow_id?: string;
  bind_form_id?: string;
  root_workflow_id?: string;
  workflow_status?: string[];
  task_status?: string[];
  orderby?: string;
  parent_workflow_id?: string;
  with_task_info?: boolean;
  with_workflow_info?: boolean;
  with_parent_workflow_info?: boolean;
}

export interface IOnetableTask {
  task_id: string;
  task_data: Record<string, any>;
  task_status: string;
  task_create_time: number;
  task_update_time: number;
  workflow_id: string;
  workflow_status: string;
  workflow_create_time: number;
  workflow_update_time: number;
  root_workflow_id: string;
  bind_form_id: string;
  task_info: Record<string, any>;
  workflow_info: Record<string, any>;
  parent_workflow_info: Record<string, any>;
}

export type IOnetableApprovalTasksQueryPost = ITasksQueryPost;

export type IOnetableAssignTasksQueryPost = ITasksQueryPost;

export type IOnetableManageTasksQueryPost = ITasksQueryPost;

export type IOnetableAssignTask = IOnetableTask;

export type IOnetableApprovalTask = IOnetableTask;

export type IOnetableManageTask = IOnetableTask;

export interface IOnetableDownstreamUsers {
  users: number[];
  reinforces: number[];
}

interface IOnetableDownSteamStatStat {
  total_assign_rows: number;
  total_assign_users: number;
  earliest_data_update_time: number;
  latest_data_update_time: number;
}

export interface IOnetableDownstreamStat {
  assignees: number[];
  assign_workflow_id: string;
  parent_workflow_id: string;
  data_state: string;
  stat: IOnetableDownSteamStatStat;
  stat_include_downstream: IOnetableDownSteamStatStat & {
    max_send_down_depth: number;
  };
}

export interface IOnetableExecuteCommandsResult {
  success: boolean;
}

export type IOnetableExecuteCommandCommand =
  | 'reinforce'
  | 'transfer'
  | 'refuse'
  | 'submit'
  | 'unsubmit'
  | 'approve'
  | 'reject'
  | 'send_down'
  | 'approval_reinforce'
  | 'approval_transfer'
  | 'approval_unsubmit'
  | 'manage_reinforce'
  | 'manage_transfer';

export interface IOnetableExecuteCommand {
  command: IOnetableExecuteCommandCommand;
  users?: (number | { primarys: number[]; reinforces: number[] })[];
  comment?: string;
  mode?: 'append' | 'replace';
  clear_reinforce?: boolean;
  apply_all?: boolean;
  ignore_if_empty?: boolean;
  assign_workflow_ids?: string[];
}

export interface IOnetableExecuteCommandsPost {
  root_workflow_id: string;
  commands: IOnetableExecuteCommand[];
}

export interface IOnetablePeriodFormNameQueryPost {
  template: string;
  now?: number;
  form?: IFloworkFormPatch;
  previous_form?: IFloworkFormPatch;
  strict?: boolean;
}
