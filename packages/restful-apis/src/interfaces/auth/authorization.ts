export interface IAuthorizationPost {
  resource_ids?: (string | number)[]; // 用于授权的权限/资源id列表，可以是permission的ID，也可以是vault资源的uuid
  privilege_type?: string; // 传入时仅授权对应权限类型，默认为read
  grantee_ids?: (string | number)[]; // 授予对象id 列表
  expire_time?: number; // 权限过期时间戳，该时间戳是一个整数，代表从1970年1月1日00:00:00(UTC至当前时刻的秒数。
  grantee_name_id_pair?: {
    grantee_id: number | string;
    grantee_name: string;
    grantee_app_id: number | string;
    privilege_list?: string[];
  }[];
  grantee_with_privilege?: {
    grantee_id: number | string;
    grantee_name?: string;
    grantee_app_id: number | string;
    privilege_list?: string[];
  }[];
}
export interface IAuthorizationDelete {
  resource_ids?: (string | number)[]; // 用于授权的权限/资源id列表，可以是permission的ID，也可以是vault资源的uuid
  grantee_with_privilege: {
    grantee_id: number | string;
    grantee_name?: string;
    grantee_app_id: number | string;
    privilege_list?: string[];
  }[];
  grantee_ids?: (string | number)[]; // 授予对象id 列表
  all_privilege?: boolean; // 所有
}

export interface IAuthorizationGetParam {
  resourceType: string;
}

export interface IAuthorizationGetQuery {
  resourceIds: (string | number)[];
  privilegeType: string;
}

export interface IAuthorizationResourceResult {
  resource_id: string;
  privilege_type: string;
  name: string;
  grantee_type: string;
  grantee: number;
  app_id: number | string;
  condition?: object;
}
