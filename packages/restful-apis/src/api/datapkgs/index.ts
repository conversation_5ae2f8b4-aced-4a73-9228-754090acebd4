import { DATAPKGS_URL } from '../../config';
import {
  IAlbumIds,
  IApiResponse,
  IConstraintId,
  IConstraintIds,
  IDatapkg,
  IDatapkgChecklog,
  IDatapkgChecklogDelete,
  IDatapkgChecklogPut,
  IDatapkgChecklogsBatchQuery,
  IDatapkgChecklogsQuery,
  IDatapkgCollaborate,
  IDatapkgCollaboratePost,
  IDatapkgColumn,
  IDatapkgColumnBatchDelete,
  IDatapkgColumnBatchPatch,
  IDatapkgColumnDeleteQuery,
  IDatapkgColumnPatch,
  IDatapkgColumnPatchQuery,
  IDatapkgColumnPost,
  IDatapkgColumnsGenealogy,
  IDatapkgColumnsGenealogyDelete,
  IDatapkgColumnsGenealogyPost,
  IDatapkgColumnStats,
  IDatapkgConstraint,
  IDatapkgConstraintPost,
  IDatapkgDdlElement,
  IDatapkgDdlElementPost,
  IDatapkgDdlElementQuery,
  IDatapkgDdlElementsBatchPost,
  IDatapkgDdlElementType,
  IDatapkgDownload,
  IDatapkgDownloadPost,
  IDatapkgGenealogy,
  IDatapkgGenealogyDelete,
  IDatapkgGenealogyPost,
  IDatapkgGenealogysQuery,
  IDatapkgGetQuery,
  IDatapkgId,
  IDatapkgIds,
  IDatapkgPatch,
  IDatapkgPermission,
  IDatapkgPublishPost,
  IDatapkgQualityReport,
  IDatapkgQualityReportPatch,
  IDatapkgQualityReportPatchResult,
  IDatapkgQualityStatsResult,
  IDatapkgRefreshPost,
  IDatapkgRowPatch,
  IDatapkgRows,
  IDatapkgRowsDelete,
  IDatapkgRowsFuzzyQuery,
  IDatapkgRowsPatch,
  IDatapkgRowsPatchRslt,
  IDatapkgRowsQuery,
  IDatapkgRowsQueryByGeometryPost,
  IDatapkgRowsQueryNearestPost,
  IDatapkgsAppQuery,
  IDatapkgsColumn,
  IDatapkgsMetaPost,
  IDatapkgsQualityReportQuery,
  IDatapkgsQuery,
  IDatapkgStatsUsage,
  IDatapkgUpdateByUploadPost,
  IDatapkgViewPost,
  IEmptyObj,
  IPaginationQuery,
  IPermissions,
  IPkgBoundWfspec,
  IRequestPromise,
  IRequestRequestConfig,
  ITags,
  ITaskId,
  ITotal,
} from '../../interfaces';
import request from '../../request';

// 批量删除数据包
export const deleteDatapkgs = (data: IDatapkgIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgIds>>;
};

// 获取单个数据包的信息
export const getDatapkg = (datapkgId: string, config?: IRequestRequestConfig<IDatapkgGetQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}`,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 修改数据包的部分属性
export const patchDatapkg = (datapkgId: string, data: IDatapkgPatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 删除Datapkg
export const deleteDatapkg = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}`,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 数据包下载
export const downloadDatapkg = (datapkgId: string, data: IDatapkgDownloadPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/download`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgDownload>>;
};

// 预览数据包数据
export const previewDatapkgRows = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/preview`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 数据包发布
export const publishDatapkg = (datapkgId: string, data: IDatapkgPublishPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/publish`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 数据包刷新
export const refreshDatapkg = (datapkgId: string, data: IDatapkgRefreshPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/refresh`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 上传更新数据包数据
export const updateDatapkgByUpload = (
  datapkgId: string,
  data: IDatapkgUpdateByUploadPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/upload`,
    data,
  }) as IRequestPromise<IApiResponse<ITaskId>>;
};

// 将数据包从主题库批量下架
export const deleteDatapkgIdFromAlbums = (datapkgId: string, data: IAlbumIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/albums`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 获取数据包的检查日志
export const queryDatapkgChecklogs = (datapkgId: string, config?: IRequestRequestConfig<IDatapkgChecklogsQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/checklogs`,
  }) as IRequestPromise<IApiResponse<IDatapkgChecklog[]>>;
};

// 批量获取数据包的检查日志
export const batchQueryDatapkgChecklogs = (data: IDatapkgChecklogsBatchQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/batch/checklogs/query`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgChecklog[]>>;
};

// 更新一条数据包的检查日志
export const putDatapkgChecklog = (datapkgId: string, data: IDatapkgChecklogPut, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${DATAPKGS_URL}/${datapkgId}/checklogs`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgChecklog>>;
};

// 删除数据包的检查日志
export const deleteDatapkgChecklogs = (
  datapkgId: string,
  data: IDatapkgChecklogDelete,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/checklogs`,
    data,
  }) as IRequestPromise<IApiResponse<ITotal>>;
};

// 获取协同数据包
export const getDatapkgCollaborate = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/collaborate`,
  }) as IRequestPromise<IApiResponse<IDatapkgCollaborate>>;
};

// 新建协同数据包
export const postDatapkgCollaborate = (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/collaborate`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 更新协同数据包
export const patchDatapkgCollaborate = (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${DATAPKGS_URL}/${datapkgId}/collaborate`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 删除协同数据包
export const deleteDatapkgCollaborate = (
  datapkgId: string,
  data: IDatapkgCollaboratePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/collaborate`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};

// 获取数据包列信息
export const getDatapkgColumns = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/columns`,
  }) as IRequestPromise<IApiResponse<IDatapkgColumn[]>>;
};

// 新增数据包一列
export const postDatapkgColumn = (datapkgId: string, data: IDatapkgColumnPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/columns`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgColumn>>;
};

// 批量新增数据包列
export const batchPostDatapkgColumns = (
  datapkgId: string,
  data: IDatapkgColumnPost[],
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/batch/columns`,
    data: { columns: data },
  }) as IRequestPromise<IApiResponse<IDatapkgColumn[]>>;
};

// 批量更新列
export const batchPatchDatapkgColumns = (
  datapkgId: string,
  data: IDatapkgColumnBatchPatch[],
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}/columns`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgColumn[]>>;
};

// 批量删除数据包列
export const batchDeleteDatapkgColumns = (
  datapkgId: string,
  data: IDatapkgColumnBatchDelete,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/columns`,
    data,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 删除一列
export const deleteDatapkgColumn = (
  datapkgId: string,
  column: string,
  config?: IRequestRequestConfig<IDatapkgColumnDeleteQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/columns/${column}`,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 更新一列
export const patchDatapkgColumn = (
  datapkgId: string,
  column: string,
  data: IDatapkgColumnPatch,
  config?: IRequestRequestConfig<IDatapkgColumnPatchQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}/columns/${column}`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgColumn>>;
};

// 查询数据包ER图
export const queryDatapkgColumnsGenealogys = (
  datapkgId: string,
  config?: IRequestRequestConfig<IDatapkgGenealogysQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/columns_genealogy`,
  }) as IRequestPromise<IApiResponse<IDatapkgColumnsGenealogy[]>>;
};

// 新增数据包ER图
export const postDatapkgColumnsGenealogys = (
  datapkgId: string,
  data: IDatapkgColumnsGenealogyPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/columns_genealogy`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 删除数据包列ER图一个relation
export const deleteDatapkgColumnsGenealogy = (
  datapkgId: string,
  data: IDatapkgColumnsGenealogyDelete,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/columns_genealogy`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 统计数据包某列结果
export const statsDatapkgColumn = (datapkgId: string, columnName: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/columns/${columnName}/stats`,
  }) as IRequestPromise<IApiResponse<IDatapkgColumnStats>>;
};

// 获取数据包全部约束
export const getDatapkgAllConstraints = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints`,
  }) as IRequestPromise<IApiResponse<IDatapkgConstraint[]>>;
};

// 新增数据包约束
export const postDatapkgConstraint = (
  datapkgId: string,
  data: IDatapkgConstraintPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgConstraint>>;
};

// 删除数据包约束
export const deleteDatapkgAllConstraints = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints`,
  }) as IRequestPromise<IApiResponse<IConstraintIds>>;
};

// 获取数据包某个具体约束
export const getDatapkgConstraint = (datapkgId: string, constraintId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints/${constraintId}`,
  }) as IRequestPromise<IApiResponse<IDatapkgConstraint>>;
};

// 替换数据包约束
export const putDatapkgConstraint = (
  datapkgId: string,
  constraintId: string,
  data: IDatapkgConstraintPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints/${constraintId}`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgConstraint>>;
};

// 删除数据包某个约束
export const deleteDatapkgConstraint = (datapkgId: string, constraintId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints/${constraintId}`,
  }) as IRequestPromise<IApiResponse<IConstraintId>>;
};

// 立即检查约束
export const postCheckDatapkgConstraints = (
  datapkgId: string,
  data: IConstraintIds,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/constraints/check`,
    data,
  }) as IRequestPromise<IApiResponse<ITaskId>>;
};

// 获取数据包所有DDL信息
export const getDatapkgDdlElements = (datapkgId: string, config?: IRequestRequestConfig<IDatapkgDdlElementQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/ddl_elements`,
  }) as IRequestPromise<IApiResponse<IDatapkgDdlElement[]>>;
};

// 新增数据包DDL
export const postDatapkgDdlElements = (
  datapkgId: string,
  data: IDatapkgDdlElementPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/ddl_elements`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 删除DDL
export const deleteDatapkgDdlElement = (
  datapkgId: string,
  element: string,
  config: IRequestRequestConfig<{ type: IDatapkgDdlElementType }>,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/ddl_elements/${element}`,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 批量更新DDL
export const batchModifyDatapkgDdlElements = (
  datapkgId: string,
  data: IDatapkgDdlElementsBatchPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/ddl_elements/batch/modify`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 查询数据包族谱
export const queryDatapkgGenealogys = (datapkgId: string, config?: IRequestRequestConfig<IDatapkgGenealogysQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/genealogy`,
  }) as IRequestPromise<IApiResponse<IDatapkgGenealogy[]>>;
};

// 新增数据包族谱
export const postDatapkgGenealogy = (
  datapkgId: string,
  data: IDatapkgGenealogyPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/genealogy`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 删除数据包族谱
export const deleteDatapkgGenealogys = (datapkgId: string, config: IRequestRequestConfig<IDatapkgGenealogyDelete>) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/genealogy`,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 获取某个数据包具体的权限
export const getDatapkgPermissions = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/permissions`,
  }) as IRequestPromise<IApiResponse<IPermissions<IDatapkgPermission[]>>>;
};

// 获取某个数据包是否拥有某个权限
export const checkDatapkgHasPermission = (
  datapkgId: string,
  permission: IDatapkgPermission,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/permissions/${permission}`,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 数据包数据查询
export const queryDatapkgRows = (datapkgId: string, data: IDatapkgRowsQuery, config: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/query`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 数据包数据模糊查询
export const fuzzyQueryDatapkgRows = (datapkgId: string, config: IRequestRequestConfig<IDatapkgRowsFuzzyQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/query/fuzzy`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 使用围栏查询数据包数据
export const queryDatapkgRowsByGeometry = (
  datapkgId: string,
  data: IDatapkgRowsQueryByGeometryPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/query/geometry`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 数据包最近点查询
export const queryNearestDatapkgRows = (
  datapkgId: string,
  data: IDatapkgRowsQueryNearestPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/query/nearest`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 获取数据包数据
export const getDatapkgRows = (datapkgId: string, config?: IRequestRequestConfig<IPaginationQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/rows`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 增加数据包数据
export const postDatapkgRows = (datapkgId: string, data: IDatapkgRows, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/rows`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 更新数据包数据
export const patchDatapkgRows = (datapkgId: string, data: IDatapkgRowsPatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}/rows`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgRowsPatchRslt>>;
};

// 更新数据包数据
export const postDatapkgUpdateRows = (datapkgId: string, data: IDatapkgRows[], config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/actions/update_rows`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgRows[]>>;
};

// 删除数据包数据
export const deleteDatapkgRows = (datapkgId: string, data: IDatapkgRowsDelete, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/rows`,
    data,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 查询数据包单行数据
export const getDatapkgRow = (datapkgId: string, rowId: number, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/rows/${rowId}`,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 更改数据包单行数据
export const patchDatapkgRow = (
  datapkgId: string,
  rowId: number,
  data: IDatapkgRowPatch,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}/rows/${rowId}`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgRows>>;
};

// 删除数据包单行数据
export const deleteDatapkgRow = (datapkgId: string, rowId: number, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATAPKGS_URL}/${datapkgId}/rows/${rowId}`,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 获取数据包使用情况
export const statsDatapkgUsage = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/stats/usage`,
  }) as IRequestPromise<IApiResponse<IDatapkgStatsUsage>>;
};

// 获取多个数据包列数据
export const getDatapkgsColumns = (data: IDatapkgIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/batch/columns`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgsColumn[]>>;
};

// 更新数据包富文本类型的description
export const postDatapkgsMeta = (data: IDatapkgsMetaPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/meta`,
    data,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 批量查询数据包的信息
export const queryDatapkgs = (
  data: IDatapkgsQuery,
  config?: IRequestRequestConfig<IDatapkgsAppQuery & IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/query`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg[]>>;
};

// 获取数据包可用的tag
export const getDatapkgsUsedTags = (config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/tags`,
  }) as IRequestPromise<IApiResponse<ITags>>;
};

// 批量查询数据包数据质量情况
export const batchQueryDatapkgsQualityReport = (
  data: IDatapkgsQualityReportQuery,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/batch/quality_report`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgQualityReport[]>>;
};

// 人工标记数据包质量
export const patchDatapkgQualityReport = (
  datapkgId: string,
  data: IDatapkgQualityReportPatch,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATAPKGS_URL}/${datapkgId}/quality_report`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgQualityReportPatchResult>>;
};

// 查询数据包数据质量相关统计指标
export const getDatapkgsQualityStats = (config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/stats/quality`,
  }) as IRequestPromise<IApiResponse<IDatapkgQualityStatsResult[]>>;
};

// 将数据包关联多个主题库
export const datapkgBindAlbums = (datapkgId: string, data: IAlbumIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/albums`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 查询数据包绑定的流程
export const datapkgBoundWfspecs = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/workflow_specs`,
  }) as IRequestPromise<IApiResponse<IPkgBoundWfspec>>;
};

// 查找数据包视图
export const getDatapkgViews = (datapkgId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATAPKGS_URL}/${datapkgId}/views`,
  }) as IRequestPromise<IApiResponse<string[]>>;
};

// 创建数据包视图
export const postDatapkgView = (datapkgId: string, data: IDatapkgViewPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATAPKGS_URL}/${datapkgId}/views`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkg>>;
};
