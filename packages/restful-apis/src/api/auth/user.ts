import { AUTH_URL, AUTH_V2_URL } from '../../config';
import {
  IApiResponse,
  IEmptyObj,
  IImpersonateAppListParams,
  IImpersonaterecordApp,
  ILoginUser,
  IPostUsersQuery,
  IRequestPromise,
  IRequestRequestConfig,
  IRowValue,
  ISendBindMsgByEmail,
  ISendBindMsgByPhone,
  IShareResourcePermissions,
  IShareResourcePermissionsPatch,
  IShareResourcePermissionsQuery,
  IUser,
  IUserId,
  IUserIdRoleId,
  IUserIdRoleIdQuery,
  IUserPost,
  IUserPostExcel,
  IUserPut,
  IUserPutBatch,
  IUserQuery,
  IUserRoleItem,
  IUserRolesParams,
} from '../../interfaces';
import request from '../../request';

export function getUserByToken(config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_V2_URL}/user/bytoken`,
  }) as IRequestPromise<IApiResponse<ILoginUser>>;
}

// 获取用户列表
export const getAllUsers = (app_id: number, query?: IUserQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_V2_URL}/app/${app_id}/users`,
    params: query,
  }) as IRequestPromise<IApiResponse<IUser[]>>;
};

// 新建用户
export const postUser = (data: IUserPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/user`,
    data,
  }) as IRequestPromise<IApiResponse<IUserId>>;
};

// 通过excel新建用户
export const postUserByExcel = (data: IUserPostExcel, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/user/newbatch`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }) as IRequestPromise<IApiResponse<IUser[]>>;
};

// 修改用户
export const putUser = (data: IUserPut, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${AUTH_V2_URL}/user`,
    data,
  }) as IRequestPromise<IApiResponse<number>>;
};

// 批量修改用户
export const putUserBatch = (data: IUserPutBatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${AUTH_V2_URL}/user/batch`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 删除用户
export const deleteUsers = (ids: string, app_id: number, pass_to: number, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${AUTH_URL}/user?ids=${ids}&app_id=${app_id}&pass_to=${pass_to}`,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 获取模拟app
export const getImpersonaterecordApps = (params?: IImpersonateAppListParams, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_URL}/admin/app/impersonaterecord`,
    params,
  }) as IRequestPromise<IApiResponse<IImpersonaterecordApp[]>>;
};

// 检查密码
export function checkPassword(password: string, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_V2_URL}/user/checkpwd?password=${password}`,
  }) as IRequestPromise<IApiResponse<IRowValue>>;
}

// 重置用户密码
export function resetPassword(userId: number, password: string, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${AUTH_V2_URL}/user/${userId}/reset/password`,
    data: { password },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 解除锁定用户
export function unlock(userId: number, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${AUTH_V2_URL}/user/lock`,
    data: { user_id: userId },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 发送手机号绑定短信
export function sendBindMsgByPhone(data: ISendBindMsgByPhone, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/phone/send/bind`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 手机验证
export function verifyByPhone(vcode: string, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/phone/verify`,
    data: { vcode },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 发送邮箱号绑定短信
export function sendBindMsgByEmail(data: ISendBindMsgByEmail, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/email/send/bind`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 邮箱验证
export function verifyByEmail(vcode: string, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/email/verify`,
    data: { vcode },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 微信解绑
export function unbindByWechat(id: number, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${AUTH_URL}/login/wechat/bind`,
    data: { id },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 钉钉解绑
export function unbindByDingtalk(id: number, config?: IRequestRequestConfig) {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${AUTH_URL}/login/dingtalk/bind`,
    data: { id },
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
}

// 获取用户资源权限
export const getShareResourcePermissions = (
  userUuid: string,
  config?: IRequestRequestConfig<IShareResourcePermissionsQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_V2_URL}/user/${userUuid}/resources`,
  }) as IRequestPromise<IApiResponse<IShareResourcePermissions[]>>;
};

// 修改用户资源权限
export const patchShareResourcePermissions = (
  userUuid: string,
  data: IShareResourcePermissionsPatch,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${AUTH_V2_URL}/user/${userUuid}/resources`,
    data,
  }) as IRequestPromise<IApiResponse<IShareResourcePermissions[]>>;
};

// 通过用户Id获取角色Id
export const postQueryRoleIdsByUserIds = (data: IUserIdRoleIdQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/user/multi/roles`,
    data,
  }) as IRequestPromise<IApiResponse<IUserIdRoleId[]>>;
};

// 获取用户的角色信息
export const getUserRoles = (params?: IUserRolesParams, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_V2_URL}/user/roles`,
    params,
  }) as IRequestPromise<IApiResponse<IUserRoleItem[]>>;
};

// 使用复杂的查询条件查询本app的用户信息
export const postUsersQuery = (params: IPostUsersQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${AUTH_V2_URL}/users/query`,
    data: params,
  }) as IRequestPromise<IApiResponse<IUser[]>>;
};
