import { DATLAS_BFF_URL } from '../../config';
import {
  IApiResponse,
  IFormilyCheckRowsBySchemaBody,
  IIFormilyCheckRowsBySchemaResult,
  IOneTableNoticesBody,
  IOneTableNoticesResult,
  IOneTableUpdateAuthorizationOfGrantedFormBody,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

// 发送通知
export const postOneTableNotices = (body: IOneTableNoticesBody, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATLAS_BFF_URL}/dispatcher/oneTable/notices`,
    data: body,
  }) as IRequestPromise<IApiResponse<IOneTableNoticesResult>>;
};

// 一表通授权
export const postOneTableUpdateAuthorizationOfGrantedForm = (
  body: IOneTableUpdateAuthorizationOfGrantedFormBody,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATLAS_BFF_URL}/dispatcher/oneTable/update_authorization_of_granted_form`,
    data: body,
  }) as IRequestPromise<IApiResponse<string>>;
};

// 数据校验
export const postFormilyCheckRowsBySchema = (body: IFormilyCheckRowsBySchemaBody, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATLAS_BFF_URL}/formily/check_rows_by_schema`,
    data: body,
  }) as IRequestPromise<IApiResponse<IIFormilyCheckRowsBySchemaResult>>;
};

// 外部api转发
export const postOuterApi = (body: any, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATLAS_BFF_URL}/dispatcher/outer_api`,
    data: body,
  });
};
